#!/usr/bin/env python3
"""
Test script to verify that loss filters work correctly with multiple concurrent trades
"""

from backtesting_engine import BacktestingEngine
from config import Config
import pandas as pd

def test_loss_filters():
    """Test that loss filters work with ENABLE_GLOBAL_MARKET_FILTERS=False"""
    
    print("🧪 Testing Loss Filters with Multiple Concurrent Trades")
    print("=" * 60)
    
    # Configure for testing
    config = Config()
    config.USE_OPTIMIZED_RULES_ONLY = True
    config.ENABLE_GLOBAL_MARKET_FILTERS = False  # This should NOT disable loss filters
    config.ENABLE_TRADING_PAUSE_AFTER_LOSS = True
    config.ENABLE_CONSECUTIVE_LOSSES_FILTER = True
    config.MAX_CONCURRENT_TRADES = 2
    config.CURRENT_DATASET_SIZE = 2000  # Small dataset for quick test
    
    # Set aggressive loss thresholds for testing
    config.CONSECUTIVE_LOSSES_THRESHOLD = 3  # Pause after 3 consecutive losses
    config.TRADING_PAUSE_LOSS_THRESHOLD = 5.0  # Pause if 5% loss in lookback period
    config.TRADING_PAUSE_LOOKBACK_PERIOD = 100  # Look back 100 candles
    
    print(f"📊 Configuration:")
    print(f"   - Global market filters: {config.ENABLE_GLOBAL_MARKET_FILTERS}")
    print(f"   - Trading pause after loss: {config.ENABLE_TRADING_PAUSE_AFTER_LOSS}")
    print(f"   - Consecutive losses filter: {config.ENABLE_CONSECUTIVE_LOSSES_FILTER}")
    print(f"   - Max concurrent trades: {config.MAX_CONCURRENT_TRADES}")
    print(f"   - Consecutive losses threshold: {config.CONSECUTIVE_LOSSES_THRESHOLD}")
    print(f"   - Trading pause loss threshold: {config.TRADING_PAUSE_LOSS_THRESHOLD}%")
    
    try:
        # Initialize engine
        engine = BacktestingEngine(config)
        print(f"\n✅ Engine initialized with {len(engine.all_buy_rules)} buy rules")
        
        # Run a quick backtest
        print(f"\n🚀 Running backtest to test loss filters...")
        
        # Use unified evaluation with multiple positions
        results = engine._run_unified_backtest(
            start_idx=300,
            end_idx=2000
        )
        
        print(f"\n📈 Backtest Results:")
        print(f"   - Total trades: {results['total_trades']}")
        print(f"   - Final capital: ${results['final_capital']:,.2f}")
        print(f"   - Total return: {results['total_return_pct']:.2f}%")
        
        # Check if loss filters were triggered
        if hasattr(engine, '_trading_pause_until_idx') and engine._trading_pause_until_idx > 0:
            print(f"   🛑 Trading pause was triggered (until idx {engine._trading_pause_until_idx})")
        else:
            print(f"   ✅ No trading pause triggered")
            
        if hasattr(engine, '_consecutive_losses_pause_until_idx') and engine._consecutive_losses_pause_until_idx > 0:
            print(f"   🛑 Consecutive losses pause was triggered (until idx {engine._consecutive_losses_pause_until_idx})")
        else:
            print(f"   ✅ No consecutive losses pause triggered")
        
        print(f"\n✅ Test completed successfully!")
        print(f"✅ Loss filters are working independently of ENABLE_GLOBAL_MARKET_FILTERS setting")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_loss_filters()
    if success:
        print(f"\n🎉 All tests passed! Loss filters are working correctly.")
    else:
        print(f"\n💥 Tests failed! Please check the implementation.")
