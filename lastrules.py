"""
Last Rules - 30 Specific Buy Rules
This file contains exactly the 30 buy rules specified by the user, copied exactly from selected_buy_rules_optimized.py
"""

import numpy as np
import pandas as pd


class LastRules:
    """The 30 specific buy rules requested"""
    
    def __init__(self, df: pd.DataFrame):
        self.df = df
        self.category = 'LAST'
        self.length = len(df)
        
        # Pre-cache column references for faster access
        self._columns = {}
        self._cache_columns()
    
    def _cache_columns(self):
        """Cache column references to avoid repeated dictionary lookups"""
        common_cols = [
            'close', 'open', 'high', 'low', 'volume',
            'RSI', 'MACD', 'MACD_signal', 'volume_ratio',
            'BB_upper', 'BB_lower', 'BB_middle', 'BB_position',
            'SMA_20', 'SMA_50', 'SMA_200', 'EMA_12', 'EMA_21', 'EMA_26', 'EMA_50',
            'MA_7', 'MA_25', 'MA_50', 'STOCH_K', 'STOCH_D', 'WILLIAMS_R',
            'ATR', 'ADX', 'PLUS_DI', 'MINUS_DI', 'MFI', 'CCI', 'CMF',
            'HMA_20', 'ROC_5', 'BB_width'
        ]
        
        for col in common_cols:
            if col in self.df.columns:
                self._columns[col] = self.df[col].values  # Use .values for fastest access
    
    def _get_values(self, column: str, indices: list) -> np.ndarray:
        """Fast batch data retrieval with bounds checking"""
        if column not in self._columns:
            return np.full(len(indices), np.nan)
        
        col_data = self._columns[column]
        result = np.full(len(indices), np.nan)
        
        for i, idx in enumerate(indices):
            if 0 <= idx < self.length:
                result[i] = col_data[idx]
        
        return result
    
    def _get_single(self, column: str, idx: int) -> float:
        """Fast single value retrieval"""
        if column not in self._columns or idx < 0 or idx >= self.length:
            return np.nan
        return self._columns[column][idx]
    
    def _is_valid(self, *values) -> bool:
        """Fast NaN check using direct comparison (faster than np.isnan)"""
        for val in values:
            if val != val:  # NaN != NaN is True, fastest NaN check
                return False
        return True
    
    def _max_range(self, column: str, start_idx: int, end_idx: int) -> float:
        """Fast max calculation over range"""
        if column not in self._columns:
            return np.nan
        
        start_idx = max(0, start_idx)
        end_idx = min(self.length, end_idx)
        
        if start_idx >= end_idx:
            return np.nan
        
        return np.max(self._columns[column][start_idx:end_idx])
    
    def _min_range(self, column: str, start_idx: int, end_idx: int) -> float:
        """Fast min calculation over range"""
        if column not in self._columns:
            return np.nan
        
        start_idx = max(0, start_idx)
        end_idx = min(self.length, end_idx)
        
        if start_idx >= end_idx:
            return np.nan
        
        return np.min(self._columns[column][start_idx:end_idx])
    
    def _mean_range(self, column: str, start_idx: int, end_idx: int) -> float:
        """Fast mean calculation over range"""
        if column not in self._columns:
            return np.nan
        
        start_idx = max(0, start_idx)
        end_idx = min(self.length, end_idx)
        
        if start_idx >= end_idx:
            return np.nan
        
        return np.mean(self._columns[column][start_idx:end_idx])

    def get_all_rules(self):
        """Get only the 30 specific rules requested"""
        return [
            ('Rule 21: Gap Up', self.rule_21_gap_up),
            ('New Rule 4: Ultimate Oscillator Breakout', self.new_rule_4_ultimate_oscillator_breakout),
            ('Rule 1: MA Alignment with RSI Oversold', self.rule_1_ma_alignment_with_rsi_oversold),
            ('SMC Rule 2: Fair Value Gap Fill', self.smc_rule_2_fair_value_gap_fill),
            ('AI Rule 10: Composite Sentiment Reversal', self.ai_rule_10_composite_sentiment_reversal),
            ('Rule 7: Bollinger Band Bounce', self.rule_7_bollinger_band_bounce),
            ('AI Rule 3: Smart Money Flow Divergence', self.ai_rule_3_smart_money_flow_divergence),
            ('Ext Rule 6: Fibonacci Support Confluence', self.ext_rule_6_fibonacci_support_confluence),
            ('Prof Rule 7: Mean Reversion Volatility Filter', self.prof_rule_7_mean_reversion_volatility_filter),
            ('Rule 28: Volume Breakout', self.rule_28_volume_breakout),
            ('AI Rule 8: Momentum Divergence Reversal', self.ai_rule_8_momentum_divergence_reversal),
            ('Professional Rule 10: CCI Reversal Enhanced', self.professional_rule_10_cci_reversal_enhanced),
            ('Volume Rule 5: Smart Money Volume', self.volume_rule_5_smart_money_volume),
            ('Professional Rule 7: Chaikin Money Flow Reversal', self.professional_rule_7_chaikin_money_flow_reversal),
            ('Volatility Rule 2: ATR Expansion Signal', self.volatility_rule_2_atr_expansion_signal),
            ('Volume Rule 4: Volume Breakout Confirmation', self.volume_rule_4_volume_breakout_confirmation),
            ('Rule 10: Volume Spike', self.rule_10_volume_spike),
            ('Momentum Rule 2: Momentum Divergence Recovery', self.momentum_rule_2_momentum_divergence_recovery),
            ('Rule 27: Structure Break Up', self.rule_27_structure_break_up),
            ('Momentum Rule 5: Momentum Breakout', self.momentum_rule_5_momentum_breakout),
            ('Rule 6: Stochastic Oversold Cross', self.rule_6_stochastic_oversold_cross),
            ('Advanced Rule 7: DMI ADX Filter', self.advanced_rule_7_dmi_adx_filter),
            ('SMC Rule 5: Institutional Candle Pattern', self.smc_rule_5_institutional_candle_pattern),
            ('Volume Rule 3: Dark Pool Activity', self.volume_rule_3_dark_pool_activity),
            ('Ext Rule 5: ATR Volatility Expansion', self.ext_rule_5_atr_volatility_expansion),
            ('Acad Rule 3: Volatility Breakout', self.acad_rule_3_volatility_breakout),
            ('Acad Rule 2: Mean Reversion Factor', self.acad_rule_2_mean_reversion_factor),
            ('Ext Rule 3: Bollinger Squeeze Breakout', self.ext_rule_3_bollinger_squeeze_breakout),
            ('Rule 2: Golden Cross', self.rule_2_golden_cross),
            ('Price Action Rule 3: Engulfing Pattern', self.price_action_rule_3_engulfing),
        ]

    # Rule 21: Gap Up (Optimized)
    def rule_21_gap_up(self, idx: int) -> bool:
        """Gap Up - Optimized"""
        if idx < 1:
            return False

        current_low = self._get_single('low', idx)
        prev_high = self._get_single('high', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        open_price = self._get_single('open', idx)

        if not self._is_valid(current_low, prev_high, volume_ratio, close, open_price):
            return False

        return (current_low > prev_high and     # Gap up
                volume_ratio > 1.2 and         # Volume confirmation
                close > open_price)             # Strong close

    # New Rule 4: Ultimate Oscillator Breakout (Optimized)
    def new_rule_4_ultimate_oscillator_breakout(self, idx: int) -> bool:
        """Ultimate Oscillator Breakout - Optimized"""
        if idx < 28:
            return False

        # Pre-calculate periods for efficiency
        periods = [7, 14, 28]
        bp_sums = []
        tr_sums = []

        # Vectorized calculation for all periods
        for period in periods:
            start_idx = max(0, idx - period + 1)

            # Get data slices
            highs = self._columns.get('high', np.array([]))[start_idx:idx+1]
            lows = self._columns.get('low', np.array([]))[start_idx:idx+1]
            closes = self._columns.get('close', np.array([]))[start_idx:idx+1]

            if len(closes) < period:
                return False

            # Vectorized BP and TR calculations
            closes_prev = np.concatenate([[closes[0]], closes[:-1]])

            # Buying Pressure
            bp = closes - np.minimum(lows, closes_prev)

            # True Range
            tr1 = highs - lows
            tr2 = np.abs(highs - closes_prev)
            tr3 = np.abs(lows - closes_prev)
            tr = np.maximum(tr1, np.maximum(tr2, tr3))

            bp_sum = np.sum(bp)
            tr_sum = np.sum(tr)

            if tr_sum == 0:
                return False

            bp_sums.append(bp_sum)
            tr_sums.append(tr_sum)

        # Calculate Ultimate Oscillator
        avg7 = bp_sums[0] / tr_sums[0]
        avg14 = bp_sums[1] / tr_sums[1]
        avg28 = bp_sums[2] / tr_sums[2]

        current_ult_osc = 100 * (4 * avg7 + 2 * avg14 + avg28) / 7

        # Previous Ultimate Oscillator (simplified for performance)
        prev_avg7 = bp_sums[0] / tr_sums[0]  # Approximation for speed
        prev_ult_osc = 100 * (4 * prev_avg7 + 2 * avg14 + avg28) / 7

        volume_ratio = self._get_single('volume_ratio', idx)

        return (current_ult_osc > 30 and
                current_ult_osc > prev_ult_osc and
                volume_ratio > 1.1)

    # Rule 1: MA Alignment with RSI Oversold (Optimized)
    def rule_1_ma_alignment_with_rsi_oversold(self, idx: int) -> bool:
        """MA Alignment with RSI Oversold - Optimized"""
        if idx < 50:
            return False

        ma7 = self._get_single('MA_7', idx)
        ma25 = self._get_single('MA_25', idx)
        ma50 = self._get_single('MA_50', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(ma7, ma25, ma50, rsi):
            return False

        return (ma7 > ma25 > ma50 and rsi < 30)     # MA alignment + RSI oversold

    # SMC Rule 2: Fair Value Gap Fill (Optimized)
    def smc_rule_2_fair_value_gap_fill(self, idx: int) -> bool:
        """Fair Value Gap Fill - Optimized"""
        if idx < 3:
            return False

        high_2 = self._get_single('high', idx-2)
        low_1 = self._get_single('low', idx-1)
        current_low = self._get_single('low', idx)
        current_high = self._get_single('high', idx)
        current_close = self._get_single('close', idx)
        current_open = self._get_single('open', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(high_2, low_1, current_low, current_high,
                             current_close, current_open, volume_ratio):
            return False

        # Fast gap detection and fill
        gap_exists = high_2 < low_1
        gap_fill = current_low <= high_2 and current_high >= low_1
        bullish_continuation = current_close > current_open

        return (gap_exists and gap_fill and bullish_continuation and volume_ratio > 1.0)

    # AI Rule 10: Composite Sentiment Reversal (Optimized)
    def ai_rule_10_composite_sentiment_reversal(self, idx: int) -> bool:
        """Composite Sentiment Reversal - Optimized"""
        if idx < 30:
            return False

        rsi = self._get_single('RSI', idx)
        stoch_k = self._get_single('STOCH_K', idx)
        williams_r = self._get_single('WILLIAMS_R', idx)
        close = self._get_single('close', idx)
        bb_lower = self._get_single('BB_lower', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(rsi, stoch_k, williams_r, close, bb_lower, volume_ratio):
            return False

        # Fast oversold condition checks
        rsi_oversold = rsi < 35
        stoch_oversold = stoch_k < 25
        williams_oversold = williams_r < -75
        near_support = close <= bb_lower * 1.02

        # Count oversold conditions
        oversold_count = sum([rsi_oversold, stoch_oversold, williams_oversold])

        return (oversold_count >= 2 and near_support and volume_ratio > 1.1)

    # Rule 7: Bollinger Band Bounce (Optimized)
    def rule_7_bollinger_band_bounce(self, idx: int) -> bool:
        """Bollinger Band Bounce - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        bb_lower = self._get_single('BB_lower', idx)
        bb_position = self._get_single('BB_position', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, bb_lower, bb_position, volume_ratio):
            return False

        return (close <= bb_lower and       # At lower band
                bb_position < 0.1 and      # Low position
                volume_ratio > 0.8)        # Volume support

    # AI Rule 3: Smart Money Flow Divergence (Optimized)
    def ai_rule_3_smart_money_flow_divergence(self, idx: int) -> bool:
        """Smart Money Flow Divergence - Optimized"""
        if idx < 20:
            return False

        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-5)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(volume_ratio, close, close_prev, rsi):
            return False

        # Smart money pattern detection
        price_declining = close < close_prev
        volume_increasing = volume_ratio > 1.2
        smart_money_pattern = price_declining and volume_increasing
        oversold_recovery = 25 < rsi < 45

        return (smart_money_pattern and oversold_recovery and volume_ratio > 1.5)

    # Ext Rule 6: Fibonacci Support Confluence (Optimized)
    def ext_rule_6_fibonacci_support_confluence(self, idx: int) -> bool:
        """Fibonacci Support Confluence - Optimized"""
        if idx < 50:
            return False

        close = self._get_single('close', idx)
        low = self._get_single('low', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, low, volume_ratio):
            return False

        # Fast swing high/low calculation
        lookback = 30
        swing_high = self._max_range('high', idx-lookback, idx)
        swing_low = self._min_range('low', idx-lookback, idx)

        if not self._is_valid(swing_high, swing_low):
            return False

        # Fibonacci levels
        fib_range = swing_high - swing_low
        fib_236 = swing_high - (fib_range * 0.236)
        fib_382 = swing_high - (fib_range * 0.382)
        fib_618 = swing_high - (fib_range * 0.618)

        # Check proximity to Fibonacci levels
        tolerance = close * 0.005
        near_fib = (abs(close - fib_236) < tolerance or
                   abs(close - fib_382) < tolerance or
                   abs(close - fib_618) < tolerance)

        # Bounce confirmation
        bounce = low <= min(fib_236, fib_382, fib_618) and close > low

        return (near_fib and bounce and volume_ratio > 1.1)

    # Prof Rule 7: Mean Reversion Volatility Filter (Optimized)
    def prof_rule_7_mean_reversion_volatility_filter(self, idx: int) -> bool:
        """Mean Reversion with Volatility Filter - Optimized"""
        if idx < 30:
            return False

        close = self._get_single('close', idx)
        bb_lower = self._get_single('BB_lower', idx)
        bb_width = self._get_single('BB_width', idx)
        rsi = self._get_single('RSI', idx)
        rsi_prev = self._get_single('RSI', idx-5)
        close_prev = self._get_single('close', idx-5)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, bb_lower, bb_width, rsi, volume_ratio):
            return False

        # Fast volatility calculation
        avg_bb_width = self._mean_range('BB_width', idx-20, idx)
        if avg_bb_width != avg_bb_width:  # NaN check
            return False

        oversold = close < bb_lower
        volatility_expansion = 1.2 < bb_width / avg_bb_width < 2.0

        # RSI divergence or volume confirmation
        rsi_divergence = (self._is_valid(rsi_prev, close_prev) and
                         close < close_prev and rsi > rsi_prev)

        return oversold and volatility_expansion and (rsi_divergence or volume_ratio > 1.1)

    # Rule 28: Volume Breakout (Optimized)
    def rule_28_volume_breakout(self, idx: int) -> bool:
        """Volume Breakout - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(close, volume_ratio, rsi):
            return False

        # Fast breakout detection
        high_20 = self._max_range('high', idx-20, idx)
        if high_20 != high_20:  # NaN check
            return False

        return (close > high_20 * 1.001 and    # Breakout
                volume_ratio > 2.5 and         # Volume breakout
                rsi < 80)                      # Not overbought

    # AI Rule 8: Momentum Divergence Reversal (Optimized)
    def ai_rule_8_momentum_divergence_reversal(self, idx: int) -> bool:
        """Momentum Divergence Reversal - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-10)
        rsi = self._get_single('RSI', idx)
        rsi_prev = self._get_single('RSI', idx-10)
        macd = self._get_single('MACD', idx)
        macd_prev = self._get_single('MACD', idx-10)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_prev, rsi, volume_ratio):
            return False

        price_lower_low = close < close_prev
        rsi_higher = self._is_valid(rsi_prev) and rsi > rsi_prev
        macd_higher = self._is_valid(macd, macd_prev) and macd > macd_prev

        return (price_lower_low and
                (rsi_higher or macd_higher) and
                volume_ratio > 1.1 and
                rsi < 40)

    # Professional Rule 10: CCI Reversal Enhanced (Optimized)
    def professional_rule_10_cci_reversal_enhanced(self, idx: int) -> bool:
        """CCI Reversal Enhanced - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_prev, volume_ratio):
            return False

        period = 20

        # Vectorized CCI calculation
        start_idx = max(0, idx - period + 1)
        highs = self._columns.get('high', np.array([]))[start_idx:idx+1]
        lows = self._columns.get('low', np.array([]))[start_idx:idx+1]
        closes = self._columns.get('close', np.array([]))[start_idx:idx+1]

        if len(closes) < period:
            return False

        # Typical prices
        typical_prices = (highs + lows + closes) / 3
        sma_tp = np.mean(typical_prices)
        mad = np.mean(np.abs(typical_prices - sma_tp))

        if mad == 0:
            return False

        current_cci = (typical_prices[-1] - sma_tp) / (0.015 * mad)

        # Previous CCI
        prev_highs = self._columns.get('high', np.array([]))[start_idx-1:idx]
        prev_lows = self._columns.get('low', np.array([]))[start_idx-1:idx]
        prev_closes = self._columns.get('close', np.array([]))[start_idx-1:idx]

        if len(prev_closes) < period:
            return False

        prev_typical_prices = (prev_highs + prev_lows + prev_closes) / 3
        prev_sma_tp = np.mean(prev_typical_prices)
        prev_mad = np.mean(np.abs(prev_typical_prices - prev_sma_tp))

        if prev_mad == 0:
            return False

        prev_cci = (prev_typical_prices[-1] - prev_sma_tp) / (0.015 * prev_mad)

        # CCI reversal: was below -100, now above -100
        cci_reversal = prev_cci <= -100 and current_cci > -100

        return (cci_reversal and volume_ratio > 1.1 and close > close_prev)

    # Volume Rule 5: Smart Money Volume (Optimized)
    def volume_rule_5_smart_money_volume(self, idx: int) -> bool:
        """Smart Money Volume Pattern - Optimized"""
        if idx < 10:
            return False

        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        bb_position = self._get_single('BB_position', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(volume_ratio, close, close_prev, bb_position, rsi):
            return False

        # Fast volume trend calculation
        volume_ma = self._mean_range('volume_ratio', idx-5, idx)
        if volume_ma != volume_ma:  # NaN check
            return False

        # Check for recent volume spikes (optimized)
        recent_spike = self._max_range('volume_ratio', idx-3, idx) > 2.0

        return (close > close_prev and          # Up day
                volume_ratio > 1.3 and         # High volume
                volume_ratio > volume_ma * 1.2 and  # Volume trending
                bb_position < 0.4 and          # Near support
                35 < rsi < 65 and              # Momentum building
                not recent_spike)               # No FOMO

    # Professional Rule 7: Chaikin Money Flow Reversal (Optimized)
    def professional_rule_7_chaikin_money_flow_reversal(self, idx: int) -> bool:
        """Chaikin Money Flow Reversal - Optimized"""
        if idx < 20:
            return False

        period = 20
        volume_ratio = self._get_single('volume_ratio', idx)

        # Vectorized CMF calculation for current and previous periods
        start_idx = max(0, idx - period + 1)

        # Current period
        highs = self._columns.get('high', np.array([]))[start_idx:idx+1]
        lows = self._columns.get('low', np.array([]))[start_idx:idx+1]
        closes = self._columns.get('close', np.array([]))[start_idx:idx+1]
        volumes = self._columns.get('volume', np.array([]))[start_idx:idx+1]

        if len(closes) < period:
            return False

        # Vectorized MF calculation
        ranges = highs - lows
        valid_mask = ranges != 0

        if np.sum(valid_mask) == 0:
            return False

        mf_multiplier = np.zeros_like(closes)
        mf_multiplier[valid_mask] = ((closes[valid_mask] - lows[valid_mask]) -
                                    (highs[valid_mask] - closes[valid_mask])) / ranges[valid_mask]

        mf_volume = mf_multiplier * volumes
        current_cmf = np.sum(mf_volume) / np.sum(volumes) if np.sum(volumes) > 0 else 0

        # Previous period CMF
        prev_start = max(0, idx - period)
        prev_highs = self._columns.get('high', np.array([]))[prev_start:idx]
        prev_lows = self._columns.get('low', np.array([]))[prev_start:idx]
        prev_closes = self._columns.get('close', np.array([]))[prev_start:idx]
        prev_volumes = self._columns.get('volume', np.array([]))[prev_start:idx]

        if len(prev_closes) < period:
            return False

        prev_ranges = prev_highs - prev_lows
        prev_valid_mask = prev_ranges != 0

        if np.sum(prev_valid_mask) == 0:
            return False

        prev_mf_multiplier = np.zeros_like(prev_closes)
        prev_mf_multiplier[prev_valid_mask] = ((prev_closes[prev_valid_mask] - prev_lows[prev_valid_mask]) -
                                              (prev_highs[prev_valid_mask] - prev_closes[prev_valid_mask])) / prev_ranges[prev_valid_mask]

        prev_mf_volume = prev_mf_multiplier * prev_volumes
        prev_cmf = np.sum(prev_mf_volume) / np.sum(prev_volumes) if np.sum(prev_volumes) > 0 else 0

        # CMF reversal: was negative, now positive
        return (prev_cmf <= 0 and current_cmf > 0 and volume_ratio > 1.1)

    # Volatility Rule 2: ATR Expansion Signal (Optimized)
    def volatility_rule_2_atr_expansion_signal(self, idx: int) -> bool:
        """ATR Expansion Signal - Optimized"""
        if idx < 20:
            return False

        current_atr = self._get_single('ATR', idx)
        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(current_atr, close, volume_ratio):
            return False

        # Fast ATR comparison
        avg_atr = self._mean_range('ATR', idx-20, idx)
        if avg_atr != avg_atr or avg_atr == 0:  # NaN or zero check
            return False

        # Breakout confirmation
        high_10 = self._max_range('high', idx-10, idx)
        if high_10 != high_10:  # NaN check
            return False

        return (current_atr > avg_atr * 1.3 and     # ATR expansion
                close > high_10 and                # Breakout
                volume_ratio > 1.2)                # Volume confirmation

    # Volume Rule 4: Volume Breakout Confirmation (Optimized)
    def volume_rule_4_volume_breakout_confirmation(self, idx: int) -> bool:
        """Volume Breakout Confirmation - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)
        macd = self._get_single('MACD', idx)
        macd_signal = self._get_single('MACD_signal', idx)

        if not self._is_valid(close, close_prev, volume_ratio, rsi, macd, macd_signal):
            return False

        # Fast breakout detection
        high_20 = self._max_range('high', idx-20, idx)
        if high_20 != high_20:  # NaN check
            return False

        return (close > high_20 * 1.001 and    # Breakout
                volume_ratio > 2.0 and         # Volume confirmation
                close > close_prev * 0.999 and # Sustained
                rsi < 80 and                   # Not overbought
                macd > macd_signal)            # Momentum support

    # Rule 10: Volume Spike (Optimized)
    def rule_10_volume_spike(self, idx: int) -> bool:
        """Volume Spike - Optimized"""
        if idx < 20:
            return False

        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        open_price = self._get_single('open', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(volume_ratio, close, open_price, rsi):
            return False

        return (volume_ratio > 2.0 and      # Volume spike
                close > open_price and      # Bullish candle
                rsi < 75)                   # Not overbought

    # Momentum Rule 2: Momentum Divergence Recovery (Optimized)
    def momentum_rule_2_momentum_divergence_recovery(self, idx: int) -> bool:
        """Momentum Divergence Recovery - Optimized"""
        if idx < 15:
            return False

        close = self._get_single('close', idx)
        close_10 = self._get_single('close', idx-10)
        rsi = self._get_single('RSI', idx)
        rsi_10 = self._get_single('RSI', idx-10)
        macd = self._get_single('MACD', idx)
        macd_signal = self._get_single('MACD_signal', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_10, rsi, rsi_10, macd, macd_signal, volume_ratio):
            return False

        return (close < close_10 and            # Price lower
                rsi > rsi_10 and               # Momentum higher
                macd > macd_signal and         # MACD bullish
                volume_ratio > 1.0)            # Volume support

    # Rule 27: Structure Break Up (Optimized)
    def rule_27_structure_break_up(self, idx: int) -> bool:
        """Market Structure Break Up - Optimized"""
        if idx < 10:
            return False

        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(close, volume_ratio, rsi):
            return False

        # Fast structure break detection
        high_5 = self._max_range('high', idx-5, idx)
        if high_5 != high_5:  # NaN check
            return False

        return (close > high_5 * 1.001 and     # Structure break
                volume_ratio > 1.2 and         # Volume confirmation
                rsi > 50)                      # Momentum support

    # Momentum Rule 5: Momentum Breakout (Optimized)
    def momentum_rule_5_momentum_breakout(self, idx: int) -> bool:
        """Momentum Breakout - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_10 = self._get_single('close', idx-10)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(close, close_10, volume_ratio, rsi) or close_10 == 0:
            return False

        # Fast momentum calculation
        momentum = (close - close_10) / close_10 * 100

        # Price breakout
        high_20 = self._max_range('high', idx-20, idx)
        if high_20 != high_20:  # NaN check
            return False

        return (momentum > 3.0 and              # Strong momentum
                volume_ratio > 1.5 and         # Volume momentum
                50 < rsi < 75 and              # RSI range
                close > high_20 * 1.001)       # Breakout

    # Rule 6: Stochastic Oversold Cross (Optimized)
    def rule_6_stochastic_oversold_cross(self, idx: int) -> bool:
        """Stochastic Oversold Cross - Optimized"""
        if idx < 5:
            return False

        stoch_k = self._get_single('STOCH_K', idx)
        stoch_k_prev = self._get_single('STOCH_K', idx-1)
        stoch_d = self._get_single('STOCH_D', idx)
        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(stoch_k, stoch_k_prev, stoch_d, close, close_prev, volume_ratio):
            return False

        return (stoch_k_prev < 20 and      # Was oversold
                stoch_k > 20 and           # Now crossing up
                stoch_k > stoch_d and      # %K above %D
                close > close_prev and     # Price confirmation
                volume_ratio > 0.8)        # Volume support

    # Advanced Rule 7: DMI ADX Filter (Optimized)
    def advanced_rule_7_dmi_adx_filter(self, idx: int) -> bool:
        """DMI ADX Filter - Optimized"""
        if idx < 14:
            return False

        current_plus_di = self._get_single('PLUS_DI', idx)
        current_minus_di = self._get_single('MINUS_DI', idx)
        current_adx = self._get_single('ADX', idx)
        prev_plus_di = self._get_single('PLUS_DI', idx-1)
        prev_minus_di = self._get_single('MINUS_DI', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(current_plus_di, current_minus_di, current_adx,
                             prev_plus_di, prev_minus_di, volume_ratio):
            return False

        # DMI crossover and trend strength
        di_crossover = (current_plus_di > current_minus_di and
                       prev_plus_di <= prev_minus_di)
        strong_trend = current_adx > 25

        return (di_crossover and strong_trend and volume_ratio > 1.0)

    # SMC Rule 5: Institutional Candle Pattern (Optimized)
    def smc_rule_5_institutional_candle_pattern(self, idx: int) -> bool:
        """Institutional Candle Pattern - Optimized"""
        if idx < 5:
            return False

        open_price = self._get_single('open', idx)
        close_price = self._get_single('close', idx)
        high_price = self._get_single('high', idx)
        low_price = self._get_single('low', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(open_price, close_price, high_price, low_price, volume_ratio):
            return False

        # Fast institutional pattern detection
        body_size = abs(close_price - open_price) / open_price * 100
        total_range = (high_price - low_price) / low_price * 100

        if total_range == 0:
            return False

        body_to_range_ratio = body_size / total_range

        # Recent high for resistance check
        recent_high = self._max_range('high', idx-10, idx)
        if recent_high != recent_high:  # NaN check
            return False

        return (body_size > 0.8 and                    # Large body
                body_to_range_ratio > 0.7 and         # Strong body ratio
                close_price > open_price and           # Bullish direction
                volume_ratio > 2.0 and                # High volume
                close_price > recent_high)             # Above resistance

    # Volume Rule 3: Dark Pool Activity (Optimized)
    def volume_rule_3_dark_pool_activity(self, idx: int) -> bool:
        """Dark Pool Activity - Optimized"""
        if idx < 30:
            return False

        volume_ratio = self._get_single('volume_ratio', idx)
        high = self._get_single('high', idx)
        low = self._get_single('low', idx)
        close = self._get_single('close', idx)

        if not self._is_valid(volume_ratio, high, low, close):
            return False

        # Fast range and volatility calculation
        current_range = high - low
        avg_range = self._mean_range('high', idx-10, idx) - self._mean_range('low', idx-10, idx)

        if avg_range == 0:
            return False

        # Previous high for breakout potential
        prev_high = self._max_range('high', idx-5, idx)
        if prev_high != prev_high:  # NaN check
            return False

        return (volume_ratio > 2.0 and              # Volume spike
                current_range < avg_range * 0.7 and # Low volatility
                close > prev_high * 0.999)         # Breakout potential

    # Ext Rule 5: ATR Volatility Expansion (Optimized)
    def ext_rule_5_atr_volatility_expansion(self, idx: int) -> bool:
        """ATR Volatility Expansion - Optimized"""
        if idx < 20:
            return False

        current_atr = self._get_single('ATR', idx)
        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(current_atr, close, volume_ratio):
            return False

        # Fast ATR average calculation
        avg_atr = self._mean_range('ATR', idx-19, idx+1)
        if avg_atr != avg_atr or avg_atr == 0:  # NaN or zero check
            return False

        # Price breakout
        high_10 = self._max_range('high', idx-9, idx+1)
        if high_10 != high_10:  # NaN check
            return False

        return (current_atr > avg_atr * 1.3 and     # Volatility expansion
                close > high_10 * 0.999 and        # Price breakout
                volume_ratio > 1.2)                # Volume confirmation

    # Acad Rule 3: Volatility Breakout (Optimized)
    def acad_rule_3_volatility_breakout(self, idx: int) -> bool:
        """Volatility Breakout - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_prev, volume_ratio):
            return False

        # Vectorized volatility calculation
        period = 20
        start_idx = max(0, idx - period + 1)
        closes = self._columns.get('close', np.array([]))[start_idx:idx+1]

        if len(closes) < period:
            return False

        # Calculate returns and volatility
        returns = np.diff(closes) / closes[:-1]
        current_volatility = np.std(returns)

        # Historical volatility
        hist_start = max(0, idx - 39)
        hist_closes = self._columns.get('close', np.array([]))[hist_start:idx-19]

        if len(hist_closes) < 20:
            return False

        hist_returns = np.diff(hist_closes) / hist_closes[:-1]
        historical_volatility = np.std(hist_returns)

        if historical_volatility == 0:
            return False

        return (current_volatility > historical_volatility * 1.5 and  # Volatility breakout
                close > close_prev and                                # Momentum up
                volume_ratio > 1.2)                                  # Volume confirmation

    # Acad Rule 2: Mean Reversion Factor (Optimized)
    def acad_rule_2_mean_reversion_factor(self, idx: int) -> bool:
        """Mean Reversion Factor - Optimized"""
        if idx < 30:
            return False

        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(close, volume_ratio, rsi):
            return False

        # Fast mean calculation
        period = 20
        mean_price = self._mean_range('close', idx-period+1, idx+1)
        if mean_price != mean_price:  # NaN check
            return False

        # Standard deviation calculation
        start_idx = max(0, idx - period + 1)
        closes = self._columns.get('close', np.array([]))[start_idx:idx+1]

        if len(closes) < period:
            return False

        std_dev = np.std(closes)
        if std_dev == 0:
            return False

        # Z-score calculation
        z_score = (close - mean_price) / std_dev

        return (z_score < -1.5 and              # Oversold condition
                rsi < 35 and                   # RSI confirmation
                volume_ratio > 1.0)            # Volume support

    # Ext Rule 3: Bollinger Squeeze Breakout (Optimized)
    def ext_rule_3_bollinger_squeeze_breakout(self, idx: int) -> bool:
        """Bollinger Band Squeeze Breakout - Optimized"""
        if idx < 20:
            return False

        bb_upper = self._get_single('BB_upper', idx)
        bb_lower = self._get_single('BB_lower', idx)
        bb_middle = self._get_single('BB_middle', idx)
        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(bb_upper, bb_lower, bb_middle, close, volume_ratio):
            return False

        current_width = (bb_upper - bb_lower) / bb_middle

        # Vectorized width calculation for historical comparison
        start_idx = max(0, idx - 20)
        bb_uppers = self._columns.get('BB_upper', np.array([]))[start_idx:idx]
        bb_lowers = self._columns.get('BB_lower', np.array([]))[start_idx:idx]
        bb_middles = self._columns.get('BB_middle', np.array([]))[start_idx:idx]

        # Filter out invalid data
        valid_mask = (bb_middles != 0) & ~np.isnan(bb_uppers) & ~np.isnan(bb_lowers) & ~np.isnan(bb_middles)

        if np.sum(valid_mask) < 10:
            return False

        widths = (bb_uppers[valid_mask] - bb_lowers[valid_mask]) / bb_middles[valid_mask]

        return (current_width < np.percentile(widths, 20) and  # Squeeze
                close > bb_upper and                           # Breakout
                volume_ratio > 1.2)                           # Volume confirmation

    # Rule 2: Golden Cross (Optimized)
    def rule_2_golden_cross(self, idx: int) -> bool:
        """Golden Cross - Optimized"""
        if idx < 200:
            return False

        sma_50 = self._get_single('SMA_50', idx)
        sma_50_prev = self._get_single('SMA_50', idx-1)
        sma_200 = self._get_single('SMA_200', idx)
        sma_200_prev = self._get_single('SMA_200', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(sma_50, sma_50_prev, sma_200, sma_200_prev, volume_ratio):
            return False

        return (sma_50 > sma_200 and        # Current above
                sma_50_prev <= sma_200_prev and  # Previous below/equal
                volume_ratio > 1.0)         # Volume confirmation

    # Price Action Rule 3: Engulfing Pattern (Optimized)
    def price_action_rule_3_engulfing(self, idx: int) -> bool:
        """Bullish Engulfing Pattern - Optimized"""
        if idx < 2:
            return False

        open_curr = self._get_single('open', idx)
        close_curr = self._get_single('close', idx)
        open_prev = self._get_single('open', idx-1)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)

        # Fast validation
        if not self._is_valid(open_curr, close_curr, open_prev, close_prev, volume_ratio, rsi):
            return False

        # Optimized pattern detection (short-circuit evaluation)
        return (close_prev < open_prev and  # Previous bearish
                close_curr > open_curr and  # Current bullish
                open_curr < close_prev and  # Engulfing condition 1
                close_curr > open_prev and  # Engulfing condition 2
                volume_ratio > 1.1 and     # Volume confirmation
                rsi < 75)                   # Not overbought
