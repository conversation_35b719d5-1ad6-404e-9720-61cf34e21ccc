#!/usr/bin/env python3
"""
Quick verification that global filters are working
"""

import pandas as pd
from config import Config
from backtesting_engine import BacktestingEngine

def quick_filter_test():
    """Quick test to verify global filters are working"""
    print("🔍 QUICK GLOBAL FILTER VERIFICATION")
    print("=" * 50)
    
    # Test with a small subset of data
    config = Config()
    config.CURRENT_DATASET_SIZE = 10000  # Small test
    
    print(f"Global Filters Status:")
    print(f"   ENABLE_GLOBAL_MARKET_FILTERS = {getattr(config, 'ENABLE_GLOBAL_MARKET_FILTERS', 'NOT SET')}")
    print(f"   GLOBAL_EMA200_FILTER = {getattr(config, 'GLOBAL_EMA200_FILTER', 'NOT SET')}")
    print(f"   GLOBAL_RSI_OVERBOUGHT_FILTER = {getattr(config, 'GLOBAL_RSI_OVERBOUGHT_FILTER', 'NOT SET')}")
    print(f"   GLOBAL_RSI_OVERBOUGHT_THRESHOLD = {getattr(config, 'GLOBAL_RSI_OVERBOUGHT_THRESHOLD', 'NOT SET')}")
    
    # Initialize engine
    engine = BacktestingEngine(config)
    
    # Check if EMA_200 and RSI columns exist
    print(f"\nDataFrame columns check:")
    print(f"   EMA_200 exists: {'EMA_200' in engine.df.columns}")
    print(f"   RSI exists: {'RSI' in engine.df.columns}")
    
    if 'EMA_200' in engine.df.columns and 'RSI' in engine.df.columns:
        # Find some test points
        test_indices = [5000, 6000, 7000, 8000, 9000]
        
        print(f"\n📊 Testing filter logic at sample points:")
        print("-" * 50)
        
        blocked_count = 0
        allowed_count = 0
        
        for idx in test_indices:
            if idx < len(engine.df):
                current_price = engine.df['close'].iloc[idx]
                ema200_value = engine.df['EMA_200'].iloc[idx]
                rsi_value = engine.df['RSI'].iloc[idx]
                
                # Test the filter
                filter_result = engine._check_global_market_filters(idx)
                
                print(f"\nIndex {idx}:")
                print(f"   Price: ${current_price:.2f}")
                print(f"   EMA200: ${ema200_value:.2f}")
                print(f"   RSI: {rsi_value:.2f}")
                print(f"   Price > EMA200: {current_price > ema200_value}")
                print(f"   RSI < 75: {rsi_value < 75}")
                print(f"   Filter Result: {'✅ ALLOWED' if filter_result else '❌ BLOCKED'}")
                
                if filter_result:
                    allowed_count += 1
                else:
                    blocked_count += 1
        
        print(f"\n📈 SUMMARY:")
        print(f"   Signals Allowed: {allowed_count}")
        print(f"   Signals Blocked: {blocked_count}")
        print(f"   Block Rate: {(blocked_count / (blocked_count + allowed_count)) * 100:.1f}%")
        
        if blocked_count > 0:
            print(f"   ✅ Global filters are WORKING - {blocked_count} signals blocked")
        else:
            print(f"   ⚠️ No signals blocked in this sample - filters may not be active")
    
    else:
        print("❌ Required columns (EMA_200, RSI) not found!")
        print("Available columns:", list(engine.df.columns))

if __name__ == "__main__":
    quick_filter_test()
