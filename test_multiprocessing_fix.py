"""
Test Script for Multiprocessing Fix
Verify that the multiprocessing with multiple positions works correctly and fast
"""

import pandas as pd
import numpy as np
import time
from config import FullAnalysisConfig
from backtesting_engine import BacktestingEngine
from indicators import TechnicalIndicators


def test_multiprocessing_speed():
    """Test that multiprocessing is working and fast"""
    print("🚀 Testing Multiprocessing Speed")
    print("=" * 50)
    
    # Load real data for testing
    try:
        df = pd.read_csv('BTCUSD_last_year.csv')
        print(f"✅ Loaded {len(df):,} candles from BTCUSD_last_year.csv")
    except:
        print("❌ Could not load BTCUSD_last_year.csv")
        return False
    
    # Calculate indicators
    print("📈 Calculating technical indicators...")
    indicators = TechnicalIndicators(df)
    df = indicators.calculate_all_indicators()
    
    # Test configuration
    config = FullAnalysisConfig()
    
    # Verify configuration
    print(f"✅ Configuration:")
    print(f"   - USE_UNIFIED_EVALUATION: {config.USE_UNIFIED_EVALUATION}")
    print(f"   - USE_MULTIPROCESSING: {config.USE_MULTIPROCESSING}")
    print(f"   - MAX_CONCURRENT_TRADES: {config.MAX_CONCURRENT_TRADES}")
    print(f"   - MAX_WORKERS: {config.MAX_WORKERS}")
    
    # Initialize engine
    print("\n🔧 Initializing backtesting engine...")
    engine = BacktestingEngine(df, config)
    
    print(f"✅ Engine initialized with {len(engine.all_buy_rules)} rules")
    
    # Test on a subset first
    start_idx = 300
    end_idx = 5000  # Test on 5k candles
    
    print(f"\n⚡ Testing multiprocessing on {end_idx - start_idx:,} candles...")
    
    start_time = time.time()
    
    try:
        # This should use multiprocessing now
        result = engine._run_independent_backtests(
            start_idx=start_idx,
            end_idx=end_idx,
            stop_loss_pct=1.3,
            take_profit_pct=0.75
        )
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"✅ Multiprocessing test completed!")
        print(f"   - Time taken: {elapsed:.2f} seconds")
        print(f"   - Speed: {(end_idx - start_idx) / elapsed:.0f} candles/second")
        print(f"   - Rules processed: {len(result)}")
        
        # Check if we got results
        if result and len(result) > 0:
            sample_rule = list(result.keys())[0]
            sample_result = result[sample_rule]
            print(f"   - Sample rule: {sample_rule}")
            print(f"   - Sample trades: {sample_result.get('total_trades', 0)}")
            print(f"   - Sample return: {sample_result.get('total_return_pct', 0):.2f}%")
            
            # Check if multiple positions were used
            trades = sample_result.get('trades', [])
            if trades:
                print(f"   - First trade duration: {trades[0].get('duration_minutes', 0)} minutes")
            
            return True
        else:
            print("❌ No results returned")
            return False
            
    except Exception as e:
        print(f"❌ Multiprocessing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_configuration_logic():
    """Test that the configuration logic routes to the right method"""
    print("\n🔧 Testing Configuration Logic")
    print("=" * 50)
    
    config = FullAnalysisConfig()
    
    print(f"Configuration check:")
    print(f"   - USE_UNIFIED_EVALUATION: {config.USE_UNIFIED_EVALUATION}")
    print(f"   - USE_MULTIPROCESSING: {config.USE_MULTIPROCESSING}")
    print(f"   - MAX_CONCURRENT_TRADES: {config.MAX_CONCURRENT_TRADES}")
    
    # Check the logic in main.py
    if config.USE_MULTIPROCESSING and config.MAX_CONCURRENT_TRADES > 1:
        print("✅ Should use: Multiprocessing independent evaluation with multiple positions")
        return True
    elif config.USE_UNIFIED_EVALUATION:
        print("⚠️ Should use: Unified evaluation (slower)")
        return False
    else:
        print("⚠️ Should use: Independent evaluation")
        return False


def test_worker_data_config():
    """Test that worker data includes MAX_CONCURRENT_TRADES"""
    print("\n📊 Testing Worker Data Configuration")
    print("=" * 50)
    
    try:
        # Generate small test data
        df = pd.DataFrame({
            'close': [100, 101, 102, 103, 104],
            'open': [99, 100, 101, 102, 103],
            'high': [101, 102, 103, 104, 105],
            'low': [98, 99, 100, 101, 102],
            'volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        indicators = TechnicalIndicators(df)
        df = indicators.calculate_all_indicators()
        
        config = FullAnalysisConfig()
        engine = BacktestingEngine(df, config)
        
        # Check if the worker data preparation includes MAX_CONCURRENT_TRADES
        # This is a bit of a hack to test internal logic
        engine.current_stop_loss_pct = 1.3
        engine.current_take_profit_pct = 0.75
        
        # Simulate the worker data preparation
        start_idx = 0
        end_idx = len(df)
        
        essential_columns = ['close', 'high', 'low', 'open', 'volume']
        df_slice = df[essential_columns].iloc[start_idx:end_idx].copy()
        df_slice = df_slice.reset_index(drop=False)
        df_slice['original_index'] = df_slice.index + start_idx
        
        worker_data = {
            'df_data': df_slice,
            'start_idx': start_idx,
            'end_idx': end_idx,
            'config': {
                'INITIAL_CAPITAL': config.INITIAL_CAPITAL,
                'POSITION_SIZE_PCT': config.POSITION_SIZE_PCT,
                'stop_loss_pct': engine.current_stop_loss_pct,
                'take_profit_pct': engine.current_take_profit_pct,
                'MAX_CONCURRENT_TRADES': getattr(config, 'MAX_CONCURRENT_TRADES', 1),
                'ENABLE_GLOBAL_MARKET_FILTERS': getattr(config, 'ENABLE_GLOBAL_MARKET_FILTERS', True),
            }
        }
        
        max_concurrent = worker_data['config']['MAX_CONCURRENT_TRADES']
        print(f"✅ Worker data includes MAX_CONCURRENT_TRADES: {max_concurrent}")
        
        if max_concurrent == 5:
            print("✅ Correct value: 5 concurrent trades")
            return True
        else:
            print(f"❌ Wrong value: Expected 5, got {max_concurrent}")
            return False
            
    except Exception as e:
        print(f"❌ Worker data test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 MULTIPROCESSING FIX TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_configuration_logic,
        test_worker_data_config,
        test_multiprocessing_speed,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Multiprocessing fix verified")
        print("✅ Multiple positions support working")
        print("✅ Configuration routing correctly")
        print("\nReady to run: python main.py full")
        print("Expected speed: 2-3x faster than before!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the error messages above")


if __name__ == "__main__":
    main()
