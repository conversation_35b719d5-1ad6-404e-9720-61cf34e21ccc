"""
Performance Test Script for Trading Rules Optimization
Compares original vs optimized implementations
"""

import time
import numpy as np
import pandas as pd
from typing import List, Tuple
import matplotlib.pyplot as plt

# Import both implementations
try:
    from selected_buy_rules import SelectedBuyRules as OriginalRules
    from selected_buy_rules_optimized import SelectedBuyRulesOptimized as OptimizedRules
    BOTH_AVAILABLE = True
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure both rule files are available for comparison")
    BOTH_AVAILABLE = False


def generate_test_data(num_rows: int = 10000) -> pd.DataFrame:
    """Generate realistic trading data for testing"""
    np.random.seed(42)  # For reproducible results
    
    # Generate base price series with trend and volatility
    base_price = 100
    returns = np.random.normal(0.0001, 0.02, num_rows)  # 0.01% mean, 2% volatility
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    prices = np.array(prices)
    
    # Generate OHLC data
    high_noise = np.random.uniform(0.001, 0.01, num_rows)
    low_noise = np.random.uniform(-0.01, -0.001, num_rows)
    
    df = pd.DataFrame({
        'close': prices,
        'open': prices * (1 + np.random.uniform(-0.005, 0.005, num_rows)),
        'high': prices * (1 + high_noise),
        'low': prices * (1 + low_noise),
        'volume': np.random.lognormal(10, 1, num_rows),
    })
    
    # Calculate technical indicators
    df['RSI'] = calculate_rsi(df['close'], 14)
    df['SMA_20'] = df['close'].rolling(20).mean()
    df['SMA_50'] = df['close'].rolling(50).mean()
    df['SMA_200'] = df['close'].rolling(200).mean()
    df['EMA_12'] = df['close'].ewm(span=12).mean()
    df['EMA_21'] = df['close'].ewm(span=21).mean()
    df['EMA_26'] = df['close'].ewm(span=26).mean()
    df['EMA_50'] = df['close'].ewm(span=50).mean()
    
    # Bollinger Bands
    bb_period = 20
    bb_std = 2
    bb_middle = df['close'].rolling(bb_period).mean()
    bb_std_dev = df['close'].rolling(bb_period).std()
    df['BB_upper'] = bb_middle + (bb_std_dev * bb_std)
    df['BB_lower'] = bb_middle - (bb_std_dev * bb_std)
    df['BB_middle'] = bb_middle
    df['BB_width'] = (df['BB_upper'] - df['BB_lower']) / df['BB_middle']
    df['BB_position'] = (df['close'] - df['BB_lower']) / (df['BB_upper'] - df['BB_lower'])
    
    # MACD
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    df['MACD'] = ema_12 - ema_26
    df['MACD_signal'] = df['MACD'].ewm(span=9).mean()
    
    # Stochastic
    low_14 = df['low'].rolling(14).min()
    high_14 = df['high'].rolling(14).max()
    df['STOCH_K'] = 100 * (df['close'] - low_14) / (high_14 - low_14)
    df['STOCH_D'] = df['STOCH_K'].rolling(3).mean()
    
    # Additional indicators
    df['WILLIAMS_R'] = -100 * (high_14 - df['close']) / (high_14 - low_14)
    df['volume_ratio'] = df['volume'] / df['volume'].rolling(20).mean()
    df['ATR'] = calculate_atr(df, 14)
    df['ROC_5'] = df['close'].pct_change(5) * 100
    df['MA_7'] = df['close'].rolling(7).mean()
    df['MA_25'] = df['close'].rolling(25).mean()
    df['MA_50'] = df['close'].rolling(50).mean()
    
    # Fill NaN values for testing
    df = df.fillna(method='bfill').fillna(0)
    
    return df


def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
    """Calculate RSI indicator"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))


def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
    """Calculate Average True Range"""
    high_low = df['high'] - df['low']
    high_close = np.abs(df['high'] - df['close'].shift())
    low_close = np.abs(df['low'] - df['close'].shift())
    
    true_range = np.maximum(high_low, np.maximum(high_close, low_close))
    return true_range.rolling(period).mean()


def benchmark_rule_performance(df: pd.DataFrame, num_tests: int = 1000) -> Tuple[List[float], List[float]]:
    """Benchmark performance of both implementations"""
    if not BOTH_AVAILABLE:
        print("Cannot run benchmark - both implementations not available")
        return [], []
    
    # Initialize both rule engines
    original_rules = OriginalRules(df)
    optimized_rules = OptimizedRules(df)
    
    # Get comparable rules (first 6 rules that exist in both)
    original_rule_list = original_rules.get_all_rules()[:6]
    optimized_rule_list = optimized_rules.get_all_rules()[:6]
    
    print(f"Benchmarking {len(original_rule_list)} rules with {num_tests} iterations each...")
    
    original_times = []
    optimized_times = []
    
    # Test indices (avoid early indices that might return False due to insufficient data)
    test_indices = np.random.randint(200, len(df) - 100, num_tests)
    
    for rule_name, original_func in original_rule_list:
        print(f"Testing: {rule_name}")
        
        # Find corresponding optimized function
        optimized_func = None
        for opt_name, opt_func in optimized_rule_list:
            if opt_name == rule_name:
                optimized_func = opt_func
                break
        
        if optimized_func is None:
            print(f"  Skipping {rule_name} - not found in optimized version")
            continue
        
        # Benchmark original implementation
        start_time = time.perf_counter()
        original_results = []
        for idx in test_indices:
            try:
                result = original_func(idx)
                original_results.append(result)
            except Exception as e:
                print(f"  Error in original {rule_name}: {e}")
                original_results.append(False)
        original_time = time.perf_counter() - start_time
        
        # Benchmark optimized implementation
        start_time = time.perf_counter()
        optimized_results = []
        for idx in test_indices:
            try:
                result = optimized_func(idx)
                optimized_results.append(result)
            except Exception as e:
                print(f"  Error in optimized {rule_name}: {e}")
                optimized_results.append(False)
        optimized_time = time.perf_counter() - start_time
        
        # Verify results are identical
        matches = sum(1 for a, b in zip(original_results, optimized_results) if a == b)
        match_rate = matches / len(original_results) * 100
        
        original_times.append(original_time)
        optimized_times.append(optimized_time)
        
        speedup = original_time / optimized_time if optimized_time > 0 else float('inf')
        
        print(f"  Original: {original_time*1000:.2f}ms")
        print(f"  Optimized: {optimized_time*1000:.2f}ms")
        print(f"  Speedup: {speedup:.2f}x")
        print(f"  Result match rate: {match_rate:.1f}%")
        print()
    
    return original_times, optimized_times


def plot_performance_comparison(original_times: List[float], optimized_times: List[float]):
    """Create performance comparison visualization"""
    if not original_times or not optimized_times:
        print("No timing data available for plotting")
        return
    
    rule_names = [f"Rule {i+1}" for i in range(len(original_times))]
    speedups = [orig/opt for orig, opt in zip(original_times, optimized_times)]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Execution time comparison
    x = np.arange(len(rule_names))
    width = 0.35
    
    ax1.bar(x - width/2, [t*1000 for t in original_times], width, label='Original', alpha=0.8)
    ax1.bar(x + width/2, [t*1000 for t in optimized_times], width, label='Optimized', alpha=0.8)
    
    ax1.set_xlabel('Rules')
    ax1.set_ylabel('Execution Time (ms)')
    ax1.set_title('Execution Time Comparison')
    ax1.set_xticks(x)
    ax1.set_xticklabels(rule_names, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Speedup chart
    bars = ax2.bar(rule_names, speedups, color='green', alpha=0.7)
    ax2.set_xlabel('Rules')
    ax2.set_ylabel('Speedup Factor')
    ax2.set_title('Performance Improvement (Higher is Better)')
    ax2.set_xticklabels(rule_names, rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # Add speedup labels on bars
    for bar, speedup in zip(bars, speedups):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{speedup:.1f}x', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print summary statistics
    avg_speedup = np.mean(speedups)
    total_original = sum(original_times) * 1000
    total_optimized = sum(optimized_times) * 1000
    
    print(f"\n📊 PERFORMANCE SUMMARY")
    print(f"{'='*50}")
    print(f"Average speedup: {avg_speedup:.2f}x")
    print(f"Total original time: {total_original:.2f}ms")
    print(f"Total optimized time: {total_optimized:.2f}ms")
    print(f"Total improvement: {total_original/total_optimized:.2f}x")
    print(f"Time saved: {total_original - total_optimized:.2f}ms ({(1-total_optimized/total_original)*100:.1f}%)")


def main():
    """Run the complete performance test suite"""
    print("🚀 Trading Rules Performance Test")
    print("=" * 50)
    
    # Generate test data
    print("Generating test data...")
    df = generate_test_data(10000)
    print(f"Generated {len(df)} rows of test data")
    
    # Run benchmarks
    print("\nRunning performance benchmarks...")
    original_times, optimized_times = benchmark_rule_performance(df, num_tests=500)
    
    # Create visualizations
    if original_times and optimized_times:
        print("\nGenerating performance comparison charts...")
        plot_performance_comparison(original_times, optimized_times)
    
    print("\n✅ Performance test completed!")


if __name__ == "__main__":
    main()
