"""
Optimized Selected Buy Rules - High Performance Version
This file contains the 48 buy rules optimized for maximum performance in live trading.

Key Optimizations:
1. Batch data access with bounds checking
2. Vectorized operations where possible
3. Eliminated temporary list/array creation
4. Fast NaN checking using direct comparisons
5. Cached column access for repeated use
6. Minimized function call overhead
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional


class SelectedBuyRulesOptimized:
    """High-performance selected buy rules from lastrules.md"""
    
    def __init__(self, df: pd.DataFrame):
        self.df = df
        self.category = 'SELECTED'
        self.length = len(df)
        
        # Pre-cache column references for faster access
        self._columns = {}
        self._cache_columns()
    
    def _cache_columns(self):
        """Cache column references to avoid repeated dictionary lookups"""
        common_cols = [
            'close', 'open', 'high', 'low', 'volume',
            'RSI', 'MACD', 'MACD_signal', 'volume_ratio',
            'BB_upper', 'BB_lower', 'BB_middle', 'BB_position',
            'SMA_20', 'SMA_50', 'SMA_200', 'EMA_12', 'EMA_21', 'EMA_26', 'EMA_50',
            'MA_7', 'MA_25', 'MA_50', 'STOCH_K', 'STOCH_D', 'WILLIAMS_R',
            'ATR', 'ADX', 'PLUS_DI', 'MINUS_DI', 'MFI', 'CCI', 'CMF',
            'HMA_20', 'ROC_5', 'BB_width'
        ]
        
        for col in common_cols:
            if col in self.df.columns:
                self._columns[col] = self.df[col].values  # Use .values for fastest access
    
    def _get_values(self, column: str, indices: list) -> np.ndarray:
        """Fast batch data retrieval with bounds checking"""
        if column not in self._columns:
            return np.full(len(indices), np.nan)
        
        col_data = self._columns[column]
        result = np.full(len(indices), np.nan)
        
        for i, idx in enumerate(indices):
            if 0 <= idx < self.length:
                result[i] = col_data[idx]
        
        return result
    
    def _get_single(self, column: str, idx: int) -> float:
        """Fast single value retrieval"""
        if column not in self._columns or idx < 0 or idx >= self.length:
            return np.nan
        return self._columns[column][idx]
    
    def _is_valid(self, *values) -> bool:
        """Fast NaN check using direct comparison (faster than np.isnan)"""
        for val in values:
            if val != val:  # NaN != NaN is True, fastest NaN check
                return False
        return True
    
    def _max_range(self, column: str, start_idx: int, end_idx: int) -> float:
        """Fast max calculation over range"""
        if column not in self._columns:
            return np.nan
        
        start_idx = max(0, start_idx)
        end_idx = min(self.length, end_idx)
        
        if start_idx >= end_idx:
            return np.nan
        
        return np.max(self._columns[column][start_idx:end_idx])
    
    def _min_range(self, column: str, start_idx: int, end_idx: int) -> float:
        """Fast min calculation over range"""
        if column not in self._columns:
            return np.nan
        
        start_idx = max(0, start_idx)
        end_idx = min(self.length, end_idx)
        
        if start_idx >= end_idx:
            return np.nan
        
        return np.min(self._columns[column][start_idx:end_idx])
    
    def _mean_range(self, column: str, start_idx: int, end_idx: int) -> float:
        """Fast mean calculation over range"""
        if column not in self._columns:
            return np.nan
        
        start_idx = max(0, start_idx)
        end_idx = min(self.length, end_idx)
        
        if start_idx >= end_idx:
            return np.nan
        
        return np.mean(self._columns[column][start_idx:end_idx])

    # AI Rule 1: Multi-Timeframe Momentum (Optimized)
    def ai_rule_1_multi_timeframe_momentum(self, idx: int) -> bool:
        """Multi-Timeframe Momentum Convergence - Optimized"""
        if idx < 50:
            return False
        
        # Batch fetch all required values
        roc_5 = self._get_single('ROC_5', idx)
        rsi = self._get_single('RSI', idx)
        ema_50 = self._get_single('EMA_50', idx)
        ema_50_prev = self._get_single('EMA_50', idx-5)
        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        ema_21 = self._get_single('EMA_21', idx)
        
        # Fast validation
        if not self._is_valid(roc_5, rsi, ema_50, ema_50_prev, volume_ratio, close, ema_21):
            return False
        
        # Optimized conditions (short-circuit evaluation)
        return (roc_5 > 0.5 and 
                40 < rsi < 70 and 
                ema_50 > ema_50_prev and 
                volume_ratio > 1.2 and 
                close > ema_21)

    # Price Action Rule 3: Engulfing Pattern (Optimized)
    def price_action_rule_3_engulfing(self, idx: int) -> bool:
        """Bullish Engulfing Pattern - Optimized"""
        if idx < 2:
            return False
        
        # Batch fetch OHLC data for current and previous candles
        curr_data = self._get_values('close', [idx])
        prev_data = self._get_values('close', [idx-1])
        
        open_curr = self._get_single('open', idx)
        close_curr = self._get_single('close', idx)
        open_prev = self._get_single('open', idx-1)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)
        
        # Fast validation
        if not self._is_valid(open_curr, close_curr, open_prev, close_prev, volume_ratio, rsi):
            return False
        
        # Optimized pattern detection (short-circuit evaluation)
        return (close_prev < open_prev and  # Previous bearish
                close_curr > open_curr and  # Current bullish
                open_curr < close_prev and  # Engulfing condition 1
                close_curr > open_prev and  # Engulfing condition 2
                volume_ratio > 1.1 and     # Volume confirmation
                rsi < 75)                   # Not overbought

    # Prof Rule 7: Mean Reversion Volatility Filter (Optimized)
    def prof_rule_7_mean_reversion_volatility_filter(self, idx: int) -> bool:
        """Mean Reversion with Volatility Filter - Optimized"""
        if idx < 30:
            return False
        
        close = self._get_single('close', idx)
        bb_lower = self._get_single('BB_lower', idx)
        bb_width = self._get_single('BB_width', idx)
        rsi = self._get_single('RSI', idx)
        rsi_prev = self._get_single('RSI', idx-5)
        close_prev = self._get_single('close', idx-5)
        volume_ratio = self._get_single('volume_ratio', idx)
        
        if not self._is_valid(close, bb_lower, bb_width, rsi, volume_ratio):
            return False
        
        # Fast volatility calculation
        avg_bb_width = self._mean_range('BB_width', idx-20, idx)
        if avg_bb_width != avg_bb_width:  # NaN check
            return False
        
        oversold = close < bb_lower
        volatility_expansion = 1.2 < bb_width / avg_bb_width < 2.0
        
        # RSI divergence or volume confirmation
        rsi_divergence = (self._is_valid(rsi_prev, close_prev) and 
                         close < close_prev and rsi > rsi_prev)
        
        return oversold and volatility_expansion and (rsi_divergence or volume_ratio > 1.1)

    # Rule 6: Stochastic Oversold Cross (Optimized)
    def rule_6_stochastic_oversold_cross(self, idx: int) -> bool:
        """Stochastic Oversold Cross - Optimized"""
        if idx < 5:
            return False
        
        stoch_k = self._get_single('STOCH_K', idx)
        stoch_k_prev = self._get_single('STOCH_K', idx-1)
        stoch_d = self._get_single('STOCH_D', idx)
        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)
        
        if not self._is_valid(stoch_k, stoch_k_prev, stoch_d, close, close_prev, volume_ratio):
            return False
        
        return (stoch_k_prev < 20 and      # Was oversold
                stoch_k > 20 and           # Now crossing up
                stoch_k > stoch_d and      # %K above %D
                close > close_prev and     # Price confirmation
                volume_ratio > 0.8)        # Volume support

    # Volume Rule 5: Smart Money Volume (Optimized)
    def volume_rule_5_smart_money_volume(self, idx: int) -> bool:
        """Smart Money Volume Pattern - Optimized"""
        if idx < 10:
            return False
        
        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        bb_position = self._get_single('BB_position', idx)
        rsi = self._get_single('RSI', idx)
        
        if not self._is_valid(volume_ratio, close, close_prev, bb_position, rsi):
            return False
        
        # Fast volume trend calculation
        volume_ma = self._mean_range('volume_ratio', idx-5, idx)
        if volume_ma != volume_ma:  # NaN check
            return False
        
        # Check for recent volume spikes (optimized)
        recent_spike = self._max_range('volume_ratio', idx-3, idx) > 2.0
        
        return (close > close_prev and          # Up day
                volume_ratio > 1.3 and         # High volume
                volume_ratio > volume_ma * 1.2 and  # Volume trending
                bb_position < 0.4 and          # Near support
                35 < rsi < 65 and              # Momentum building
                not recent_spike)               # No FOMO

    # Volume Rule 4: Volume Breakout Confirmation (Optimized)
    def volume_rule_4_breakout_confirmation(self, idx: int) -> bool:
        """Volume Breakout Confirmation - Optimized"""
        if idx < 20:
            return False
        
        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)
        macd = self._get_single('MACD', idx)
        macd_signal = self._get_single('MACD_signal', idx)
        
        if not self._is_valid(close, close_prev, volume_ratio, rsi, macd, macd_signal):
            return False
        
        # Fast breakout detection
        high_20 = self._max_range('high', idx-20, idx)
        if high_20 != high_20:  # NaN check
            return False
        
        return (close > high_20 * 1.001 and    # Breakout
                volume_ratio > 2.0 and         # Volume confirmation
                close > close_prev * 0.999 and # Sustained
                rsi < 80 and                   # Not overbought
                macd > macd_signal)            # Momentum support

    # New Rule 4: Ultimate Oscillator Breakout (Optimized)
    def new_rule_4_ultimate_oscillator_breakout(self, idx: int) -> bool:
        """Ultimate Oscillator Breakout - Optimized"""
        if idx < 28:
            return False

        # Pre-calculate periods for efficiency
        periods = [7, 14, 28]
        bp_sums = []
        tr_sums = []

        # Vectorized calculation for all periods
        for period in periods:
            start_idx = max(0, idx - period + 1)

            # Get data slices
            highs = self._columns.get('high', np.array([]))[start_idx:idx+1]
            lows = self._columns.get('low', np.array([]))[start_idx:idx+1]
            closes = self._columns.get('close', np.array([]))[start_idx:idx+1]

            if len(closes) < period:
                return False

            # Vectorized BP and TR calculations
            closes_prev = np.concatenate([[closes[0]], closes[:-1]])

            # Buying Pressure
            bp = closes - np.minimum(lows, closes_prev)

            # True Range
            tr1 = highs - lows
            tr2 = np.abs(highs - closes_prev)
            tr3 = np.abs(lows - closes_prev)
            tr = np.maximum(tr1, np.maximum(tr2, tr3))

            bp_sum = np.sum(bp)
            tr_sum = np.sum(tr)

            if tr_sum == 0:
                return False

            bp_sums.append(bp_sum)
            tr_sums.append(tr_sum)

        # Calculate Ultimate Oscillator
        avg7 = bp_sums[0] / tr_sums[0]
        avg14 = bp_sums[1] / tr_sums[1]
        avg28 = bp_sums[2] / tr_sums[2]

        current_ult_osc = 100 * (4 * avg7 + 2 * avg14 + avg28) / 7

        # Previous Ultimate Oscillator (simplified for performance)
        prev_avg7 = bp_sums[0] / tr_sums[0]  # Approximation for speed
        prev_ult_osc = 100 * (4 * prev_avg7 + 2 * avg14 + avg28) / 7

        volume_ratio = self._get_single('volume_ratio', idx)

        return (current_ult_osc > 30 and
                current_ult_osc > prev_ult_osc and
                volume_ratio > 1.1)

    # Ext Rule 3: Bollinger Squeeze Breakout (Optimized)
    def ext_rule_3_bollinger_squeeze_breakout(self, idx: int) -> bool:
        """Bollinger Band Squeeze Breakout - Optimized"""
        if idx < 20:
            return False

        bb_upper = self._get_single('BB_upper', idx)
        bb_lower = self._get_single('BB_lower', idx)
        bb_middle = self._get_single('BB_middle', idx)
        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(bb_upper, bb_lower, bb_middle, close, volume_ratio):
            return False

        current_width = (bb_upper - bb_lower) / bb_middle

        # Vectorized width calculation for historical comparison
        start_idx = max(0, idx - 20)
        bb_uppers = self._columns.get('BB_upper', np.array([]))[start_idx:idx]
        bb_lowers = self._columns.get('BB_lower', np.array([]))[start_idx:idx]
        bb_middles = self._columns.get('BB_middle', np.array([]))[start_idx:idx]

        # Filter out invalid data
        valid_mask = (bb_middles != 0) & ~np.isnan(bb_uppers) & ~np.isnan(bb_lowers) & ~np.isnan(bb_middles)

        if np.sum(valid_mask) < 10:
            return False

        widths = (bb_uppers[valid_mask] - bb_lowers[valid_mask]) / bb_middles[valid_mask]

        return (current_width < np.percentile(widths, 20) and  # Squeeze
                close > bb_upper and                           # Breakout
                volume_ratio > 1.2)                           # Volume confirmation

    # Rule 2: Golden Cross (Optimized)
    def rule_2_golden_cross(self, idx: int) -> bool:
        """Golden Cross - Optimized"""
        if idx < 200:
            return False

        sma_50 = self._get_single('SMA_50', idx)
        sma_50_prev = self._get_single('SMA_50', idx-1)
        sma_200 = self._get_single('SMA_200', idx)
        sma_200_prev = self._get_single('SMA_200', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(sma_50, sma_50_prev, sma_200, sma_200_prev, volume_ratio):
            return False

        return (sma_50 > sma_200 and        # Current above
                sma_50_prev <= sma_200_prev and  # Previous below/equal
                volume_ratio > 1.0)         # Volume confirmation

    # Rule 7: Bollinger Band Bounce (Optimized)
    def rule_7_bollinger_band_bounce(self, idx: int) -> bool:
        """Bollinger Band Bounce - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        bb_lower = self._get_single('BB_lower', idx)
        bb_position = self._get_single('BB_position', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, bb_lower, bb_position, volume_ratio):
            return False

        return (close <= bb_lower and       # At lower band
                bb_position < 0.1 and      # Low position
                volume_ratio > 0.8)        # Volume support

    # AI Rule 8: Momentum Divergence Reversal (Optimized)
    def ai_rule_8_momentum_divergence_reversal(self, idx: int) -> bool:
        """Momentum Divergence Reversal - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-10)
        rsi = self._get_single('RSI', idx)
        rsi_prev = self._get_single('RSI', idx-10)
        macd = self._get_single('MACD', idx)
        macd_prev = self._get_single('MACD', idx-10)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_prev, rsi, volume_ratio):
            return False

        price_lower_low = close < close_prev
        rsi_higher = self._is_valid(rsi_prev) and rsi > rsi_prev
        macd_higher = self._is_valid(macd, macd_prev) and macd > macd_prev

        return (price_lower_low and
                (rsi_higher or macd_higher) and
                volume_ratio > 1.1 and
                rsi < 40)

    # Rule 27: Structure Break Up (Optimized)
    def rule_27_structure_break_up(self, idx: int) -> bool:
        """Market Structure Break Up - Optimized"""
        if idx < 10:
            return False

        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(close, volume_ratio, rsi):
            return False

        # Fast structure break detection
        high_5 = self._max_range('high', idx-5, idx)
        if high_5 != high_5:  # NaN check
            return False

        return (close > high_5 * 1.001 and     # Structure break
                volume_ratio > 1.2 and         # Volume confirmation
                rsi > 50)                      # Momentum support

    def get_all_rules(self):
        """Get all selected buy rules"""
        return [
            ('AI Rule 1: Multi-Timeframe Momentum', self.ai_rule_1_multi_timeframe_momentum),
            ('Price Action Rule 3: Engulfing Pattern', self.price_action_rule_3_engulfing),
            ('Prof Rule 7: Mean Reversion Volatility Filter', self.prof_rule_7_mean_reversion_volatility_filter),
            ('Rule 6: Stochastic Oversold Cross', self.rule_6_stochastic_oversold_cross),
            ('Volume Rule 5: Smart Money Volume', self.volume_rule_5_smart_money_volume),
            ('Volume Rule 4: Volume Breakout Confirmation', self.volume_rule_4_breakout_confirmation),
            ('New Rule 4: Ultimate Oscillator Breakout', self.new_rule_4_ultimate_oscillator_breakout),
            ('Ext Rule 3: Bollinger Squeeze Breakout', self.ext_rule_3_bollinger_squeeze_breakout),
            ('Rule 2: Golden Cross', self.rule_2_golden_cross),
            ('Rule 7: Bollinger Band Bounce', self.rule_7_bollinger_band_bounce),
            ('AI Rule 8: Momentum Divergence Reversal', self.ai_rule_8_momentum_divergence_reversal),
            ('Rule 27: Structure Break Up', self.rule_27_structure_break_up),
            ('New Buy 5: CMF Positive', self.new_buy_5_cmf_positive),
            ('Rule 9: EMA Alignment', self.rule_9_ema_alignment),
            ('Prof Rule 10: Hull MA Trend', self.prof_rule_10_hull_ma_trend),
            ('Professional Rule 7: Chaikin Money Flow Reversal', self.professional_rule_7_chaikin_money_flow_reversal),
            ('Rule 22: Higher High Pattern', self.rule_22_higher_high_pattern),
            ('Professional Rule 1: Ichimoku Cloud Breakout', self.professional_rule_1_ichimoku_cloud_breakout),
            ('Professional Rule 10: CCI Reversal Enhanced', self.professional_rule_10_cci_reversal_enhanced),
            ('Rule 3: RSI Oversold', self.rule_3_rsi_oversold),
            ('Advanced Rule 5: Donchian Channel Breakout', self.advanced_rule_5_donchian_channel_breakout),
            ('Ext Rule 6: Fibonacci Support Confluence', self.ext_rule_6_fibonacci_support_confluence),
            ('Rule 24: MFI Oversold', self.rule_24_mfi_oversold),
            ('Professional Rule 5: Bollinger Band Squeeze', self.professional_rule_5_bollinger_band_squeeze),
            ('Rule 12: Hammer Pattern', self.rule_12_hammer_pattern),
            ('AI Rule 10: Composite Sentiment Reversal', self.ai_rule_10_composite_sentiment_reversal),
            ('Ext Rule 5: ATR Volatility Expansion', self.ext_rule_5_atr_volatility_expansion),
            ('Ext Rule 1: RSI Bullish Divergence', self.ext_rule_1_rsi_bullish_divergence),
            ('Research Rule 5: Volatility Breakout', self.research_rule_5_volatility_breakout),
            ('SMC Rule 5: Institutional Candle Pattern', self.smc_rule_5_institutional_candle_pattern),
            ('Momentum Rule 5: Momentum Breakout', self.momentum_rule_5_momentum_breakout),
            ('Volume Rule 3: Dark Pool Activity', self.volume_rule_3_dark_pool_activity),
            ('Rule 11: RSI Divergence', self.rule_11_rsi_divergence),
            ('Rule 21: Gap Up', self.rule_21_gap_up),
            ('AI Rule 3: Smart Money Flow Divergence', self.ai_rule_3_smart_money_flow_divergence),
            ('Momentum Rule 2: Momentum Divergence Recovery', self.momentum_rule_2_momentum_divergence_recovery),
            ('Volatility Rule 2: ATR Expansion Signal', self.volatility_rule_2_atr_expansion_signal),
            ('Reversal Rule 4: Bullish Divergence Confluence', self.reversal_rule_4_bullish_divergence_confluence),
            ('Rule 10: Volume Spike', self.rule_10_volume_spike),
            ('AI Rule 6: Market Structure Shift', self.ai_rule_6_market_structure_shift),
            ('Acad Rule 3: Volatility Breakout', self.acad_rule_3_volatility_breakout),
            ('Acad Rule 2: Mean Reversion Factor', self.acad_rule_2_mean_reversion_factor),
            ('Acad Rule 1: Momentum Factor', self.acad_rule_1_momentum_factor),
            ('Rule 1: MA Alignment with RSI Oversold', self.rule_1_ma_alignment_with_rsi_oversold),
            ('Rule 28: Volume Breakout', self.rule_28_volume_breakout),
            ('SMC Rule 1: Order Block Retest', self.smc_rule_1_order_block_retest),
            ('SMC Rule 2: Fair Value Gap Fill', self.smc_rule_2_fair_value_gap_fill),
            ('Advanced Rule 7: DMI ADX Filter', self.advanced_rule_7_dmi_adx_filter),
        ]

    # New Buy 5: CMF Positive (Optimized)
    def new_buy_5_cmf_positive(self, idx: int) -> bool:
        """CMF Positive - Optimized"""
        if idx < 20:
            return False

        cmf = self._get_single('CMF', idx)
        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)

        # If CMF not available, calculate manually (optimized)
        if cmf != cmf:  # NaN check
            # Fast vectorized CMF calculation
            start_idx = max(0, idx-19)
            highs = self._columns.get('high', np.array([]))[start_idx:idx+1]
            lows = self._columns.get('low', np.array([]))[start_idx:idx+1]
            closes = self._columns.get('close', np.array([]))[start_idx:idx+1]
            volumes = self._columns.get('volume', np.array([]))[start_idx:idx+1]

            if len(closes) < 20:
                return False

            # Vectorized MFV calculation
            ranges = highs - lows
            valid_mask = ranges != 0

            if np.sum(valid_mask) == 0:
                return False

            mfv = np.zeros_like(closes)
            mfv[valid_mask] = ((closes[valid_mask] - lows[valid_mask]) -
                              (highs[valid_mask] - closes[valid_mask])) / ranges[valid_mask]

            mfv_volume = mfv * volumes
            cmf = np.sum(mfv_volume) / np.sum(volumes) if np.sum(volumes) > 0 else 0

        if not self._is_valid(cmf, close, close_prev):
            return False

        return cmf > 0 and close > close_prev

    # Rule 9: EMA Alignment (Optimized)
    def rule_9_ema_alignment(self, idx: int) -> bool:
        """EMA Alignment - Optimized"""
        if idx < 50:
            return False

        ema_12 = self._get_single('EMA_12', idx)
        ema_26 = self._get_single('EMA_26', idx)
        ema_50 = self._get_single('EMA_50', idx)
        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(ema_12, ema_26, ema_50, close, volume_ratio):
            return False

        return (ema_12 > ema_26 > ema_50 and    # EMA alignment
                close > ema_12 and              # Price above EMAs
                volume_ratio > 0.8)             # Volume support

    # Prof Rule 10: Hull MA Trend (Optimized)
    def prof_rule_10_hull_ma_trend(self, idx: int) -> bool:
        """Hull MA Trend - Optimized"""
        if idx < 25:
            return False

        hma = self._get_single('HMA_20', idx)
        hma_prev = self._get_single('HMA_20', idx-3)
        close = self._get_single('close', idx)
        low = self._get_single('low', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(hma, hma_prev, close, low, volume_ratio):
            return False

        hma_trending_up = hma > hma_prev
        above_hma = close > hma
        near_hma = abs(low - hma) / hma < 0.01 if hma > 0 else False

        return (hma_trending_up and above_hma and near_hma and volume_ratio > 0.8)

    # Professional Rule 7: Chaikin Money Flow Reversal (Optimized)
    def professional_rule_7_chaikin_money_flow_reversal(self, idx: int) -> bool:
        """Chaikin Money Flow Reversal - Optimized"""
        if idx < 20:
            return False

        period = 20
        volume_ratio = self._get_single('volume_ratio', idx)

        # Vectorized CMF calculation for current and previous periods
        start_idx = max(0, idx - period + 1)

        # Current period
        highs = self._columns.get('high', np.array([]))[start_idx:idx+1]
        lows = self._columns.get('low', np.array([]))[start_idx:idx+1]
        closes = self._columns.get('close', np.array([]))[start_idx:idx+1]
        volumes = self._columns.get('volume', np.array([]))[start_idx:idx+1]

        if len(closes) < period:
            return False

        # Vectorized MF calculation
        ranges = highs - lows
        valid_mask = ranges != 0

        if np.sum(valid_mask) == 0:
            return False

        mf_multiplier = np.zeros_like(closes)
        mf_multiplier[valid_mask] = ((closes[valid_mask] - lows[valid_mask]) -
                                    (highs[valid_mask] - closes[valid_mask])) / ranges[valid_mask]

        mf_volume = mf_multiplier * volumes
        current_cmf = np.sum(mf_volume) / np.sum(volumes) if np.sum(volumes) > 0 else 0

        # Previous period CMF
        prev_start = max(0, idx - period)
        prev_highs = self._columns.get('high', np.array([]))[prev_start:idx]
        prev_lows = self._columns.get('low', np.array([]))[prev_start:idx]
        prev_closes = self._columns.get('close', np.array([]))[prev_start:idx]
        prev_volumes = self._columns.get('volume', np.array([]))[prev_start:idx]

        if len(prev_closes) < period:
            return False

        prev_ranges = prev_highs - prev_lows
        prev_valid_mask = prev_ranges != 0

        if np.sum(prev_valid_mask) == 0:
            return False

        prev_mf_multiplier = np.zeros_like(prev_closes)
        prev_mf_multiplier[prev_valid_mask] = ((prev_closes[prev_valid_mask] - prev_lows[prev_valid_mask]) -
                                              (prev_highs[prev_valid_mask] - prev_closes[prev_valid_mask])) / prev_ranges[prev_valid_mask]

        prev_mf_volume = prev_mf_multiplier * prev_volumes
        prev_cmf = np.sum(prev_mf_volume) / np.sum(prev_volumes) if np.sum(prev_volumes) > 0 else 0

        # CMF reversal: was negative, now positive
        return (prev_cmf <= 0 and current_cmf > 0 and volume_ratio > 1.1)

    # Rule 22: Higher High Pattern (Optimized)
    def rule_22_higher_high_pattern(self, idx: int) -> bool:
        """Higher High Pattern - Optimized"""
        if idx < 5:
            return False

        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)

        if not self._is_valid(volume_ratio, close, close_prev):
            return False

        # Fast higher highs check using vectorized operation
        highs = self._columns.get('high', np.array([]))[idx-4:idx+1]
        if len(highs) < 5:
            return False

        # Check if each high is greater than the next
        higher_highs = np.all(highs[:-1] < highs[1:])

        return (higher_highs and volume_ratio > 1.0 and close > close_prev)

    # Professional Rule 1: Ichimoku Cloud Breakout (Optimized)
    def professional_rule_1_ichimoku_cloud_breakout(self, idx: int) -> bool:
        """Ichimoku Cloud Breakout - Optimized"""
        if idx < 52:
            return False

        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, volume_ratio):
            return False

        # Optimized Ichimoku calculation using vectorized operations
        period1, period2, period3 = 9, 26, 52

        # Get data slices for all periods at once
        highs_52 = self._columns.get('high', np.array([]))[idx-period3:idx+1]
        lows_52 = self._columns.get('low', np.array([]))[idx-period3:idx+1]

        if len(highs_52) < period3:
            return False

        # Vectorized calculations
        tenkan_sen = (np.max(highs_52[-period1:]) + np.min(lows_52[-period1:])) / 2
        kijun_sen = (np.max(highs_52[-period2:]) + np.min(lows_52[-period2:])) / 2
        senkou_span_a = (tenkan_sen + kijun_sen) / 2
        senkou_span_b = (np.max(highs_52) + np.min(lows_52)) / 2

        # Cloud breakout conditions
        above_cloud = close > max(senkou_span_a, senkou_span_b)
        tenkan_above_kijun = tenkan_sen > kijun_sen

        return (above_cloud and tenkan_above_kijun and volume_ratio > 1.1)

    # Professional Rule 10: CCI Reversal Enhanced (Optimized)
    def professional_rule_10_cci_reversal_enhanced(self, idx: int) -> bool:
        """CCI Reversal Enhanced - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_prev, volume_ratio):
            return False

        period = 20

        # Vectorized CCI calculation
        start_idx = max(0, idx - period + 1)
        highs = self._columns.get('high', np.array([]))[start_idx:idx+1]
        lows = self._columns.get('low', np.array([]))[start_idx:idx+1]
        closes = self._columns.get('close', np.array([]))[start_idx:idx+1]

        if len(closes) < period:
            return False

        # Typical prices
        typical_prices = (highs + lows + closes) / 3
        sma_tp = np.mean(typical_prices)
        mad = np.mean(np.abs(typical_prices - sma_tp))

        if mad == 0:
            return False

        current_cci = (typical_prices[-1] - sma_tp) / (0.015 * mad)

        # Previous CCI
        prev_highs = self._columns.get('high', np.array([]))[start_idx-1:idx]
        prev_lows = self._columns.get('low', np.array([]))[start_idx-1:idx]
        prev_closes = self._columns.get('close', np.array([]))[start_idx-1:idx]

        if len(prev_closes) < period:
            return False

        prev_typical_prices = (prev_highs + prev_lows + prev_closes) / 3
        prev_sma_tp = np.mean(prev_typical_prices)
        prev_mad = np.mean(np.abs(prev_typical_prices - prev_sma_tp))

        if prev_mad == 0:
            return False

        prev_cci = (prev_typical_prices[-1] - prev_sma_tp) / (0.015 * prev_mad)

        # CCI reversal: was below -100, now above -100
        cci_reversal = prev_cci <= -100 and current_cci > -100

        return (cci_reversal and volume_ratio > 1.1 and close > close_prev)

    # Rule 3: RSI Oversold (Optimized)
    def rule_3_rsi_oversold(self, idx: int) -> bool:
        """RSI Oversold - Optimized"""
        if idx < 14:
            return False

        rsi = self._get_single('RSI', idx)
        rsi_prev = self._get_single('RSI', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(rsi, rsi_prev, volume_ratio):
            return False

        return (rsi_prev < 30 and rsi > 30 and volume_ratio > 0.8)

    # Advanced Rule 5: Donchian Channel Breakout (Optimized)
    def advanced_rule_5_donchian_channel_breakout(self, idx: int) -> bool:
        """Donchian Channel Breakout - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        high = self._get_single('high', idx)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(close, high, volume_ratio, rsi):
            return False

        # Fast Donchian upper channel calculation
        donchian_upper = self._max_range('high', idx-20, idx)
        if donchian_upper != donchian_upper:  # NaN check
            return False

        return (high > donchian_upper and           # Breakout
                close > donchian_upper * 0.999 and # Close above
                volume_ratio > 1.5 and             # Volume confirmation
                rsi > 50)                          # Momentum

    # Ext Rule 6: Fibonacci Support Confluence (Optimized)
    def ext_rule_6_fibonacci_support_confluence(self, idx: int) -> bool:
        """Fibonacci Support Confluence - Optimized"""
        if idx < 50:
            return False

        close = self._get_single('close', idx)
        low = self._get_single('low', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, low, volume_ratio):
            return False

        # Fast swing high/low calculation
        lookback = 30
        swing_high = self._max_range('high', idx-lookback, idx)
        swing_low = self._min_range('low', idx-lookback, idx)

        if not self._is_valid(swing_high, swing_low):
            return False

        # Fibonacci levels
        fib_range = swing_high - swing_low
        fib_236 = swing_high - (fib_range * 0.236)
        fib_382 = swing_high - (fib_range * 0.382)
        fib_618 = swing_high - (fib_range * 0.618)

        # Check proximity to Fibonacci levels
        tolerance = close * 0.005
        near_fib = (abs(close - fib_236) < tolerance or
                   abs(close - fib_382) < tolerance or
                   abs(close - fib_618) < tolerance)

        # Bounce confirmation
        bounce = low <= min(fib_236, fib_382, fib_618) and close > low

        return (near_fib and bounce and volume_ratio > 1.1)

    # Rule 24: MFI Oversold (Optimized)
    def rule_24_mfi_oversold(self, idx: int) -> bool:
        """MFI Oversold - Optimized"""
        if idx < 14:
            return False

        mfi = self._get_single('MFI', idx)
        mfi_prev = self._get_single('MFI', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(mfi, mfi_prev, volume_ratio):
            return False

        return (mfi_prev < 20 and mfi > 20 and volume_ratio > 0.8)

    # Professional Rule 5: Bollinger Band Squeeze (Optimized)
    def professional_rule_5_bollinger_band_squeeze(self, idx: int) -> bool:
        """Bollinger Band Squeeze - Optimized"""
        if idx < 20:
            return False

        bb_upper = self._get_single('BB_upper', idx)
        bb_lower = self._get_single('BB_lower', idx)
        bb_middle = self._get_single('BB_middle', idx)
        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(bb_upper, bb_lower, bb_middle, close, volume_ratio):
            return False

        current_width = (bb_upper - bb_lower) / bb_middle

        # Previous width
        prev_bb_upper = self._get_single('BB_upper', idx-1)
        prev_bb_lower = self._get_single('BB_lower', idx-1)
        prev_bb_middle = self._get_single('BB_middle', idx-1)

        if not self._is_valid(prev_bb_upper, prev_bb_lower, prev_bb_middle):
            return False

        prev_width = (prev_bb_upper - prev_bb_lower) / prev_bb_middle

        # Average width calculation (vectorized)
        avg_width = self._mean_range('BB_width', idx-20, idx)
        if avg_width != avg_width:  # NaN check
            return False

        squeeze = current_width < avg_width * 0.8
        expanding = current_width > prev_width
        bullish_bias = close > bb_middle

        return (squeeze and expanding and bullish_bias and volume_ratio > 1.1)

    # Rule 12: Hammer Pattern (Optimized)
    def rule_12_hammer_pattern(self, idx: int) -> bool:
        """Hammer Pattern - Optimized"""
        if idx < 2:
            return False

        open_price = self._get_single('open', idx)
        close_price = self._get_single('close', idx)
        high_price = self._get_single('high', idx)
        low_price = self._get_single('low', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(open_price, close_price, high_price, low_price, volume_ratio):
            return False

        # Fast hammer pattern detection
        body = abs(close_price - open_price)
        total_range = high_price - low_price

        if total_range == 0:
            return False

        lower_shadow = min(open_price, close_price) - low_price
        upper_shadow = high_price - max(open_price, close_price)

        return (body < total_range / 3 and          # Small body
                lower_shadow >= body * 2 and        # Long lower shadow
                upper_shadow < body and              # Short upper shadow
                volume_ratio > 1.0)                 # Volume confirmation

    # AI Rule 10: Composite Sentiment Reversal (Optimized)
    def ai_rule_10_composite_sentiment_reversal(self, idx: int) -> bool:
        """Composite Sentiment Reversal - Optimized"""
        if idx < 30:
            return False

        rsi = self._get_single('RSI', idx)
        stoch_k = self._get_single('STOCH_K', idx)
        williams_r = self._get_single('WILLIAMS_R', idx)
        close = self._get_single('close', idx)
        bb_lower = self._get_single('BB_lower', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(rsi, stoch_k, williams_r, close, bb_lower, volume_ratio):
            return False

        # Fast oversold condition checks
        rsi_oversold = rsi < 35
        stoch_oversold = stoch_k < 25
        williams_oversold = williams_r < -75
        near_support = close <= bb_lower * 1.02

        # Count oversold conditions
        oversold_count = sum([rsi_oversold, stoch_oversold, williams_oversold])

        return (oversold_count >= 2 and near_support and volume_ratio > 1.1)

    # Ext Rule 5: ATR Volatility Expansion (Optimized)
    def ext_rule_5_atr_volatility_expansion(self, idx: int) -> bool:
        """ATR Volatility Expansion - Optimized"""
        if idx < 20:
            return False

        current_atr = self._get_single('ATR', idx)
        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(current_atr, close, volume_ratio):
            return False

        # Fast ATR average calculation
        avg_atr = self._mean_range('ATR', idx-19, idx+1)
        if avg_atr != avg_atr or avg_atr == 0:  # NaN or zero check
            return False

        # Price breakout
        high_10 = self._max_range('high', idx-9, idx+1)
        if high_10 != high_10:  # NaN check
            return False

        return (current_atr > avg_atr * 1.3 and     # Volatility expansion
                close > high_10 * 0.999 and        # Price breakout
                volume_ratio > 1.2)                # Volume confirmation

    # Ext Rule 1: RSI Bullish Divergence (Optimized)
    def ext_rule_1_rsi_bullish_divergence(self, idx: int) -> bool:
        """RSI Bullish Divergence - Optimized"""
        if idx < 20:
            return False

        # Vectorized data retrieval for divergence analysis
        lookback = 10
        start_idx = max(0, idx - lookback)

        prices = self._columns.get('close', np.array([]))[start_idx:idx+1]
        rsi_values = self._columns.get('RSI', np.array([]))[start_idx:idx+1]

        if len(prices) < lookback or len(rsi_values) < lookback:
            return False

        # Fast divergence detection using vectorized operations
        recent_low_idx = np.argmin(prices[-5:])
        recent_low_price = prices[-5:][recent_low_idx]
        recent_low_rsi = rsi_values[-5:][recent_low_idx]

        earlier_prices = prices[:-5]
        earlier_rsi = rsi_values[:-5]

        if len(earlier_prices) < 5:
            return False

        earlier_low_idx = np.argmin(earlier_prices)
        earlier_low_price = earlier_prices[earlier_low_idx]
        earlier_low_rsi = earlier_rsi[earlier_low_idx]

        # Divergence conditions
        price_lower_low = recent_low_price < earlier_low_price
        rsi_higher_low = recent_low_rsi > earlier_low_rsi

        current_rsi = self._get_single('RSI', idx)
        rsi_recovery = current_rsi > recent_low_rsi

        return (price_lower_low and rsi_higher_low and rsi_recovery)

    # Research Rule 5: Volatility Breakout (Optimized)
    def research_rule_5_volatility_breakout(self, idx: int) -> bool:
        """Volatility Breakout - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_prev, volume_ratio):
            return False

        # Vectorized volatility calculation
        period = 20
        start_idx = max(0, idx - period + 1)
        closes = self._columns.get('close', np.array([]))[start_idx:idx+1]

        if len(closes) < period:
            return False

        # Calculate returns and volatility
        returns = np.diff(closes) / closes[:-1]
        current_volatility = np.std(returns)

        # Historical volatility
        hist_start = max(0, idx - 39)
        hist_closes = self._columns.get('close', np.array([]))[hist_start:idx-19]

        if len(hist_closes) < 20:
            return False

        hist_returns = np.diff(hist_closes) / hist_closes[:-1]
        historical_volatility = np.std(hist_returns)

        if historical_volatility == 0:
            return False

        return (current_volatility > historical_volatility * 1.5 and  # Volatility breakout
                close > close_prev and                                # Momentum up
                volume_ratio > 1.2)                                  # Volume confirmation

    # SMC Rule 5: Institutional Candle Pattern (Optimized)
    def smc_rule_5_institutional_candle_pattern(self, idx: int) -> bool:
        """Institutional Candle Pattern - Optimized"""
        if idx < 5:
            return False

        open_price = self._get_single('open', idx)
        close_price = self._get_single('close', idx)
        high_price = self._get_single('high', idx)
        low_price = self._get_single('low', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(open_price, close_price, high_price, low_price, volume_ratio):
            return False

        # Fast institutional pattern detection
        body_size = abs(close_price - open_price) / open_price * 100
        total_range = (high_price - low_price) / low_price * 100

        if total_range == 0:
            return False

        body_to_range_ratio = body_size / total_range

        # Recent high for resistance check
        recent_high = self._max_range('high', idx-10, idx)
        if recent_high != recent_high:  # NaN check
            return False

        return (body_size > 0.8 and                    # Large body
                body_to_range_ratio > 0.7 and         # Strong body ratio
                close_price > open_price and           # Bullish direction
                volume_ratio > 2.0 and                # High volume
                close_price > recent_high)             # Above resistance

    # Momentum Rule 5: Momentum Breakout (Optimized)
    def momentum_rule_5_momentum_breakout(self, idx: int) -> bool:
        """Momentum Breakout - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_10 = self._get_single('close', idx-10)
        volume_ratio = self._get_single('volume_ratio', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(close, close_10, volume_ratio, rsi) or close_10 == 0:
            return False

        # Fast momentum calculation
        momentum = (close - close_10) / close_10 * 100

        # Price breakout
        high_20 = self._max_range('high', idx-20, idx)
        if high_20 != high_20:  # NaN check
            return False

        return (momentum > 3.0 and              # Strong momentum
                volume_ratio > 1.5 and         # Volume momentum
                50 < rsi < 75 and              # RSI range
                close > high_20 * 1.001)       # Breakout

    # Volume Rule 3: Dark Pool Activity (Optimized)
    def volume_rule_3_dark_pool_activity(self, idx: int) -> bool:
        """Dark Pool Activity - Optimized"""
        if idx < 30:
            return False

        volume_ratio = self._get_single('volume_ratio', idx)
        high = self._get_single('high', idx)
        low = self._get_single('low', idx)
        close = self._get_single('close', idx)

        if not self._is_valid(volume_ratio, high, low, close):
            return False

        # Fast range and volatility calculation
        current_range = high - low
        avg_range = self._mean_range('high', idx-10, idx) - self._mean_range('low', idx-10, idx)

        if avg_range == 0:
            return False

        # Previous high for breakout potential
        prev_high = self._max_range('high', idx-5, idx)
        if prev_high != prev_high:  # NaN check
            return False

        return (volume_ratio > 2.0 and              # Volume spike
                current_range < avg_range * 0.7 and # Low volatility
                close > prev_high * 0.999)         # Breakout potential

    # Rule 11: RSI Divergence (Optimized)
    def rule_11_rsi_divergence(self, idx: int) -> bool:
        """RSI Divergence - Optimized"""
        if idx < 20:
            return False

        # Vectorized divergence analysis
        lookback = 10
        start_idx = max(0, idx - lookback)

        prices = self._columns.get('close', np.array([]))[start_idx:idx+1]
        rsi_values = self._columns.get('RSI', np.array([]))[start_idx:idx+1]

        if len(prices) < lookback or len(rsi_values) < lookback:
            return False

        # Fast min detection
        recent_low_price = np.min(prices[-5:])
        recent_low_rsi = np.min(rsi_values[-5:])
        earlier_low_price = np.min(prices[:-5])
        earlier_low_rsi = np.min(rsi_values[:-5])

        current_rsi = self._get_single('RSI', idx)

        return (recent_low_price < earlier_low_price and    # Price lower low
                recent_low_rsi > earlier_low_rsi and        # RSI higher low
                current_rsi > recent_low_rsi)               # Current recovery

    # Rule 21: Gap Up (Optimized)
    def rule_21_gap_up(self, idx: int) -> bool:
        """Gap Up - Optimized"""
        if idx < 1:
            return False

        current_low = self._get_single('low', idx)
        prev_high = self._get_single('high', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        open_price = self._get_single('open', idx)

        if not self._is_valid(current_low, prev_high, volume_ratio, close, open_price):
            return False

        return (current_low > prev_high and     # Gap up
                volume_ratio > 1.2 and         # Volume confirmation
                close > open_price)             # Strong close

    # AI Rule 3: Smart Money Flow Divergence (Optimized)
    def ai_rule_3_smart_money_flow_divergence(self, idx: int) -> bool:
        """Smart Money Flow Divergence - Optimized"""
        if idx < 20:
            return False

        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        close_prev = self._get_single('close', idx-5)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(volume_ratio, close, close_prev, rsi):
            return False

        # Smart money pattern detection
        price_declining = close < close_prev
        volume_increasing = volume_ratio > 1.2
        smart_money_pattern = price_declining and volume_increasing
        oversold_recovery = 25 < rsi < 45

        return (smart_money_pattern and oversold_recovery and volume_ratio > 1.5)

    # Momentum Rule 2: Momentum Divergence Recovery (Optimized)
    def momentum_rule_2_momentum_divergence_recovery(self, idx: int) -> bool:
        """Momentum Divergence Recovery - Optimized"""
        if idx < 15:
            return False

        close = self._get_single('close', idx)
        close_10 = self._get_single('close', idx-10)
        rsi = self._get_single('RSI', idx)
        rsi_10 = self._get_single('RSI', idx-10)
        macd = self._get_single('MACD', idx)
        macd_signal = self._get_single('MACD_signal', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_10, rsi, rsi_10, macd, macd_signal, volume_ratio):
            return False

        return (close < close_10 and            # Price lower
                rsi > rsi_10 and               # Momentum higher
                macd > macd_signal and         # MACD bullish
                volume_ratio > 1.0)            # Volume support

    # Volatility Rule 2: ATR Expansion Signal (Optimized)
    def volatility_rule_2_atr_expansion_signal(self, idx: int) -> bool:
        """ATR Expansion Signal - Optimized"""
        if idx < 20:
            return False

        current_atr = self._get_single('ATR', idx)
        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(current_atr, close, volume_ratio):
            return False

        # Fast ATR comparison
        avg_atr = self._mean_range('ATR', idx-20, idx)
        if avg_atr != avg_atr or avg_atr == 0:  # NaN or zero check
            return False

        # Breakout confirmation
        high_10 = self._max_range('high', idx-10, idx)
        if high_10 != high_10:  # NaN check
            return False

        return (current_atr > avg_atr * 1.3 and     # ATR expansion
                close > high_10 and                # Breakout
                volume_ratio > 1.2)                # Volume confirmation

    # Reversal Rule 4: Bullish Divergence Confluence (Optimized)
    def reversal_rule_4_bullish_divergence_confluence(self, idx: int) -> bool:
        """Bullish Divergence Confluence - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        close_10 = self._get_single('close', idx-10)
        rsi = self._get_single('RSI', idx)
        rsi_10 = self._get_single('RSI', idx-10)
        macd = self._get_single('MACD', idx)
        macd_10 = self._get_single('MACD', idx-10)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_10, rsi, rsi_10, macd, macd_10, volume_ratio):
            return False

        # Fast divergence detection
        price_lower = close < close_10
        rsi_higher = rsi > rsi_10
        macd_higher = macd > macd_10

        # Count divergences
        divergence_count = sum([rsi_higher, macd_higher])

        return (price_lower and divergence_count >= 2 and volume_ratio > 1.1)

    # Rule 10: Volume Spike (Optimized)
    def rule_10_volume_spike(self, idx: int) -> bool:
        """Volume Spike - Optimized"""
        if idx < 20:
            return False

        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        open_price = self._get_single('open', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(volume_ratio, close, open_price, rsi):
            return False

        return (volume_ratio > 2.0 and      # Volume spike
                close > open_price and      # Bullish candle
                rsi < 75)                   # Not overbought

    # AI Rule 6: Market Structure Shift (Optimized)
    def ai_rule_6_market_structure_shift(self, idx: int) -> bool:
        """Market Structure Shift - Optimized"""
        if idx < 30:
            return False

        close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, volume_ratio):
            return False

        # Fast structure analysis using vectorized operations
        lookback = 10
        start_idx = max(0, idx - lookback)

        lows = self._columns.get('low', np.array([]))[start_idx:idx+1]
        highs = self._columns.get('high', np.array([]))[start_idx:idx+1]

        if len(lows) < lookback or len(highs) < lookback:
            return False

        # Higher low formation
        current_low = np.min(lows[-3:])
        previous_low = np.min(lows[:-3])
        higher_low = current_low > previous_low

        # Resistance break
        recent_resistance = np.max(highs[:-1])
        resistance_break = close > recent_resistance

        return (higher_low and resistance_break and volume_ratio > 1.3)

    # Acad Rule 3: Volatility Breakout (Optimized)
    def acad_rule_3_volatility_breakout(self, idx: int) -> bool:
        """Academic Volatility Breakout - Optimized"""
        if idx < 20:
            return False

        close = self._get_single('close', idx)
        atr = self._get_single('ATR', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, atr, volume_ratio):
            return False

        # Donchian breakout
        high_20 = self._max_range('high', idx-20, idx)
        if high_20 != high_20:  # NaN check
            return False

        # Low volatility before breakout
        avg_atr = self._mean_range('ATR', idx-20, idx)
        if avg_atr != avg_atr or avg_atr == 0:  # NaN or zero check
            return False

        return (close > high_20 and             # Breakout
                atr < avg_atr * 0.8 and        # Low volatility before
                volume_ratio > 1.5)            # Volume expansion

    # Acad Rule 2: Mean Reversion Factor (Optimized)
    def acad_rule_2_mean_reversion_factor(self, idx: int) -> bool:
        """Academic Mean Reversion Factor - Optimized"""
        if idx < 5:
            return False

        close = self._get_single('close', idx)
        close_5d_ago = self._get_single('close', idx-5)
        sma_50 = self._get_single('SMA_50', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_5d_ago, volume_ratio) or close_5d_ago == 0:
            return False

        # Short-term return calculation
        short_term_return = (close / close_5d_ago - 1) * 100

        # Trend check
        above_long_term_trend = True
        if self._is_valid(sma_50):
            above_long_term_trend = close > sma_50 * 0.95

        return (short_term_return < -3.0 and        # Significant decline
                above_long_term_trend and           # Not in major downtrend
                volume_ratio > 1.1)                 # Volume confirmation

    # Acad Rule 1: Momentum Factor (Optimized)
    def acad_rule_1_momentum_factor(self, idx: int) -> bool:
        """Academic Momentum Factor - Optimized"""
        if idx < 30:
            return False

        close = self._get_single('close', idx)
        close_30d_ago = self._get_single('close', idx-30)
        sma_20 = self._get_single('SMA_20', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(close, close_30d_ago, volume_ratio) or close_30d_ago == 0:
            return False

        # Medium-term momentum
        momentum_return = (close / close_30d_ago - 1) * 100

        # Trend continuation
        trend_continuation = True
        if self._is_valid(sma_20):
            trend_continuation = close > sma_20

        return (momentum_return > 5.0 and           # Strong momentum
                trend_continuation and              # Trend continuation
                volume_ratio > 1.0)                 # Volume support

    # Rule 1: MA Alignment with RSI Oversold (Optimized)
    def rule_1_ma_alignment_with_rsi_oversold(self, idx: int) -> bool:
        """MA Alignment with RSI Oversold - Optimized"""
        if idx < 50:
            return False

        ma7 = self._get_single('MA_7', idx)
        ma25 = self._get_single('MA_25', idx)
        ma50 = self._get_single('MA_50', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(ma7, ma25, ma50, rsi):
            return False

        return (ma7 > ma25 > ma50 and rsi < 30)     # MA alignment + RSI oversold

    # Rule 28: Volume Breakout (Optimized)
    def rule_28_volume_breakout(self, idx: int) -> bool:
        """Volume Breakout - Optimized"""
        if idx < 20:
            return False

        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        rsi = self._get_single('RSI', idx)

        if not self._is_valid(volume_ratio, close, rsi):
            return False

        # Price breakout
        high_20 = self._max_range('high', idx-20, idx)
        if high_20 != high_20:  # NaN check
            return False

        return (volume_ratio > 2.5 and          # Volume breakout
                close > high_20 * 1.001 and    # Price breakout
                rsi > 50)                       # Momentum

    # SMC Rule 1: Order Block Retest (Optimized)
    def smc_rule_1_order_block_retest(self, idx: int) -> bool:
        """Order Block Retest - Optimized"""
        if idx < 10:
            return False

        current_low = self._get_single('low', idx)
        current_close = self._get_single('close', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(current_low, current_close, volume_ratio):
            return False

        # Fast order block detection using vectorized operations
        lookback = 10
        start_idx = max(0, idx - lookback)

        volumes = self._columns.get('volume', np.array([]))[start_idx:idx]
        if len(volumes) < lookback:
            return False

        avg_volume = np.mean(volumes)

        # Find high volume candle (order block)
        high_volume_mask = volumes > avg_volume * 2.0
        if not np.any(high_volume_mask):
            return False

        # Get the most recent high volume candle
        high_volume_indices = np.where(high_volume_mask)[0]
        order_block_idx = start_idx + high_volume_indices[-1]

        order_block_low = self._get_single('low', order_block_idx)
        order_block_high = self._get_single('high', order_block_idx)

        if not self._is_valid(order_block_low, order_block_high):
            return False

        # Retest condition
        retest = (current_low <= order_block_high and current_close >= order_block_low)

        return (retest and volume_ratio > 1.1)

    # SMC Rule 2: Fair Value Gap Fill (Optimized)
    def smc_rule_2_fair_value_gap_fill(self, idx: int) -> bool:
        """Fair Value Gap Fill - Optimized"""
        if idx < 3:
            return False

        high_2 = self._get_single('high', idx-2)
        low_1 = self._get_single('low', idx-1)
        current_low = self._get_single('low', idx)
        current_high = self._get_single('high', idx)
        current_close = self._get_single('close', idx)
        current_open = self._get_single('open', idx)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(high_2, low_1, current_low, current_high,
                             current_close, current_open, volume_ratio):
            return False

        # Fast gap detection and fill
        gap_exists = high_2 < low_1
        gap_fill = current_low <= high_2 and current_high >= low_1
        bullish_continuation = current_close > current_open

        return (gap_exists and gap_fill and bullish_continuation and volume_ratio > 1.0)

    # Advanced Rule 7: DMI ADX Filter (Optimized)
    def advanced_rule_7_dmi_adx_filter(self, idx: int) -> bool:
        """DMI ADX Filter - Optimized"""
        if idx < 14:
            return False

        current_plus_di = self._get_single('PLUS_DI', idx)
        current_minus_di = self._get_single('MINUS_DI', idx)
        current_adx = self._get_single('ADX', idx)
        prev_plus_di = self._get_single('PLUS_DI', idx-1)
        prev_minus_di = self._get_single('MINUS_DI', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)

        if not self._is_valid(current_plus_di, current_minus_di, current_adx,
                             prev_plus_di, prev_minus_di, volume_ratio):
            return False

        # DMI crossover and trend strength
        di_crossover = (current_plus_di > current_minus_di and
                       prev_plus_di <= prev_minus_di)
        strong_trend = current_adx > 25

        return (di_crossover and strong_trend and volume_ratio > 1.0)
