"""
Test Script for Multiple Concurrent Positions
Verify that FullAnalysisConfig now supports up to 5 concurrent trades
"""

import pandas as pd
import numpy as np
from config import FullAnalysisConfig
from backtesting_engine import BacktestingEngine
from indicators import TechnicalIndicators


def generate_test_data(num_rows=2000):
    """Generate test data with multiple trading opportunities"""
    np.random.seed(42)
    
    # Generate price series with multiple trends
    base_price = 100
    returns = np.random.normal(0.0002, 0.015, num_rows)  # Slightly positive trend
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    prices = np.array(prices)
    
    # Generate OHLC
    high_noise = np.random.uniform(0.002, 0.008, num_rows)
    low_noise = np.random.uniform(-0.008, -0.002, num_rows)
    
    df = pd.DataFrame({
        'close': prices,
        'open': prices * (1 + np.random.uniform(-0.003, 0.003, num_rows)),
        'high': prices * (1 + high_noise),
        'low': prices * (1 + low_noise),
        'volume': np.random.lognormal(10, 1, num_rows),
    })
    
    return df


def test_multiple_positions():
    """Test the multiple concurrent positions functionality"""
    print("🧪 Testing Multiple Concurrent Positions")
    print("=" * 50)
    
    # Generate test data
    print("📊 Generating test data...")
    df = generate_test_data(2000)
    
    # Calculate indicators
    print("📈 Calculating technical indicators...")
    indicators = TechnicalIndicators(df)
    df = indicators.calculate_all_indicators()
    
    # Test configuration
    config = FullAnalysisConfig()
    
    # Verify configuration
    print(f"✅ Configuration loaded:")
    print(f"   - USE_UNIFIED_EVALUATION: {config.USE_UNIFIED_EVALUATION}")
    print(f"   - USE_INDEPENDENT_EVALUATION: {config.USE_INDEPENDENT_EVALUATION}")
    print(f"   - MAX_CONCURRENT_TRADES: {config.MAX_CONCURRENT_TRADES}")
    print(f"   - DATASET_SIZE: {config.CURRENT_DATASET_SIZE}")
    
    if config.MAX_CONCURRENT_TRADES != 5:
        print(f"❌ Expected MAX_CONCURRENT_TRADES=5, got {config.MAX_CONCURRENT_TRADES}")
        return False
    
    if not config.USE_UNIFIED_EVALUATION:
        print(f"❌ Expected USE_UNIFIED_EVALUATION=True, got {config.USE_UNIFIED_EVALUATION}")
        return False
    
    # Initialize backtesting engine
    print("\n🚀 Initializing backtesting engine...")
    try:
        engine = BacktestingEngine(df, config)
        print("✅ Backtesting engine initialized successfully")
        
        # Check if multiple positions support is enabled
        if hasattr(engine, 'max_concurrent_trades'):
            print(f"✅ Multiple positions support detected: {engine.max_concurrent_trades} max trades")
        else:
            print("⚠️ Multiple positions support not detected in engine")
        
        # Run a small test
        print("\n📊 Running small unified backtest...")
        
        # Use a smaller subset for testing
        start_idx = 200
        end_idx = 800
        
        result = engine._run_unified_backtest(start_idx, end_idx)
        
        print(f"\n📈 Test Results:")
        print(f"   - Total trades: {result['total_trades']}")
        print(f"   - Final capital: ${result['final_capital']:,.0f}")
        print(f"   - Total return: {result['total_return_pct']:.2f}%")
        
        # Check if we had multiple concurrent positions
        trades = result['trades']
        if trades:
            # Analyze trade overlaps
            overlapping_trades = 0
            for i, trade in enumerate(trades):
                entry_idx = trade['entry_idx']
                exit_idx = trade['exit_idx']
                
                # Count how many other trades were active during this trade
                concurrent_count = 0
                for j, other_trade in enumerate(trades):
                    if i != j:
                        other_entry = other_trade['entry_idx']
                        other_exit = other_trade['exit_idx']
                        
                        # Check if trades overlap
                        if (other_entry <= entry_idx <= other_exit or 
                            other_entry <= exit_idx <= other_exit or
                            entry_idx <= other_entry <= exit_idx):
                            concurrent_count += 1
                
                if concurrent_count > 0:
                    overlapping_trades += 1
            
            print(f"   - Trades with concurrent positions: {overlapping_trades}")
            
            if overlapping_trades > 0:
                print("✅ Multiple concurrent positions detected!")
            else:
                print("⚠️ No concurrent positions detected (may be normal for small test)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_configuration_values():
    """Test that configuration values are set correctly"""
    print("\n🔧 Testing Configuration Values")
    print("=" * 40)
    
    config = FullAnalysisConfig()
    
    tests = [
        ("USE_UNIFIED_EVALUATION", True),
        ("USE_INDEPENDENT_EVALUATION", False),
        ("MAX_CONCURRENT_TRADES", 5),
        ("DISABLE_ALL_SELL_RULES", True),
    ]
    
    all_passed = True
    
    for attr_name, expected_value in tests:
        actual_value = getattr(config, attr_name, None)
        if actual_value == expected_value:
            print(f"✅ {attr_name}: {actual_value}")
        else:
            print(f"❌ {attr_name}: Expected {expected_value}, got {actual_value}")
            all_passed = False
    
    return all_passed


def main():
    """Run all tests"""
    print("🚀 MULTIPLE CONCURRENT POSITIONS TEST SUITE")
    print("=" * 60)
    
    # Test configuration
    config_passed = test_configuration_values()
    
    # Test functionality
    if config_passed:
        functionality_passed = test_multiple_positions()
        
        if functionality_passed:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ FullAnalysisConfig now supports up to 5 concurrent trades")
            print("\nTo run full analysis with multiple positions:")
            print("  python main.py full")
        else:
            print("\n❌ FUNCTIONALITY TESTS FAILED!")
    else:
        print("\n❌ CONFIGURATION TESTS FAILED!")
        print("Please check the FullAnalysisConfig settings")


if __name__ == "__main__":
    main()
