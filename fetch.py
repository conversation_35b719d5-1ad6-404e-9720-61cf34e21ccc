# Binance 1 dakikalık BTCUSDT verilerini verilen tarih aral<PERSON>ında indirir ve CSV'ye kaydeder.
import pandas as pd
from datetime import datetime, timedelta
import requests
import zipfile
import io
import os

def download_and_save_binance_klines(symbol: str, interval: str, start_date: str, end_date: str, output_path: str):
    base_url = f"https://data.binance.vision/data/spot/daily/klines/{symbol}/{interval}/"
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    all_data = []

    for i in range((end_dt - start_dt).days + 1):
        date = start_dt + timedelta(days=i)
        file_date = date.strftime("%Y-%m-%d")
        file_name = f"{symbol}-{interval}-{file_date}.zip"
        url = base_url + file_name
        print(f"🔄 Downloading: {file_name}")

        try:
            response = requests.get(url)
            if response.status_code == 200:
                z = zipfile.ZipFile(io.BytesIO(response.content))
                csv_filename = z.namelist()[0]
                with z.open(csv_filename) as f:
                    df = pd.read_csv(f, header=None)
                    all_data.append(df)
            else:
                print(f"❌ Failed to download: {file_name}")
        except Exception as e:
            print(f"⚠️ Exception occurred for {file_name}: {e}")

    columns = [
        "open_time", "open", "high", "low", "close", "volume", "close_time",
        "quote_asset_volume", "number_of_trades", "taker_buy_base_volume",
        "taker_buy_quote_volume", "ignore"
    ]

    if all_data:
        df_all = pd.concat(all_data, ignore_index=True)
        df_all.columns = columns

        # ✅ Klasör varsa oluşturur, yoksa "." kullanır
        os.makedirs(os.path.dirname(output_path) or ".", exist_ok=True)

        df_all.to_csv(output_path, index=False)
        print(f"\n✅ Veriler başarıyla kaydedildi: {output_path}")
    else:
        print("🚫 Hiçbir veri indirilemedi. Dosya oluşturulmadı.")

# 🔧 Ayarlar (burayı ihtiyacına göre düzenle)
download_and_save_binance_klines(
    symbol="BTCUSDT",
    interval="1m",
    start_date="2025-02-14",
    end_date="2025-07-01",
    output_path="raw_BTCUSDT_1m.csv"  # klasör verilmediyse sorun olmaz artık
)
