#!/usr/bin/env python3
"""
Test script to verify the new global market filters and warmup functionality
"""

from backtesting_engine import BacktestingEngine
from config import Config
import pandas as pd
import numpy as np

def test_warmup_functionality():
    """Test that warmup period prevents trades during warmup"""
    print("🧪 Testing Warmup Functionality")
    print("=" * 50)
    
    config = Config()
    config.USE_OPTIMIZED_RULES_ONLY = True
    config.ENABLE_DATA_WARMUP = True
    config.DATA_WARMUP_PERIOD = 1000
    config.CURRENT_DATASET_SIZE = 2000
    config.ENABLE_GLOBAL_MARKET_FILTERS = False  # Disable other filters for clean test
    
    try:
        engine = BacktestingEngine(config)
        
        # Get the actual backtest range
        start_idx, end_idx = config.get_backtest_range(len(engine.df))
        print(f"📊 Backtest range: {start_idx:,} to {end_idx:,}")
        print(f"✅ Warmup working: {start_idx >= config.DATA_WARMUP_PERIOD}")
        
        # Run a quick backtest to verify no trades during warmup
        results = engine._run_unified_backtest(start_idx, end_idx)
        
        print(f"📈 Backtest Results:")
        print(f"   - Total trades: {results['total_trades']}")
        print(f"   - Start index: {start_idx:,}")
        print(f"   - Warmup period: {config.DATA_WARMUP_PERIOD:,}")
        
        if start_idx >= config.DATA_WARMUP_PERIOD:
            print(f"✅ Warmup test PASSED: No trades during warmup period")
            return True
        else:
            print(f"❌ Warmup test FAILED: Trading started before warmup period ended")
            return False
            
    except Exception as e:
        print(f"❌ Warmup test failed: {e}")
        return False

def test_ma_waterfall_filter():
    """Test the MA waterfall filter"""
    print("\n🧪 Testing MA Waterfall Filter")
    print("=" * 50)
    
    config = Config()
    config.USE_OPTIMIZED_RULES_ONLY = True
    config.ENABLE_DATA_WARMUP = False  # Disable warmup for this test
    config.ENABLE_GLOBAL_MARKET_FILTERS = True
    config.GLOBAL_MA_WATERFALL_FILTER = True
    config.GLOBAL_MA_WATERFALL_MAX_TRADES = 1
    config.MAX_CONCURRENT_TRADES = 2  # Allow multiple trades to test the limit
    config.CURRENT_DATASET_SIZE = 2000
    
    try:
        engine = BacktestingEngine(config)
        
        print(f"✅ Engine initialized with waterfall filter")
        print(f"   - Waterfall filter enabled: {config.GLOBAL_MA_WATERFALL_FILTER}")
        print(f"   - Max trades during waterfall: {config.GLOBAL_MA_WATERFALL_MAX_TRADES}")
        print(f"   - Max concurrent trades: {config.MAX_CONCURRENT_TRADES}")
        
        # Check if daily MA columns exist
        has_daily_mas = all(col in engine.df.columns for col in ['DAILY_MA_7', 'DAILY_MA_25', 'DAILY_MA_50'])
        print(f"   - Daily MAs available: {has_daily_mas}")
        
        if has_daily_mas:
            # Check for waterfall patterns in the data
            ma7 = engine.df['DAILY_MA_7'].dropna()
            ma25 = engine.df['DAILY_MA_25'].dropna()
            ma50 = engine.df['DAILY_MA_50'].dropna()
            
            if len(ma7) > 0 and len(ma25) > 0 and len(ma50) > 0:
                print(f"   - Daily MA7 values: {len(ma7):,}")
                print(f"   - Daily MA25 values: {len(ma25):,}")
                print(f"   - Daily MA50 values: {len(ma50):,}")
                print(f"✅ MA waterfall filter test setup complete")
                return True
            else:
                print(f"⚠️ Not enough data for daily MAs")
                return False
        else:
            print(f"❌ Daily MAs not calculated")
            return False
            
    except Exception as e:
        print(f"❌ MA waterfall test failed: {e}")
        return False

def test_ema200_below_filter():
    """Test the EMA200 below filter"""
    print("\n🧪 Testing EMA200 Below Filter")
    print("=" * 50)
    
    config = Config()
    config.USE_OPTIMIZED_RULES_ONLY = True
    config.ENABLE_DATA_WARMUP = False
    config.ENABLE_GLOBAL_MARKET_FILTERS = True
    config.GLOBAL_EMA200_FILTER = True
    config.GLOBAL_EMA200_BELOW_MAX_TRADES = 1
    config.MAX_CONCURRENT_TRADES = 2
    config.CURRENT_DATASET_SIZE = 2000
    
    try:
        engine = BacktestingEngine(config)
        
        print(f"✅ Engine initialized with EMA200 filter")
        print(f"   - EMA200 filter enabled: {config.GLOBAL_EMA200_FILTER}")
        print(f"   - Max trades below EMA200: {config.GLOBAL_EMA200_BELOW_MAX_TRADES}")
        
        # Check if daily EMA200 exists
        has_daily_ema = 'DAILY_EMA_200' in engine.df.columns
        print(f"   - Daily EMA200 available: {has_daily_ema}")
        
        if has_daily_ema:
            ema200_values = engine.df['DAILY_EMA_200'].dropna()
            print(f"   - Daily EMA200 values: {len(ema200_values):,}")
            print(f"✅ EMA200 below filter test setup complete")
            return True
        else:
            print(f"❌ Daily EMA200 not calculated")
            return False
            
    except Exception as e:
        print(f"❌ EMA200 below test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing New Global Market Filters and Warmup")
    print("=" * 60)
    
    results = []
    
    # Test warmup functionality
    results.append(test_warmup_functionality())
    
    # Test MA waterfall filter
    results.append(test_ma_waterfall_filter())
    
    # Test EMA200 below filter
    results.append(test_ema200_below_filter())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    
    if passed == total:
        print(f"🎉 All tests PASSED! New features are working correctly.")
    else:
        print(f"⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
