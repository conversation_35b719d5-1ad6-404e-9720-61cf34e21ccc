
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 13:54:48 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">120.54%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">3,917</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.9%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">3.43</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>53.83%</strong></td>
                    <td>63.4%</td>
                    <td>1190</td>
                    <td>1.08</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.8812</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h0m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>39.83%</strong></td>
                    <td>63.2%</td>
                    <td>926</td>
                    <td>1.07</td>
                    <td class="negative">40.62%</td>
                    <td class="positive"><strong>0.8074</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>1h58m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>22.76%</strong></td>
                    <td>68.6%</td>
                    <td>156</td>
                    <td>1.27</td>
                    <td class="negative">12.13%</td>
                    <td class="positive"><strong>0.6607</strong></td>
                    <td class="negative">+0.90% / -1.57%</td>
                    <td>1h27m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>22.51%</strong></td>
                    <td>69.2%</td>
                    <td>156</td>
                    <td>1.28</td>
                    <td class="negative">9.73%</td>
                    <td class="positive"><strong>0.6771</strong></td>
                    <td class="negative">+0.88% / -1.46%</td>
                    <td>1h37m<br><small>(2.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>10.35%</strong></td>
                    <td>80.0%</td>
                    <td>15</td>
                    <td>3.45</td>
                    <td class="neutral">2.30%</td>
                    <td class="positive"><strong>0.6682</strong></td>
                    <td class="negative">+0.94% / -1.27%</td>
                    <td>2h57m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>7.91%</strong></td>
                    <td>65.6%</td>
                    <td>61</td>
                    <td>1.23</td>
                    <td class="negative">6.72%</td>
                    <td class="positive"><strong>0.5468</strong></td>
                    <td class="negative">+0.89% / -1.36%</td>
                    <td>2h39m<br><small>(7.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>6.12%</strong></td>
                    <td>65.2%</td>
                    <td>69</td>
                    <td>1.15</td>
                    <td class="negative">6.52%</td>
                    <td class="positive"><strong>0.5295</strong></td>
                    <td class="negative">+0.90% / -1.47%</td>
                    <td>2h33m<br><small>(1.0m - 13h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>3.43%</strong></td>
                    <td>100.0%</td>
                    <td>3</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.88% / 0.00%</td>
                    <td>45.7m<br><small>(13.0m - 1h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>2.30%</strong></td>
                    <td>63.0%</td>
                    <td>27</td>
                    <td>1.15</td>
                    <td class="negative">6.06%</td>
                    <td class="positive"><strong>0.4481</strong></td>
                    <td class="negative">+1.00% / -1.32%</td>
                    <td>2h4m<br><small>(2.0m - 11h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>0.59%</strong></td>
                    <td>62.2%</td>
                    <td>37</td>
                    <td>1.03</td>
                    <td class="negative">5.39%</td>
                    <td class="positive"><strong>0.4328</strong></td>
                    <td class="negative">+0.88% / -1.40%</td>
                    <td>2h8m<br><small>(4.0m - 15h22m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>0.55%</strong></td>
                    <td>55.6%</td>
                    <td>9</td>
                    <td>1.08</td>
                    <td class="neutral">2.31%</td>
                    <td class="positive"><strong>0.2970</strong></td>
                    <td class="negative">+1.11% / -1.59%</td>
                    <td>3.0m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-0.22%</strong></td>
                    <td>66.7%</td>
                    <td>9</td>
                    <td>0.96</td>
                    <td class="neutral">2.59%</td>
                    <td class="positive"><strong>0.2833</strong></td>
                    <td class="negative">+0.81% / -1.38%</td>
                    <td>1h42m<br><small>(6.0m - 4h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-0.82%</strong></td>
                    <td>60.0%</td>
                    <td>15</td>
                    <td>0.92</td>
                    <td class="neutral">4.13%</td>
                    <td class="positive"><strong>0.3120</strong></td>
                    <td class="negative">+0.90% / -1.43%</td>
                    <td>2h53m<br><small>(16.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-4.25%</strong></td>
                    <td>46.2%</td>
                    <td>13</td>
                    <td>0.63</td>
                    <td class="negative">5.31%</td>
                    <td class="positive"><strong>0.1970</strong></td>
                    <td class="negative">+0.93% / -1.34%</td>
                    <td>1h27m<br><small>(13.0m - 4h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-6.11%</strong></td>
                    <td>59.3%</td>
                    <td>91</td>
                    <td>0.90</td>
                    <td class="negative">14.50%</td>
                    <td class="positive"><strong>0.4492</strong></td>
                    <td class="negative">+0.89% / -1.50%</td>
                    <td>1h23m<br><small>(1.0m - 8h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-38.22%</strong></td>
                    <td>60.5%</td>
                    <td>1140</td>
                    <td>0.95</td>
                    <td class="negative">42.83%</td>
                    <td class="positive"><strong>0.6017</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h25m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">3.43%</td>
                    <td>3</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.88% / 0.00%</td>
                    <td>45.7m<br><small>(13.0m - 1h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>80.0%</strong></td>
                    <td class="positive">10.35%</td>
                    <td>15</td>
                    <td>3.45</td>
                    <td class="neutral">2.30%</td>
                    <td class="positive"><strong>0.6682</strong></td>
                    <td class="negative">+0.94% / -1.27%</td>
                    <td>2h57m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>69.2%</strong></td>
                    <td class="positive">22.51%</td>
                    <td>156</td>
                    <td>1.28</td>
                    <td class="negative">9.73%</td>
                    <td class="positive"><strong>0.6771</strong></td>
                    <td class="negative">+0.88% / -1.46%</td>
                    <td>1h37m<br><small>(2.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>68.6%</strong></td>
                    <td class="positive">22.76%</td>
                    <td>156</td>
                    <td>1.27</td>
                    <td class="negative">12.13%</td>
                    <td class="positive"><strong>0.6607</strong></td>
                    <td class="negative">+0.90% / -1.57%</td>
                    <td>1h27m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="negative">-0.22%</td>
                    <td>9</td>
                    <td>0.96</td>
                    <td class="neutral">2.59%</td>
                    <td class="positive"><strong>0.2833</strong></td>
                    <td class="negative">+0.81% / -1.38%</td>
                    <td>1h42m<br><small>(6.0m - 4h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>65.6%</strong></td>
                    <td class="positive">7.91%</td>
                    <td>61</td>
                    <td>1.23</td>
                    <td class="negative">6.72%</td>
                    <td class="positive"><strong>0.5468</strong></td>
                    <td class="negative">+0.89% / -1.36%</td>
                    <td>2h39m<br><small>(7.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>65.2%</strong></td>
                    <td class="positive">6.12%</td>
                    <td>69</td>
                    <td>1.15</td>
                    <td class="negative">6.52%</td>
                    <td class="positive"><strong>0.5295</strong></td>
                    <td class="negative">+0.90% / -1.47%</td>
                    <td>2h33m<br><small>(1.0m - 13h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>63.4%</strong></td>
                    <td class="positive">53.83%</td>
                    <td>1190</td>
                    <td>1.08</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.8812</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h0m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>63.2%</strong></td>
                    <td class="positive">39.83%</td>
                    <td>926</td>
                    <td>1.07</td>
                    <td class="negative">40.62%</td>
                    <td class="positive"><strong>0.8074</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>1h58m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>63.0%</strong></td>
                    <td class="positive">2.30%</td>
                    <td>27</td>
                    <td>1.15</td>
                    <td class="negative">6.06%</td>
                    <td class="positive"><strong>0.4481</strong></td>
                    <td class="negative">+1.00% / -1.32%</td>
                    <td>2h4m<br><small>(2.0m - 11h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>62.2%</strong></td>
                    <td class="positive">0.59%</td>
                    <td>37</td>
                    <td>1.03</td>
                    <td class="negative">5.39%</td>
                    <td class="positive"><strong>0.4328</strong></td>
                    <td class="negative">+0.88% / -1.40%</td>
                    <td>2h8m<br><small>(4.0m - 15h22m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>60.5%</strong></td>
                    <td class="negative">-38.22%</td>
                    <td>1140</td>
                    <td>0.95</td>
                    <td class="negative">42.83%</td>
                    <td class="positive"><strong>0.6017</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h25m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="negative">-0.82%</td>
                    <td>15</td>
                    <td>0.92</td>
                    <td class="neutral">4.13%</td>
                    <td class="positive"><strong>0.3120</strong></td>
                    <td class="negative">+0.90% / -1.43%</td>
                    <td>2h53m<br><small>(16.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>59.3%</strong></td>
                    <td class="negative">-6.11%</td>
                    <td>91</td>
                    <td>0.90</td>
                    <td class="negative">14.50%</td>
                    <td class="positive"><strong>0.4492</strong></td>
                    <td class="negative">+0.89% / -1.50%</td>
                    <td>1h23m<br><small>(1.0m - 8h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>55.6%</strong></td>
                    <td class="positive">0.55%</td>
                    <td>9</td>
                    <td>1.08</td>
                    <td class="neutral">2.31%</td>
                    <td class="positive"><strong>0.2970</strong></td>
                    <td class="negative">+1.11% / -1.59%</td>
                    <td>3.0m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="neutral"><strong>46.2%</strong></td>
                    <td class="negative">-4.25%</td>
                    <td>13</td>
                    <td>0.63</td>
                    <td class="negative">5.31%</td>
                    <td class="positive"><strong>0.1970</strong></td>
                    <td class="negative">+0.93% / -1.34%</td>
                    <td>1h27m<br><small>(13.0m - 4h11m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'Prof Rule 7: Mean Reversion Vo...', 'Volume Rule 5: Smart Money Vol...', 'AI Rule 8: Momentum Divergence...', 'Professional Rule 10: CCI Reve...', 'Ext Rule 6: Fibonacci Support ...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [63.4453781512605, 63.17494600431965, 69.23076923076923, 68.58974358974359, 65.21739130434783, 59.34065934065934, 65.57377049180327, 60.526315789473685, 80.0, 100.0, 62.16216216216216, 62.96296296296296, 66.66666666666666, 60.0, 55.55555555555556],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'Prof Rule 7: Mean Reversion Vo...', 'Volume Rule 5: Smart Money Vol...', 'AI Rule 8: Momentum Divergence...', 'Professional Rule 10: CCI Reve...', 'Ext Rule 6: Fibonacci Support ...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [np.float64(53.831686402051226), np.float64(39.82902204159502), np.float64(22.51441900210225), np.float64(22.75850447781953), np.float64(6.118398438251242), np.float64(-6.114424755518106), np.float64(7.907000517575288), np.float64(-38.21552689424444), np.float64(10.347921891884718), np.float64(3.4264639736133105), np.float64(0.5853240050838504), np.float64(2.3015933631788212), np.float64(-0.21650208215987365), np.float64(-0.8248682641492051), np.float64(0.5455022591848683)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
