
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 4 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 15:01:24</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">4</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">25.0%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">13.9%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">30.8%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">67.5%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">1,477</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["30.8%","14.8%","5.6%","4.3%"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Volume Rule 5: Smart Money Volume","Momentum Rule 2: Momentum Divergence Recovery"],"y":[30.82089180485316,14.774983158635113,5.575064542577515,4.328101622805799],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Volume Rule 5: Smart Money Volume","Momentum Rule 2: Momentum Divergence Recovery"],"y":[105,781,29,24],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Volume Rule 5: Smart Money Volume","Momentum Rule 2: Momentum Divergence Recovery"],"y":[37,475,13,13],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[30.82089180485316,14.774983158635113,5.575064542577515,4.328101622805799],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Prof Rule 7","AI Rule 10","Volume Rule 5","Momentum Rule 2"],"textposition":"top center","x":[6.7698268520898175,33.86739554427189,2.9874358583105916,4.872150359806947],"y":[30.82089180485316,14.774983158635113,5.575064542577515,4.328101622805799],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22"]},"text":["30.8%","14.8%","5.0%"],"textposition":"auto","x":["PROFESSIONAL","AI_GENERATED","UNKNOWN"],"y":[30.82089180485316,14.774983158635113,4.951583082691657],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["142","1256","42","37"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Volume Rule 5: Smart Money Volume","Momentum Rule 2: Momentum Divergence Recovery"],"y":[142,1256,42,37],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142],"y":[0,0.2170485338369941,0.4340970676739882,0.6511456015109822,0.8681941353479764,1.0852426691849706,1.3022912030219644,1.5193397368589585,1.7363882706959528,1.9534368045329469,2.170485338369941,2.387533872206935,2.604582406043929,2.821630939880923,3.038679473717917,3.2557280075549113,3.4727765413919056,3.6898250752288995,3.9068736090658938,4.123922142902887,4.340970676739882,4.558019210576876,4.77506774441387,4.992116278250864,5.209164812087858,5.426213345924852,5.643261879761846,5.860310413598841,6.077358947435834,6.294407481272829,6.511456015109823,6.728504548946817,6.945553082783811,7.162601616620805,7.379650150457799,7.596698684294793,7.8137472181317875,8.030795751968782,8.247844285805774,8.46489281964277,8.681941353479765,8.898989887316757,9.116038421153752,9.333086954990746,9.55013548882774,9.767184022664734,9.984232556501729,10.201281090338723,10.418329624175716,10.63537815801271,10.852426691849704,11.0694752256867,11.286523759523693,11.503572293360687,11.720620827197681,11.937669361034676,12.154717894871668,12.371766428708664,12.588814962545658,12.805863496382651,13.022912030219645,13.23996056405664,13.457009097893634,13.674057631730628,13.891106165567622,14.108154699404617,14.32520323324161,14.542251767078604,14.759300300915598,14.976348834752594,15.193397368589586,15.41044590242658,15.627494436263575,15.844542970100568,16.061591503937564,16.278640037774554,16.49568857161155,16.712737105448547,16.92978563928554,17.146834173122535,17.36388270695953,17.580931240796524,17.797979774633514,18.01502830847051,18.232076842307503,18.449125376144497,18.66617390998149,18.883222443818486,19.10027097765548,19.317319511492475,19.53436804532947,19.751416579166463,19.968465113003457,20.18551364684045,20.402562180677446,20.61961071451444,20.83665924835143,21.053707782188425,21.27075631602542,21.487804849862414,21.704853383699408,21.921901917536406,22.1389504513734,22.35599898521039,22.573047519047385,22.79009605288438,23.007144586721374,23.22419312055837,23.441241654395363,23.658290188232357,23.87533872206935,24.092387255906342,24.309435789743336,24.526484323580334,24.74353285741733,24.960581391254323,25.177629925091317,25.39467845892831,25.611726992765302,25.828775526602296,26.04582406043929,26.262872594276285,26.47992112811328,26.696969661950273,26.914018195787268,27.131066729624262,27.348115263461256,27.56516379729825,27.782212331135245,27.99926086497224,28.216309398809233,28.433357932646228,28.65040646648322,28.867455000320213,29.084503534157207,29.3015520679942,29.518600601831196,29.735649135668194,29.952697669505188,30.16974620334218,30.386794737179173,30.603843271016167,30.82089180485316],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256],"y":[0,0.011763521623117128,0.023527043246234256,0.035290564869351386,0.04705408649246851,0.05881760811558564,0.07058112973870277,0.08234465136181988,0.09410817298493702,0.10587169460805415,0.11763521623117128,0.1293987378542884,0.14116225947740554,0.15292578110052266,0.16468930272363977,0.17645282434675694,0.18821634596987405,0.1999798675929912,0.2117433892161083,0.22350691083922544,0.23527043246234255,0.2470339540854597,0.2587974757085768,0.2705609973316939,0.2823245189548111,0.2940880405779282,0.3058515622010453,0.3176150838241624,0.32937860544727954,0.3411421270703967,0.3529056486935139,0.364669170316631,0.3764326919397481,0.38819621356286527,0.3999597351859824,0.4117232568090995,0.4234867784322166,0.4352503000553337,0.4470138216784509,0.458777343301568,0.4705408649246851,0.4823043865478022,0.4940679081709194,0.5058314297940365,0.5175949514171536,0.5293584730402707,0.5411219946633878,0.552885516286505,0.5646490379096222,0.5764125595327393,0.5881760811558564,0.5999396027789735,0.6117031244020906,0.6234666460252077,0.6352301676483249,0.646993689271442,0.6587572108945591,0.6705207325176763,0.6822842541407934,0.6940477757639106,0.7058112973870277,0.7175748190101449,0.729338340633262,0.7411018622563791,0.7528653838794962,0.7646289055026133,0.7763924271257305,0.7881559487488476,0.7999194703719648,0.8116829919950819,0.823446513618199,0.8352100352413161,0.8469735568644332,0.8587370784875503,0.8705006001106674,0.8822641217337847,0.8940276433569018,0.9057911649800189,0.917554686603136,0.9293182082262531,0.9410817298493702,0.9528452514724873,0.9646087730956044,0.9763722947187217,0.9881358163418388,0.9998993379649559,1.011662859588073,1.0234263812111901,1.0351899028343072,1.0469534244574243,1.0587169460805415,1.0704804677036586,1.0822439893267757,1.0940075109498928,1.10577103257301,1.117534554196127,1.1292980758192444,1.1410615974423615,1.1528251190654786,1.1645886406885957,1.1763521623117128,1.18811568393483,1.199879205557947,1.2116427271810641,1.2234062488041813,1.2351697704272984,1.2469332920504155,1.2586968136735326,1.2704603352966497,1.2822238569197668,1.293987378542884,1.305750900166001,1.3175144217891182,1.3292779434122355,1.3410414650353526,1.3528049866584697,1.3645685082815868,1.376332029904704,1.3880955515278213,1.3998590731509384,1.4116225947740555,1.4233861163971726,1.4351496380202897,1.4469131596434068,1.458676681266524,1.470440202889641,1.4822037245127582,1.4939672461358753,1.5057307677589924,1.5174942893821095,1.5292578110052266,1.5410213326283437,1.552784854251461,1.5645483758745782,1.5763118974976953,1.5880754191208124,1.5998389407439295,1.6116024623670466,1.6233659839901637,1.6351295056132809,1.646893027236398,1.658656548859515,1.6704200704826322,1.6821835921057493,1.6939471137288664,1.7057106353519835,1.7174741569751006,1.7292376785982178,1.7410012002213349,1.7527647218444522,1.7645282434675693,1.7762917650906864,1.7880552867138035,1.7998188083369207,1.8115823299600378,1.8233458515831549,1.835109373206272,1.846872894829389,1.8586364164525062,1.8703999380756233,1.8821634596987404,1.8939269813218576,1.9056905029449747,1.9174540245680918,1.9292175461912089,1.940981067814326,1.9527445894374433,1.9645081110605604,1.9762716326836776,1.9880351543067947,1.9997986759299118,2.0115621975530287,2.023325719176146,2.035089240799263,2.0468527624223802,2.0586162840454976,2.0703798056686145,2.082143327291732,2.0939068489148487,2.105670370537966,2.117433892161083,2.1291974137842002,2.140960935407317,2.1527244570304345,2.1644879786535514,2.1762515002766687,2.1880150218997856,2.199778543522903,2.21154206514602,2.223305586769137,2.235069108392254,2.2468326300153714,2.2585961516384887,2.2703596732616056,2.282123194884723,2.29388671650784,2.305650238130957,2.317413759754074,2.3291772813771914,2.3409408030003083,2.3527043246234256,2.3644678462465425,2.37623136786966,2.3879948894927767,2.399758411115894,2.411521932739011,2.4232854543621283,2.435048975985245,2.4468124976083625,2.45857601923148,2.4703395408545967,2.482103062477714,2.493866584100831,2.5056301057239483,2.517393627347065,2.5291571489701825,2.5409206705932994,2.5526841922164167,2.5644477138395336,2.576211235462651,2.587974757085768,2.599738278708885,2.611501800332002,2.6232653219551194,2.6350288435782363,2.6467923652013536,2.658555886824471,2.670319408447588,2.682082930070705,2.693846451693822,2.7056099733169394,2.7173734949400563,2.7291370165631736,2.7409005381862905,2.752664059809408,2.7644275814325248,2.7761911030556425,2.7879546246787594,2.7997181463018768,2.8114816679249937,2.823245189548111,2.835008711171228,2.846772232794345,2.858535754417462,2.8702992760405794,2.8820627976636963,2.8938263192868137,2.905589840909931,2.917353362533048,2.9291168841561652,2.940880405779282,2.9526439274023994,2.9644074490255163,2.9761709706486337,2.9879344922717506,2.999698013894868,3.011461535517985,3.023225057141102,3.034988578764219,3.0467521003873363,3.0585156220104532,3.0702791436335706,3.0820426652566875,3.093806186879805,3.105569708502922,3.117333230126039,3.1290967517491564,3.1408602733722732,3.1526237949953906,3.1643873166185075,3.176150838241625,3.1879143598647417,3.199677881487859,3.211441403110976,3.2232049247340933,3.23496844635721,3.2467319679803275,3.2584954896034444,3.2702590112265617,3.2820225328496786,3.293786054472796,3.3055495760959133,3.31731309771903,3.3290766193421475,3.3408401409652644,3.3526036625883817,3.3643671842114986,3.376130705834616,3.387894227457733,3.39965774908085,3.411421270703967,3.4231847923270844,3.4349483139502013,3.4467118355733186,3.4584753571964355,3.470238878819553,3.4820024004426697,3.493765922065787,3.5055294436889044,3.5172929653120213,3.5290564869351386,3.5408200085582555,3.552583530181373,3.5643470518044897,3.576110573427607,3.587874095050724,3.5996376166738413,3.611401138296958,3.6231646599200755,3.6349281815431924,3.6466917031663098,3.6584552247894266,3.670218746412544,3.681982268035661,3.693745789658778,3.7055093112818955,3.7172728329050124,3.72903635452813,3.7407998761512467,3.7525633977743644,3.764326919397481,3.7760904410205987,3.787853962643715,3.799617484266833,3.8113810058899493,3.823144527513067,3.8349080491361835,3.8466715707593013,3.8584350923824178,3.8701986140055356,3.881962135628652,3.89372565725177,3.9054891788748867,3.917252700498004,3.929016222121121,3.9407797437442382,3.952543265367355,3.9643067869904725,3.9760703086135893,3.9878338302367067,3.9995973518598236,4.011360873482941,4.023124395106057,4.034887916729176,4.046651438352292,4.058414959975409,4.070178481598526,4.081942003221644,4.0937055248447605,4.105469046467878,4.117232568090995,4.1289960897141125,4.140759611337229,4.152523132960346,4.164286654583464,4.176050176206581,4.187813697829697,4.199577219452815,4.211340741075932,4.223104262699049,4.234867784322166,4.246631305945283,4.2583948275684005,4.270158349191518,4.281921870814634,4.293685392437752,4.305448914060869,4.317212435683986,4.328975957307103,4.340739478930221,4.352503000553337,4.364266522176455,4.376030043799571,4.387793565422689,4.399557087045806,4.411320608668923,4.42308413029204,4.434847651915158,4.446611173538274,4.458374695161392,4.470138216784508,4.481901738407626,4.493665260030743,4.50542878165386,4.517192303276977,4.528955824900095,4.540719346523211,4.5524828681463285,4.564246389769446,4.576009911392563,4.58777343301568,4.599536954638797,4.611300476261914,4.623063997885032,4.634827519508148,4.646591041131265,4.658354562754383,4.6701180843775,4.6818816060006165,4.693645127623734,4.705408649246851,4.7171721708699685,4.728935692493085,4.740699214116203,4.75246273573932,4.764226257362437,4.775989778985553,4.787753300608672,4.799516822231788,4.811280343854905,4.823043865478022,4.83480738710114,4.8465709087242566,4.858334430347374,4.87009795197049,4.881861473593609,4.893624995216725,4.905388516839842,4.91715203846296,4.928915560086077,4.9406790817091935,4.952442603332311,4.964206124955428,4.9759696465785455,4.987733168201662,4.999496689824779,5.011260211447897,5.023023733071014,5.03478725469413,5.046550776317248,5.058314297940365,5.070077819563482,5.081841341186599,5.093604862809716,5.1053683844328335,5.117131906055951,5.128895427679067,5.1406589493021855,5.152422470925302,5.164185992548419,5.175949514171536,5.187713035794654,5.19947655741777,5.211240079040888,5.223003600664004,5.234767122287122,5.246530643910239,5.258294165533356,5.270057687156473,5.281821208779591,5.293584730402707,5.305348252025825,5.317111773648942,5.328875295272059,5.340638816895176,5.352402338518293,5.36416586014141,5.375929381764528,5.387692903387644,5.3994564250107615,5.411219946633879,5.422983468256996,5.434746989880113,5.44651051150323,5.458274033126347,5.470037554749465,5.481801076372581,5.493564597995698,5.505328119618816,5.517091641241933,5.5288551628650495,5.540618684488168,5.552382206111285,5.5641457277344015,5.575909249357519,5.587672770980636,5.5994362926037535,5.61119981422687,5.622963335849987,5.634726857473105,5.646490379096222,5.658253900719338,5.670017422342456,5.681780943965573,5.69354446558869,5.705307987211807,5.717071508834924,5.7288350304580415,5.740598552081159,5.752362073704275,5.764125595327393,5.77588911695051,5.787652638573627,5.799416160196744,5.811179681819862,5.822943203442978,5.834706725066096,5.846470246689212,5.8582337683123304,5.869997289935447,5.881760811558564,5.893524333181681,5.905287854804799,5.917051376427915,5.928814898051033,5.940578419674149,5.952341941297267,5.964105462920384,5.975868984543501,5.9876325061666185,5.999396027789736,6.011159549412852,6.02292307103597,6.034686592659087,6.046450114282204,6.058213635905321,6.069977157528438,6.081740679151555,6.093504200774673,6.105267722397789,6.1170312440209065,6.128794765644024,6.140558287267141,6.152321808890258,6.164085330513375,6.175848852136492,6.18761237375961,6.199375895382726,6.211139417005844,6.222902938628961,6.234666460252078,6.2464299818751945,6.258193503498313,6.269957025121429,6.2817205467445465,6.293484068367663,6.305247589990781,6.317011111613898,6.328774633237015,6.340538154860131,6.35230167648325,6.364065198106366,6.375828719729483,6.387592241352601,6.399355762975718,6.4111192845988345,6.422882806221952,6.434646327845069,6.4464098494681865,6.458173371091303,6.46993689271442,6.481700414337538,6.493463935960655,6.505227457583771,6.516990979206889,6.528754500830006,6.540518022453123,6.55228154407624,6.564045065699357,6.5758085873224745,6.587572108945592,6.599335630568708,6.6110991521918265,6.622862673814943,6.63462619543806,6.646389717061177,6.658153238684295,6.669916760307411,6.681680281930529,6.693443803553645,6.705207325176763,6.71697084679988,6.728734368422997,6.740497890046114,6.752261411669232,6.764024933292348,6.775788454915466,6.787551976538583,6.7993154981617,6.811079019784817,6.822842541407934,6.8346060630310514,6.846369584654169,6.858133106277285,6.869896627900403,6.88166014952352,6.893423671146637,6.905187192769754,6.916950714392871,6.928714236015988,6.940477757639106,6.952241279262222,6.9640048008853395,6.975768322508457,6.987531844131574,6.999295365754691,7.011058887377809,7.022822409000925,7.034585930624043,7.046349452247159,7.058112973870277,7.069876495493394,7.081640017116511,7.0934035387396275,7.105167060362746,7.116930581985862,7.1286941036089795,7.140457625232096,7.152221146855214,7.163984668478331,7.175748190101448,7.187511711724565,7.199275233347683,7.211038754970799,7.222802276593916,7.234565798217034,7.246329319840151,7.2580928414632675,7.269856363086385,7.281619884709502,7.2933834063326195,7.305146927955736,7.316910449578853,7.328673971201971,7.340437492825088,7.352201014448204,7.363964536071322,7.375728057694439,7.387491579317556,7.399255100940674,7.411018622563791,7.4227821441869075,7.434545665810025,7.446309187433142,7.45807270905626,7.469836230679376,7.481599752302493,7.493363273925611,7.505126795548729,7.516890317171844,7.528653838794962,7.540417360418079,7.552180882041197,7.563944403664313,7.57570792528743,7.5874714469105475,7.599234968533666,7.610998490156781,7.622762011779899,7.634525533403017,7.646289055026134,7.65805257664925,7.669816098272367,7.681579619895485,7.693343141518603,7.705106663141718,7.7168701847648355,7.728633706387954,7.740397228011071,7.752160749634187,7.763924271257304,7.775687792880422,7.78745131450354,7.799214836126655,7.810978357749773,7.822741879372891,7.834505400996008,7.846268922619124,7.858032444242242,7.869795965865359,7.8815594874884765,7.893323009111592,7.90508653073471,7.916850052357828,7.928613573980945,7.9403770956040605,7.952140617227179,7.963904138850296,7.975667660473413,7.98743118209653,7.999194703719647,8.010958225342764,8.022721746965882,8.034485268588998,8.046248790212115,8.058012311835233,8.069775833458351,8.081539355081466,8.093302876704584,8.105066398327702,8.116829919950819,8.128593441573935,8.140356963197052,8.15212048482017,8.163884006443288,8.175647528066403,8.187411049689521,8.19917457131264,8.210938092935756,8.222701614558872,8.23446513618199,8.246228657805107,8.257992179428225,8.26975570105134,8.281519222674458,8.293282744297576,8.305046265920693,8.316809787543809,8.328573309166927,8.340336830790044,8.352100352413162,8.363863874036277,8.375627395659395,8.387390917282513,8.39915443890563,8.410917960528746,8.422681482151864,8.43444500377498,8.446208525398099,8.457972047021215,8.469735568644332,8.48149909026745,8.493262611890566,8.505026133513683,8.516789655136801,8.528553176759917,8.540316698383036,8.552080220006152,8.563843741629269,8.575607263252387,8.587370784875503,8.59913430649862,8.610897828121738,8.622661349744854,8.634424871367973,8.646188392991089,8.657951914614205,8.669715436237324,8.681478957860442,8.693242479483557,8.705006001106675,8.716769522729791,8.72853304435291,8.740296565976026,8.752060087599142,8.76382360922226,8.775587130845379,8.787350652468493,8.799114174091612,8.810877695714728,8.822641217337846,8.834404738960963,8.84616826058408,8.857931782207197,8.869695303830316,8.88145882545343,8.893222347076549,8.904985868699667,8.916749390322783,8.9285129119459,8.940276433569016,8.952039955192134,8.963803476815253,8.975566998438367,8.987330520061485,8.999094041684604,9.01085756330772,9.022621084930837,9.034384606553955,9.046148128177071,9.05791164980019,9.069675171423304,9.081438693046422,9.09320221466954,9.104965736292657,9.116729257915773,9.128492779538892,9.140256301162008,9.152019822785126,9.163783344408241,9.17554686603136,9.187310387654477,9.199073909277594,9.21083743090071,9.222600952523829,9.234364474146945,9.246127995770063,9.25789151739318,9.269655039016296,9.281418560639414,9.29318208226253,9.304945603885649,9.316709125508766,9.328472647131882,9.340236168755,9.351999690378118,9.363763212001233,9.375526733624351,9.387290255247468,9.399053776870586,9.410817298493702,9.422580820116819,9.434344341739937,9.446107863363055,9.45787138498617,9.469634906609288,9.481398428232406,9.493161949855523,9.50492547147864,9.516688993101756,9.528452514724874,9.540216036347992,9.551979557971107,9.563743079594225,9.575506601217343,9.58727012284046,9.599033644463576,9.610797166086693,9.62256068770981,9.634324209332929,9.646087730956044,9.657851252579162,9.66961477420228,9.681378295825397,9.693141817448513,9.704905339071631,9.716668860694748,9.728432382317866,9.74019590394098,9.751959425564099,9.763722947187217,9.775486468810334,9.78724999043345,9.799013512056568,9.810777033679685,9.822540555302803,9.83430407692592,9.846067598549036,9.857831120172154,9.86959464179527,9.881358163418387,9.893121685041505,9.904885206664622,9.91664872828774,9.928412249910856,9.940175771533973,9.951939293157091,9.963702814780207,9.975466336403324,9.987229858026442,9.998993379649558,10.010756901272677,10.022520422895793,10.03428394451891,10.046047466142028,10.057810987765144,10.06957450938826,10.081338031011379,10.093101552634495,10.104865074257614,10.11662859588073,10.128392117503846,10.140155639126965,10.151919160750083,10.163682682373198,10.175446203996316,10.187209725619432,10.19897324724255,10.210736768865667,10.222500290488783,10.234263812111902,10.24602733373502,10.257790855358135,10.269554376981253,10.281317898604371,10.293081420227487,10.304844941850604,10.31660846347372,10.328371985096839,10.340135506719957,10.351899028343071,10.36366254996619,10.375426071589308,10.387189593212424,10.39895311483554,10.410716636458657,10.422480158081775,10.434243679704894,10.446007201328008,10.457770722951127,10.469534244574245,10.481297766197361,10.493061287820478,10.504824809443596,10.516588331066712,10.52835185268983,10.540115374312945,10.551878895936063,10.563642417559182,10.575405939182298,10.587169460805415,10.598932982428533,10.61069650405165,10.622460025674767,10.634223547297884,10.645987068921,10.657750590544119,10.669514112167235,10.681277633790351,10.69304115541347,10.704804677036586,10.716568198659704,10.72833172028282,10.740095241905937,10.751858763529055,10.763622285152172,10.775385806775288,10.787149328398407,10.798912850021523,10.810676371644641,10.822439893267758,10.834203414890874,10.845966936513992,10.857730458137109,10.869493979760225,10.881257501383343,10.89302102300646,10.904784544629578,10.916548066252695,10.928311587875811,10.94007510949893,10.951838631122047,10.963602152745162,10.97536567436828,10.987129195991397,10.998892717614515,11.010656239237631,11.022419760860748,11.034183282483866,11.045946804106984,11.057710325730099,11.069473847353217,11.081237368976335,11.093000890599452,11.10476441222257,11.116527933845685,11.128291455468803,11.140054977091921,11.151818498715038,11.163582020338154,11.175345541961272,11.187109063584389,11.198872585207507,11.210636106830622,11.22239962845374,11.234163150076858,11.245926671699975,11.257690193323091,11.26945371494621,11.281217236569326,11.292980758192444,11.30474427981556,11.316507801438677,11.328271323061795,11.340034844684912,11.351798366308028,11.363561887931146,11.375325409554263,11.38708893117738,11.398852452800497,11.410615974423614,11.422379496046732,11.434143017669848,11.445906539292965,11.457670060916083,11.4694335825392,11.481197104162318,11.492960625785434,11.50472414740855,11.516487669031669,11.528251190654785,11.540014712277902,11.55177823390102,11.563541755524136,11.575305277147255,11.587068798770371,11.598832320393488,11.610595842016606,11.622359363639724,11.634122885262839,11.645886406885957,11.657649928509073,11.669413450132192,11.681176971755308,11.692940493378424,11.704704015001543,11.716467536624661,11.728231058247776,11.739994579870894,11.751758101494012,11.763521623117128,11.775285144740245,11.787048666363361,11.79881218798648,11.810575709609598,11.822339231232712,11.83410275285583,11.845866274478949,11.857629796102065,11.869393317725182,11.881156839348298,11.892920360971416,11.904683882594535,11.91644740421765,11.928210925840768,11.939974447463886,11.951737969087002,11.963501490710119,11.975265012333237,11.987028533956353,11.998792055579472,12.010555577202586,12.022319098825704,12.034082620448823,12.04584614207194,12.057609663695056,12.069373185318174,12.08113670694129,12.092900228564408,12.104663750187525,12.116427271810641,12.12819079343376,12.139954315056876,12.151717836679993,12.16348135830311,12.175244879926227,12.187008401549345,12.198771923172462,12.210535444795578,12.222298966418697,12.234062488041813,12.24582600966493,12.257589531288048,12.269353052911164,12.281116574534282,12.292880096157399,12.304643617780515,12.316407139403633,12.32817066102675,12.339934182649866,12.351697704272985,12.363461225896101,12.37522474751922,12.386988269142336,12.398751790765452,12.41051531238857,12.422278834011689,12.434042355634803,12.445805877257921,12.457569398881038,12.469332920504156,12.481096442127273,12.492859963750389,12.504623485373507,12.516387006996625,12.52815052861974,12.539914050242858,12.551677571865977,12.563441093489093,12.57520461511221,12.586968136735326,12.598731658358444,12.610495179981562,12.622258701604677,12.634022223227795,12.645785744850913,12.65754926647403,12.669312788097146,12.681076309720263,12.692839831343381,12.7046033529665,12.716366874589614,12.728130396212732,12.73989391783585,12.751657439458967,12.763420961082083,12.775184482705201,12.786948004328318,12.798711525951436,12.81047504757455,12.822238569197669,12.834002090820787,12.845765612443904,12.85752913406702,12.869292655690138,12.881056177313255,12.892819698936373,12.90458322055949,12.916346742182606,12.928110263805724,12.93987378542884,12.951637307051959,12.963400828675075,12.975164350298192,12.98692787192131,12.998691393544428,13.010454915167543,13.022218436790661,13.033981958413777,13.045745480036896,13.057509001660012,13.069272523283129,13.081036044906247,13.092799566529365,13.10456308815248,13.116326609775598,13.128090131398714,13.139853653021833,13.151617174644949,13.163380696268066,13.175144217891184,13.186907739514302,13.198671261137417,13.210434782760535,13.222198304383653,13.23396182600677,13.245725347629886,13.257488869253002,13.26925239087612,13.281015912499239,13.292779434122354,13.304542955745472,13.31630647736859,13.328069998991706,13.339833520614823,13.351597042237941,13.363360563861058,13.375124085484176,13.38688760710729,13.398651128730409,13.410414650353527,13.422178171976643,13.43394169359976,13.445705215222878,13.457468736845994,13.469232258469113,13.480995780092227,13.492759301715346,13.504522823338464,13.51628634496158,13.528049866584697,13.539813388207815,13.551576909830931,13.56334043145405,13.575103953077166,13.586867474700282,13.5986309963234,13.610394517946517,13.622158039569634,13.633921561192752,13.645685082815868,13.657448604438986,13.669212126062103,13.68097564768522,13.692739169308338,13.704502690931454,13.71626621255457,13.728029734177689,13.739793255800805,13.751556777423923,13.76332029904704,13.775083820670156,13.786847342293274,13.798610863916393,13.810374385539507,13.822137907162626,13.833901428785742,13.84566495040886,13.857428472031977,13.869191993655093,13.880955515278211,13.89271903690133,13.904482558524444,13.916246080147562,13.928009601770679,13.939773123393797,13.951536645016914,13.96330016664003,13.975063688263148,13.986827209886266,13.998590731509381,14.0103542531325,14.022117774755618,14.033881296378734,14.04564481800185,14.057408339624967,14.069171861248085,14.080935382871203,14.092698904494318,14.104462426117436,14.116225947740555,14.127989469363671,14.139752990986787,14.151516512609906,14.163280034233022,14.17504355585614,14.186807077479255,14.198570599102373,14.210334120725491,14.222097642348608,14.233861163971724,14.245624685594843,14.257388207217959,14.269151728841077,14.280915250464192,14.29267877208731,14.304442293710428,14.316205815333545,14.327969336956661,14.33973285857978,14.351496380202896,14.363259901826014,14.37502342344913,14.386786945072247,14.398550466695365,14.410313988318482,14.422077509941598,14.433841031564716,14.445604553187833,14.457368074810951,14.469131596434067,14.480895118057184,14.492658639680302,14.504422161303419,14.516185682926535,14.527949204549653,14.53971272617277,14.551476247795888,14.563239769419004,14.57500329104212,14.586766812665239,14.598530334288355,14.610293855911472,14.62205737753459,14.633820899157707,14.645584420780825,14.657347942403941,14.669111464027058,14.680874985650176,14.692638507273294,14.704402028896409,14.716165550519527,14.727929072142643,14.739692593765762,14.751456115388878,14.763219637011995,14.774983158635113],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Volume Rule 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],"y":[0,0.13273963196613128,0.26547926393226257,0.3982188958983939,0.5309585278645251,0.6636981598306565,0.7964377917967878,0.9291774237629191,1.0619170557290503,1.1946566876951816,1.327396319661313,1.4601359516274444,1.5928755835935755,1.725615215559707,1.8583548475258382,1.9910944794919696,2.1238341114581005,2.2565737434242323,2.3893133753903633,2.5220530073564946,2.654792639322626,2.7875322712887574,2.9202719032548887,3.05301153522102,3.185751167187151,3.3184907991532824,3.451230431119414,3.5839700630855456,3.7167096950516765,3.849449327017808,3.982188958983939,4.114928590950071,4.247668222916201,4.380407854882333,4.513147486848465,4.645887118814596,4.7786267507807265,4.911366382746858,5.044106014712989,5.176845646679121,5.309585278645252,5.442324910611383,5.575064542577515],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Momentum Rule 2","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37],"y":[0,0.11697571953529187,0.23395143907058374,0.35092715860587564,0.4679028781411675,0.5848785976764593,0.7018543172117513,0.8188300367470431,0.935805756282335,1.0527814758176268,1.1697571953529187,1.2867329148882107,1.4037086344235026,1.5206843539587944,1.6376600734940863,1.754635793029378,1.87161151256467,1.9885872320999618,2.1055629516352536,2.2225386711705455,2.3395143907058373,2.456490110241129,2.5734658297764215,2.690441549311713,2.807417268847005,2.9243929883822966,3.041368707917589,3.1583444274528802,3.2753201469881725,3.392295866523464,3.509271586058756,3.626247305594048,3.74322302512934,3.8601987446646318,3.9771744641999236,4.094150183735215,4.211125903270507,4.328101622805799],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">30.82%</td>
                <td>73.9%</td>
                <td>142</td>
                <td>1.63</td>
                <td>0.00</td>
                <td>6.77%</td>
                <td>64.5</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">14.77%</td>
                <td>62.2%</td>
                <td>1256</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>33.87%</td>
                <td>54.6</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">5.58%</td>
                <td>69.0%</td>
                <td>42</td>
                <td>1.31</td>
                <td>0.00</td>
                <td>2.99%</td>
                <td>35.5</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                <td>UNKNOWN</td>
                <td class="positive">4.33%</td>
                <td>64.9%</td>
                <td>37</td>
                <td>1.26</td>
                <td>0.00</td>
                <td>4.87%</td>
                <td>32.3</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 525,301<br>
            • Backtest Range: 300 to 525,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
