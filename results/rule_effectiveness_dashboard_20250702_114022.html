
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 11:40:22 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">-152.54%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">11,767</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">60.6%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.31</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>19.70%</strong></td>
                    <td>64.5%</td>
                    <td>1041</td>
                    <td>1.08</td>
                    <td class="negative">9.15%</td>
                    <td class="positive"><strong>0.8160</strong></td>
                    <td class="negative">+0.90% / -1.48%</td>
                    <td>1h40m<br><small>(1.0m - 49h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>16.69%</strong></td>
                    <td>70.2%</td>
                    <td>238</td>
                    <td>1.31</td>
                    <td class="neutral">4.69%</td>
                    <td class="positive"><strong>0.7085</strong></td>
                    <td class="negative">+0.89% / -1.57%</td>
                    <td>1h50m<br><small>(1.0m - 49h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>16.54%</strong></td>
                    <td>63.1%</td>
                    <td>850</td>
                    <td>1.08</td>
                    <td class="negative">12.68%</td>
                    <td class="positive"><strong>0.7820</strong></td>
                    <td class="negative">+0.91% / -1.47%</td>
                    <td>1h53m<br><small>(1.0m - 49h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 3: RSI Oversold</td>
                    <td class="positive"><strong>7.17%</strong></td>
                    <td>65.6%</td>
                    <td>250</td>
                    <td>1.12</td>
                    <td class="negative">7.13%</td>
                    <td class="positive"><strong>0.6590</strong></td>
                    <td class="negative">+0.87% / -1.42%</td>
                    <td>1h48m<br><small>(1.0m - 17h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>4.60%</strong></td>
                    <td>64.1%</td>
                    <td>192</td>
                    <td>1.10</td>
                    <td class="negative">6.70%</td>
                    <td class="positive"><strong>0.6177</strong></td>
                    <td class="negative">+0.89% / -1.46%</td>
                    <td>1h42m<br><small>(2.0m - 17h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 24: MFI Oversold</td>
                    <td class="positive"><strong>1.76%</strong></td>
                    <td>64.7%</td>
                    <td>150</td>
                    <td>1.05</td>
                    <td class="negative">7.14%</td>
                    <td class="positive"><strong>0.5776</strong></td>
                    <td class="negative">+0.85% / -1.41%</td>
                    <td>2h10m<br><small>(1.0m - 30h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>1.10%</strong></td>
                    <td>62.5%</td>
                    <td>24</td>
                    <td>1.18</td>
                    <td class="neutral">2.12%</td>
                    <td class="positive"><strong>0.4299</strong></td>
                    <td class="negative">+0.93% / -1.38%</td>
                    <td>2h8m<br><small>(7.0m - 13h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 12: Hammer Pattern</td>
                    <td class="positive"><strong>0.69%</strong></td>
                    <td>65.4%</td>
                    <td>162</td>
                    <td>1.02</td>
                    <td class="negative">8.05%</td>
                    <td class="positive"><strong>0.5723</strong></td>
                    <td class="negative">+0.88% / -1.48%</td>
                    <td>2h26m<br><small>(1.0m - 30h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>0.03%</strong></td>
                    <td>59.4%</td>
                    <td>64</td>
                    <td>1.00</td>
                    <td class="neutral">2.21%</td>
                    <td class="positive"><strong>0.4891</strong></td>
                    <td class="negative">+0.94% / -1.37%</td>
                    <td>2h24m<br><small>(1.0m - 19h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="negative"><strong>-0.08%</strong></td>
                    <td>64.3%</td>
                    <td>28</td>
                    <td>0.99</td>
                    <td class="neutral">3.44%</td>
                    <td class="positive"><strong>0.3927</strong></td>
                    <td class="negative">+0.86% / -1.48%</td>
                    <td>56.4m<br><small>(2.0m - 4h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-0.17%</strong></td>
                    <td>60.0%</td>
                    <td>20</td>
                    <td>0.97</td>
                    <td class="positive">1.37%</td>
                    <td class="positive"><strong>0.3797</strong></td>
                    <td class="negative">+1.01% / -1.31%</td>
                    <td>5h42m<br><small>(8.0m - 32h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="negative"><strong>-0.95%</strong></td>
                    <td>60.4%</td>
                    <td>53</td>
                    <td>0.93</td>
                    <td class="neutral">2.42%</td>
                    <td class="positive"><strong>0.4395</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>2h43m<br><small>(3.0m - 32h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-1.23%</strong></td>
                    <td>56.5%</td>
                    <td>23</td>
                    <td>0.80</td>
                    <td class="positive">1.99%</td>
                    <td class="positive"><strong>0.3310</strong></td>
                    <td class="negative">+0.88% / -1.34%</td>
                    <td>2h9m<br><small>(1.0m - 11h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="negative"><strong>-1.35%</strong></td>
                    <td>16.7%</td>
                    <td>6</td>
                    <td>0.33</td>
                    <td class="positive">1.99%</td>
                    <td class="negative"><strong>-0.0320</strong></td>
                    <td class="negative">+1.23% / -1.37%</td>
                    <td>1h16m<br><small>(7.0m - 3h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Advanced Rule 5: Donchian Channel Breakout</td>
                    <td class="negative"><strong>-1.61%</strong></td>
                    <td>59.5%</td>
                    <td>131</td>
                    <td>0.95</td>
                    <td class="negative">7.54%</td>
                    <td class="positive"><strong>0.5308</strong></td>
                    <td class="negative">+0.90% / -1.41%</td>
                    <td>3h24m<br><small>(1.0m - 42h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-2.66%</strong></td>
                    <td>62.4%</td>
                    <td>125</td>
                    <td>0.91</td>
                    <td class="negative">5.27%</td>
                    <td class="positive"><strong>0.5111</strong></td>
                    <td class="negative">+0.87% / -1.51%</td>
                    <td>1h7m<br><small>(1.0m - 6h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Ext Rule 1: RSI Bullish Divergence</td>
                    <td class="negative"><strong>-2.80%</strong></td>
                    <td>53.8%</td>
                    <td>39</td>
                    <td>0.77</td>
                    <td class="neutral">3.20%</td>
                    <td class="positive"><strong>0.3600</strong></td>
                    <td class="negative">+0.94% / -1.44%</td>
                    <td>1h35m<br><small>(3.0m - 10h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Professional Rule 1: Ichimoku Cloud Breakout</td>
                    <td class="negative"><strong>-4.27%</strong></td>
                    <td>61.2%</td>
                    <td>902</td>
                    <td>0.98</td>
                    <td class="negative">30.92%</td>
                    <td class="positive"><strong>0.6925</strong></td>
                    <td class="negative">+0.91% / -1.40%</td>
                    <td>2h30m<br><small>(1.0m - 45h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>AI Rule 1: Multi-Timeframe Momentum</td>
                    <td class="negative"><strong>-4.69%</strong></td>
                    <td>56.6%</td>
                    <td>113</td>
                    <td>0.85</td>
                    <td class="negative">5.73%</td>
                    <td class="positive"><strong>0.4845</strong></td>
                    <td class="negative">+0.93% / -1.41%</td>
                    <td>1h4m<br><small>(1.0m - 12h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="negative"><strong>-5.13%</strong></td>
                    <td>43.5%</td>
                    <td>23</td>
                    <td>0.42</td>
                    <td class="neutral">4.13%</td>
                    <td class="positive"><strong>0.1665</strong></td>
                    <td class="negative">+0.92% / -1.36%</td>
                    <td>5h18m<br><small>(7.0m - 42h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-5.35%</strong></td>
                    <td>62.2%</td>
                    <td>556</td>
                    <td>0.96</td>
                    <td class="negative">17.69%</td>
                    <td class="positive"><strong>0.6538</strong></td>
                    <td class="negative">+0.86% / -1.41%</td>
                    <td>2h28m<br><small>(1.0m - 49h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Research Rule 5: Volatility Breakout</td>
                    <td class="negative"><strong>-5.92%</strong></td>
                    <td>59.1%</td>
                    <td>115</td>
                    <td>0.82</td>
                    <td class="negative">6.86%</td>
                    <td class="positive"><strong>0.4762</strong></td>
                    <td class="negative">+0.92% / -1.44%</td>
                    <td>2h13m<br><small>(1.0m - 28h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Rule 22: Higher High Pattern</td>
                    <td class="negative"><strong>-9.63%</strong></td>
                    <td>56.6%</td>
                    <td>145</td>
                    <td>0.79</td>
                    <td class="negative">10.35%</td>
                    <td class="positive"><strong>0.4650</strong></td>
                    <td class="negative">+0.90% / -1.48%</td>
                    <td>2h37m<br><small>(2.0m - 28h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Professional Rule 5: Bollinger Band Squeeze</td>
                    <td class="negative"><strong>-15.09%</strong></td>
                    <td>38.7%</td>
                    <td>62</td>
                    <td>0.39</td>
                    <td class="negative">15.38%</td>
                    <td class="positive"><strong>0.1866</strong></td>
                    <td class="negative">+0.90% / -1.42%</td>
                    <td>2h2m<br><small>(1.0m - 12h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Rule 9: EMA Alignment</td>
                    <td class="negative"><strong>-34.71%</strong></td>
                    <td>59.8%</td>
                    <td>2367</td>
                    <td>0.94</td>
                    <td class="negative">45.77%</td>
                    <td class="positive"><strong>0.6808</strong></td>
                    <td class="negative">+0.90% / -1.38%</td>
                    <td>2h40m<br><small>(1.0m - 49h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Prof Rule 10: Hull MA Trend</td>
                    <td class="negative"><strong>-46.18%</strong></td>
                    <td>59.1%</td>
                    <td>2199</td>
                    <td>0.92</td>
                    <td class="negative">50.61%</td>
                    <td class="positive"><strong>0.6305</strong></td>
                    <td class="negative">+0.91% / -1.40%</td>
                    <td>2h22m<br><small>(1.0m - 49h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>New Buy 5: CMF Positive</td>
                    <td class="negative"><strong>-79.02%</strong></td>
                    <td>57.9%</td>
                    <td>1889</td>
                    <td>0.85</td>
                    <td class="negative">81.32%</td>
                    <td class="positive"><strong>0.4680</strong></td>
                    <td class="negative">+0.90% / -1.40%</td>
                    <td>2h34m<br><small>(1.0m - 45h19m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>70.2%</strong></td>
                    <td class="positive">16.69%</td>
                    <td>238</td>
                    <td>1.31</td>
                    <td class="neutral">4.69%</td>
                    <td class="positive"><strong>0.7085</strong></td>
                    <td class="negative">+0.89% / -1.57%</td>
                    <td>1h50m<br><small>(1.0m - 49h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 3: RSI Oversold</td>
                    <td class="positive"><strong>65.6%</strong></td>
                    <td class="positive">7.17%</td>
                    <td>250</td>
                    <td>1.12</td>
                    <td class="negative">7.13%</td>
                    <td class="positive"><strong>0.6590</strong></td>
                    <td class="negative">+0.87% / -1.42%</td>
                    <td>1h48m<br><small>(1.0m - 17h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 12: Hammer Pattern</td>
                    <td class="positive"><strong>65.4%</strong></td>
                    <td class="positive">0.69%</td>
                    <td>162</td>
                    <td>1.02</td>
                    <td class="negative">8.05%</td>
                    <td class="positive"><strong>0.5723</strong></td>
                    <td class="negative">+0.88% / -1.48%</td>
                    <td>2h26m<br><small>(1.0m - 30h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 24: MFI Oversold</td>
                    <td class="positive"><strong>64.7%</strong></td>
                    <td class="positive">1.76%</td>
                    <td>150</td>
                    <td>1.05</td>
                    <td class="negative">7.14%</td>
                    <td class="positive"><strong>0.5776</strong></td>
                    <td class="negative">+0.85% / -1.41%</td>
                    <td>2h10m<br><small>(1.0m - 30h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>64.5%</strong></td>
                    <td class="positive">19.70%</td>
                    <td>1041</td>
                    <td>1.08</td>
                    <td class="negative">9.15%</td>
                    <td class="positive"><strong>0.8160</strong></td>
                    <td class="negative">+0.90% / -1.48%</td>
                    <td>1h40m<br><small>(1.0m - 49h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>64.3%</strong></td>
                    <td class="negative">-0.08%</td>
                    <td>28</td>
                    <td>0.99</td>
                    <td class="neutral">3.44%</td>
                    <td class="positive"><strong>0.3927</strong></td>
                    <td class="negative">+0.86% / -1.48%</td>
                    <td>56.4m<br><small>(2.0m - 4h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>64.1%</strong></td>
                    <td class="positive">4.60%</td>
                    <td>192</td>
                    <td>1.10</td>
                    <td class="negative">6.70%</td>
                    <td class="positive"><strong>0.6177</strong></td>
                    <td class="negative">+0.89% / -1.46%</td>
                    <td>1h42m<br><small>(2.0m - 17h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>63.1%</strong></td>
                    <td class="positive">16.54%</td>
                    <td>850</td>
                    <td>1.08</td>
                    <td class="negative">12.68%</td>
                    <td class="positive"><strong>0.7820</strong></td>
                    <td class="negative">+0.91% / -1.47%</td>
                    <td>1h53m<br><small>(1.0m - 49h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>62.5%</strong></td>
                    <td class="positive">1.10%</td>
                    <td>24</td>
                    <td>1.18</td>
                    <td class="neutral">2.12%</td>
                    <td class="positive"><strong>0.4299</strong></td>
                    <td class="negative">+0.93% / -1.38%</td>
                    <td>2h8m<br><small>(7.0m - 13h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>62.4%</strong></td>
                    <td class="negative">-2.66%</td>
                    <td>125</td>
                    <td>0.91</td>
                    <td class="negative">5.27%</td>
                    <td class="positive"><strong>0.5111</strong></td>
                    <td class="negative">+0.87% / -1.51%</td>
                    <td>1h7m<br><small>(1.0m - 6h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>62.2%</strong></td>
                    <td class="negative">-5.35%</td>
                    <td>556</td>
                    <td>0.96</td>
                    <td class="negative">17.69%</td>
                    <td class="positive"><strong>0.6538</strong></td>
                    <td class="negative">+0.86% / -1.41%</td>
                    <td>2h28m<br><small>(1.0m - 49h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Professional Rule 1: Ichimoku Cloud Breakout</td>
                    <td class="positive"><strong>61.2%</strong></td>
                    <td class="negative">-4.27%</td>
                    <td>902</td>
                    <td>0.98</td>
                    <td class="negative">30.92%</td>
                    <td class="positive"><strong>0.6925</strong></td>
                    <td class="negative">+0.91% / -1.40%</td>
                    <td>2h30m<br><small>(1.0m - 45h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>60.4%</strong></td>
                    <td class="negative">-0.95%</td>
                    <td>53</td>
                    <td>0.93</td>
                    <td class="neutral">2.42%</td>
                    <td class="positive"><strong>0.4395</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>2h43m<br><small>(3.0m - 32h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="negative">-0.17%</td>
                    <td>20</td>
                    <td>0.97</td>
                    <td class="positive">1.37%</td>
                    <td class="positive"><strong>0.3797</strong></td>
                    <td class="negative">+1.01% / -1.31%</td>
                    <td>5h42m<br><small>(8.0m - 32h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 9: EMA Alignment</td>
                    <td class="positive"><strong>59.8%</strong></td>
                    <td class="negative">-34.71%</td>
                    <td>2367</td>
                    <td>0.94</td>
                    <td class="negative">45.77%</td>
                    <td class="positive"><strong>0.6808</strong></td>
                    <td class="negative">+0.90% / -1.38%</td>
                    <td>2h40m<br><small>(1.0m - 49h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 5: Donchian Channel Breakout</td>
                    <td class="positive"><strong>59.5%</strong></td>
                    <td class="negative">-1.61%</td>
                    <td>131</td>
                    <td>0.95</td>
                    <td class="negative">7.54%</td>
                    <td class="positive"><strong>0.5308</strong></td>
                    <td class="negative">+0.90% / -1.41%</td>
                    <td>3h24m<br><small>(1.0m - 42h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>59.4%</strong></td>
                    <td class="positive">0.03%</td>
                    <td>64</td>
                    <td>1.00</td>
                    <td class="neutral">2.21%</td>
                    <td class="positive"><strong>0.4891</strong></td>
                    <td class="negative">+0.94% / -1.37%</td>
                    <td>2h24m<br><small>(1.0m - 19h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Research Rule 5: Volatility Breakout</td>
                    <td class="positive"><strong>59.1%</strong></td>
                    <td class="negative">-5.92%</td>
                    <td>115</td>
                    <td>0.82</td>
                    <td class="negative">6.86%</td>
                    <td class="positive"><strong>0.4762</strong></td>
                    <td class="negative">+0.92% / -1.44%</td>
                    <td>2h13m<br><small>(1.0m - 28h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Prof Rule 10: Hull MA Trend</td>
                    <td class="positive"><strong>59.1%</strong></td>
                    <td class="negative">-46.18%</td>
                    <td>2199</td>
                    <td>0.92</td>
                    <td class="negative">50.61%</td>
                    <td class="positive"><strong>0.6305</strong></td>
                    <td class="negative">+0.91% / -1.40%</td>
                    <td>2h22m<br><small>(1.0m - 49h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>New Buy 5: CMF Positive</td>
                    <td class="positive"><strong>57.9%</strong></td>
                    <td class="negative">-79.02%</td>
                    <td>1889</td>
                    <td>0.85</td>
                    <td class="negative">81.32%</td>
                    <td class="positive"><strong>0.4680</strong></td>
                    <td class="negative">+0.90% / -1.40%</td>
                    <td>2h34m<br><small>(1.0m - 45h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>AI Rule 1: Multi-Timeframe Momentum</td>
                    <td class="positive"><strong>56.6%</strong></td>
                    <td class="negative">-4.69%</td>
                    <td>113</td>
                    <td>0.85</td>
                    <td class="negative">5.73%</td>
                    <td class="positive"><strong>0.4845</strong></td>
                    <td class="negative">+0.93% / -1.41%</td>
                    <td>1h4m<br><small>(1.0m - 12h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Rule 22: Higher High Pattern</td>
                    <td class="positive"><strong>56.6%</strong></td>
                    <td class="negative">-9.63%</td>
                    <td>145</td>
                    <td>0.79</td>
                    <td class="negative">10.35%</td>
                    <td class="positive"><strong>0.4650</strong></td>
                    <td class="negative">+0.90% / -1.48%</td>
                    <td>2h37m<br><small>(2.0m - 28h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>56.5%</strong></td>
                    <td class="negative">-1.23%</td>
                    <td>23</td>
                    <td>0.80</td>
                    <td class="positive">1.99%</td>
                    <td class="positive"><strong>0.3310</strong></td>
                    <td class="negative">+0.88% / -1.34%</td>
                    <td>2h9m<br><small>(1.0m - 11h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Ext Rule 1: RSI Bullish Divergence</td>
                    <td class="positive"><strong>53.8%</strong></td>
                    <td class="negative">-2.80%</td>
                    <td>39</td>
                    <td>0.77</td>
                    <td class="neutral">3.20%</td>
                    <td class="positive"><strong>0.3600</strong></td>
                    <td class="negative">+0.94% / -1.44%</td>
                    <td>1h35m<br><small>(3.0m - 10h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="neutral"><strong>43.5%</strong></td>
                    <td class="negative">-5.13%</td>
                    <td>23</td>
                    <td>0.42</td>
                    <td class="neutral">4.13%</td>
                    <td class="positive"><strong>0.1665</strong></td>
                    <td class="negative">+0.92% / -1.36%</td>
                    <td>5h18m<br><small>(7.0m - 42h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Professional Rule 5: Bollinger Band Squeeze</td>
                    <td class="neutral"><strong>38.7%</strong></td>
                    <td class="negative">-15.09%</td>
                    <td>62</td>
                    <td>0.39</td>
                    <td class="negative">15.38%</td>
                    <td class="positive"><strong>0.1866</strong></td>
                    <td class="negative">+0.90% / -1.42%</td>
                    <td>2h2m<br><small>(1.0m - 12h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="neutral"><strong>16.7%</strong></td>
                    <td class="negative">-1.35%</td>
                    <td>6</td>
                    <td>0.33</td>
                    <td class="positive">1.99%</td>
                    <td class="negative"><strong>-0.0320</strong></td>
                    <td class="negative">+1.23% / -1.37%</td>
                    <td>1h16m<br><small>(7.0m - 3h13m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Rule 3: RSI Oversold', 'Rule 6: Stochastic Oversold Cr...', 'Rule 24: MFI Oversold', 'Rule 12: Hammer Pattern', 'AI Rule 8: Momentum Divergence...', 'Advanced Rule 5: Donchian Chan...', 'Professional Rule 1: Ichimoku ...', 'Ext Rule 6: Fibonacci Support ...', 'Research Rule 5: Volatility Br...', 'AI Rule 1: Multi-Timeframe Mom...', 'Rule 22: Higher High Pattern', 'Professional Rule 10: CCI Reve...'],
                y: [70.16806722689076, 64.45725264169067, 63.05882352941177, 65.60000000000001, 64.0625, 64.66666666666666, 65.4320987654321, 62.4, 59.541984732824424, 61.19733924611973, 62.23021582733813, 59.130434782608695, 56.63716814159292, 56.55172413793104, 59.375],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Rule 3: RSI Oversold', 'Rule 6: Stochastic Oversold Cr...', 'Rule 24: MFI Oversold', 'Rule 12: Hammer Pattern', 'AI Rule 8: Momentum Divergence...', 'Advanced Rule 5: Donchian Chan...', 'Professional Rule 1: Ichimoku ...', 'Ext Rule 6: Fibonacci Support ...', 'Research Rule 5: Volatility Br...', 'AI Rule 1: Multi-Timeframe Mom...', 'Rule 22: Higher High Pattern', 'Professional Rule 10: CCI Reve...'],
                y: [np.float64(16.691177933565715), np.float64(19.704830180785603), np.float64(16.541328025558165), np.float64(7.171356523712988), np.float64(4.597928351109443), np.float64(1.7608920249396216), np.float64(0.6941432740893397), np.float64(-2.657334002926309), np.float64(-1.6072633318230438), np.float64(-4.272702982011997), np.float64(-5.348983933874883), np.float64(-5.9165764857513565), np.float64(-4.692771448572391), np.float64(-9.625908817641001), np.float64(0.029769651292226626)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
