
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 8 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 11:52:50</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">8</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">50.0%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">12.3%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">34.2%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">66.0%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">4,887</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["34.2%","27.8%","12.4%","10.4%","3.7%","4.2%","4.2%","1.5%"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Professional Rule 7: Chaikin Money Flow Reversal","Volume Rule 5: Smart Money Volume","Rule 2: Golden Cross","Rule 10: Volume Spike","Acad Rule 2: Mean Reversion Factor"],"y":[34.151804751139586,27.776159792094983,12.397068199497022,10.356945806297487,3.700545950294982,4.210055788956495,4.164340250722685,1.5164327895708993],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Professional Rule 7: Chaikin Money Flow Reversal","Volume Rule 5: Smart Money Volume","Rule 2: Golden Cross","Rule 10: Volume Spike","Acad Rule 2: Mean Reversion Factor"],"y":[1204,246,1530,29,21,12,9,7],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Professional Rule 7: Chaikin Money Flow Reversal","Volume Rule 5: Smart Money Volume","Rule 2: Golden Cross","Rule 10: Volume Spike","Acad Rule 2: Mean Reversion Factor"],"y":[717,135,937,15,12,5,3,5],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[34.151804751139586,27.776159792094983,12.397068199497022,10.356945806297487,3.700545950294982,4.210055788956495,4.164340250722685,1.5164327895708993],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["AI Rule 10","Prof Rule 7","Rule 7","Professional Rule 7","Volume Rule 5","Rule 2","Rule 10","Acad Rule 2"],"textposition":"top center","x":[29.28430758762418,16.20176011042907,34.30376574391236,3.662622840270904,8.3820864123356,3.6759479055379547,2.491711589584934,2.917694798117018],"y":[34.151804751139586,27.776159792094983,12.397068199497022,10.356945806297487,3.700545950294982,4.210055788956495,4.164340250722685,1.5164327895708993],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["34.2%","27.8%","6.9%","7.0%","1.5%"],"textposition":"auto","x":["AI_GENERATED","PROFESSIONAL","ORIGINAL","UNKNOWN","ACADEMIC"],"y":[34.151804751139586,27.776159792094983,6.923821413058733,7.028745878296235,1.5164327895708993],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["1921","381","2467","44","33","17","12","12"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Professional Rule 7: Chaikin Money Flow Reversal","Volume Rule 5: Smart Money Volume","Rule 2: Golden Cross","Rule 10: Volume Spike","Acad Rule 2: Mean Reversion Factor"],"y":[1921,381,2467,44,33,17,12,12],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921],"y":[0,0.017778138860561992,0.035556277721123984,0.05333441658168597,0.07111255544224797,0.08889069430280996,0.10666883316337195,0.12444697202393394,0.14222511088449594,0.16000324974505792,0.17778138860561993,0.1955595274661819,0.2133376663267439,0.2311158051873059,0.24889394404786788,0.26667208290842986,0.2844502217689919,0.3022283606295539,0.32000649949011584,0.33778463835067785,0.35556277721123986,0.3733409160718018,0.3911190549323638,0.40889719379292583,0.4266753326534878,0.4444534715140498,0.4622316103746118,0.48000974923517375,0.49778788809573576,0.5155660269562977,0.5333441658168597,0.5511223046774217,0.5689004435379837,0.5866785823985458,0.6044567212591078,0.6222348601196697,0.6400129989802317,0.6577911378407937,0.6755692767013557,0.6933474155619177,0.7111255544224797,0.7289036932830416,0.7466818321436036,0.7644599710041656,0.7822381098647276,0.8000162487252896,0.8177943875858517,0.8355725264464136,0.8533506653069756,0.8711288041675376,0.8889069430280996,0.9066850818886616,0.9244632207492236,0.9422413596097855,0.9600194984703475,0.9777976373309095,0.9955757761914715,1.0133539150520334,1.0311320539125954,1.0489101927731574,1.0666883316337195,1.0844664704942815,1.1022446093548435,1.1200227482154055,1.1378008870759675,1.1555790259365295,1.1733571647970915,1.1911353036576535,1.2089134425182155,1.2266915813787773,1.2444697202393393,1.2622478590999013,1.2800259979604633,1.2978041368210254,1.3155822756815874,1.3333604145421494,1.3511385534027114,1.3689166922632734,1.3866948311238354,1.4044729699843974,1.4222511088449594,1.4400292477055214,1.4578073865660832,1.4755855254266452,1.4933636642872072,1.5111418031477692,1.5289199420083313,1.5466980808688933,1.5644762197294553,1.5822543585900173,1.6000324974505793,1.6178106363111413,1.6355887751717033,1.6533669140322653,1.671145052892827,1.6889231917533891,1.7067013306139511,1.7244794694745131,1.7422576083350751,1.7600357471956372,1.7778138860561992,1.7955920249167612,1.8133701637773232,1.8311483026378852,1.8489264414984472,1.8667045803590092,1.884482719219571,1.902260858080133,1.920038996940695,1.937817135801257,1.955595274661819,1.973373413522381,1.991151552382943,2.008929691243505,2.026707830104067,2.044485968964629,2.062264107825191,2.080042246685753,2.097820385546315,2.115598524406877,2.133376663267439,2.151154802128001,2.168932940988563,2.186711079849125,2.204489218709687,2.222267357570249,2.240045496430811,2.257823635291373,2.275601774151935,2.293379913012497,2.311158051873059,2.328936190733621,2.346714329594183,2.364492468454745,2.382270607315307,2.400048746175869,2.417826885036431,2.435605023896993,2.4533831627575546,2.4711613016181166,2.4889394404786787,2.5067175793392407,2.5244957181998027,2.5422738570603647,2.5600519959209267,2.5778301347814887,2.5956082736420507,2.6133864125026127,2.6311645513631747,2.6489426902237367,2.6667208290842987,2.6844989679448608,2.7022771068054228,2.7200552456659848,2.737833384526547,2.755611523387109,2.773389662247671,2.791167801108233,2.808945939968795,2.826724078829357,2.844502217689919,2.862280356550481,2.880058495411043,2.8978366342716044,2.9156147731321664,2.9333929119927284,2.9511710508532905,2.9689491897138525,2.9867273285744145,3.0045054674349765,3.0222836062955385,3.0400617451561005,3.0578398840166625,3.0756180228772245,3.0933961617377865,3.1111743005983485,3.1289524394589106,3.1467305783194726,3.1645087171800346,3.1822868560405966,3.2000649949011586,3.2178431337617206,3.2356212726222826,3.2533994114828446,3.2711775503434066,3.2889556892039686,3.3067338280645306,3.324511966925092,3.342290105785654,3.3600682446462162,3.3778463835067782,3.3956245223673402,3.4134026612279023,3.4311808000884643,3.4489589389490263,3.4667370778095883,3.4845152166701503,3.5022933555307123,3.5200714943912743,3.5378496332518363,3.5556277721123983,3.5734059109729603,3.5911840498335224,3.6089621886940844,3.6267403275546464,3.6445184664152084,3.6622966052757704,3.6800747441363324,3.6978528829968944,3.7156310218574564,3.7334091607180184,3.7511872995785804,3.768965438439142,3.786743577299704,3.804521716160266,3.822299855020828,3.84007799388139,3.857856132741952,3.875634271602514,3.893412410463076,3.911190549323638,3.9289686881842,3.946746827044762,3.964524965905324,3.982303104765886,4.000081243626448,4.01785938248701,4.035637521347572,4.053415660208134,4.071193799068696,4.088971937929258,4.10675007678982,4.124528215650382,4.142306354510944,4.160084493371506,4.177862632232068,4.19564077109263,4.213418909953192,4.231197048813754,4.248975187674316,4.266753326534878,4.284531465395441,4.302309604256002,4.320087743116565,4.337865881977126,4.355644020837689,4.37342215969825,4.391200298558813,4.408978437419374,4.426756576279937,4.444534715140498,4.46231285400106,4.480090992861622,4.497869131722184,4.515647270582746,4.533425409443308,4.55120354830387,4.568981687164432,4.586759826024994,4.604537964885556,4.622316103746118,4.64009424260668,4.657872381467242,4.675650520327804,4.693428659188366,4.711206798048928,4.72898493690949,4.746763075770052,4.764541214630614,4.782319353491176,4.800097492351738,4.8178756312123,4.835653770072862,4.853431908933424,4.871210047793986,4.888988186654548,4.906766325515109,4.924544464375672,4.942322603236233,4.960100742096796,4.977878880957357,4.99565701981792,5.013435158678481,5.031213297539044,5.048991436399605,5.066769575260168,5.084547714120729,5.102325852981292,5.120103991841853,5.137882130702416,5.155660269562977,5.17343840842354,5.191216547284101,5.208994686144664,5.226772825005225,5.244550963865788,5.2623291027263495,5.280107241586912,5.2978853804474735,5.315663519308036,5.3334416581685975,5.3512197970291595,5.3689979358897215,5.3867760747502835,5.4045542136108455,5.4223323524714075,5.4401104913319696,5.457888630192532,5.475666769053094,5.493444907913656,5.511223046774218,5.52900118563478,5.546779324495342,5.564557463355904,5.582335602216466,5.600113741077028,5.61789187993759,5.635670018798152,5.653448157658714,5.671226296519276,5.689004435379838,5.7067825742404,5.724560713100962,5.742338851961524,5.760116990822086,5.777895129682648,5.795673268543209,5.813451407403772,5.831229546264333,5.849007685124896,5.866785823985457,5.88456396284602,5.902342101706581,5.920120240567144,5.937898379427705,5.955676518288268,5.973454657148829,5.991232796009392,6.009010934869953,6.026789073730516,6.044567212591077,6.06234535145164,6.080123490312201,6.097901629172764,6.115679768033325,6.133457906893888,6.151236045754449,6.169014184615012,6.186792323475573,6.204570462336136,6.222348601196697,6.240126740057259,6.257904878917821,6.275683017778383,6.293461156638945,6.311239295499507,6.329017434360069,6.346795573220631,6.364573712081193,6.382351850941755,6.400129989802317,6.417908128662879,6.435686267523441,6.453464406384003,6.471242545244565,6.489020684105127,6.506798822965689,6.524576961826251,6.542355100686813,6.560133239547375,6.577911378407937,6.595689517268499,6.613467656129061,6.631245794989623,6.649023933850184,6.666802072710747,6.684580211571308,6.702358350431871,6.7201364892924325,6.737914628152995,6.7556927670135565,6.773470905874119,6.7912490447346805,6.809027183595243,6.8268053224558045,6.844583461316367,6.8623616001769285,6.880139739037491,6.8979178778980526,6.9156960167586154,6.933474155619177,6.9512522944797395,6.969030433340301,6.9868085722008635,7.004586711061425,7.0223648499219875,7.040142988782549,7.0579211276431115,7.075699266503673,7.0934774053642355,7.111255544224797,7.129033683085359,7.146811821945921,7.164589960806483,7.182368099667045,7.200146238527607,7.217924377388169,7.235702516248731,7.253480655109293,7.271258793969855,7.289036932830417,7.306815071690979,7.324593210551541,7.342371349412103,7.360149488272665,7.377927627133227,7.395705765993789,7.413483904854351,7.431262043714913,7.449040182575475,7.466818321436037,7.484596460296599,7.502374599157161,7.520152738017723,7.537930876878284,7.555709015738847,7.573487154599408,7.591265293459971,7.609043432320532,7.626821571181095,7.644599710041656,7.662377848902219,7.68015598776278,7.697934126623343,7.715712265483904,7.733490404344467,7.751268543205028,7.769046682065591,7.786824820926152,7.804602959786715,7.822381098647276,7.840159237507839,7.8579373763684,7.875715515228963,7.893493654089524,7.911271792950087,7.929049931810648,7.946828070671211,7.964606209531772,7.982384348392335,8.000162487252895,8.01794062611346,8.03571876497402,8.053496903834583,8.071275042695143,8.089053181555707,8.106831320416267,8.124609459276831,8.142387598137391,8.160165736997955,8.177943875858515,8.19572201471908,8.21350015357964,8.231278292440203,8.249056431300763,8.266834570161327,8.284612709021888,8.302390847882451,8.320168986743012,8.337947125603575,8.355725264464136,8.3735034033247,8.39128154218526,8.409059681045823,8.426837819906384,8.444615958766947,8.462394097627508,8.48017223648807,8.497950375348632,8.515728514209194,8.533506653069756,8.551284791930318,8.569062930790881,8.586841069651442,8.604619208512004,8.622397347372566,8.64017548623313,8.65795362509369,8.675731763954252,8.693509902814814,8.711288041675378,8.729066180535938,8.7468443193965,8.764622458257062,8.782400597117626,8.800178735978186,8.817956874838748,8.83573501369931,8.853513152559874,8.871291291420434,8.889069430280996,8.906847569141558,8.92462570800212,8.942403846862682,8.960181985723244,8.977960124583806,8.995738263444368,9.01351640230493,9.031294541165492,9.049072680026054,9.066850818886616,9.084628957747178,9.10240709660774,9.120185235468302,9.137963374328864,9.155741513189426,9.173519652049988,9.19129779091055,9.209075929771112,9.226854068631674,9.244632207492236,9.262410346352798,9.28018848521336,9.29796662407392,9.315744762934484,9.333522901795046,9.351301040655608,9.369079179516168,9.386857318376732,9.404635457237294,9.422413596097856,9.440191734958416,9.45796987381898,9.475748012679542,9.493526151540104,9.511304290400664,9.529082429261228,9.54686056812179,9.564638706982352,9.582416845842912,9.600194984703476,9.617973123564038,9.6357512624246,9.65352940128516,9.671307540145724,9.689085679006286,9.706863817866848,9.724641956727408,9.742420095587972,9.760198234448534,9.777976373309096,9.795754512169657,9.813532651030219,9.831310789890782,9.849088928751344,9.866867067611905,9.884645206472467,9.90242334533303,9.920201484193592,9.937979623054153,9.955757761914715,9.973535900775278,9.99131403963584,10.0090921784964,10.026870317356963,10.044648456217526,10.062426595078088,10.080204733938649,10.09798287279921,10.115761011659774,10.133539150520336,10.151317289380897,10.169095428241459,10.186873567102023,10.204651705962585,10.222429844823145,10.240207983683707,10.257986122544269,10.275764261404833,10.293542400265393,10.311320539125955,10.329098677986517,10.34687681684708,10.36465495570764,10.382433094568203,10.400211233428765,10.417989372289329,10.435767511149889,10.45354565001045,10.471323788871013,10.489101927731577,10.506880066592137,10.524658205452699,10.542436344313261,10.560214483173825,10.577992622034385,10.595770760894947,10.613548899755509,10.631327038616073,10.649105177476633,10.666883316337195,10.684661455197757,10.702439594058319,10.720217732918881,10.737995871779443,10.755774010640005,10.773552149500567,10.791330288361129,10.809108427221691,10.826886566082253,10.844664704942815,10.862442843803377,10.880220982663939,10.897999121524501,10.915777260385063,10.933555399245625,10.951333538106187,10.96911167696675,10.986889815827311,11.004667954687873,11.022446093548435,11.040224232408997,11.05800237126956,11.07578051013012,11.093558648990683,11.111336787851245,11.129114926711807,11.146893065572367,11.164671204432931,11.182449343293493,11.200227482154055,11.218005621014616,11.23578375987518,11.253561898735741,11.271340037596303,11.289118176456864,11.306896315317427,11.32467445417799,11.342452593038551,11.360230731899112,11.378008870759675,11.395787009620237,11.4135651484808,11.43134328734136,11.449121426201923,11.466899565062485,11.484677703923047,11.502455842783608,11.520233981644171,11.538012120504733,11.555790259365295,11.573568398225856,11.591346537086418,11.609124675946981,11.626902814807544,11.644680953668104,11.662459092528666,11.68023723138923,11.698015370249792,11.715793509110352,11.733571647970914,11.751349786831478,11.76912792569204,11.7869060645526,11.804684203413162,11.822462342273726,11.840240481134288,11.858018619994848,11.87579675885541,11.893574897715974,11.911353036576536,11.929131175437096,11.946909314297658,11.964687453158222,11.982465592018784,12.000243730879344,12.018021869739906,12.035800008600468,12.053578147461032,12.071356286321592,12.089134425182154,12.106912564042716,12.12469070290328,12.14246884176384,12.160246980624402,12.178025119484964,12.195803258345528,12.213581397206088,12.23135953606665,12.249137674927212,12.266915813787776,12.284693952648336,12.302472091508898,12.32025023036946,12.338028369230024,12.355806508090584,12.373584646951146,12.391362785811708,12.409140924672272,12.426919063532832,12.444697202393394,12.462475341253956,12.480253480114518,12.49803161897508,12.515809757835642,12.533587896696204,12.551366035556766,12.569144174417328,12.58692231327789,12.604700452138452,12.622478590999014,12.640256729859576,12.658034868720138,12.6758130075807,12.693591146441262,12.711369285301824,12.729147424162386,12.746925563022948,12.76470370188351,12.782481840744072,12.800259979604634,12.818038118465196,12.835816257325758,12.853594396186319,12.871372535046882,12.889150673907444,12.906928812768006,12.924706951628567,12.94248509048913,12.960263229349692,12.978041368210254,12.995819507070815,13.013597645931378,13.03137578479194,13.049153923652502,13.066932062513063,13.084710201373627,13.102488340234189,13.12026647909475,13.13804461795531,13.155822756815875,13.173600895676437,13.191379034536999,13.209157173397559,13.226935312258123,13.244713451118685,13.262491589979247,13.280269728839807,13.298047867700369,13.315826006560933,13.333604145421495,13.351382284282055,13.369160423142617,13.38693856200318,13.404716700863743,13.422494839724303,13.440272978584865,13.458051117445429,13.47582925630599,13.493607395166551,13.511385534027113,13.529163672887677,13.546941811748239,13.564719950608799,13.582498089469361,13.600276228329925,13.618054367190487,13.635832506051047,13.653610644911609,13.671388783772173,13.689166922632735,13.706945061493295,13.724723200353857,13.74250133921442,13.760279478074983,13.778057616935543,13.795835755796105,13.813613894656667,13.831392033517231,13.849170172377791,13.866948311238353,13.884726450098915,13.902504588959479,13.92028272782004,13.938060866680601,13.955839005541163,13.973617144401727,13.991395283262287,14.00917342212285,14.026951560983411,14.044729699843975,14.062507838704535,14.080285977565097,14.09806411642566,14.115842255286223,14.133620394146783,14.151398533007345,14.169176671867907,14.186954810728471,14.204732949589031,14.222511088449593,14.240289227310155,14.258067366170717,14.27584550503128,14.293623643891841,14.311401782752403,14.329179921612965,14.346958060473527,14.36473619933409,14.382514338194651,14.400292477055213,14.418070615915775,14.435848754776337,14.4536268936369,14.471405032497461,14.489183171358023,14.506961310218585,14.524739449079147,14.54251758793971,14.560295726800272,14.578073865660834,14.595852004521396,14.613630143381958,14.631408282242518,14.649186421103082,14.666964559963644,14.684742698824206,14.702520837684766,14.72029897654533,14.738077115405892,14.755855254266454,14.773633393127014,14.791411531987578,14.80918967084814,14.826967809708702,14.844745948569262,14.862524087429826,14.880302226290388,14.89808036515095,14.91585850401151,14.933636642872074,14.951414781732636,14.969192920593198,14.986971059453758,15.004749198314322,15.022527337174884,15.040305476035446,15.058083614896006,15.075861753756568,15.093639892617132,15.111418031477694,15.129196170338254,15.146974309198816,15.16475244805938,15.182530586919942,15.200308725780502,15.218086864641064,15.235865003501628,15.25364314236219,15.27142128122275,15.289199420083312,15.306977558943876,15.324755697804438,15.342533836664998,15.36031197552556,15.378090114386124,15.395868253246686,15.413646392107246,15.431424530967808,15.449202669828372,15.466980808688934,15.484758947549494,15.502537086410056,15.52031522527062,15.538093364131182,15.555871502991742,15.573649641852304,15.591427780712866,15.60920591957343,15.62698405843399,15.644762197294552,15.662540336155114,15.680318475015678,15.698096613876238,15.7158747527368,15.733652891597362,15.751431030457926,15.769209169318486,15.786987308179048,15.80476544703961,15.822543585900174,15.840321724760734,15.858099863621296,15.875878002481858,15.893656141342422,15.911434280202982,15.929212419063544,15.946990557924106,15.96476869678467,15.98254683564523,16.00032497450579,16.018103113366355,16.03588125222692,16.05365939108748,16.07143752994804,16.089215668808603,16.106993807669166,16.124771946529727,16.142550085390287,16.16032822425085,16.178106363111414,16.195884501971975,16.213662640832535,16.2314407796931,16.249218918553662,16.266997057414223,16.284775196274783,16.302553335135347,16.32033147399591,16.33810961285647,16.35588775171703,16.373665890577595,16.39144402943816,16.40922216829872,16.42700030715928,16.444778446019843,16.462556584880407,16.480334723740967,16.498112862601527,16.51589100146209,16.533669140322655,16.551447279183215,16.569225418043775,16.58700355690434,16.604781695764903,16.622559834625463,16.640337973486023,16.658116112346587,16.67589425120715,16.69367239006771,16.71145052892827,16.729228667788835,16.7470068066494,16.76478494550996,16.78256308437052,16.800341223231083,16.818119362091647,16.835897500952207,16.853675639812767,16.87145377867333,16.889231917533895,16.907010056394455,16.924788195255015,16.94256633411558,16.96034447297614,16.978122611836703,16.995900750697263,17.013678889557827,17.031457028418387,17.04923516727895,17.06701330613951,17.084791445000075,17.102569583860635,17.1203477227212,17.138125861581763,17.15590400044232,17.173682139302883,17.191460278163447,17.209238417024007,17.22701655588457,17.24479469474513,17.262572833605695,17.28035097246626,17.298129111326816,17.31590725018738,17.33368538904794,17.351463527908503,17.369241666769067,17.387019805629627,17.40479794449019,17.422576083350755,17.44035422221131,17.458132361071875,17.475910499932436,17.493688638793,17.511466777653563,17.529244916514124,17.547023055374687,17.56480119423525,17.582579333095808,17.60035747195637,17.618135610816932,17.635913749677496,17.65369188853806,17.67147002739862,17.689248166259183,17.707026305119747,17.724804443980304,17.742582582840868,17.760360721701428,17.77813886056199,17.795916999422555,17.813695138283116,17.83147327714368,17.84925141600424,17.8670295548648,17.884807693725364,17.902585832585924,17.920363971446488,17.93814211030705,17.95592024916761,17.973698388028176,17.991476526888736,18.009254665749296,18.02703280460986,18.04481094347042,18.062589082330984,18.080367221191548,18.098145360052108,18.11592349891267,18.133701637773232,18.151479776633792,18.169257915494356,18.187036054354916,18.20481419321548,18.22259233207604,18.240370470936604,18.258148609797168,18.275926748657728,18.293704887518288,18.311483026378852,18.329261165239412,18.347039304099976,18.364817442960536,18.3825955818211,18.400373720681664,18.418151859542224,18.435929998402784,18.453708137263348,18.47148627612391,18.489264414984472,18.507042553845032,18.524820692705596,18.54259883156616,18.56037697042672,18.57815510928728,18.59593324814784,18.613711387008404,18.631489525868968,18.64926766472953,18.667045803590092,18.684823942450656,18.702602081311216,18.720380220171776,18.738158359032337,18.7559364978929,18.773714636753464,18.791492775614024,18.809270914474588,18.827049053335152,18.844827192195712,18.862605331056272,18.880383469916833,18.898161608777396,18.91593974763796,18.93371788649852,18.951496025359084,18.969274164219648,18.98705230308021,19.00483044194077,19.02260858080133,19.040386719661893,19.058164858522456,19.075942997383017,19.09372113624358,19.111499275104144,19.129277413964704,19.147055552825265,19.164833691685825,19.18261183054639,19.200389969406952,19.218168108267513,19.235946247128076,19.253724385988637,19.2715025248492,19.28928066370976,19.30705880257032,19.324836941430885,19.34261508029145,19.36039321915201,19.378171358012573,19.395949496873133,19.413727635733697,19.431505774594257,19.449283913454817,19.46706205231538,19.484840191175945,19.502618330036505,19.52039646889707,19.53817460775763,19.555952746618193,19.573730885478753,19.591509024339313,19.609287163199877,19.627065302060437,19.644843440921,19.662621579781565,19.680399718642125,19.69817785750269,19.71595599636325,19.73373413522381,19.751512274084373,19.769290412944933,19.787068551805497,19.80484669066606,19.82262482952662,19.840402968387185,19.85818110724774,19.875959246108305,19.89373738496887,19.91151552382943,19.929293662689993,19.947071801550557,19.964849940411117,19.98262807927168,20.000406218132238,20.0181843569928,20.035962495853365,20.053740634713925,20.07151877357449,20.089296912435053,20.107075051295613,20.124853190156177,20.142631329016734,20.160409467877297,20.17818760673786,20.19596574559842,20.213743884458985,20.23152202331955,20.24930016218011,20.267078301040673,20.28485643990123,20.302634578761793,20.320412717622357,20.338190856482917,20.35596899534348,20.373747134204045,20.391525273064605,20.40930341192517,20.427081550785726,20.44485968964629,20.462637828506853,20.480415967367414,20.498194106227977,20.515972245088538,20.5337503839491,20.551528522809665,20.569306661670222,20.587084800530786,20.60486293939135,20.62264107825191,20.640419217112473,20.658197355973034,20.675975494833597,20.69375363369416,20.711531772554718,20.72930991141528,20.747088050275845,20.764866189136406,20.78264432799697,20.80042246685753,20.818200605718093,20.835978744578657,20.853756883439214,20.871535022299778,20.889313161160338,20.9070913000209,20.924869438881466,20.942647577742026,20.96042571660259,20.978203855463153,20.99598199432371,21.013760133184274,21.031538272044834,21.049316410905398,21.06709454976596,21.084872688626522,21.102650827487086,21.12042896634765,21.138207105208206,21.15598524406877,21.17376338292933,21.191541521789894,21.209319660650458,21.227097799511018,21.24487593837158,21.262654077232146,21.280432216092702,21.298210354953266,21.315988493813826,21.33376663267439,21.351544771534954,21.369322910395514,21.387101049256078,21.404879188116638,21.4226573269772,21.440435465837762,21.458213604698322,21.475991743558886,21.49376988241945,21.51154802128001,21.529326160140574,21.547104299001134,21.564882437861694,21.582660576722258,21.60043871558282,21.618216854443382,21.635994993303946,21.653773132164506,21.67155127102507,21.68932940988563,21.70710754874619,21.724885687606754,21.742663826467314,21.760441965327878,21.77822010418844,21.795998243049002,21.813776381909566,21.831554520770126,21.849332659630686,21.86711079849125,21.88488893735181,21.902667076212374,21.920445215072935,21.9382233539335,21.956001492794062,21.973779631654622,21.991557770515183,22.009335909375746,22.027114048236307,22.04489218709687,22.06267032595743,22.080448464817994,22.098226603678558,22.11600474253912,22.13378288139968,22.15156102026024,22.169339159120803,22.187117297981366,22.204895436841927,22.22267357570249,22.240451714563054,22.258229853423614,22.276007992284175,22.293786131144735,22.3115642700053,22.329342408865863,22.347120547726423,22.364898686586987,22.38267682544755,22.40045496430811,22.41823310316867,22.43601124202923,22.453789380889795,22.47156751975036,22.48934565861092,22.507123797471483,22.524901936332046,22.542680075192607,22.560458214053167,22.578236352913727,22.59601449177429,22.613792630634855,22.631570769495415,22.64934890835598,22.667127047216542,22.684905186077103,22.702683324937663,22.720461463798223,22.738239602658787,22.75601774151935,22.77379588037991,22.791574019240475,22.809352158101035,22.8271302969616,22.84490843582216,22.86268657468272,22.880464713543283,22.898242852403847,22.916020991264407,22.93379913012497,22.95157726898553,22.969355407846095,22.987133546706655,23.004911685567215,23.02268982442778,23.040467963288343,23.058246102148903,23.076024241009467,23.093802379870027,23.11158051873059,23.12935865759115,23.14713679645171,23.164914935312275,23.182693074172835,23.2004712130334,23.218249351893963,23.236027490754523,23.253805629615087,23.271583768475647,23.289361907336207,23.30714004619677,23.32491818505733,23.342696323917895,23.36047446277846,23.37825260163902,23.396030740499583,23.41380887936014,23.431587018220704,23.449365157081267,23.467143295941828,23.48492143480239,23.502699573662955,23.520477712523515,23.53825585138408,23.556033990244636,23.5738121291052,23.591590267965763,23.609368406826324,23.627146545686887,23.64492468454745,23.66270282340801,23.680480962268575,23.698259101129132,23.716037239989696,23.73381537885026,23.75159351771082,23.769371656571384,23.787149795431947,23.804927934292508,23.82270607315307,23.840484212013628,23.858262350874192,23.876040489734756,23.893818628595316,23.91159676745588,23.929374906316443,23.947153045177004,23.964931184037567,23.982709322898124,24.000487461758688,24.01826560061925,24.036043739479812,24.053821878340376,24.071600017200936,24.0893781560615,24.107156294922063,24.12493443378262,24.142712572643184,24.160490711503748,24.178268850364308,24.19604698922487,24.213825128085432,24.231603266945996,24.24938140580656,24.267159544667116,24.28493768352768,24.302715822388244,24.320493961248804,24.338272100109368,24.356050238969928,24.373828377830492,24.391606516691056,24.409384655551612,24.427162794412176,24.444940933272736,24.4627190721333,24.480497210993864,24.498275349854424,24.516053488714988,24.53383162757555,24.55160976643611,24.569387905296672,24.587166044157232,24.604944183017796,24.62272232187836,24.64050046073892,24.658278599599484,24.676056738460048,24.693834877320604,24.71161301618117,24.72939115504173,24.747169293902292,24.764947432762856,24.782725571623416,24.80050371048398,24.818281849344544,24.8360599882051,24.853838127065664,24.871616265926225,24.88939440478679,24.907172543647352,24.924950682507912,24.942728821368476,24.960506960229036,24.978285099089597,24.99606323795016,25.01384137681072,25.031619515671284,25.049397654531848,25.06717579339241,25.084953932252972,25.102732071113532,25.120510209974093,25.138288348834656,25.156066487695217,25.17384462655578,25.191622765416344,25.209400904276904,25.22717904313747,25.24495718199803,25.26273532085859,25.280513459719153,25.298291598579713,25.316069737440277,25.333847876300837,25.3516260151614,25.369404154021964,25.387182292882525,25.404960431743085,25.42273857060365,25.44051670946421,25.458294848324773,25.476072987185333,25.493851126045897,25.51162926490646,25.52940740376702,25.54718554262758,25.564963681488145,25.582741820348705,25.60051995920927,25.61829809806983,25.636076236930393,25.653854375790957,25.671632514651517,25.689410653512077,25.707188792372637,25.7249669312332,25.742745070093765,25.760523208954325,25.77830134781489,25.796079486675453,25.813857625536013,25.831635764396573,25.849413903257133,25.867192042117697,25.88497018097826,25.90274831983882,25.920526458699385,25.93830459755995,25.95608273642051,25.97386087528107,25.99163901414163,26.009417153002193,26.027195291862757,26.044973430723317,26.06275156958388,26.080529708444445,26.098307847305005,26.116085986165565,26.133864125026125,26.15164226388669,26.169420402747253,26.187198541607813,26.204976680468377,26.22275481932894,26.2405329581895,26.25831109705006,26.27608923591062,26.293867374771185,26.31164551363175,26.32942365249231,26.347201791352873,26.364979930213433,26.382758069073997,26.400536207934557,26.418314346795118,26.43609248565568,26.453870624516245,26.471648763376805,26.48942690223737,26.50720504109793,26.524983179958493,26.542761318819053,26.560539457679614,26.578317596540177,26.596095735400738,26.6138738742613,26.631652013121865,26.649430151982425,26.66720829084299,26.68498642970355,26.70276456856411,26.720542707424674,26.738320846285234,26.756098985145798,26.77387712400636,26.79165526286692,26.809433401727485,26.827211540588046,26.844989679448606,26.86276781830917,26.88054595716973,26.898324096030294,26.916102234890857,26.933880373751418,26.95165851261198,26.969436651472538,26.987214790333102,27.004992929193666,27.022771068054226,27.04054920691479,27.058327345775353,27.076105484635914,27.093883623496477,27.111661762357034,27.129439901217598,27.14721804007816,27.164996178938722,27.182774317799286,27.20055245665985,27.21833059552041,27.236108734380974,27.25388687324153,27.271665012102094,27.289443150962658,27.307221289823218,27.324999428683782,27.342777567544346,27.360555706404906,27.37833384526547,27.396111984126026,27.41389012298659,27.431668261847154,27.449446400707714,27.467224539568278,27.48500267842884,27.502780817289402,27.520558956149966,27.538337095010522,27.556115233871086,27.57389337273165,27.59167151159221,27.609449650452774,27.627227789313334,27.645005928173898,27.662784067034462,27.68056220589502,27.698340344755582,27.716118483616146,27.733896622476706,27.75167476133727,27.76945290019783,27.787231039058394,27.805009177918958,27.822787316779515,27.84056545564008,27.858343594500642,27.876121733361202,27.893899872221766,27.911678011082326,27.92945614994289,27.947234288803454,27.96501242766401,27.982790566524574,28.000568705385135,28.0183468442457,28.036124983106262,28.053903121966822,28.071681260827386,28.08945939968795,28.107237538548507,28.12501567740907,28.14279381626963,28.160571955130195,28.17835009399076,28.19612823285132,28.213906371711882,28.231684510572446,28.249462649433003,28.267240788293567,28.285018927154127,28.30279706601469,28.320575204875254,28.338353343735815,28.35613148259638,28.373909621456942,28.3916877603175,28.409465899178063,28.427244038038623,28.445022176899187,28.46280031575975,28.48057845462031,28.498356593480874,28.516134732341435,28.533912871201995,28.55169101006256,28.56946914892312,28.587247287783683,28.605025426644247,28.622803565504807,28.64058170436537,28.65835984322593,28.67613798208649,28.693916120947055,28.711694259807615,28.72947239866818,28.747250537528743,28.765028676389303,28.782806815249867,28.800584954110427,28.818363092970987,28.83614123183155,28.85391937069211,28.871697509552675,28.889475648413235,28.9072537872738,28.925031926134363,28.942810064994923,28.960588203855483,28.978366342716047,28.996144481576607,29.01392262043717,29.03170075929773,29.049478898158295,29.06725703701886,29.08503517587942,29.10281331473998,29.120591453600543,29.138369592461103,29.156147731321667,29.173925870182227,29.19170400904279,29.209482147903355,29.227260286763915,29.245038425624475,29.262816564485036,29.2805947033456,29.298372842206163,29.316150981066723,29.333929119927287,29.35170725878785,29.36948539764841,29.38726353650897,29.40504167536953,29.422819814230095,29.44059795309066,29.45837609195122,29.476154230811783,29.493932369672347,29.511710508532907,29.529488647393467,29.547266786254028,29.56504492511459,29.582823063975155,29.600601202835715,29.61837934169628,29.636157480556843,29.653935619417403,29.671713758277964,29.689491897138524,29.707270035999088,29.72504817485965,29.74282631372021,29.760604452580775,29.77838259144134,29.7961607303019,29.81393886916246,29.83171700802302,29.849495146883584,29.867273285744147,29.885051424604708,29.90282956346527,29.92060770232583,29.938385841186395,29.956163980046956,29.973942118907516,29.99172025776808,30.009498396628643,30.027276535489204,30.045054674349768,30.062832813210328,30.08061095207089,30.09838909093145,30.116167229792012,30.133945368652576,30.151723507513136,30.1695016463737,30.187279785234264,30.205057924094824,30.222836062955388,30.240614201815948,30.258392340676508,30.276170479537072,30.293948618397632,30.311726757258196,30.32950489611876,30.34728303497932,30.365061173839884,30.382839312700444,30.400617451561004,30.418395590421568,30.436173729282128,30.453951868142692,30.471730007003256,30.489508145863816,30.50728628472438,30.525064423584936,30.5428425624455,30.560620701306064,30.578398840166624,30.596176979027188,30.613955117887752,30.631733256748312,30.649511395608876,30.667289534469433,30.685067673329996,30.70284581219056,30.72062395105112,30.738402089911684,30.756180228772248,30.773958367632808,30.791736506493372,30.80951464535393,30.827292784214492,30.845070923075056,30.862849061935616,30.88062720079618,30.898405339656744,30.916183478517304,30.933961617377868,30.951739756238425,30.96951789509899,30.987296033959552,31.005074172820112,31.022852311680676,31.04063045054124,31.0584085894018,31.076186728262364,31.09396486712292,31.111743005983485,31.12952114484405,31.14729928370461,31.165077422565172,31.182855561425733,31.200633700286296,31.21841183914686,31.236189978007417,31.25396811686798,31.271746255728544,31.289524394589105,31.30730253344967,31.32508067231023,31.342858811170792,31.360636950031356,31.378415088891913,31.396193227752477,31.41397136661304,31.4317495054736,31.449527644334164,31.467305783194725,31.48508392205529,31.502862060915852,31.52064019977641,31.538418338636973,31.556196477497533,31.573974616358097,31.59175275521866,31.60953089407922,31.627309032939785,31.64508717180035,31.662865310660905,31.68064344952147,31.69842158838203,31.716199727242593,31.733977866103157,31.751756004963717,31.76953414382428,31.787312282684844,31.8050904215454,31.822868560405965,31.840646699266525,31.85842483812709,31.876202976987653,31.893981115848213,31.911759254708777,31.92953739356934,31.947315532429897,31.96509367129046,31.98287181015102,32.00064994901158,32.018428087872145,32.03620622673271,32.05398436559327,32.07176250445384,32.08954064331439,32.10731878217496,32.12509692103552,32.14287505989608,32.16065319875664,32.178431337617205,32.19620947647777,32.21398761533833,32.23176575419889,32.24954389305945,32.26732203192002,32.285100170780574,32.30287830964114,32.3206564485017,32.338434587362265,32.35621272622283,32.373990865083385,32.39176900394395,32.40954714280451,32.42732528166507,32.44510342052563,32.4628815593862,32.48065969824676,32.498437837107325,32.51621597596788,32.533994114828445,32.55177225368901,32.569550392549566,32.58732853141013,32.60510667027069,32.62288480913126,32.64066294799182,32.65844108685238,32.67621922571294,32.693997364573505,32.71177550343406,32.729553642294626,32.74733178115519,32.76510992001575,32.78288805887632,32.800666197736874,32.81844433659744,32.836222475458,32.85400061431856,32.87177875317912,32.889556892039685,32.90733503090025,32.92511316976081,32.94289130862137,32.96066944748193,32.9784475863425,32.996225725203054,33.01400386406362,33.03178200292418,33.049560141784745,33.06733828064531,33.085116419505866,33.10289455836643,33.12067269722699,33.13845083608755,33.156228974948114,33.17400711380868,33.19178525266924,33.209563391529805,33.22734153039036,33.245119669250926,33.26289780811148,33.280675946972046,33.29845408583261,33.316232224693174,33.33401036355374,33.3517885024143,33.36956664127486,33.38734478013542,33.40512291899598,33.42290105785654,33.440679196717106,33.45845733557767,33.47623547443823,33.4940136132988,33.511791752159354,33.52956989101992,33.547348029880474,33.56512616874104,33.5829043076016,33.600682446462166,33.61846058532273,33.63623872418329,33.65401686304385,33.671795001904414,33.68957314076497,33.707351279625534,33.7251294184861,33.74290755734666,33.760685696207226,33.77846383506779,33.796241973928346,33.81402011278891,33.83179825164947,33.84957639051003,33.867354529370594,33.88513266823116,33.90291080709172,33.92068894595228,33.93846708481284,33.956245223673406,33.97402336253396,33.99180150139453,34.00957964025509,34.027357779115654,34.04513591797622,34.062914056836775,34.08069219569734,34.0984703345579,34.11624847341846,34.13402661227902,34.151804751139586],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381],"y":[0,0.07290330654093172,0.14580661308186343,0.21870991962279515,0.29161322616372687,0.36451653270465856,0.4374198392455903,0.510323145786522,0.5832264523274537,0.6561297588683854,0.7290330654093171,0.8019363719502488,0.8748396784911806,0.9477429850321123,1.020646291573044,1.0935495981139758,1.1664529046549075,1.2393562111958392,1.3122595177367709,1.3851628242777025,1.4580661308186342,1.530969437359566,1.6038727439004976,1.6767760504414293,1.7496793569823612,1.822582663523293,1.8954859700642246,1.9683892766051563,2.041292583146088,2.1141958896870197,2.1870991962279516,2.260002502768883,2.332905809309815,2.4058091158507464,2.4787124223916783,2.55161572893261,2.6245190354735417,2.6974223420144736,2.770325648555405,2.843228955096337,2.9161322616372685,2.9890355681782004,3.061938874719132,3.1348421812600638,3.207745487800995,3.280648794341927,3.3535521008828586,3.4264554074237905,3.4993587139647224,3.5722620205056543,3.645165327046586,3.7180686335875177,3.790971940128449,3.863875246669381,3.9367785532103126,4.009681859751245,4.082585166292176,4.155488472833108,4.228391779374039,4.301295085914972,4.374198392455903,4.447101698996835,4.520005005537766,4.592908312078698,4.66581161861963,4.738714925160561,4.811618231701493,4.884521538242425,4.957424844783357,5.030328151324289,5.10323145786522,5.176134764406152,5.249038070947083,5.321941377488016,5.394844684028947,5.467747990569879,5.54065129711081,5.6135546036517425,5.686457910192674,5.7593612167336055,5.832264523274537,5.905167829815469,5.978071136356401,6.050974442897332,6.123877749438264,6.196781055979196,6.2696843625201275,6.34258766906106,6.41549097560199,6.488394282142923,6.561297588683854,6.634200895224787,6.707104201765717,6.7800075083066496,6.852910814847581,6.925814121388513,6.998717427929445,7.071620734470376,7.144524041011309,7.217427347552239,7.290330654093172,7.363233960634103,7.436137267175035,7.509040573715966,7.581943880256898,7.654847186797831,7.727750493338762,7.800653799879693,7.873557106420625,7.9464604129615575,8.01936371950249,8.09226702604342,8.165170332584353,8.238073639125284,8.310976945666216,8.383880252207147,8.456783558748079,8.52968686528901,8.602590171829943,8.675493478370873,8.748396784911806,8.821300091452738,8.89420339799367,8.9671067045346,9.040010011075532,9.112913317616465,9.185816624157397,9.258719930698327,9.33162323723926,9.404526543780191,9.477429850321123,9.550333156862054,9.623236463402986,9.696139769943919,9.76904307648485,9.84194638302578,9.914849689566713,9.987752996107645,10.060656302648578,10.133559609189508,10.20646291573044,10.279366222271372,10.352269528812304,10.425172835353235,10.498076141894167,10.570979448435098,10.643882754976032,10.716786061516961,10.789689368057894,10.862592674598826,10.935495981139757,11.008399287680689,11.08130259422162,11.154205900762552,11.227109207303485,11.300012513844415,11.372915820385348,11.44581912692628,11.518722433467211,11.591625740008142,11.664529046549074,11.737432353090007,11.810335659630939,11.883238966171868,11.956142272712801,12.029045579253733,12.101948885794664,12.174852192335596,12.247755498876527,12.32065880541746,12.393562111958392,12.466465418499322,12.539368725040255,12.612272031581186,12.68517533812212,12.75807864466305,12.83098195120398,12.903885257744914,12.976788564285846,13.049691870826777,13.122595177367709,13.19549848390864,13.268401790449573,13.341305096990503,13.414208403531434,13.487111710072368,13.560015016613299,13.63291832315423,13.705821629695162,13.778724936236094,13.851628242777027,13.924531549317958,13.99743485585889,14.07033816239982,14.143241468940753,14.216144775481684,14.289048082022617,14.361951388563549,14.434854695104478,14.507758001645412,14.580661308186343,14.653564614727273,14.726467921268206,14.799371227809138,14.87227453435007,14.945177840891002,15.018081147431932,15.090984453972865,15.163887760513797,15.236791067054726,15.309694373595661,15.382597680136591,15.455500986677524,15.528404293218456,15.601307599759386,15.674210906300319,15.74711421284125,15.82001751938218,15.892920825923115,15.965824132464045,16.03872743900498,16.11163074554591,16.18453405208684,16.257437358627772,16.330340665168706,16.403243971709635,16.47614727825057,16.549050584791498,16.62195389133243,16.69485719787336,16.767760504414294,16.840663810955228,16.913567117496157,16.986470424037087,17.05937373057802,17.132277037118953,17.205180343659887,17.278083650200816,17.350986956741746,17.42389026328268,17.496793569823613,17.569696876364542,17.642600182905475,17.715503489446405,17.78840679598734,17.86131010252827,17.9342134090692,18.007116715610135,18.080020022151064,18.152923328691994,18.22582663523293,18.29872994177386,18.371633248314794,18.444536554855723,18.517439861396653,18.590343167937586,18.66324647447852,18.73614978101945,18.809053087560383,18.881956394101312,18.954859700642245,19.02776300718318,19.10066631372411,19.17356962026504,19.24647292680597,19.319376233346905,19.392279539887838,19.465182846428768,19.5380861529697,19.61098945951063,19.68389276605156,19.756796072592497,19.829699379133427,19.902602685674356,19.97550599221529,20.04840929875622,20.121312605297156,20.194215911838086,20.267119218379015,20.34002252491995,20.41292583146088,20.48582913800181,20.558732444542745,20.631635751083675,20.704539057624608,20.777442364165537,20.85034567070647,20.923248977247404,20.996152283788334,21.069055590329263,21.141958896870197,21.21486220341113,21.287765509952063,21.360668816492993,21.433572123033922,21.506475429574856,21.57937873611579,21.65228204265672,21.725185349197652,21.79808865573858,21.870991962279515,21.943895268820444,22.016798575361378,22.08970188190231,22.16260518844324,22.23550849498417,22.308411801525104,22.381315108066037,22.45421841460697,22.5271217211479,22.60002502768883,22.672928334229763,22.745831640770696,22.818734947311626,22.89163825385256,22.96454156039349,23.037444866934422,23.110348173475355,23.183251480016285,23.256154786557218,23.329058093098148,23.401961399639077,23.474864706180014,23.547768012720944,23.620671319261877,23.693574625802807,23.766477932343737,23.83938123888467,23.912284545425603,23.985187851966533,24.058091158507466,24.130994465048396,24.20389777158933,24.276801078130262,24.349704384671192,24.422607691212125,24.495510997753055,24.568414304293988,24.64131761083492,24.71422091737585,24.787124223916784,24.860027530457714,24.932930836998644,25.00583414353958,25.07873745008051,25.15164075662144,25.224544063162373,25.297447369703303,25.37035067624424,25.44325398278517,25.5161572893261,25.589060595867032,25.66196390240796,25.734867208948895,25.80777051548983,25.880673822030758,25.95357712857169,26.02648043511262,26.099383741653554,26.172287048194487,26.245190354735417,26.318093661276347,26.39099696781728,26.463900274358213,26.536803580899146,26.609706887440076,26.682610193981006,26.75551350052194,26.82841680706287,26.901320113603802,26.974223420144735,27.047126726685665,27.120030033226598,27.192933339767528,27.26583664630846,27.338739952849394,27.411643259390324,27.484546565931254,27.557449872472187,27.63035317901312,27.703256485554054,27.776159792094983],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467],"y":[0,0.005025159383663162,0.010050318767326325,0.01507547815098949,0.02010063753465265,0.02512579691831581,0.03015095630197898,0.03517611568564214,0.0402012750693053,0.045226434452968464,0.05025159383663162,0.05527675322029479,0.06030191260395796,0.06532707198762111,0.07035223137128428,0.07537739075494744,0.0804025501386106,0.08542770952227377,0.09045286890593693,0.09547802828960009,0.10050318767326324,0.10552834705692643,0.11055350644058957,0.11557866582425273,0.12060382520791592,0.12562898459157906,0.13065414397524222,0.1356793033589054,0.14070446274256856,0.14572962212623172,0.15075478150989488,0.15577994089355804,0.1608051002772212,0.16583025966088435,0.17085541904454754,0.1758805784282107,0.18090573781187386,0.18593089719553701,0.19095605657920017,0.19598121596286336,0.2010063753465265,0.20603153473018967,0.21105669411385286,0.216081853497516,0.22110701288117915,0.22613217226484233,0.23115733164850547,0.23618249103216865,0.24120765041583184,0.24623280979949497,0.2512579691831581,0.25628312856682134,0.26130828795048444,0.26633344733414765,0.2713586067178108,0.2763837661014739,0.28140892548513713,0.2864340848688003,0.29145924425246345,0.2964844036361266,0.30150956301978976,0.3065347224034529,0.3115598817871161,0.31658504117077924,0.3216102005544424,0.3266353599381056,0.3316605193217687,0.3366856787054319,0.3417108380890951,0.3467359974727582,0.3517611568564214,0.35678631624008456,0.3618114756237477,0.36683663500741087,0.37186179439107403,0.3768869537747372,0.38191211315840035,0.38693727254206356,0.3919624319257267,0.3969875913093899,0.402012750693053,0.4070379100767162,0.41206306946037935,0.41708822884404245,0.4221133882277057,0.4271385476113688,0.432163706995032,0.4371888663786952,0.4422140257623583,0.44723918514602146,0.45226434452968467,0.45728950391334783,0.46231466329701093,0.46733982268067414,0.4723649820643373,0.47739014144800046,0.4824153008316637,0.4874404602153268,0.49246561959898993,0.49749077898265315,0.5025159383663163,0.5075410977499795,0.5125662571336427,0.5175914165173058,0.5226165759009689,0.5276417352846321,0.5326668946682953,0.5376920540519584,0.5427172134356216,0.5477423728192847,0.5527675322029478,0.5577926915866112,0.5628178509702743,0.5678430103539374,0.5728681697376006,0.5778933291212637,0.5829184885049269,0.5879436478885901,0.5929688072722532,0.5979939666559163,0.6030191260395795,0.6080442854232427,0.6130694448069058,0.6180946041905689,0.6231197635742322,0.6281449229578954,0.6331700823415585,0.6381952417252217,0.6432204011088848,0.648245560492548,0.6532707198762112,0.6582958792598743,0.6633210386435374,0.6683461980272006,0.6733713574108638,0.678396516794527,0.6834216761781902,0.6884468355618533,0.6934719949455164,0.6984971543291797,0.7035223137128428,0.7085474730965059,0.7135726324801691,0.7185977918638322,0.7236229512474954,0.7286481106311586,0.7336732700148217,0.7386984293984848,0.7437235887821481,0.7487487481658113,0.7537739075494744,0.7587990669331376,0.7638242263168007,0.7688493857004639,0.7738745450841271,0.7788997044677903,0.7839248638514534,0.7889500232351165,0.7939751826187798,0.7990003420024429,0.804025501386106,0.8090506607697691,0.8140758201534324,0.8191009795370956,0.8241261389207587,0.8291512983044218,0.8341764576880849,0.8392016170717481,0.8442267764554114,0.8492519358390745,0.8542770952227376,0.8593022546064008,0.864327413990064,0.8693525733737271,0.8743777327573904,0.8794028921410535,0.8844280515247166,0.8894532109083798,0.8944783702920429,0.899503529675706,0.9045286890593693,0.9095538484430324,0.9145790078266957,0.9196041672103588,0.9246293265940219,0.9296544859776851,0.9346796453613483,0.9397048047450115,0.9447299641286746,0.9497551235123377,0.9547802828960009,0.959805442279664,0.9648306016633273,0.9698557610469904,0.9748809204306536,0.9799060798143168,0.9849312391979799,0.989956398581643,0.9949815579653063,1.0000067173489695,1.0050318767326325,1.0100570361162957,1.015082195499959,1.020107354883622,1.0251325142672854,1.0301576736509483,1.0351828330346116,1.0402079924182748,1.0452331518019378,1.050258311185601,1.0552834705692642,1.0603086299529274,1.0653337893365906,1.0703589487202536,1.0753841081039168,1.0804092674875798,1.0854344268712433,1.0904595862549065,1.0954847456385695,1.1005099050222327,1.1055350644058957,1.1105602237895589,1.1155853831732223,1.1206105425568853,1.1256357019405485,1.1306608613242115,1.1356860207078747,1.140711180091538,1.1457363394752011,1.1507614988588644,1.1557866582425274,1.1608118176261906,1.1658369770098538,1.1708621363935168,1.1758872957771802,1.1809124551608432,1.1859376145445064,1.1909627739281696,1.1959879333118326,1.2010130926954958,1.206038252079159,1.2110634114628223,1.2160885708464855,1.2211137302301485,1.2261388896138117,1.231164048997475,1.2361892083811379,1.2412143677648013,1.2462395271484643,1.2512646865321275,1.2562898459157907,1.2613150052994537,1.266340164683117,1.2713653240667802,1.2763904834504434,1.2814156428341066,1.2864408022177696,1.2914659616014328,1.296491120985096,1.3015162803687592,1.3065414397524224,1.3115665991360854,1.3165917585197486,1.3216169179034118,1.3266420772870748,1.3316672366707383,1.3366923960544013,1.3417175554380645,1.3467427148217277,1.3517678742053907,1.356793033589054,1.3618181929727171,1.3668433523563803,1.3718685117400435,1.3768936711237065,1.3819188305073697,1.3869439898910327,1.3919691492746962,1.3969943086583594,1.4020194680420224,1.4070446274256856,1.4120697868093486,1.4170949461930118,1.4221201055766752,1.4271452649603382,1.4321704243440014,1.4371955837276644,1.4422207431113276,1.4472459024949909,1.452271061878654,1.4572962212623173,1.4623213806459803,1.4673465400296435,1.4723716994133067,1.4773968587969697,1.4824220181806331,1.4874471775642961,1.4924723369479593,1.4974974963316225,1.5025226557152855,1.5075478150989488,1.512572974482612,1.5175981338662752,1.5226232932499384,1.5276484526336014,1.5326736120172646,1.5376987714009278,1.542723930784591,1.5477490901682542,1.5527742495519172,1.5577994089355807,1.5628245683192437,1.5678497277029069,1.57287488708657,1.577900046470233,1.5829252058538963,1.5879503652375595,1.5929755246212225,1.5980006840048857,1.603025843388549,1.608051002772212,1.6130761621558751,1.6181013215395381,1.6231264809232018,1.6281516403068648,1.633176799690528,1.6382019590741912,1.6432271184578542,1.6482522778415174,1.6532774372251806,1.6583025966088436,1.6633277559925068,1.6683529153761698,1.673378074759833,1.6784032341434962,1.6834283935271597,1.6884535529108229,1.6934787122944859,1.698503871678149,1.7035290310618123,1.7085541904454753,1.7135793498291385,1.7186045092128015,1.7236296685964647,1.728654827980128,1.733679987363791,1.7387051467474541,1.7437303061311176,1.7487554655147808,1.753780624898444,1.758805784282107,1.7638309436657702,1.7688561030494332,1.7738812624330964,1.7789064218167596,1.7839315812004226,1.7889567405840858,1.793981899967749,1.799007059351412,1.8040322187350755,1.8090573781187387,1.814082537502402,1.8191076968860649,1.824132856269728,1.8291580156533913,1.8341831750370543,1.8392083344207175,1.8442334938043807,1.8492586531880437,1.854283812571707,1.8593089719553701,1.8643341313390336,1.8693592907226966,1.8743844501063598,1.879409609490023,1.884434768873686,1.8894599282573492,1.8944850876410124,1.8995102470246754,1.9045354064083386,1.9095605657920018,1.9145857251756648,1.919610884559328,1.9246360439429915,1.9296612033266547,1.9346863627103177,1.939711522093981,1.9447366814776441,1.949761840861307,1.9547870002449703,1.9598121596286335,1.9648373190122965,1.9698624783959597,1.9748876377796227,1.979912797163286,1.9849379565469494,1.9899631159306126,1.9949882753142758,2.000013434697939,2.005038594081602,2.010063753465265,2.0150889128489284,2.0201140722325914,2.0251392316162544,2.030164390999918,2.035189550383581,2.040214709767244,2.0452398691509073,2.0502650285345707,2.0552901879182337,2.0603153473018967,2.06534050668556,2.070365666069223,2.075390825452886,2.0804159848365495,2.0854411442202125,2.0904663036038755,2.095491462987539,2.100516622371202,2.105541781754865,2.1105669411385284,2.115592100522192,2.120617259905855,2.125642419289518,2.1306675786731812,2.1356927380568442,2.1407178974405072,2.14574305682417,2.1507682162078336,2.1557933755914966,2.1608185349751596,2.165843694358823,2.1708688537424865,2.1758940131261495,2.180919172509813,2.185944331893476,2.190969491277139,2.195994650660802,2.2010198100444653,2.2060449694281283,2.2110701288117913,2.2160952881954548,2.2211204475791178,2.2261456069627807,2.2311707663464446,2.2361959257301076,2.2412210851137706,2.2462462444974336,2.251271403881097,2.25629656326476,2.261321722648423,2.2663468820320865,2.2713720414157494,2.2763972007994124,2.281422360183076,2.286447519566739,2.2914726789504023,2.2964978383340653,2.3015229977177287,2.3065481571013917,2.3115733164850547,2.316598475868718,2.321623635252381,2.326648794636044,2.3316739540197076,2.3366991134033706,2.3417242727870335,2.346749432170697,2.3517745915543604,2.3567997509380234,2.3618249103216864,2.36685006970535,2.371875229089013,2.376900388472676,2.3819255478563393,2.3869507072400022,2.3919758666236652,2.3970010260073287,2.4020261853909917,2.4070513447746547,2.412076504158318,2.4171016635419815,2.4221268229256445,2.4271519823093075,2.432177141692971,2.437202301076634,2.442227460460297,2.4472526198439604,2.4522777792276234,2.4573029386112863,2.46232809799495,2.4673532573786128,2.4723784167622758,2.477403576145939,2.4824287355296026,2.4874538949132656,2.4924790542969286,2.497504213680592,2.502529373064255,2.507554532447918,2.5125796918315815,2.5176048512152445,2.5226300105989075,2.527655169982571,2.532680329366234,2.5377054887498973,2.5427306481335603,2.5477558075172237,2.5527809669008867,2.5578061262845497,2.562831285668213,2.567856445051876,2.572881604435539,2.5779067638192026,2.5829319232028656,2.5879570825865286,2.592982241970192,2.5980074013538554,2.6030325607375184,2.6080577201211814,2.613082879504845,2.618108038888508,2.623133198272171,2.6281583576558343,2.6331835170394973,2.6382086764231603,2.6432338358068237,2.6482589951904867,2.6532841545741497,2.658309313957813,2.6633344733414765,2.6683596327251395,2.6733847921088025,2.678409951492466,2.683435110876129,2.688460270259792,2.6934854296434554,2.6985105890271184,2.7035357484107814,2.708560907794445,2.713586067178108,2.7186112265617712,2.7236363859454342,2.7286615453290977,2.7336867047127607,2.7387118640964236,2.743737023480087,2.74876218286375,2.753787342247413,2.758812501631076,2.7638376610147395,2.7688628203984025,2.7738879797820655,2.7789131391657294,2.7839382985493923,2.7889634579330553,2.7939886173167188,2.7990137767003818,2.8040389360840448,2.8090640954677077,2.814089254851371,2.819114414235034,2.824139573618697,2.8291647330023606,2.8341898923860236,2.8392150517696866,2.8442402111533505,2.8492653705370135,2.8542905299206764,2.8593156893043394,2.864340848688003,2.869366008071666,2.874391167455329,2.8794163268389923,2.8844414862226553,2.8894666456063183,2.8944918049899817,2.8995169643736447,2.904542123757308,2.909567283140971,2.9145924425246346,2.9196176019082976,2.9246427612919605,2.929667920675624,2.934693080059287,2.93971823944295,2.9447433988266134,2.9497685582102764,2.9547937175939394,2.959818876977603,2.9648440363612663,2.9698691957449292,2.9748943551285922,2.9799195145122557,2.9849446738959187,2.9899698332795817,2.994994992663245,3.000020152046908,3.005045311430571,3.0100704708142345,3.0150956301978975,3.0201207895815605,3.025145948965224,3.0301711083488874,3.0351962677325504,3.0402214271162133,3.045246586499877,3.0502717458835398,3.0552969052672028,3.060322064650866,3.065347224034529,3.070372383418192,3.0753975428018556,3.0804227021855186,3.085447861569182,3.090473020952845,3.0954981803365085,3.1005233397201715,3.1055484991038345,3.110573658487498,3.1155988178711613,3.120623977254824,3.1256491366384873,3.1306742960221503,3.1356994554058137,3.1407246147894767,3.14574977417314,3.1507749335568027,3.155800092940466,3.160825252324129,3.1658504117077926,3.1708755710914556,3.175900730475119,3.1809258898587824,3.185951049242445,3.1909762086261084,3.1960013680097714,3.201026527393435,3.206051686777098,3.2110768461607613,3.216102005544424,3.2211271649280873,3.2261523243117503,3.2311774836954137,3.2362026430790762,3.24122780246274,3.2462529618464036,3.251278121230066,3.2563032806137295,3.2613284399973925,3.266353599381056,3.271378758764719,3.2764039181483824,3.281429077532045,3.2864542369157084,3.2914793962993714,3.296504555683035,3.3015297150666982,3.3065548744503612,3.3115800338340247,3.316605193217687,3.3216303526013506,3.3266555119850136,3.331680671368677,3.3367058307523396,3.3417309901360035,3.346756149519666,3.3517813089033295,3.3568064682869925,3.361831627670656,3.3668567870543193,3.371881946437982,3.3769071058216458,3.3819322652053083,3.3869574245889718,3.3919825839726347,3.397007743356298,3.4020329027399607,3.4070580621236246,3.412083221507287,3.4171083808909506,3.422133540274614,3.427158699658277,3.4321838590419405,3.437209018425603,3.442234177809267,3.4472593371929294,3.452284496576593,3.457309655960256,3.4623348153439193,3.467359974727582,3.4723851341112453,3.4774102934949083,3.4824354528785717,3.487460612262235,3.492485771645898,3.4975109310295616,3.502536090413224,3.507561249796888,3.5125864091805505,3.517611568564214,3.522636727947877,3.5276618873315404,3.532687046715203,3.5377122060988664,3.5427373654825303,3.547762524866193,3.5527876842498562,3.5578128436335192,3.5628380030171827,3.5678631624008452,3.5728883217845087,3.5779134811681716,3.582938640551835,3.587963799935498,3.5929889593191615,3.598014118702824,3.6030392780864875,3.608064437470151,3.613089596853814,3.6181147562374774,3.6231399156211404,3.628165075004804,3.6331902343884663,3.6382153937721298,3.6432405531557928,3.648265712539456,3.653290871923119,3.6583160313067826,3.663341190690445,3.6683663500741086,3.673391509457772,3.678416668841435,3.6834418282250985,3.6884669876087615,3.693492146992425,3.6985173063760874,3.703542465759751,3.708567625143414,3.7135927845270773,3.7186179439107403,3.7236431032944037,3.728668262678067,3.7336934220617297,3.738718581445393,3.743743740829056,3.7487689002127196,3.7537940595963826,3.758819218980046,3.7638443783637086,3.768869537747372,3.773894697131035,3.7789198565146984,3.7839450158983614,3.788970175282025,3.7939953346656883,3.799020494049351,3.8040456534330143,3.8090708128166773,3.8140959722003407,3.8191211315840037,3.824146290967667,3.8291714503513297,3.834196609734993,3.839221769118656,3.8442469285023195,3.849272087885983,3.854297247269646,3.8593224066533094,3.864347566036972,3.8693727254206354,3.8743978848042984,3.879423044187962,3.884448203571625,3.8894733629552882,3.8944985223389508,3.899523681722614,3.904548841106277,3.9095740004899406,3.914599159873604,3.919624319257267,3.9246494786409305,3.929674638024593,3.9346997974082565,3.9397249567919195,3.944750116175583,3.9497752755592455,3.9548004349429093,3.959825594326572,3.9648507537102353,3.9698759130938988,3.9749010724775617,3.979926231861225,3.9849513912448877,3.9899765506285516,3.995001710012214,4.000026869395878,4.005052028779541,4.010077188163204,4.015102347546867,4.02012750693053,4.025152666314193,4.030177825697857,4.03520298508152,4.040228144465183,4.045253303848846,4.050278463232509,4.055303622616172,4.060328781999836,4.065353941383499,4.070379100767162,4.075404260150825,4.080429419534488,4.085454578918151,4.0904797383018145,4.095504897685478,4.100530057069141,4.105555216452804,4.110580375836467,4.11560553522013,4.120630694603793,4.125655853987457,4.13068101337112,4.135706172754783,4.140731332138446,4.145756491522109,4.150781650905772,4.155806810289436,4.160831969673099,4.1658571290567625,4.170882288440425,4.1759074478240885,4.180932607207751,4.1859577665914145,4.190982925975078,4.196008085358741,4.201033244742404,4.206058404126067,4.21108356350973,4.216108722893393,4.221133882277057,4.22615904166072,4.231184201044384,4.236209360428046,4.24123451981171,4.246259679195372,4.251284838579036,4.256309997962698,4.2613351573463625,4.266360316730025,4.2713854761136885,4.276410635497352,4.2814357948810144,4.286460954264678,4.29148611364834,4.296511273032005,4.301536432415667,4.306561591799331,4.311586751182993,4.316611910566657,4.321637069950319,4.326662229333984,4.331687388717646,4.33671254810131,4.341737707484973,4.3467628668686356,4.351788026252299,4.3568131856359615,4.361838345019626,4.366863504403288,4.371888663786952,4.376913823170614,4.381938982554278,4.38696414193794,4.391989301321604,4.397014460705268,4.402039620088931,4.407064779472594,4.412089938856257,4.41711509823992,4.422140257623583,4.427165417007247,4.4321905763909095,4.437215735774573,4.4422408951582355,4.447266054541899,4.4522912139255615,4.457316373309225,4.462341532692889,4.467366692076552,4.472391851460215,4.477417010843878,4.482442170227541,4.487467329611204,4.492492488994867,4.497517648378531,4.502542807762194,4.507567967145857,4.51259312652952,4.517618285913183,4.522643445296846,4.5276686046805095,4.532693764064173,4.537718923447836,4.542744082831499,4.547769242215162,4.552794401598825,4.557819560982488,4.562844720366152,4.567869879749815,4.572895039133478,4.577920198517141,4.582945357900805,4.587970517284467,4.592995676668131,4.598020836051794,4.6030459954354574,4.60807115481912,4.613096314202783,4.618121473586446,4.623146632970109,4.628171792353773,4.633196951737436,4.638222111121099,4.643247270504762,4.648272429888426,4.653297589272088,4.658322748655752,4.663347908039415,4.668373067423079,4.673398226806741,4.6784233861904045,4.683448545574067,4.6884737049577305,4.693498864341394,4.698524023725057,4.703549183108721,4.708574342492383,4.713599501876047,4.718624661259709,4.723649820643373,4.728674980027036,4.7337001394107,4.738725298794362,4.743750458178026,4.748775617561688,4.753800776945352,4.758825936329015,4.7638510957126785,4.768876255096342,4.7739014144800045,4.778926573863668,4.7839517332473305,4.788976892630994,4.794002052014657,4.799027211398321,4.804052370781983,4.809077530165647,4.814102689549309,4.819127848932973,4.824153008316636,4.8291781677003,4.834203327083963,4.839228486467626,4.844253645851289,4.849278805234952,4.854303964618615,4.8593291240022785,4.864354283385942,4.869379442769604,4.874404602153268,4.87942976153693,4.884454920920594,4.889480080304257,4.894505239687921,4.899530399071584,4.904555558455247,4.90958071783891,4.914605877222573,4.919631036606236,4.9246561959899,4.929681355373563,4.9347065147572255,4.939731674140889,4.9447568335245515,4.949781992908215,4.954807152291878,4.959832311675542,4.964857471059205,4.969882630442868,4.974907789826531,4.979932949210194,4.984958108593857,4.989983267977521,4.995008427361184,5.000033586744847,5.00505874612851,5.0100839055121735,5.015109064895836,5.0201342242794995,5.025159383663163,5.030184543046826,5.035209702430489,5.040234861814152,5.045260021197815,5.050285180581478,5.055310339965142,5.060335499348805,5.065360658732468,5.070385818116131,5.075410977499795,5.080436136883457,5.085461296267121,5.090486455650784,5.0955116150344475,5.10053677441811,5.1055619338017735,5.110587093185436,5.1156122525690995,5.120637411952763,5.125662571336426,5.13068773072009,5.135712890103752,5.140738049487416,5.145763208871078,5.150788368254742,5.155813527638405,5.160838687022069,5.165863846405731,5.170889005789395,5.175914165173057,5.180939324556721,5.185964483940384,5.190989643324047,5.196014802707711,5.201039962091373,5.206065121475037,5.211090280858699,5.216115440242363,5.221140599626026,5.22616575900969,5.231190918393352,5.236216077777016,5.241241237160678,5.246266396544342,5.251291555928005,5.2563167153116686,5.261341874695332,5.2663670340789945,5.271392193462658,5.2764173528463205,5.281442512229984,5.286467671613647,5.291492830997311,5.296517990380973,5.301543149764637,5.306568309148299,5.311593468531963,5.316618627915626,5.32164378729929,5.326668946682953,5.331694106066616,5.336719265450279,5.341744424833942,5.346769584217605,5.3517947436012685,5.356819902984932,5.3618450623685945,5.366870221752258,5.3718953811359205,5.376920540519584,5.381945699903247,5.386970859286911,5.391996018670574,5.397021178054237,5.4020463374379,5.407071496821563,5.412096656205226,5.41712181558889,5.422146974972553,5.427172134356216,5.432197293739879,5.4372224531235425,5.442247612507205,5.4472727718908684,5.452297931274532,5.457323090658195,5.462348250041858,5.467373409425521,5.472398568809184,5.477423728192847,5.48244888757651,5.487474046960174,5.492499206343837,5.4975243657275,5.502549525111164,5.507574684494826,5.51259984387849,5.517625003262152,5.522650162645816,5.527675322029479,5.532700481413142,5.537725640796805,5.542750800180468,5.547775959564131,5.552801118947795,5.557826278331459,5.562851437715121,5.567876597098785,5.572901756482447,5.577926915866111,5.582952075249773,5.5879772346334375,5.5930023940171,5.5980275534007635,5.603052712784426,5.6080778721680895,5.613103031551752,5.6181281909354155,5.62315335031908,5.628178509702742,5.633203669086406,5.638228828470068,5.643253987853732,5.648279147237394,5.653304306621059,5.658329466004721,5.663354625388385,5.668379784772047,5.673404944155711,5.678430103539373,5.683455262923037,5.688480422306701,5.6935055816903635,5.698530741074027,5.7035559004576895,5.708581059841353,5.713606219225015,5.718631378608679,5.723656537992342,5.728681697376006,5.733706856759668,5.738732016143332,5.743757175526995,5.748782334910658,5.753807494294321,5.758832653677985,5.763857813061648,5.768882972445311,5.773908131828974,5.7789332912126365,5.7839584505963,5.788983609979963,5.794008769363627,5.799033928747289,5.804059088130953,5.809084247514616,5.814109406898279,5.819134566281942,5.824159725665606,5.829184885049269,5.834210044432932,5.839235203816595,5.844260363200258,5.849285522583921,5.8543106819675845,5.859335841351248,5.864361000734911,5.869386160118574,5.874411319502237,5.8794364788859,5.884461638269563,5.889486797653227,5.89451195703689,5.899537116420553,5.904562275804216,5.909587435187879,5.914612594571542,5.919637753955206,5.924662913338869,5.9296880727225325,5.934713232106195,5.9397383914898585,5.944763550873521,5.9497887102571845,5.954813869640848,5.959839029024511,5.964864188408174,5.969889347791837,5.9749145071755,5.979939666559163,5.984964825942827,5.98998998532649,5.995015144710154,6.000040304093816,6.00506546347748,6.010090622861142,6.015115782244806,6.020140941628469,6.0251661010121325,6.030191260395795,6.035216419779458,6.040241579163121,6.045266738546784,6.050291897930448,6.055317057314111,6.060342216697775,6.065367376081437,6.070392535465101,6.075417694848763,6.080442854232427,6.08546801361609,6.090493172999754,6.095518332383416,6.1005434917670796,6.105568651150742,6.1105938105344055,6.115618969918069,6.120644129301732,6.125669288685396,6.130694448069058,6.135719607452722,6.140744766836384,6.145769926220048,6.150795085603711,6.155820244987375,6.160845404371037,6.165870563754701,6.170895723138364,6.175920882522027,6.18094604190569,6.1859712012893535,6.190996360673017,6.1960215200566795,6.201046679440343,6.206071838824006,6.211096998207669,6.216122157591332,6.221147316974996,6.226172476358659,6.231197635742323,6.236222795125984,6.241247954509648,6.246273113893311,6.251298273276975,6.256323432660637,6.261348592044301,6.266373751427964,6.2713989108116275,6.27642407019529,6.2814492295789535,6.286474388962617,6.29149954834628,6.296524707729944,6.301549867113605,6.306575026497269,6.311600185880932,6.316625345264596,6.321650504648258,6.326675664031922,6.331700823415585,6.336725982799249,6.341751142182911,6.346776301566575,6.351801460950238,6.3568266203339014,6.361851779717565,6.3668769391012265,6.37190209848489,6.376927257868553,6.381952417252217,6.386977576635879,6.392002736019543,6.397027895403206,6.40205305478687,6.407078214170532,6.412103373554196,6.417128532937859,6.4221536923215226,6.427178851705186,6.432204011088848,6.437229170472511,6.4422543298561745,6.447279489239838,6.4523046486235005,6.457329808007164,6.462354967390827,6.467380126774491,6.4724052861581525,6.477430445541817,6.48245560492548,6.487480764309144,6.492505923692807,6.497531083076469,6.502556242460132,6.507581401843796,6.512606561227459,6.517631720611122,6.522656879994785,6.5276820393784485,6.532707198762112,6.537732358145775,6.542757517529438,6.547782676913101,6.552807836296765,6.557832995680428,6.56285815506409,6.567883314447753,6.572908473831417,6.57793363321508,6.582958792598743,6.587983951982406,6.59300911136607,6.598034270749733,6.6030594301333965,6.608084589517058,6.6131097489007225,6.618134908284386,6.623160067668049,6.628185227051711,6.633210386435374,6.638235545819038,6.643260705202701,6.648285864586364,6.653311023970027,6.658336183353691,6.663361342737354,6.668386502121018,6.673411661504679,6.678436820888344,6.683461980272007,6.68848713965567,6.693512299039332,6.6985374584229955,6.703562617806659,6.708587777190322,6.713612936573985,6.718638095957648,6.723663255341312,6.728688414724975,6.733713574108639,6.7387387334923,6.743763892875964,6.748789052259628,6.7538142116432915,6.758839371026953,6.763864530410617,6.76888968979428,6.7739148491779435,6.778940008561607,6.7839651679452695,6.788990327328933,6.794015486712596,6.79904064609626,6.8040658054799215,6.809090964863585,6.814116124247249,6.819141283630913,6.824166443014574,6.829191602398238,6.834216761781901,6.839241921165565,6.844267080549228,6.849292239932891,6.854317399316554,6.8593425587002175,6.864367718083881,6.869392877467543,6.874418036851206,6.87944319623487,6.884468355618534,6.889493515002195,6.894518674385859,6.899543833769522,6.904568993153186,6.909594152536849,6.914619311920512,6.919644471304175,6.924669630687839,6.929694790071502,6.934719949455164,6.939745108838827,6.9447702682224906,6.949795427606155,6.9548205869898165,6.95984574637348,6.964870905757143,6.969896065140807,6.97492122452447,6.979946383908133,6.984971543291796,6.98999670267546,6.995021862059123,7.000047021442785,7.005072180826448,7.010097340210112,7.015122499593776,7.020147658977438,7.025172818361101,7.0301979777447645,7.035223137128428,7.040248296512091,7.045273455895754,7.050298615279417,7.055323774663081,7.060348934046744,7.065374093430406,7.070399252814069,7.075424412197733,7.080449571581396,7.0854747309650605,7.090499890348722,7.095525049732386,7.100550209116049,7.1055753684997125,7.110600527883375,7.1156256872670385,7.120650846650702,7.125676006034365,7.130701165418027,7.1357263248016904,7.140751484185354,7.145776643569017,7.150801802952682,7.155826962336343,7.160852121720007,7.16587728110367,7.170902440487334,7.175927599870996,7.18095275925466,7.185977918638323,7.1910030780219865,7.196028237405648,7.2010533967893116,7.206078556172975,7.211103715556638,7.216128874940302,7.221154034323964,7.226179193707628,7.231204353091291,7.236229512474955,7.241254671858617,7.246279831242281,7.251304990625944,7.256330150009608,7.261355309393269,7.266380468776933,7.271405628160596,7.2764307875442595,7.281455946927923,7.2864811063115855,7.291506265695249,7.296531425078912,7.301556584462576,7.306581743846238,7.311606903229902,7.316632062613565,7.321657221997229,7.32668238138089,7.331707540764554,7.336732700148217,7.341757859531881,7.346783018915544,7.351808178299207,7.35683333768287,7.3618584970665335,7.366883656450197,7.3719088158338595,7.376933975217523,7.381959134601186,7.38698429398485,7.392009453368513,7.397034612752175,7.402059772135838,7.407084931519502,7.412110090903165,7.417135250286828,7.422160409670491,7.427185569054155,7.432210728437818,7.437235887821481,7.442261047205144,7.4472862065888075,7.452311365972471,7.457336525356134,7.462361684739796,7.467386844123459,7.472412003507123,7.477437162890786,7.482462322274449,7.487487481658112,7.492512641041776,7.497537800425439,7.502562959809102,7.507588119192765,7.512613278576429,7.517638437960092,7.5226635973437554,7.527688756727417,7.5327139161110805,7.537739075494744,7.542764234878407,7.54778939426207,7.552814553645733,7.557839713029397,7.56286487241306,7.567890031796723,7.572915191180386,7.57794035056405,7.582965509947713,7.587990669331377,7.593015828715038,7.598040988098702,7.603066147482365,7.6080913068660285,7.613116466249691,7.6181416256333545,7.623166785017018,7.628191944400681,7.633217103784344,7.638242263168007,7.643267422551671,7.648292581935334,7.653317741318998,7.658342900702659,7.663368060086323,7.668393219469986,7.67341837885365,7.678443538237312,7.683468697620976,7.688493857004639,7.6935190163883025,7.698544175771966,7.7035693351556285,7.708594494539292,7.713619653922955,7.718644813306619,7.72366997269028,7.728695132073944,7.733720291457607,7.738745450841271,7.743770610224933,7.748795769608597,7.75382092899226,7.758846088375924,7.763871247759587,7.76889640714325,7.773921566526913,7.7789467259105765,7.78397188529424,7.7889970446779015,7.794022204061565,7.799047363445228,7.804072522828892,7.809097682212554,7.814122841596218,7.819148000979881,7.824173160363545,7.829198319747208,7.83422347913087,7.839248638514534,7.844273797898198,7.849298957281861,7.854324116665523,7.859349276049186,7.8643744354328495,7.869399594816513,7.8744247542001755,7.879449913583839,7.884475072967502,7.889500232351166,7.894525391734829,7.899550551118491,7.904575710502155,7.909600869885819,7.914626029269482,7.919651188653144,7.924676348036807,7.929701507420471,7.934726666804134,7.9397518261877975,7.94477698557146,7.9498021449551235,7.954827304338787,7.95985246372245,7.964877623106112,7.9699027824897755,7.97492794187344,7.979953101257103,7.984978260640765,7.990003420024428,7.995028579408092,8.000053738791756,8.00507889817542,8.010104057559081,8.015129216942745,8.020154376326408,8.025179535710071,8.030204695093733,8.035229854477397,8.04025501386106,8.045280173244723,8.050305332628387,8.05533049201205,8.060355651395714,8.065380810779377,8.07040597016304,8.075431129546702,8.080456288930366,8.08548144831403,8.090506607697693,8.095531767081354,8.100556926465018,8.105582085848681,8.110607245232345,8.115632404616008,8.120657563999671,8.125682723383335,8.130707882766998,8.135733042150662,8.140758201534323,8.145783360917987,8.15080852030165,8.155833679685314,8.160858839068975,8.165883998452639,8.170909157836302,8.175934317219966,8.180959476603629,8.185984635987293,8.191009795370956,8.19603495475462,8.201060114138283,8.206085273521945,8.211110432905608,8.216135592289271,8.221160751672935,8.226185911056596,8.23121107044026,8.236236229823923,8.241261389207587,8.24628654859125,8.251311707974914,8.256336867358577,8.26136202674224,8.266387186125904,8.271412345509566,8.276437504893229,8.281462664276892,8.286487823660556,8.291512983044218,8.296538142427881,8.301563301811544,8.306588461195208,8.311613620578871,8.316638779962535,8.321663939346198,8.326689098729862,8.331714258113525,8.336739417497187,8.34176457688085,8.346789736264514,8.351814895648177,8.356840055031839,8.361865214415502,8.366890373799166,8.371915533182829,8.376940692566492,8.381965851950156,8.38699101133382,8.392016170717483,8.397041330101146,8.402066489484808,8.407091648868471,8.412116808252135,8.417141967635798,8.42216712701946,8.427192286403123,8.432217445786787,8.43724260517045,8.442267764554114,8.447292923937775,8.45231808332144,8.457343242705104,8.462368402088767,8.467393561472429,8.472418720856092,8.477443880239756,8.48246903962342,8.487494199007081,8.492519358390744,8.497544517774408,8.502569677158071,8.507594836541735,8.512619995925396,8.517645155309062,8.522670314692725,8.527695474076388,8.53272063346005,8.537745792843713,8.542770952227377,8.54779611161104,8.552821270994704,8.557846430378365,8.562871589762029,8.567896749145692,8.572921908529356,8.577947067913017,8.58297222729668,8.587997386680346,8.59302254606401,8.598047705447671,8.603072864831335,8.608098024214998,8.613123183598661,8.618148342982325,8.623173502365987,8.62819866174965,8.633223821133313,8.638248980516977,8.643274139900639,8.648299299284302,8.653324458667967,8.65834961805163,8.663374777435292,8.668399936818956,8.67342509620262,8.678450255586283,8.683475414969946,8.688500574353608,8.693525733737271,8.698550893120935,8.703576052504598,8.70860121188826,8.713626371271923,8.718651530655588,8.723676690039252,8.728701849422913,8.733727008806577,8.73875216819024,8.743777327573904,8.748802486957567,8.753827646341229,8.758852805724892,8.763877965108556,8.768903124492219,8.77392828387588,8.778953443259544,8.783978602643208,8.789003762026873,8.794028921410536,8.799054080794198,8.804079240177861,8.809104399561525,8.814129558945188,8.81915471832885,8.824179877712513,8.829205037096177,8.83423019647984,8.839255355863502,8.844280515247165,8.849305674630829,8.854330834014494,8.859355993398157,8.864381152781819,8.869406312165482,8.874431471549146,8.87945663093281,8.884481790316471,8.889506949700134,8.894532109083798,8.899557268467461,8.904582427851123,8.909607587234786,8.91463274661845,8.919657906002113,8.924683065385778,8.92970822476944,8.934733384153104,8.939758543536767,8.94478370292043,8.949808862304092,8.954834021687756,8.959859181071419,8.964884340455082,8.969909499838744,8.974934659222408,8.979959818606071,8.984984977989734,8.9900101373734,8.995035296757061,9.000060456140725,9.005085615524388,9.010110774908052,9.015135934291713,9.020161093675377,9.02518625305904,9.030211412442704,9.035236571826365,9.040261731210029,9.045286890593692,9.050312049977355,9.055337209361019,9.060362368744682,9.065387528128346,9.07041268751201,9.075437846895673,9.080463006279334,9.085488165662998,9.090513325046661,9.095538484430325,9.100563643813988,9.10558880319765,9.110613962581313,9.115639121964977,9.12066428134864,9.125689440732303,9.130714600115967,9.13573975949963,9.140764918883294,9.145790078266955,9.150815237650619,9.155840397034282,9.160865556417946,9.16589071580161,9.17091587518527,9.175941034568934,9.180966193952598,9.185991353336261,9.191016512719925,9.196041672103588,9.201066831487251,9.206091990870915,9.211117150254577,9.21614230963824,9.221167469021903,9.226192628405567,9.23121778778923,9.236242947172892,9.241268106556555,9.246293265940219,9.251318425323882,9.256343584707546,9.26136874409121,9.266393903474873,9.271419062858536,9.276444222242198,9.281469381625861,9.286494541009525,9.291519700393188,9.296544859776851,9.301570019160513,9.306595178544177,9.31162033792784,9.316645497311503,9.321670656695167,9.32669581607883,9.331720975462494,9.336746134846157,9.341771294229819,9.346796453613482,9.351821612997146,9.356846772380809,9.361871931764473,9.366897091148134,9.371922250531798,9.376947409915461,9.381972569299124,9.386997728682788,9.392022888066451,9.397048047450115,9.402073206833778,9.407098366217442,9.412123525601103,9.417148684984767,9.42217384436843,9.427199003752094,9.432224163135755,9.437249322519419,9.442274481903082,9.447299641286746,9.452324800670409,9.457349960054072,9.462375119437736,9.4674002788214,9.472425438205063,9.477450597588724,9.482475756972388,9.487500916356051,9.492526075739715,9.497551235123376,9.50257639450704,9.507601553890703,9.512626713274367,9.51765187265803,9.522677032041694,9.527702191425357,9.53272735080902,9.537752510192684,9.542777669576346,9.547802828960009,9.552827988343672,9.557853147727336,9.562878307110998,9.567903466494661,9.572928625878324,9.577953785261988,9.582978944645651,9.588004104029315,9.593029263412978,9.598054422796642,9.603079582180305,9.608104741563967,9.61312990094763,9.618155060331294,9.623180219714957,9.628205379098619,9.633230538482282,9.638255697865945,9.643280857249609,9.648306016633272,9.653331176016936,9.6583563354006,9.663381494784263,9.668406654167926,9.673431813551588,9.678456972935251,9.683482132318915,9.688507291702578,9.69353245108624,9.698557610469903,9.703582769853567,9.70860792923723,9.713633088620893,9.718658248004557,9.72368340738822,9.728708566771884,9.733733726155547,9.738758885539209,9.743784044922872,9.748809204306536,9.7538343636902,9.75885952307386,9.763884682457524,9.768909841841188,9.773935001224851,9.778960160608515,9.783985319992178,9.789010479375841,9.794035638759505,9.799060798143168,9.80408595752683,9.809111116910493,9.814136276294157,9.81916143567782,9.824186595061482,9.829211754445145,9.834236913828809,9.839262073212472,9.844287232596136,9.8493123919798,9.854337551363463,9.859362710747126,9.86438787013079,9.869413029514451,9.874438188898115,9.879463348281778,9.884488507665441,9.889513667049103,9.894538826432766,9.89956398581643,9.904589145200093,9.909614304583757,9.91463946396742,9.919664623351084,9.924689782734747,9.92971494211841,9.934740101502072,9.939765260885736,9.944790420269399,9.949815579653063,9.954840739036726,9.959865898420388,9.964891057804051,9.969916217187714,9.974941376571378,9.979966535955041,9.984991695338705,9.990016854722368,9.995042014106032,10.000067173489693,10.005092332873357,10.01011749225702,10.015142651640684,10.020167811024347,10.025192970408009,10.030218129791672,10.035243289175336,10.040268448558999,10.045293607942662,10.050318767326326,10.05534392670999,10.060369086093653,10.065394245477314,10.070419404860978,10.075444564244641,10.080469723628305,10.085494883011968,10.09052004239563,10.095545201779293,10.100570361162957,10.10559552054662,10.110620679930284,10.115645839313947,10.12067099869761,10.125696158081274,10.130721317464936,10.135746476848599,10.140771636232262,10.145796795615926,10.15082195499959,10.155847114383251,10.160872273766914,10.165897433150578,10.170922592534241,10.175947751917905,10.180972911301568,10.185998070685232,10.191023230068895,10.196048389452557,10.20107354883622,10.206098708219884,10.211123867603547,10.21614902698721,10.221174186370872,10.226199345754535,10.231224505138199,10.236249664521862,10.241274823905526,10.24629998328919,10.251325142672853,10.256350302056516,10.26137546144018,10.266400620823841,10.271425780207505,10.276450939591168,10.281476098974832,10.286501258358493,10.291526417742157,10.29655157712582,10.301576736509483,10.306601895893147,10.31162705527681,10.316652214660474,10.321677374044137,10.3267025334278,10.331727692811462,10.336752852195126,10.34177801157879,10.346803170962453,10.351828330346114,10.356853489729778,10.361878649113441,10.366903808497105,10.371928967880768,10.376954127264431,10.381979286648095,10.387004446031758,10.392029605415422,10.397054764799083,10.402079924182747,10.40710508356641,10.412130242950074,10.417155402333735,10.422180561717399,10.427205721101062,10.432230880484726,10.43725603986839,10.442281199252053,10.447306358635716,10.45233151801938,10.457356677403043,10.462381836786705,10.467406996170368,10.472432155554031,10.477457314937695,10.482482474321356,10.48750763370502,10.492532793088683,10.497557952472347,10.50258311185601,10.507608271239674,10.512633430623337,10.517658590007,10.522683749390664,10.527708908774326,10.532734068157989,10.537759227541653,10.542784386925316,10.547809546308978,10.552834705692641,10.557859865076304,10.562885024459968,10.567910183843631,10.572935343227295,10.577960502610958,10.582985661994622,10.588010821378285,10.593035980761947,10.59806114014561,10.603086299529274,10.608111458912937,10.613136618296599,10.618161777680262,10.623186937063926,10.628212096447589,10.633237255831252,10.638262415214916,10.64328757459858,10.648312733982243,10.653337893365906,10.658363052749568,10.663388212133231,10.668413371516895,10.673438530900558,10.67846369028422,10.683488849667883,10.688514009051547,10.69353916843521,10.698564327818874,10.703589487202537,10.7086146465862,10.713639805969864,10.718664965353527,10.723690124737189,10.728715284120852,10.733740443504516,10.73876560288818,10.743790762271841,10.748815921655504,10.753841081039168,10.758866240422831,10.763891399806495,10.768916559190158,10.773941718573822,10.778966877957485,10.783992037341148,10.78901719672481,10.794042356108474,10.799067515492137,10.8040926748758,10.809117834259462,10.814142993643125,10.819168153026789,10.824193312410452,10.829218471794116,10.83424363117778,10.839268790561443,10.844293949945106,10.84931910932877,10.854344268712431,10.859369428096095,10.864394587479758,10.869419746863421,10.874444906247085,10.879470065630747,10.88449522501441,10.889520384398073,10.894545543781737,10.899570703165399,10.904595862549064,10.909621021932727,10.91464618131639,10.919671340700052,10.924696500083716,10.92972165946738,10.934746818851043,10.939771978234706,10.944797137618368,10.949822297002031,10.954847456385695,10.959872615769358,10.96489777515302,10.969922934536685,10.974948093920348,10.979973253304012,10.984998412687673,10.990023572071337,10.995048731455,11.000073890838664,11.005099050222327,11.010124209605989,11.015149368989652,11.020174528373316,11.02519968775698,11.03022484714064,11.035250006524304,11.04027516590797,11.045300325291633,11.050325484675295,11.055350644058958,11.060375803442621,11.065400962826285,11.070426122209948,11.07545128159361,11.080476440977273,11.085501600360937,11.0905267597446,11.095551919128262,11.100577078511925,11.10560223789559,11.110627397279254,11.115652556662917,11.120677716046579,11.125702875430242,11.130728034813906,11.13575319419757,11.140778353581231,11.145803512964894,11.150828672348558,11.155853831732221,11.160878991115883,11.165904150499546,11.170929309883212,11.175954469266875,11.180979628650539,11.1860047880342,11.191029947417864,11.196055106801527,11.20108026618519,11.206105425568852,11.211130584952516,11.216155744336179,11.221180903719842,11.226206063103504,11.231231222487168,11.236256381870831,11.241281541254496,11.24630670063816,11.251331860021821,11.256357019405485,11.261382178789148,11.266407338172812,11.271432497556473,11.276457656940137,11.2814828163238,11.286507975707464,11.291533135091125,11.296558294474789,11.301583453858452,11.306608613242117,11.31163377262578,11.316658932009442,11.321684091393106,11.32670925077677,11.331734410160433,11.336759569544094,11.341784728927758,11.346809888311421,11.351835047695085,11.356860207078746,11.36188536646241,11.366910525846073,11.371935685229737,11.376960844613402,11.381986003997064,11.387011163380727,11.39203632276439,11.397061482148054,11.402086641531715,11.407111800915379,11.412136960299042,11.417162119682706,11.42218727906637,11.42721243845003,11.432237597833694,11.437262757217358,11.442287916601023,11.447313075984685,11.452338235368348,11.457363394752011,11.462388554135675,11.467413713519337,11.472438872903,11.477464032286663,11.482489191670327,11.48751435105399,11.492539510437652,11.497564669821315,11.502589829204979,11.507614988588642,11.512640147972306,11.51766530735597,11.522690466739633,11.527715626123296,11.532740785506958,11.537765944890621,11.542791104274285,11.547816263657948,11.552841423041611,11.557866582425273,11.562891741808937,11.5679169011926,11.572942060576263,11.577967219959927,11.58299237934359,11.588017538727254,11.593042698110917,11.598067857494579,11.603093016878242,11.608118176261906,11.613143335645569,11.618168495029233,11.623193654412894,11.628218813796558,11.633243973180221,11.638269132563885,11.643294291947548,11.648319451331211,11.653344610714875,11.658369770098538,11.6633949294822,11.668420088865863,11.673445248249527,11.67847040763319,11.683495567016854,11.688520726400515,11.693545885784179,11.698571045167842,11.703596204551506,11.708621363935169,11.713646523318832,11.718671682702496,11.72369684208616,11.728722001469823,11.733747160853484,11.738772320237148,11.743797479620811,11.748822639004475,11.753847798388136,11.7588729577718,11.763898117155463,11.768923276539127,11.77394843592279,11.778973595306454,11.783998754690117,11.78902391407378,11.794049073457444,11.799074232841106,11.804099392224769,11.809124551608432,11.814149710992096,11.819174870375758,11.824200029759421,11.829225189143084,11.834250348526748,11.839275507910411,11.844300667294075,11.849325826677738,11.854350986061402,11.859376145445065,11.864401304828727,11.86942646421239,11.874451623596054,11.879476782979717,11.884501942363379,11.889527101747042,11.894552261130706,11.899577420514369,11.904602579898032,11.909627739281696,11.91465289866536,11.919678058049023,11.924703217432686,11.929728376816348,11.934753536200011,11.939778695583675,11.944803854967338,11.949829014351,11.954854173734663,11.959879333118327,11.96490449250199,11.969929651885653,11.974954811269317,11.97997997065298,11.985005130036644,11.990030289420307,11.995055448803969,12.000080608187632,12.005105767571296,12.01013092695496,12.01515608633862,12.020181245722284,12.025206405105948,12.030231564489611,12.035256723873275,12.040281883256938,12.045307042640601,12.050332202024265,12.055357361407928,12.06038252079159,12.065407680175253,12.070432839558917,12.07545799894258,12.080483158326242,12.085508317709905,12.090533477093569,12.095558636477232,12.100583795860896,12.10560895524456,12.110634114628223,12.115659274011886,12.12068443339555,12.125709592779211,12.130734752162875,12.135759911546538,12.140785070930201,12.145810230313863,12.150835389697527,12.15586054908119,12.160885708464853,12.165910867848517,12.17093602723218,12.175961186615844,12.180986345999507,12.18601150538317,12.191036664766832,12.196061824150496,12.201086983534159,12.206112142917823,12.211137302301484,12.216162461685148,12.221187621068811,12.226212780452475,12.231237939836138,12.236263099219801,12.241288258603465,12.246313417987128,12.251338577370792,12.256363736754453,12.261388896138117,12.26641405552178,12.271439214905444,12.276464374289107,12.281489533672769,12.286514693056432,12.291539852440096,12.296565011823759,12.301590171207422,12.306615330591086,12.31164048997475,12.316665649358413,12.321690808742074,12.326715968125738,12.331741127509401,12.336766286893065,12.341791446276728,12.34681660566039,12.351841765044053,12.356866924427717,12.36189208381138,12.366917243195044,12.371942402578707,12.37696756196237,12.381992721346034,12.387017880729696,12.392043040113359,12.397068199497022],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Professional Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],"y":[0,0.23538513196130653,0.47077026392261306,0.7061553958839195,0.9415405278452261,1.1769256598065325,1.412310791767839,1.6476959237291458,1.8830810556904523,2.1184661876517588,2.353851319613065,2.5892364515743718,2.824621583535678,3.0600067154969848,3.2953918474582915,3.530776979419598,3.7661621113809045,4.001547243342211,4.2369323753035175,4.472317507264824,4.70770263922613,4.943087771187437,5.1784729031487435,5.41385803511005,5.649243167071356,5.884628299032664,6.1200134309939695,6.355398562955276,6.590783694916583,6.826168826877889,7.061553958839196,7.296939090800502,7.532324222761809,7.767709354723115,8.003094486684422,8.238479618645728,8.473864750607035,8.709249882568342,8.944635014529648,9.180020146490955,9.41540527845226,9.650790410413567,9.886175542374874,10.12156067433618,10.356945806297487],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Volume Rule 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33],"y":[0,0.1121377560695449,0.2242755121390898,0.3364132682086347,0.4485510242781796,0.5606887803477245,0.6728265364172694,0.7849642924868143,0.8971020485563592,1.0092398046259041,1.121377560695449,1.233515316764994,1.3456530728345388,1.4577908289040837,1.5699285849736286,1.6820663410431735,1.7942040971127184,1.9063418531822633,2.0184796092518082,2.130617365321353,2.242755121390898,2.354892877460443,2.467030633529988,2.5791683895995328,2.6913061456690777,2.8034439017386226,2.9155816578081675,3.0277194138777124,3.1398571699472573,3.251994926016802,3.364132682086347,3.476270438155892,3.588408194225437,3.700545950294982],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">34.15%</td>
                <td>62.7%</td>
                <td>1921</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>29.28%</td>
                <td>62.5</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">27.78%</td>
                <td>64.6%</td>
                <td>381</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>16.20%</td>
                <td>60.5</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">12.40%</td>
                <td>62.0%</td>
                <td>2467</td>
                <td>1.01</td>
                <td>0.00</td>
                <td>34.30%</td>
                <td>53.6</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">10.36%</td>
                <td>68.2%</td>
                <td>44</td>
                <td>1.46</td>
                <td>0.00</td>
                <td>3.66%</td>
                <td>37.8</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">3.70%</td>
                <td>66.7%</td>
                <td>33</td>
                <td>1.19</td>
                <td>0.00</td>
                <td>8.38%</td>
                <td>31.4</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Rule 2: Golden Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">4.21%</td>
                <td>70.6%</td>
                <td>17</td>
                <td>1.52</td>
                <td>0.00</td>
                <td>3.68%</td>
                <td>28.0</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">4.16%</td>
                <td>75.0%</td>
                <td>12</td>
                <td>1.74</td>
                <td>0.00</td>
                <td>2.49%</td>
                <td>27.8</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Acad Rule 2: Mean Reversion Factor</td>
                <td>ACADEMIC</td>
                <td class="positive">1.52%</td>
                <td>58.3%</td>
                <td>12</td>
                <td>1.19</td>
                <td>0.00</td>
                <td>2.92%</td>
                <td>21.7</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
