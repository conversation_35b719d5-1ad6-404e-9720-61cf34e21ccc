
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 01:13:50 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">211.80%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">7,278</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">63.8%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">2.18</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>66.78%</strong></td>
                    <td>65.9%</td>
                    <td>299</td>
                    <td>1.29</td>
                    <td class="negative">8.03%</td>
                    <td class="positive"><strong>0.8626</strong></td>
                    <td class="negative">+0.89% / -1.34%</td>
                    <td>6h24m<br><small>(1.0m - 78h15m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>43.32%</strong></td>
                    <td>64.6%</td>
                    <td>553</td>
                    <td>1.09</td>
                    <td class="negative">30.69%</td>
                    <td class="positive"><strong>0.7888</strong></td>
                    <td class="negative">+0.85% / -1.37%</td>
                    <td>4h45m<br><small>(1.0m - 55h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>42.54%</strong></td>
                    <td>64.9%</td>
                    <td>937</td>
                    <td>1.05</td>
                    <td class="negative">33.76%</td>
                    <td class="positive"><strong>0.8209</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>4h6m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>42.03%</strong></td>
                    <td>65.4%</td>
                    <td>696</td>
                    <td>1.07</td>
                    <td class="negative">26.22%</td>
                    <td class="positive"><strong>0.8035</strong></td>
                    <td class="negative">+0.83% / -1.44%</td>
                    <td>4h3m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>34.67%</strong></td>
                    <td>68.3%</td>
                    <td>199</td>
                    <td>1.21</td>
                    <td class="negative">18.99%</td>
                    <td class="positive"><strong>0.7077</strong></td>
                    <td class="negative">+0.83% / -1.38%</td>
                    <td>3h15m<br><small>(1.0m - 49h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>30.64%</strong></td>
                    <td>64.2%</td>
                    <td>520</td>
                    <td>1.07</td>
                    <td class="negative">25.22%</td>
                    <td class="positive"><strong>0.7541</strong></td>
                    <td class="negative">+0.87% / -1.38%</td>
                    <td>3h26m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>24.28%</strong></td>
                    <td>80.6%</td>
                    <td>31</td>
                    <td>2.59</td>
                    <td class="neutral">4.29%</td>
                    <td class="positive"><strong>0.7013</strong></td>
                    <td class="negative">+0.83% / -1.31%</td>
                    <td>4h18m<br><small>(4.0m - 17h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>22.37%</strong></td>
                    <td>63.3%</td>
                    <td>656</td>
                    <td>1.04</td>
                    <td class="negative">27.48%</td>
                    <td class="positive"><strong>0.7424</strong></td>
                    <td class="negative">+0.87% / -1.42%</td>
                    <td>4h37m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>17.41%</strong></td>
                    <td>67.0%</td>
                    <td>94</td>
                    <td>1.23</td>
                    <td class="negative">31.37%</td>
                    <td class="positive"><strong>0.5764</strong></td>
                    <td class="negative">+0.86% / -1.36%</td>
                    <td>4h37m<br><small>(8.0m - 26h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>16.87%</strong></td>
                    <td>64.4%</td>
                    <td>160</td>
                    <td>1.12</td>
                    <td class="negative">17.70%</td>
                    <td class="positive"><strong>0.6258</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>3h29m<br><small>(1.0m - 26h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>15.04%</strong></td>
                    <td>65.4%</td>
                    <td>191</td>
                    <td>1.10</td>
                    <td class="negative">16.79%</td>
                    <td class="positive"><strong>0.6335</strong></td>
                    <td class="negative">+0.84% / -1.37%</td>
                    <td>5h38m<br><small>(1.0m - 73h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>14.00%</strong></td>
                    <td>63.0%</td>
                    <td>270</td>
                    <td>1.06</td>
                    <td class="negative">18.69%</td>
                    <td class="positive"><strong>0.6556</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>4h53m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>10.80%</strong></td>
                    <td>64.5%</td>
                    <td>121</td>
                    <td>1.10</td>
                    <td class="negative">12.88%</td>
                    <td class="positive"><strong>0.5836</strong></td>
                    <td class="negative">+0.86% / -1.38%</td>
                    <td>3h53m<br><small>(1.0m - 26h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>6.87%</strong></td>
                    <td>66.7%</td>
                    <td>24</td>
                    <td>1.40</td>
                    <td class="negative">5.21%</td>
                    <td class="positive"><strong>0.4738</strong></td>
                    <td class="negative">+0.83% / -1.37%</td>
                    <td>8h10m<br><small>(5.0m - 46h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>2.34%</strong></td>
                    <td>75.0%</td>
                    <td>4</td>
                    <td>1.88</td>
                    <td class="neutral">2.57%</td>
                    <td class="positive"><strong>0.3701</strong></td>
                    <td class="negative">+0.81% / -1.28%</td>
                    <td>3h19m<br><small>(16.0m - 8h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>1.82%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.90% / 0.00%</td>
                    <td>1.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-0.81%</strong></td>
                    <td>63.1%</td>
                    <td>141</td>
                    <td>0.99</td>
                    <td class="negative">15.15%</td>
                    <td class="positive"><strong>0.5374</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>3h31m<br><small>(1.0m - 48h23m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-1.25%</strong></td>
                    <td>65.4%</td>
                    <td>26</td>
                    <td>0.95</td>
                    <td class="negative">5.96%</td>
                    <td class="positive"><strong>0.3764</strong></td>
                    <td class="negative">+0.81% / -1.39%</td>
                    <td>3h30m<br><small>(4.0m - 16h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="negative"><strong>-3.19%</strong></td>
                    <td>56.7%</td>
                    <td>30</td>
                    <td>0.89</td>
                    <td class="negative">13.40%</td>
                    <td class="positive"><strong>0.3530</strong></td>
                    <td class="negative">+0.87% / -1.36%</td>
                    <td>4h26m<br><small>(2.0m - 53h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-4.89%</strong></td>
                    <td>61.1%</td>
                    <td>54</td>
                    <td>0.91</td>
                    <td class="negative">19.39%</td>
                    <td class="positive"><strong>0.4055</strong></td>
                    <td class="negative">+0.86% / -1.40%</td>
                    <td>4h14m<br><small>(3.0m - 19h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-8.91%</strong></td>
                    <td>56.5%</td>
                    <td>46</td>
                    <td>0.81</td>
                    <td class="negative">14.63%</td>
                    <td class="positive"><strong>0.3608</strong></td>
                    <td class="negative">+0.87% / -1.36%</td>
                    <td>3h57m<br><small>(4.0m - 27h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="negative"><strong>-14.56%</strong></td>
                    <td>59.8%</td>
                    <td>132</td>
                    <td>0.89</td>
                    <td class="negative">22.43%</td>
                    <td class="positive"><strong>0.4604</strong></td>
                    <td class="negative">+0.84% / -1.38%</td>
                    <td>4h53m<br><small>(1.0m - 53h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-15.28%</strong></td>
                    <td>63.1%</td>
                    <td>605</td>
                    <td>0.97</td>
                    <td class="negative">42.59%</td>
                    <td class="positive"><strong>0.5991</strong></td>
                    <td class="negative">+0.83% / -1.43%</td>
                    <td>4h13m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-17.62%</strong></td>
                    <td>61.4%</td>
                    <td>140</td>
                    <td>0.87</td>
                    <td class="negative">28.02%</td>
                    <td class="positive"><strong>0.4425</strong></td>
                    <td class="negative">+0.84% / -1.44%</td>
                    <td>4h8m<br><small>(3.0m - 119h34m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="negative"><strong>-39.79%</strong></td>
                    <td>61.5%</td>
                    <td>434</td>
                    <td>0.91</td>
                    <td class="negative">58.39%</td>
                    <td class="positive"><strong>0.4649</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>3h37m<br><small>(1.0m - 49h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-73.69%</strong></td>
                    <td>61.6%</td>
                    <td>914</td>
                    <td>0.92</td>
                    <td class="negative">91.55%</td>
                    <td class="positive"><strong>0.4095</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>4h50m<br><small>(1.0m - 119h34m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.82%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.90% / 0.00%</td>
                    <td>1.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>80.6%</strong></td>
                    <td class="positive">24.28%</td>
                    <td>31</td>
                    <td>2.59</td>
                    <td class="neutral">4.29%</td>
                    <td class="positive"><strong>0.7013</strong></td>
                    <td class="negative">+0.83% / -1.31%</td>
                    <td>4h18m<br><small>(4.0m - 17h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">2.34%</td>
                    <td>4</td>
                    <td>1.88</td>
                    <td class="neutral">2.57%</td>
                    <td class="positive"><strong>0.3701</strong></td>
                    <td class="negative">+0.81% / -1.28%</td>
                    <td>3h19m<br><small>(16.0m - 8h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>68.3%</strong></td>
                    <td class="positive">34.67%</td>
                    <td>199</td>
                    <td>1.21</td>
                    <td class="negative">18.99%</td>
                    <td class="positive"><strong>0.7077</strong></td>
                    <td class="negative">+0.83% / -1.38%</td>
                    <td>3h15m<br><small>(1.0m - 49h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>67.0%</strong></td>
                    <td class="positive">17.41%</td>
                    <td>94</td>
                    <td>1.23</td>
                    <td class="negative">31.37%</td>
                    <td class="positive"><strong>0.5764</strong></td>
                    <td class="negative">+0.86% / -1.36%</td>
                    <td>4h37m<br><small>(8.0m - 26h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">6.87%</td>
                    <td>24</td>
                    <td>1.40</td>
                    <td class="negative">5.21%</td>
                    <td class="positive"><strong>0.4738</strong></td>
                    <td class="negative">+0.83% / -1.37%</td>
                    <td>8h10m<br><small>(5.0m - 46h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>65.9%</strong></td>
                    <td class="positive">66.78%</td>
                    <td>299</td>
                    <td>1.29</td>
                    <td class="negative">8.03%</td>
                    <td class="positive"><strong>0.8626</strong></td>
                    <td class="negative">+0.89% / -1.34%</td>
                    <td>6h24m<br><small>(1.0m - 78h15m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>65.4%</strong></td>
                    <td class="positive">15.04%</td>
                    <td>191</td>
                    <td>1.10</td>
                    <td class="negative">16.79%</td>
                    <td class="positive"><strong>0.6335</strong></td>
                    <td class="negative">+0.84% / -1.37%</td>
                    <td>5h38m<br><small>(1.0m - 73h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>65.4%</strong></td>
                    <td class="negative">-1.25%</td>
                    <td>26</td>
                    <td>0.95</td>
                    <td class="negative">5.96%</td>
                    <td class="positive"><strong>0.3764</strong></td>
                    <td class="negative">+0.81% / -1.39%</td>
                    <td>3h30m<br><small>(4.0m - 16h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>65.4%</strong></td>
                    <td class="positive">42.03%</td>
                    <td>696</td>
                    <td>1.07</td>
                    <td class="negative">26.22%</td>
                    <td class="positive"><strong>0.8035</strong></td>
                    <td class="negative">+0.83% / -1.44%</td>
                    <td>4h3m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>64.9%</strong></td>
                    <td class="positive">42.54%</td>
                    <td>937</td>
                    <td>1.05</td>
                    <td class="negative">33.76%</td>
                    <td class="positive"><strong>0.8209</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>4h6m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>64.6%</strong></td>
                    <td class="positive">43.32%</td>
                    <td>553</td>
                    <td>1.09</td>
                    <td class="negative">30.69%</td>
                    <td class="positive"><strong>0.7888</strong></td>
                    <td class="negative">+0.85% / -1.37%</td>
                    <td>4h45m<br><small>(1.0m - 55h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>64.5%</strong></td>
                    <td class="positive">10.80%</td>
                    <td>121</td>
                    <td>1.10</td>
                    <td class="negative">12.88%</td>
                    <td class="positive"><strong>0.5836</strong></td>
                    <td class="negative">+0.86% / -1.38%</td>
                    <td>3h53m<br><small>(1.0m - 26h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>64.4%</strong></td>
                    <td class="positive">16.87%</td>
                    <td>160</td>
                    <td>1.12</td>
                    <td class="negative">17.70%</td>
                    <td class="positive"><strong>0.6258</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>3h29m<br><small>(1.0m - 26h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>64.2%</strong></td>
                    <td class="positive">30.64%</td>
                    <td>520</td>
                    <td>1.07</td>
                    <td class="negative">25.22%</td>
                    <td class="positive"><strong>0.7541</strong></td>
                    <td class="negative">+0.87% / -1.38%</td>
                    <td>3h26m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>63.3%</strong></td>
                    <td class="positive">22.37%</td>
                    <td>656</td>
                    <td>1.04</td>
                    <td class="negative">27.48%</td>
                    <td class="positive"><strong>0.7424</strong></td>
                    <td class="negative">+0.87% / -1.42%</td>
                    <td>4h37m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>63.1%</strong></td>
                    <td class="negative">-15.28%</td>
                    <td>605</td>
                    <td>0.97</td>
                    <td class="negative">42.59%</td>
                    <td class="positive"><strong>0.5991</strong></td>
                    <td class="negative">+0.83% / -1.43%</td>
                    <td>4h13m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>63.1%</strong></td>
                    <td class="negative">-0.81%</td>
                    <td>141</td>
                    <td>0.99</td>
                    <td class="negative">15.15%</td>
                    <td class="positive"><strong>0.5374</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>3h31m<br><small>(1.0m - 48h23m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>63.0%</strong></td>
                    <td class="positive">14.00%</td>
                    <td>270</td>
                    <td>1.06</td>
                    <td class="negative">18.69%</td>
                    <td class="positive"><strong>0.6556</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>4h53m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>61.6%</strong></td>
                    <td class="negative">-73.69%</td>
                    <td>914</td>
                    <td>0.92</td>
                    <td class="negative">91.55%</td>
                    <td class="positive"><strong>0.4095</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>4h50m<br><small>(1.0m - 119h34m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>61.5%</strong></td>
                    <td class="negative">-39.79%</td>
                    <td>434</td>
                    <td>0.91</td>
                    <td class="negative">58.39%</td>
                    <td class="positive"><strong>0.4649</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>3h37m<br><small>(1.0m - 49h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>61.4%</strong></td>
                    <td class="negative">-17.62%</td>
                    <td>140</td>
                    <td>0.87</td>
                    <td class="negative">28.02%</td>
                    <td class="positive"><strong>0.4425</strong></td>
                    <td class="negative">+0.84% / -1.44%</td>
                    <td>4h8m<br><small>(3.0m - 119h34m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>61.1%</strong></td>
                    <td class="negative">-4.89%</td>
                    <td>54</td>
                    <td>0.91</td>
                    <td class="negative">19.39%</td>
                    <td class="positive"><strong>0.4055</strong></td>
                    <td class="negative">+0.86% / -1.40%</td>
                    <td>4h14m<br><small>(3.0m - 19h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>59.8%</strong></td>
                    <td class="negative">-14.56%</td>
                    <td>132</td>
                    <td>0.89</td>
                    <td class="negative">22.43%</td>
                    <td class="positive"><strong>0.4604</strong></td>
                    <td class="negative">+0.84% / -1.38%</td>
                    <td>4h53m<br><small>(1.0m - 53h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>56.7%</strong></td>
                    <td class="negative">-3.19%</td>
                    <td>30</td>
                    <td>0.89</td>
                    <td class="negative">13.40%</td>
                    <td class="positive"><strong>0.3530</strong></td>
                    <td class="negative">+0.87% / -1.36%</td>
                    <td>4h26m<br><small>(2.0m - 53h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>56.5%</strong></td>
                    <td class="negative">-8.91%</td>
                    <td>46</td>
                    <td>0.81</td>
                    <td class="negative">14.63%</td>
                    <td class="positive"><strong>0.3608</strong></td>
                    <td class="negative">+0.87% / -1.36%</td>
                    <td>3h57m<br><small>(4.0m - 27h7m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Ext Rule 5: ATR Volatility Exp...', 'Rule 10: Volume Spike', 'AI Rule 10: Composite Sentimen...', 'Acad Rule 2: Mean Reversion Fa...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 27: Structure Break Up', 'Acad Rule 3: Volatility Breako...', 'Volume Rule 4: Volume Breakout...', 'Professional Rule 7: Chaikin M...', 'Ext Rule 3: Bollinger Squeeze ...', 'Volatility Rule 2: ATR Expansi...', 'Rule 28: Volume Breakout', 'Rule 6: Stochastic Oversold Cr...', 'Rule 2: Golden Cross', 'Rule 7: Bollinger Band Bounce'],
                y: [65.88628762541806, 64.55696202531645, 64.88794023479188, 65.37356321839081, 68.34170854271356, 64.23076923076924, 63.262195121951216, 64.375, 65.44502617801047, 67.02127659574468, 62.96296296296296, 64.46280991735537, 63.12056737588653, 80.64516129032258, 63.1404958677686],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Ext Rule 5: ATR Volatility Exp...', 'Rule 10: Volume Spike', 'AI Rule 10: Composite Sentimen...', 'Acad Rule 2: Mean Reversion Fa...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 27: Structure Break Up', 'Acad Rule 3: Volatility Breako...', 'Volume Rule 4: Volume Breakout...', 'Professional Rule 7: Chaikin M...', 'Ext Rule 3: Bollinger Squeeze ...', 'Volatility Rule 2: ATR Expansi...', 'Rule 28: Volume Breakout', 'Rule 6: Stochastic Oversold Cr...', 'Rule 2: Golden Cross', 'Rule 7: Bollinger Band Bounce'],
                y: [np.float64(66.77601392348883), np.float64(43.319791250666704), np.float64(42.537075903287786), np.float64(42.02822251730629), np.float64(34.6686369422493), np.float64(30.638940092930206), np.float64(22.370324785890407), np.float64(16.87193218875531), np.float64(15.043124600192154), np.float64(17.41429890000836), np.float64(14.001188774160022), np.float64(10.804137431649579), np.float64(-0.8098805750889004), np.float64(24.279605778318743), np.float64(-15.282556302362964)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
