
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 13:09:15 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">61.68%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">6,145</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.3%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1.96</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>44.14%</strong></td>
                    <td>69.1%</td>
                    <td>311</td>
                    <td>1.29</td>
                    <td class="negative">11.51%</td>
                    <td class="positive"><strong>0.7975</strong></td>
                    <td class="negative">+0.90% / -1.49%</td>
                    <td>1h48m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>22.89%</strong></td>
                    <td>65.5%</td>
                    <td>316</td>
                    <td>1.14</td>
                    <td class="negative">14.23%</td>
                    <td class="positive"><strong>0.7159</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h18m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>21.91%</strong></td>
                    <td>69.7%</td>
                    <td>132</td>
                    <td>1.34</td>
                    <td class="negative">6.44%</td>
                    <td class="positive"><strong>0.6752</strong></td>
                    <td class="negative">+0.88% / -1.45%</td>
                    <td>2h51m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>11.66%</strong></td>
                    <td>63.4%</td>
                    <td>145</td>
                    <td>1.15</td>
                    <td class="negative">10.50%</td>
                    <td class="positive"><strong>0.6168</strong></td>
                    <td class="negative">+0.89% / -1.39%</td>
                    <td>3h19m<br><small>(1.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>5.62%</strong></td>
                    <td>64.6%</td>
                    <td>65</td>
                    <td>1.16</td>
                    <td class="negative">5.14%</td>
                    <td class="positive"><strong>0.5388</strong></td>
                    <td class="negative">+0.95% / -1.39%</td>
                    <td>2h29m<br><small>(1.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>5.16%</strong></td>
                    <td>68.6%</td>
                    <td>35</td>
                    <td>1.30</td>
                    <td class="neutral">3.26%</td>
                    <td class="positive"><strong>0.4928</strong></td>
                    <td class="negative">+0.85% / -1.46%</td>
                    <td>3h22m<br><small>(16.0m - 14h29m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>4.56%</strong></td>
                    <td>80.0%</td>
                    <td>10</td>
                    <td>2.51</td>
                    <td class="positive">1.62%</td>
                    <td class="positive"><strong>0.5215</strong></td>
                    <td class="negative">+0.85% / -1.46%</td>
                    <td>4h4m<br><small>(13.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>3.67%</strong></td>
                    <td>61.8%</td>
                    <td>2629</td>
                    <td>1.00</td>
                    <td class="negative">39.06%</td>
                    <td class="positive"><strong>0.8093</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>2h31m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>3.44%</strong></td>
                    <td>72.2%</td>
                    <td>18</td>
                    <td>1.43</td>
                    <td class="neutral">4.15%</td>
                    <td class="positive"><strong>0.4606</strong></td>
                    <td class="negative">+0.90% / -1.37%</td>
                    <td>1h57m<br><small>(3.0m - 8h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>1.46%</strong></td>
                    <td>59.0%</td>
                    <td>39</td>
                    <td>1.07</td>
                    <td class="neutral">4.20%</td>
                    <td class="positive"><strong>0.4517</strong></td>
                    <td class="negative">+0.90% / -1.33%</td>
                    <td>2h41m<br><small>(13.0m - 14h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>1.04%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.91% / 0.00%</td>
                    <td>5h23m</td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>0.52%</strong></td>
                    <td>60.3%</td>
                    <td>68</td>
                    <td>1.01</td>
                    <td class="negative">7.95%</td>
                    <td class="positive"><strong>0.4882</strong></td>
                    <td class="negative">+0.95% / -1.41%</td>
                    <td>3h12m<br><small>(1.0m - 30h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="negative"><strong>-3.45%</strong></td>
                    <td>45.5%</td>
                    <td>11</td>
                    <td>0.63</td>
                    <td class="neutral">4.63%</td>
                    <td class="positive"><strong>0.1716</strong></td>
                    <td class="negative">+1.10% / -1.62%</td>
                    <td>4.5m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-7.61%</strong></td>
                    <td>60.7%</td>
                    <td>234</td>
                    <td>0.94</td>
                    <td class="negative">16.16%</td>
                    <td class="positive"><strong>0.5573</strong></td>
                    <td class="negative">+0.86% / -1.41%</td>
                    <td>2h5m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-11.20%</strong></td>
                    <td>44.8%</td>
                    <td>29</td>
                    <td>0.52</td>
                    <td class="negative">14.13%</td>
                    <td class="positive"><strong>0.2061</strong></td>
                    <td class="negative">+0.93% / -1.34%</td>
                    <td>1h50m<br><small>(13.0m - 7h10m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-42.14%</strong></td>
                    <td>61.0%</td>
                    <td>2102</td>
                    <td>0.96</td>
                    <td class="negative">57.95%</td>
                    <td class="positive"><strong>0.6338</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h30m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.04%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.91% / 0.00%</td>
                    <td>5h23m</td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>80.0%</strong></td>
                    <td class="positive">4.56%</td>
                    <td>10</td>
                    <td>2.51</td>
                    <td class="positive">1.62%</td>
                    <td class="positive"><strong>0.5215</strong></td>
                    <td class="negative">+0.85% / -1.46%</td>
                    <td>4h4m<br><small>(13.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>72.2%</strong></td>
                    <td class="positive">3.44%</td>
                    <td>18</td>
                    <td>1.43</td>
                    <td class="neutral">4.15%</td>
                    <td class="positive"><strong>0.4606</strong></td>
                    <td class="negative">+0.90% / -1.37%</td>
                    <td>1h57m<br><small>(3.0m - 8h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>69.7%</strong></td>
                    <td class="positive">21.91%</td>
                    <td>132</td>
                    <td>1.34</td>
                    <td class="negative">6.44%</td>
                    <td class="positive"><strong>0.6752</strong></td>
                    <td class="negative">+0.88% / -1.45%</td>
                    <td>2h51m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>69.1%</strong></td>
                    <td class="positive">44.14%</td>
                    <td>311</td>
                    <td>1.29</td>
                    <td class="negative">11.51%</td>
                    <td class="positive"><strong>0.7975</strong></td>
                    <td class="negative">+0.90% / -1.49%</td>
                    <td>1h48m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>68.6%</strong></td>
                    <td class="positive">5.16%</td>
                    <td>35</td>
                    <td>1.30</td>
                    <td class="neutral">3.26%</td>
                    <td class="positive"><strong>0.4928</strong></td>
                    <td class="negative">+0.85% / -1.46%</td>
                    <td>3h22m<br><small>(16.0m - 14h29m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>65.5%</strong></td>
                    <td class="positive">22.89%</td>
                    <td>316</td>
                    <td>1.14</td>
                    <td class="negative">14.23%</td>
                    <td class="positive"><strong>0.7159</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h18m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>64.6%</strong></td>
                    <td class="positive">5.62%</td>
                    <td>65</td>
                    <td>1.16</td>
                    <td class="negative">5.14%</td>
                    <td class="positive"><strong>0.5388</strong></td>
                    <td class="negative">+0.95% / -1.39%</td>
                    <td>2h29m<br><small>(1.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>63.4%</strong></td>
                    <td class="positive">11.66%</td>
                    <td>145</td>
                    <td>1.15</td>
                    <td class="negative">10.50%</td>
                    <td class="positive"><strong>0.6168</strong></td>
                    <td class="negative">+0.89% / -1.39%</td>
                    <td>3h19m<br><small>(1.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>61.8%</strong></td>
                    <td class="positive">3.67%</td>
                    <td>2629</td>
                    <td>1.00</td>
                    <td class="negative">39.06%</td>
                    <td class="positive"><strong>0.8093</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>2h31m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>61.0%</strong></td>
                    <td class="negative">-42.14%</td>
                    <td>2102</td>
                    <td>0.96</td>
                    <td class="negative">57.95%</td>
                    <td class="positive"><strong>0.6338</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h30m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>60.7%</strong></td>
                    <td class="negative">-7.61%</td>
                    <td>234</td>
                    <td>0.94</td>
                    <td class="negative">16.16%</td>
                    <td class="positive"><strong>0.5573</strong></td>
                    <td class="negative">+0.86% / -1.41%</td>
                    <td>2h5m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>60.3%</strong></td>
                    <td class="positive">0.52%</td>
                    <td>68</td>
                    <td>1.01</td>
                    <td class="negative">7.95%</td>
                    <td class="positive"><strong>0.4882</strong></td>
                    <td class="negative">+0.95% / -1.41%</td>
                    <td>3h12m<br><small>(1.0m - 30h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>59.0%</strong></td>
                    <td class="positive">1.46%</td>
                    <td>39</td>
                    <td>1.07</td>
                    <td class="neutral">4.20%</td>
                    <td class="positive"><strong>0.4517</strong></td>
                    <td class="negative">+0.90% / -1.33%</td>
                    <td>2h41m<br><small>(13.0m - 14h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="neutral"><strong>45.5%</strong></td>
                    <td class="negative">-3.45%</td>
                    <td>11</td>
                    <td>0.63</td>
                    <td class="neutral">4.63%</td>
                    <td class="positive"><strong>0.1716</strong></td>
                    <td class="negative">+1.10% / -1.62%</td>
                    <td>4.5m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="neutral"><strong>44.8%</strong></td>
                    <td class="negative">-11.20%</td>
                    <td>29</td>
                    <td>0.52</td>
                    <td class="negative">14.13%</td>
                    <td class="positive"><strong>0.2061</strong></td>
                    <td class="negative">+0.93% / -1.34%</td>
                    <td>1h50m<br><small>(13.0m - 7h10m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'Volume Rule 5: Smart Money Vol...', 'Rule 6: Stochastic Oversold Cr...', 'Professional Rule 10: CCI Reve...', 'AI Rule 10: Composite Sentimen...', 'AI Rule 8: Momentum Divergence...', 'Professional Rule 7: Chaikin M...', 'Momentum Rule 2: Momentum Dive...', 'Rule 10: Volume Spike', 'Rule 7: Bollinger Band Bounce', 'Volume Rule 3: Dark Pool Activ...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Rule 2: Golden Cross', 'Advanced Rule 7: DMI ADX Filte...'],
                y: [69.13183279742765, 69.6969696969697, 65.50632911392405, 63.44827586206897, 61.84861163940661, 60.68376068376068, 64.61538461538461, 60.29411764705882, 68.57142857142857, 61.03710751665081, 100.0, 58.97435897435898, 80.0, 72.22222222222221, 44.827586206896555],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'Volume Rule 5: Smart Money Vol...', 'Rule 6: Stochastic Oversold Cr...', 'Professional Rule 10: CCI Reve...', 'AI Rule 10: Composite Sentimen...', 'AI Rule 8: Momentum Divergence...', 'Professional Rule 7: Chaikin M...', 'Momentum Rule 2: Momentum Dive...', 'Rule 10: Volume Spike', 'Rule 7: Bollinger Band Bounce', 'Volume Rule 3: Dark Pool Activ...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Rule 2: Golden Cross', 'Advanced Rule 7: DMI ADX Filte...'],
                y: [np.float64(44.135914658082065), np.float64(21.906172090573193), np.float64(22.8928128490197), np.float64(11.664594688649043), np.float64(3.665960176156543), np.float64(-7.611176769185607), np.float64(5.62081498697371), np.float64(0.5248616963899113), np.float64(5.162894357461642), np.float64(-42.13560591308787), np.float64(1.0369253510744312), np.float64(1.4640825590535096), np.float64(4.5600882245379175), np.float64(3.44373412188211), np.float64(-11.198349734458287)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
