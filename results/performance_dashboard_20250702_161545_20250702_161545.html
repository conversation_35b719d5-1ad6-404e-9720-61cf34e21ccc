
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 9 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 16:15:45</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">9</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">50.0%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">31.3%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">82.7%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">69.2%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">3,549</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["82.7%","72.7%","45.9%","19.7%","16.3%","14.9%","14.9%","10.3%","4.1%"],"textposition":"auto","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","Rule 7: Bollinger Band Bounce","AI Rule 8: Momentum Divergence Reversal","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[82.6862042301552,72.66181387266204,45.85125631751894,19.656332192181335,16.299224638973143,14.855337049722731,14.874057355808109,10.304429140876529,4.120938457172989],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","Rule 7: Bollinger Band Bounce","AI Rule 8: Momentum Divergence Reversal","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[714,723,101,86,580,51,19,12,13],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","Rule 7: Bollinger Band Bounce","AI Rule 8: Momentum Divergence Reversal","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[394,394,45,40,339,23,6,3,6],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[82.6862042301552,72.66181387266204,45.85125631751894,19.656332192181335,16.299224638973143,14.855337049722731,14.874057355808109,10.304429140876529,4.120938457172989],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Ext Rule 6","AI Rule 10","Prof Rule 7","Rule 6","Rule 7","AI Rule 8","Momentum Rule 2","Price Action Rule 3","Rule 10"],"textposition":"top center","x":[25.862504441640823,22.869736447270185,12.47633310185683,10.76848788048068,40.676270670888606,8.91726513936649,3.703492501509868,2.857911655797833,3.5425665854214765],"y":[82.6862042301552,72.66181387266204,45.85125631751894,19.656332192181335,16.299224638973143,14.855337049722731,14.874057355808109,10.304429140876529,4.120938457172989],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["36.0%","43.8%","45.9%","13.4%"],"textposition":"auto","x":["UNKNOWN","AI_GENERATED","PROFESSIONAL","ORIGINAL"],"y":[35.95489690894661,43.758575461192386,45.85125631751894,13.358831762775823],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["1108","1117","146","126","919","74","25","15","19"],"textposition":"auto","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","Rule 7: Bollinger Band Bounce","AI Rule 8: Momentum Divergence Reversal","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[1108,1117,146,126,919,74,25,15,19],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Ext Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108],"y":[0,0.07462653811385848,0.14925307622771697,0.22387961434157544,0.29850615245543394,0.3731326905692924,0.4477592286831509,0.5223857667970093,0.5970123049108679,0.6716388430247263,0.7462653811385848,0.8208919192524433,0.8955184573663018,0.9701449954801603,1.0447715335940186,1.1193980717078773,1.1940246098217357,1.2686511479355942,1.3432776860494526,1.4179042241633113,1.4925307622771695,1.567157300391028,1.6417838385048866,1.716410376618745,1.7910369147326035,1.8656634528464622,1.9402899909603206,2.014916529074179,2.0895430671880373,2.164169605301896,2.2387961434157546,2.313422681529613,2.3880492196434715,2.4626757577573297,2.5373022958711884,2.6119288339850466,2.6865553720989053,2.761181910212764,2.8358084483266226,2.910434986440481,2.985061524554339,3.0596880626681977,3.134314600782056,3.208941138895915,3.2835676770097733,3.358194215123632,3.43282075323749,3.5074472913513484,3.582073829465207,3.6567003675790652,3.7313269056929244,3.8059534438067826,3.8805799819206412,3.9552065200344995,4.029833058148358,4.104459596262216,4.179086134376075,4.253712672489934,4.328339210603792,4.402965748717651,4.477592286831509,4.552218824945367,4.626845363059226,4.701471901173084,4.776098439286943,4.850724977400802,4.925351515514659,4.999978053628518,5.074604591742377,5.149231129856235,5.223857667970093,5.298484206083952,5.3731107441978105,5.44773728231167,5.522363820425528,5.5969903585393865,5.671616896653245,5.746243434767103,5.820869972880962,5.89549651099482,5.970123049108678,6.044749587222537,6.119376125336395,6.194002663450254,6.268629201564112,6.3432557396779705,6.41788227779183,6.492508815905689,6.5671353540195465,6.641761892133405,6.716388430247264,6.791014968361122,6.86564150647498,6.940268044588839,7.014894582702697,7.089521120816555,7.164147658930414,7.238774197044273,7.3134007351581305,7.38802727327199,7.462653811385849,7.537280349499707,7.611906887613565,7.686533425727424,7.7611599638412825,7.83578650195514,7.910413040068999,7.985039578182858,8.059666116296716,8.134292654410574,8.208919192524432,8.283545730638291,8.35817226875215,8.432798806866009,8.507425344979868,8.582051883093726,8.656678421207584,8.731304959321443,8.805931497435301,8.880558035549159,8.955184573663018,9.029811111776876,9.104437649890734,9.179064188004594,9.253690726118451,9.32831726423231,9.402943802346169,9.477570340460028,9.552196878573886,9.626823416687744,9.701449954801603,9.776076492915461,9.850703031029319,9.925329569143178,9.999956107257036,10.074582645370894,10.149209183484754,10.223835721598611,10.29846225971247,10.373088797826329,10.447715335940186,10.522341874054046,10.596968412167904,10.671594950281763,10.746221488395621,10.82084802650948,10.89547456462334,10.970101102737196,11.044727640851056,11.119354178964914,11.193980717078773,11.26860725519263,11.34323379330649,11.417860331420346,11.492486869534206,11.567113407648064,11.641739945761923,11.716366483875781,11.79099302198964,11.8656195601035,11.940246098217356,12.014872636331216,12.089499174445073,12.164125712558933,12.23875225067279,12.31337878878665,12.388005326900508,12.462631865014368,12.537258403128224,12.611884941242083,12.686511479355941,12.7611380174698,12.83576455558366,12.910391093697518,12.985017631811377,13.059644169925233,13.134270708039093,13.20889724615295,13.28352378426681,13.358150322380668,13.432776860494528,13.507403398608384,13.582029936722243,13.656656474836101,13.73128301294996,13.80590955106382,13.880536089177678,13.955162627291537,14.029789165405393,14.104415703519253,14.17904224163311,14.25366877974697,14.328295317860828,14.402921855974688,14.477548394088545,14.552174932202405,14.626801470316261,14.70142800843012,14.77605454654398,14.850681084657838,14.925307622771697,14.999934160885555,15.074560698999415,15.14918723711327,15.22381377522713,15.298440313340988,15.373066851454848,15.447693389568705,15.522319927682565,15.596946465796421,15.67157300391028,15.746199542024138,15.820826080137998,15.895452618251857,15.970079156365715,16.044705694479575,16.119332232593433,16.19395877070729,16.268585308821148,16.34321184693501,16.417838385048864,16.492464923162725,16.567091461276583,16.64171799939044,16.7163445375043,16.79097107561816,16.865597613732017,16.940224151845875,17.014850689959736,17.08947722807359,17.164103766187452,17.23873030430131,17.313356842415168,17.387983380529025,17.462609918642887,17.53723645675674,17.611862994870602,17.68648953298446,17.761116071098318,17.83574260921218,17.910369147326037,17.984995685439895,18.059622223553752,18.13424876166761,18.208875299781468,18.28350183789533,18.358128376009187,18.432754914123045,18.507381452236903,18.58200799035076,18.65663452846462,18.73126106657848,18.805887604692337,18.880514142806195,18.955140680920056,19.029767219033914,19.104393757147772,19.17902029526163,19.253646833375488,19.328273371489345,19.402899909603207,19.477526447717064,19.552152985830922,19.62677952394478,19.701406062058638,19.7760326001725,19.850659138286357,19.925285676400215,19.999912214514072,20.074538752627934,20.149165290741788,20.22379182885565,20.298418366969507,20.373044905083365,20.447671443197223,20.522297981311084,20.59692451942494,20.6715510575388,20.746177595652657,20.82080413376652,20.895430671880373,20.970057209994234,21.044683748108092,21.119310286221953,21.193936824335808,21.268563362449665,21.343189900563527,21.417816438677384,21.492442976791242,21.5670695149051,21.64169605301896,21.71632259113282,21.79094912924668,21.865575667360535,21.940202205474392,22.014828743588254,22.08945528170211,22.164081819815966,22.238708357929827,22.313334896043685,22.387961434157546,22.4625879722714,22.53721451038526,22.61184104849912,22.68646758661298,22.76109412472684,22.835720662840693,22.910347200954554,22.984973739068412,23.059600277182273,23.134226815296127,23.20885335340999,23.283479891523847,23.358106429637708,23.432732967751562,23.50735950586542,23.58198604397928,23.65661258209314,23.731239120207,23.805865658320855,23.880492196434712,23.955118734548574,24.02974527266243,24.10437181077629,24.178998348890147,24.25362488700401,24.328251425117866,24.40287796323172,24.47750450134558,24.55213103945944,24.6267575775733,24.70138411568716,24.776010653801016,24.850637191914874,24.925263730028735,24.999890268142593,25.074516806256447,25.14914334437031,25.223769882484167,25.298396420598028,25.373022958711882,25.44764949682574,25.5222760349396,25.59690257305346,25.67152911116732,25.746155649281175,25.820782187395036,25.895408725508894,25.970035263622755,26.04466180173661,26.119288339850467,26.19391487796433,26.268541416078186,26.343167954192044,26.4177944923059,26.492421030419763,26.56704756853362,26.641674106647482,26.716300644761336,26.790927182875194,26.865553720989055,26.940180259102913,27.014806797216767,27.08943333533063,27.164059873444486,27.238686411558348,27.313312949672202,27.387939487786063,27.46256602589992,27.537192564013782,27.61181910212764,27.686445640241494,27.761072178355356,27.835698716469214,27.910325254583075,27.98495179269693,28.059578330810787,28.13420486892465,28.208831407038506,28.283457945152364,28.35808448326622,28.432711021380083,28.50733755949394,28.581964097607802,28.656590635721656,28.731217173835514,28.805843711949375,28.880470250063233,28.95509678817709,29.02972332629095,29.10434986440481,29.178976402518668,29.253602940632522,29.328229478746383,29.40285601686024,29.477482554974102,29.55210909308796,29.626735631201814,29.701362169315676,29.775988707429534,29.850615245543395,29.92524178365725,29.99986832177111,30.074494859884968,30.14912139799883,30.223747936112684,30.29837447422654,30.373001012340403,30.44762755045426,30.522254088568122,30.596880626681976,30.671507164795837,30.746133702909695,30.820760241023557,30.89538677913741,30.97001331725127,31.04463985536513,31.119266393478988,31.193892931592842,31.268519469706703,31.34314600782056,31.417772545934422,31.492399084048277,31.567025622162138,31.641652160275996,31.716278698389857,31.790905236503715,31.86553177461757,31.94015831273143,32.01478485084529,32.08941138895915,32.164037927073004,32.238664465186865,32.313291003300726,32.38791754141458,32.462544079528435,32.537170617642296,32.61179715575616,32.68642369387002,32.76105023198387,32.83567677009773,32.91030330821159,32.98492984632545,33.05955638443931,33.134182922553165,33.20880946066703,33.28343599878088,33.35806253689474,33.4326890750086,33.50731561312246,33.58194215123632,33.65656868935017,33.731195227464035,33.80582176557789,33.88044830369175,33.95507484180561,34.02970137991947,34.10432791803333,34.17895445614718,34.25358099426104,34.328207532374904,34.40283407048876,34.47746060860262,34.552087146716474,34.626713684830335,34.7013402229442,34.77596676105805,34.85059329917191,34.92521983728577,34.99984637539963,35.07447291351348,35.14909945162734,35.223725989741205,35.298352527855066,35.37297906596892,35.447605604082774,35.522232142196636,35.5968586803105,35.67148521842436,35.74611175653821,35.820738294652074,35.89536483276593,35.96999137087979,36.044617908993644,36.119244447107505,36.193870985221366,36.26849752333522,36.34312406144908,36.417750599562936,36.4923771376768,36.56700367579066,36.64163021390452,36.716256752018374,36.79088329013223,36.86550982824609,36.94013636635995,37.014762904473805,37.08938944258767,37.16401598070152,37.23864251881538,37.31326905692924,37.3878955950431,37.46252213315696,37.53714867127082,37.611775209384675,37.68640174749853,37.76102828561239,37.83565482372625,37.91028136184011,37.98490789995397,38.05953443806783,38.13416097618168,38.208787514295544,38.2834140524094,38.35804059052326,38.43266712863712,38.507293666750975,38.581920204864836,38.65654674297869,38.73117328109255,38.80579981920641,38.88042635732027,38.95505289543413,39.02967943354798,39.104305971661844,39.178932509775706,39.25355904788956,39.32818558600342,39.402812124117276,39.47743866223114,39.552065200345,39.62669173845885,39.701318276572714,39.775944814686575,39.85057135280043,39.92519789091428,39.999824429028145,40.074450967142006,40.14907750525587,40.22370404336972,40.298330581483576,40.37295711959744,40.4475836577113,40.52221019582516,40.596836733939014,40.671463272052875,40.74608981016673,40.82071634828059,40.895342886394445,40.96996942450831,41.04459596262217,41.11922250073602,41.19384903884988,41.26847557696374,41.3431021150776,41.41772865319146,41.492355191305315,41.566981729419176,41.64160826753304,41.716234805646884,41.790861343760746,41.86548788187461,41.94011441998847,42.01474095810232,42.089367496216184,42.163994034330045,42.23862057244391,42.313247110557754,42.387873648671615,42.462500186785476,42.53712672489933,42.61175326301319,42.68637980112705,42.761006339240915,42.83563287735477,42.91025941546863,42.984885953582484,43.05951249169634,43.1341390298102,43.20876556792406,43.28339210603792,43.35801864415178,43.43264518226564,43.5072717203795,43.58189825849336,43.65652479660721,43.73115133472107,43.80577787283493,43.880404410948785,43.955030949062646,44.02965748717651,44.10428402529037,44.17891056340422,44.25353710151808,44.32816363963193,44.40279017774579,44.477416715859654,44.552043253973515,44.62666979208737,44.70129633020123,44.77592286831509,44.850549406428954,44.9251759445428,44.99980248265666,45.07442902077052,45.14905555888438,45.22368209699824,45.2983086351121,45.37293517322596,45.447561711339816,45.52218824945368,45.59681478756753,45.671441325681386,45.74606786379525,45.82069440190911,45.89532094002297,45.969947478136824,46.044574016250685,46.11920055436455,46.193827092478394,46.268453630592255,46.343080168706116,46.41770670681998,46.49233324493383,46.56695978304769,46.641586321161554,46.716212859275416,46.79083939738927,46.865465935503124,46.940092473616986,47.01471901173084,47.0893455498447,47.16397208795856,47.238598626072424,47.31322516418628,47.38785170230014,47.462478240414,47.53710477852785,47.61173131664171,47.68635785475557,47.760984392869425,47.835610930983286,47.91023746909715,47.98486400721101,48.05949054532486,48.13411708343872,48.20874362155258,48.28337015966643,48.357996697780294,48.432623235894155,48.50724977400802,48.58187631212187,48.65650285023573,48.73112938834959,48.80575592646344,48.8803824645773,48.95500900269116,49.029635540805025,49.10426207891888,49.17888861703274,49.2535151551466,49.32814169326046,49.40276823137432,49.47739476948817,49.55202130760203,49.62664784571589,49.70127438382975,49.77590092194361,49.85052746005747,49.925153998171325,49.999780536285186,50.07440707439903,50.149033612512895,50.223660150626756,50.29828668874062,50.37291322685447,50.44753976496833,50.522166303082194,50.596792841196056,50.67141937930991,50.746045917423764,50.820672455537625,50.89529899365148,50.96992553176534,51.0445520698792,51.119178607993064,51.19380514610692,51.26843168422078,51.34305822233464,51.41768476044849,51.49231129856235,51.56693783667621,51.64156437479007,51.716190912903926,51.79081745101779,51.86544398913165,51.94007052724551,52.01469706535936,52.08932360347322,52.16395014158708,52.238576679700934,52.313203217814795,52.38782975592866,52.46245629404252,52.53708283215637,52.61170937027023,52.68633590838409,52.76096244649794,52.8355889846118,52.910215522725665,52.984842060839526,53.05946859895338,53.13409513706724,53.2087216751811,53.283348213294964,53.35797475140881,53.43260128952267,53.50722782763653,53.58185436575039,53.65648090386425,53.73110744197811,53.805733980091965,53.880360518205826,53.95498705631968,54.029613594433535,54.104240132547396,54.17886667066126,54.25349320877512,54.32811974688897,54.402746285002834,54.477372823116696,54.55199936123056,54.626625899344404,54.701252437458265,54.77587897557213,54.85050551368598,54.92513205179984,54.999758589913704,55.074385128027565,55.14901166614142,55.22363820425528,55.298264742369135,55.37289128048299,55.44751781859685,55.52214435671071,55.59677089482457,55.67139743293843,55.74602397105229,55.82065050916615,55.89527704728,55.96990358539386,56.04453012350772,56.119156661621574,56.193783199735435,56.2684097378493,56.34303627596316,56.41766281407701,56.49228935219087,56.56691589030473,56.64154242841858,56.71616896653244,56.790795504646304,56.865422042760166,56.94004858087402,57.01467511898788,57.08930165710174,57.163928195215604,57.23855473332945,57.31318127144331,57.387807809557174,57.46243434767103,57.53706088578489,57.61168742389875,57.68631396201261,57.760940500126466,57.83556703824032,57.91019357635418,57.984820114468036,58.0594466525819,58.13407319069576,58.20869972880962,58.283326266923474,58.357952805037336,58.4325793431512,58.507205881265044,58.581832419378905,58.65645895749277,58.73108549560663,58.80571203372048,58.88033857183434,58.954965109948205,59.029591648062066,59.10421818617592,59.178844724289775,59.25347126240363,59.32809780051749,59.40272433863135,59.47735087674521,59.55197741485907,59.62660395297293,59.70123049108679,59.77585702920064,59.8504835673145,59.92511010542836,59.99973664354222,60.074363181656075,60.148989719769936,60.2236162578838,60.29824279599766,60.37286933411151,60.44749587222537,60.52212241033923,60.59674894845308,60.671375486566944,60.746002024680806,60.82062856279467,60.89525510090852,60.96988163902238,61.044508177136244,61.11913471525009,61.19376125336395,61.268387791477814,61.343014329591675,61.41764086770553,61.49226740581939,61.56689394393325,61.64152048204711,61.71614702016096,61.79077355827482,61.86540009638868,61.94002663450254,62.0146531726164,62.08927971073026,62.163906248844114,62.238532786957975,62.31315932507184,62.387785863185684,62.462412401299545,62.53703893941341,62.61166547752727,62.68629201564112,62.76091855375498,62.835545091868845,62.910171629982706,62.98479816809655,63.059424706210415,63.134051244324276,63.20867778243813,63.28330432055199,63.35793085866585,63.432557396779714,63.50718393489357,63.58181047300743,63.656437011121284,63.73106354923514,63.805690087349,63.88031662546286,63.95494316357672,64.02956970169058,64.10419623980444,64.1788227779183,64.25344931603216,64.32807585414601,64.40270239225987,64.47732893037373,64.55195546848759,64.62658200660145,64.7012085447153,64.77583508282916,64.85046162094302,64.92508815905687,64.99971469717073,65.07434123528459,65.14896777339845,65.22359431151231,65.29822084962618,65.37284738774004,65.44747392585388,65.52210046396775,65.59672700208161,65.67135354019545,65.74598007830932,65.82060661642318,65.89523315453704,65.9698596926509,66.04448623076476,66.11911276887862,66.19373930699248,66.26836584510633,66.34299238322019,66.41761892133405,66.4922454594479,66.56687199756176,66.64149853567562,66.71612507378948,66.79075161190335,66.8653781500172,66.94000468813105,67.01463122624492,67.08925776435878,67.16388430247264,67.2385108405865,67.31313737870035,67.38776391681421,67.46239045492807,67.53701699304192,67.61164353115578,67.68627006926964,67.7608966073835,67.83552314549736,67.91014968361122,67.98477622172508,68.05940275983895,68.1340292979528,68.20865583606665,68.2832823741805,68.35790891229436,68.43253545040822,68.50716198852209,68.58178852663595,68.65641506474981,68.73104160286367,68.80566814097752,68.88029467909138,68.95492121720524,69.0295477553191,69.10417429343295,69.17880083154681,69.25342736966067,69.32805390777453,69.4026804458884,69.47730698400224,69.5519335221161,69.62656006022996,69.70118659834382,69.77581313645769,69.85043967457155,69.9250662126854,69.99969275079926,70.07431928891312,70.14894582702696,70.22357236514083,70.29819890325469,70.37282544136855,70.44745197948241,70.52207851759627,70.59670505571013,70.671331593824,70.74595813193784,70.8205846700517,70.89521120816555,70.96983774627941,71.04446428439327,71.11909082250713,71.193717360621,71.26834389873486,71.34297043684872,71.41759697496256,71.49222351307642,71.56685005119029,71.64147658930415,71.716103127418,71.79072966553186,71.86535620364572,71.93998274175958,72.01460927987344,72.08923581798729,72.16386235610115,72.23848889421501,72.31311543232887,72.38774197044273,72.4623685085566,72.53699504667044,72.6116215847843,72.68624812289816,72.76087466101201,72.83550119912587,72.91012773723973,72.9847542753536,73.05938081346746,73.13400735158132,73.20863388969518,73.28326042780904,73.35788696592289,73.43251350403675,73.50714004215061,73.58176658026446,73.65639311837832,73.73101965649218,73.80564619460604,73.8802727327199,73.95489927083376,74.02952580894761,74.10415234706147,74.17877888517533,74.2534054232892,74.32803196140304,74.4026584995169,74.47728503763076,74.55191157574463,74.62653811385847,74.70116465197233,74.7757911900862,74.85041772820006,74.92504426631392,74.99967080442778,75.07429734254164,75.14892388065549,75.22355041876935,75.29817695688321,75.37280349499706,75.44743003311092,75.52205657122478,75.59668310933864,75.6713096474525,75.74593618556636,75.82056272368023,75.89518926179409,75.96981579990793,76.0444423380218,76.11906887613566,76.1936954142495,76.26832195236337,76.34294849047723,76.41757502859109,76.49220156670495,76.5668281048188,76.64145464293266,76.71608118104652,76.79070771916038,76.86533425727424,76.93996079538809,77.01458733350195,77.08921387161581,77.16384040972967,77.23846694784352,77.31309348595738,77.38772002407124,77.4623465621851,77.53697310029897,77.61159963841283,77.68622617652669,77.76085271464054,77.8354792527544,77.91010579086826,77.9847323289821,78.05935886709597,78.13398540520983,78.20861194332369,78.28323848143755,78.35786501955141,78.43249155766527,78.50711809577912,78.58174463389298,78.65637117200684,78.7309977101207,78.80562424823455,78.88025078634841,78.95487732446227,79.02950386257614,79.10413040069,79.17875693880384,79.2533834769177,79.32801001503157,79.40263655314543,79.47726309125929,79.55188962937315,79.626516167487,79.70114270560086,79.77576924371472,79.85039578182857,79.92502231994243,79.99964885805629,80.07427539617015,80.14890193428401,80.22352847239787,80.29815501051173,80.37278154862558,80.44740808673944,80.5220346248533,80.59666116296715,80.67128770108101,80.74591423919487,80.82054077730874,80.8951673154226,80.96979385353646,81.04442039165032,81.11904692976417,81.19367346787803,81.26830000599189,81.34292654410575,81.4175530822196,81.49217962033346,81.56680615844732,81.64143269656118,81.71605923467504,81.79068577278889,81.86531231090275,81.93993884901661,82.01456538713047,82.08919192524434,82.1638184633582,82.23844500147204,82.3130715395859,82.38769807769977,82.46232461581361,82.53695115392748,82.61157769204134,82.6862042301552],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117],"y":[0,0.06505086291196244,0.13010172582392487,0.1951525887358873,0.26020345164784975,0.32525431455981213,0.3903051774717746,0.45535604038373706,0.5204069032956995,0.585457766207662,0.6505086291196243,0.7155594920315868,0.7806103549435492,0.8456612178555116,0.9107120807674741,0.9757629436794364,1.040813806591399,1.1058646695033614,1.170915532415324,1.2359663953272861,1.3010172582392485,1.3660681211512111,1.4311189840631735,1.496169846975136,1.5612207098870985,1.6262715727990606,1.6913224357110233,1.7563732986229856,1.8214241615349482,1.8864750244469106,1.9515258873588728,2.0165767502708354,2.081627613182798,2.14667847609476,2.2117293390067227,2.2767802019186854,2.341831064830648,2.40688192774261,2.4719327906545723,2.536983653566535,2.602034516478497,2.6670853793904596,2.7321362423024222,2.7971871052143844,2.862237968126347,2.9272888310383096,2.992339693950272,3.0573905568622344,3.122441419774197,3.187492282686159,3.2525431455981213,3.317594008510084,3.3826448714220465,3.447695734334009,3.5127465972459713,3.577797460157934,3.6428483230698965,3.7078991859818586,3.7729500488938212,3.838000911805784,3.9030517747177456,3.968102637629708,4.033153500541671,4.098204363453633,4.163255226365596,4.228306089277559,4.29335695218952,4.358407815101483,4.4234586780134455,4.488509540925408,4.553560403837371,4.618611266749333,4.683662129661296,4.748712992573258,4.81376385548522,4.878814718397183,4.9438655813091446,5.008916444221107,5.07396730713307,5.1390181700450315,5.204069032956994,5.269119895868957,5.334170758780919,5.399221621692882,5.4642724846048445,5.529323347516807,5.594374210428769,5.659425073340731,5.724475936252694,5.789526799164657,5.854577662076619,5.919628524988582,5.984679387900544,6.049730250812506,6.114781113724469,6.179831976636431,6.244882839548394,6.309933702460356,6.374984565372318,6.440035428284281,6.505086291196243,6.570137154108205,6.635188017020168,6.70023887993213,6.765289742844093,6.830340605756056,6.895391468668018,6.96044233157998,7.0254931944919425,7.090544057403905,7.155594920315868,7.22064578322783,7.285696646139793,7.350747509051756,7.415798371963717,7.48084923487568,7.5459000977876425,7.610950960699605,7.676001823611568,7.741052686523529,7.806103549435491,7.871154412347454,7.936205275259416,8.00125613817138,8.066307001083342,8.131357863995303,8.196408726907267,8.261459589819228,8.326510452731192,8.391561315643154,8.456612178555117,8.521663041467079,8.58671390437904,8.651764767291004,8.716815630202966,8.78186649311493,8.846917356026891,8.911968218938854,8.977019081850816,9.042069944762778,9.107120807674741,9.172171670586703,9.237222533498667,9.302273396410628,9.367324259322592,9.432375122234554,9.497425985146515,9.562476848058479,9.62752771097044,9.692578573882404,9.757629436794366,9.822680299706326,9.887731162618289,9.95278202553025,10.017832888442214,10.082883751354176,10.14793461426614,10.212985477178101,10.278036340090063,10.343087203002026,10.408138065913988,10.473188928825952,10.538239791737913,10.603290654649877,10.668341517561839,10.7333923804738,10.798443243385764,10.863494106297725,10.928544969209689,10.99359583212165,11.058646695033614,11.123697557945576,11.188748420857538,11.253799283769501,11.318850146681463,11.383901009593426,11.448951872505388,11.514002735417352,11.579053598329313,11.644104461241275,11.709155324153238,11.7742061870652,11.839257049977164,11.904307912889125,11.969358775801089,12.03440963871305,12.099460501625012,12.164511364536976,12.229562227448938,12.294613090360901,12.359663953272863,12.424714816184826,12.489765679096788,12.55481654200875,12.619867404920711,12.684918267832673,12.749969130744637,12.815019993656598,12.880070856568562,12.945121719480523,13.010172582392485,13.075223445304449,13.14027430821641,13.205325171128374,13.270376034040336,13.335426896952299,13.40047775986426,13.465528622776223,13.530579485688186,13.595630348600148,13.660681211512111,13.725732074424073,13.790782937336036,13.855833800247998,13.92088466315996,13.985935526071923,14.050986388983885,14.116037251895849,14.18108811480781,14.246138977719774,14.311189840631735,14.376240703543697,14.44129156645566,14.506342429367622,14.571393292279586,14.636444155191548,14.701495018103511,14.766545881015473,14.831596743927435,14.896647606839398,14.96169846975136,15.026749332663323,15.091800195575285,15.156851058487248,15.22190192139921,15.286952784311172,15.352003647223135,15.417054510135095,15.482105373047059,15.54715623595902,15.612207098870982,15.677257961782946,15.742308824694907,15.807359687606871,15.872410550518833,15.937461413430796,16.00251227634276,16.06756313925472,16.132614002166683,16.197664865078647,16.262715727990606,16.32776659090257,16.392817453814533,16.457868316726497,16.522919179638457,16.58797004255042,16.653020905462384,16.718071768374344,16.783122631286307,16.84817349419827,16.913224357110234,16.978275220022194,17.043326082934158,17.10837694584612,17.17342780875808,17.238478671670045,17.303529534582008,17.36858039749397,17.43363126040593,17.498682123317895,17.56373298622986,17.62878384914182,17.693834712053782,17.758885574965745,17.82393643787771,17.88898730078967,17.954038163701632,18.019089026613596,18.084139889525556,18.14919075243752,18.214241615349483,18.279292478261446,18.344343341173406,18.40939420408537,18.474445066997333,18.539495929909293,18.604546792821257,18.66959765573322,18.734648518645184,18.799699381557144,18.864750244469107,18.92980110738107,18.99485197029303,19.059902833204994,19.124953696116958,19.19000455902892,19.25505542194088,19.320106284852844,19.385157147764808,19.450208010676768,19.51525887358873,19.58030973650069,19.64536059941265,19.710411462324615,19.775462325236578,19.84051318814854,19.9055640510605,19.970614913972465,20.03566577688443,20.10071663979639,20.165767502708352,20.230818365620316,20.29586922853228,20.36092009144424,20.425970954356202,20.491021817268166,20.556072680180126,20.62112354309209,20.686174406004053,20.751225268916016,20.816276131827976,20.88132699473994,20.946377857651903,21.011428720563863,21.076479583475827,21.14153044638779,21.206581309299754,21.271632172211714,21.336683035123677,21.40173389803564,21.4667847609476,21.531835623859564,21.596886486771528,21.66193734968349,21.72698821259545,21.792039075507414,21.857089938419378,21.922140801331338,21.9871916642433,22.052242527155265,22.11729339006723,22.18234425297919,22.247395115891152,22.312445978803115,22.377496841715075,22.44254770462704,22.507598567539002,22.572649430450966,22.637700293362926,22.70275115627489,22.767802019186853,22.832852882098813,22.897903745010776,22.96295460792274,23.028005470834703,23.093056333746663,23.158107196658626,23.22315805957059,23.28820892248255,23.353259785394513,23.418310648306477,23.48336151121844,23.5484123741304,23.613463237042364,23.678514099954327,23.743564962866287,23.80861582577825,23.873666688690214,23.938717551602178,24.003768414514138,24.0688192774261,24.133870140338065,24.198921003250025,24.263971866161988,24.32902272907395,24.394073591985915,24.459124454897875,24.52417531780984,24.589226180721802,24.654277043633762,24.719327906545725,24.78437876945769,24.849429632369652,24.914480495281612,24.979531358193576,25.04458222110554,25.1096330840175,25.17468394692946,25.239734809841423,25.304785672753383,25.369836535665346,25.43488739857731,25.499938261489273,25.564989124401233,25.630039987313197,25.69509085022516,25.760141713137124,25.825192576049083,25.890243438961047,25.95529430187301,26.02034516478497,26.085396027696934,26.150446890608897,26.21549775352086,26.28054861643282,26.345599479344784,26.410650342256748,26.475701205168708,26.54075206808067,26.605802930992635,26.670853793904598,26.735904656816558,26.80095551972852,26.866006382640485,26.931057245552445,26.99610810846441,27.061158971376372,27.126209834288336,27.191260697200295,27.25631156011226,27.321362423024222,27.386413285936182,27.451464148848146,27.51651501176011,27.581565874672073,27.646616737584033,27.711667600495996,27.77671846340796,27.84176932631992,27.906820189231883,27.971871052143847,28.03692191505581,28.10197277796777,28.167023640879734,28.232074503791697,28.297125366703657,28.36217622961562,28.427227092527584,28.492277955439548,28.557328818351507,28.62237968126347,28.687430544175434,28.752481407087394,28.817532269999358,28.88258313291132,28.947633995823285,29.012684858735245,29.07773572164721,29.142786584559172,29.20783744747113,29.272888310383095,29.33793917329506,29.402990036207022,29.468040899118982,29.533091762030946,29.59814262494291,29.66319348785487,29.728244350766833,29.793295213678796,29.85834607659076,29.92339693950272,29.988447802414683,30.053498665326646,30.118549528238606,30.18360039115057,30.248651254062533,30.313702116974497,30.378752979886457,30.44380384279842,30.508854705710384,30.573905568622344,30.638956431534307,30.70400729444627,30.769058157358227,30.83410902027019,30.899159883182154,30.964210746094118,31.029261609006078,31.09431247191804,31.159363334830005,31.224414197741964,31.289465060653928,31.35451592356589,31.419566786477855,31.484617649389815,31.54966851230178,31.614719375213742,31.679770238125702,31.744821101037665,31.80987196394963,31.874922826861592,31.939973689773552,32.00502455268552,32.07007541559748,32.13512627850944,32.200177141421406,32.265228004333366,32.330278867245326,32.39532973015729,32.46038059306925,32.52543145598121,32.59048231889318,32.65553318180514,32.7205840447171,32.78563490762907,32.85068577054103,32.915736633452994,32.980787496364954,33.045838359276914,33.11088922218888,33.17594008510084,33.2409909480128,33.30604181092477,33.37109267383673,33.43614353674869,33.501194399660655,33.566245262572615,33.631296125484575,33.69634698839654,33.7613978513085,33.82644871422047,33.89149957713243,33.95655044004439,34.021601302956356,34.086652165868315,34.151703028780275,34.21675389169224,34.2818047546042,34.34685561751616,34.41190648042813,34.47695734334009,34.54200820625205,34.607059069164016,34.672109932075976,34.73716079498794,34.8022116578999,34.86726252081186,34.93231338372383,34.99736424663579,35.06241510954775,35.12746597245972,35.19251683537168,35.25756769828364,35.322618561195604,35.387669424107564,35.452720287019524,35.51777114993149,35.58282201284345,35.64787287575542,35.71292373866738,35.77797460157934,35.843025464491305,35.908076327403265,35.973127190315225,36.03817805322719,36.10322891613915,36.16827977905111,36.23333064196308,36.29838150487504,36.363432367787,36.428483230698966,36.493534093610926,36.55858495652289,36.623635819434845,36.68868668234681,36.75373754525877,36.81878840817074,36.8838392710827,36.94889013399467,37.01394099690662,37.078991859818586,37.144042722730546,37.20909358564251,37.27414444855447,37.33919531146644,37.4042461743784,37.46929703729037,37.53434790020232,37.59939876311429,37.66444962602625,37.729500488938214,37.794551351850174,37.85960221476214,37.924653077674094,37.98970394058606,38.05475480349802,38.11980566640999,38.18485652932195,38.249907392233915,38.314958255145875,38.38000911805784,38.445059980969795,38.51011084388176,38.57516170679372,38.64021256970569,38.70526343261765,38.770314295529616,38.83536515844157,38.900416021353536,38.965466884265496,39.03051774717746,39.09556861008942,39.16061947300138,39.22567033591335,39.2907211988253,39.35577206173727,39.42082292464923,39.4858737875612,39.550924650473156,39.61597551338512,39.68102637629708,39.74607723920904,39.811128102121,39.87617896503297,39.94122982794493,40.0062806908569,40.07133155376886,40.136382416680824,40.20143327959278,40.266484142504744,40.331535005416704,40.39658586832867,40.46163673124063,40.5266875941526,40.59173845706456,40.65678931997652,40.72184018288848,40.786891045800445,40.851941908712405,40.91699277162437,40.98204363453633,41.0470944974483,41.11214536036025,41.17719622327222,41.24224708618418,41.307297949096146,41.372348812008106,41.43739967492007,41.50245053783203,41.56750140074399,41.63255226365595,41.69760312656792,41.76265398947988,41.82770485239185,41.89275571530381,41.957806578215774,42.02285744112773,42.08790830403969,42.15295916695165,42.21801002986362,42.28306089277558,42.34811175568755,42.41316261859951,42.478213481511474,42.54326434442343,42.608315207335394,42.673366070247354,42.73841693315932,42.80346779607128,42.86851865898325,42.9335695218952,42.99862038480717,43.06367124771913,43.128722110631095,43.193772973543055,43.25882383645502,43.32387469936698,43.38892556227895,43.4539764251909,43.51902728810287,43.58407815101483,43.649129013926796,43.714179876838756,43.77923073975072,43.844281602662676,43.90933246557464,43.9743833284866,44.03943419139857,44.10448505431053,44.1695359172225,44.23458678013446,44.299637643046424,44.36468850595838,44.429739368870344,44.494790231782304,44.55984109469427,44.62489195760623,44.6899428205182,44.75499368343015,44.82004454634211,44.88509540925408,44.95014627216604,45.015197135078004,45.080247997989964,45.14529886090193,45.210349723813884,45.27540058672585,45.34045144963781,45.40550231254978,45.47055317546174,45.535604038373705,45.600654901285665,45.665705764197625,45.730756627109585,45.79580749002155,45.86085835293351,45.92590921584548,45.99096007875744,46.056010941669406,46.12106180458136,46.186112667493326,46.251163530405286,46.31621439331725,46.38126525622921,46.44631611914118,46.51136698205314,46.5764178449651,46.64146870787706,46.70651957078903,46.77157043370099,46.836621296612954,46.901672159524914,46.96672302243688,47.031773885348834,47.0968247482608,47.16187561117276,47.22692647408473,47.29197733699669,47.357028199908655,47.422079062820615,47.487129925732575,47.552180788644534,47.6172316515565,47.68228251446846,47.74733337738043,47.81238424029239,47.877435103204355,47.94248596611631,48.007536829028275,48.072587691940235,48.1376385548522,48.20268941776416,48.26774028067613,48.33279114358809,48.39784200650005,48.46289286941201,48.527943732323976,48.592994595235936,48.6580454581479,48.72309632105986,48.78814718397183,48.85319804688378,48.91824890979575,48.98329977270771,49.04835063561968,49.11340149853164,49.178452361443604,49.243503224355564,49.308554087267524,49.373604950179484,49.43865581309145,49.50370667600341,49.56875753891538,49.63380840182734,49.698859264739305,49.76391012765126,49.828960990563225,49.894011853475185,49.95906271638715,50.02411357929911,50.08916444221108,50.15421530512304,50.219266168035,50.28431703094696,50.34936789385892,50.414418756770885,50.479469619682845,50.54452048259481,50.609571345506765,50.67462220841873,50.73967307133069,50.80472393424266,50.86977479715462,50.934825660066586,50.999876522978546,51.06492738589051,51.129978248802466,51.19502911171443,51.26007997462639,51.32513083753836,51.39018170045032,51.45523256336229,51.52028342627425,51.58533428918621,51.65038515209817,51.715436015010134,51.780486877922094,51.84553774083406,51.91058860374602,51.97563946665799,52.04069032956994,52.10574119248191,52.17079205539387,52.235842918305835,52.300893781217795,52.36594464412976,52.43099550704172,52.49604636995368,52.56109723286564,52.62614809577761,52.69119895868957,52.756249821601536,52.821300684513496,52.88635154742546,52.951402410337415,53.01645327324938,53.08150413616134,53.14655499907331,53.21160586198527,53.27665672489724,53.341707587809196,53.406758450721156,53.471809313633116,53.53686017654508,53.60191103945704,53.66696190236901,53.73201276528097,53.79706362819294,53.86211449110489,53.92716535401686,53.99221621692882,54.057267079840784,54.122317942752744,54.18736880566471,54.25241966857667,54.31747053148863,54.38252139440059,54.44757225731256,54.51262312022452,54.577673983136485,54.642724846048445,54.70777570896041,54.772826571872365,54.83787743478433,54.90292829769629,54.96797916060826,55.03303002352022,55.098080886432186,55.163131749344146,55.228182612256106,55.293233475168066,55.35828433808003,55.42333520099199,55.48838606390396,55.55343692681592,55.61848778972789,55.68353865263984,55.74858951555181,55.81364037846377,55.87869124137573,55.94374210428769,56.00879296719965,56.07384383011162,56.13889469302357,56.20394555593554,56.2689964188475,56.33404728175947,56.39909814467143,56.464149007583394,56.52919987049535,56.594250733407314,56.659301596319274,56.72435245923124,56.7894033221432,56.85445418505517,56.91950504796713,56.984555910879095,57.04960677379105,57.114657636703015,57.179708499614975,57.24475936252694,57.3098102254389,57.37486108835087,57.43991195126282,57.50496281417479,57.57001367708675,57.635064539998716,57.700115402910676,57.76516626582264,57.8302171287346,57.89526799164657,57.96031885455852,58.02536971747049,58.09042058038245,58.15547144329442,58.22052230620638,58.285573169118344,58.3506240320303,58.41567489494226,58.48072575785422,58.54577662076619,58.61082748367815,58.67587834659012,58.74092920950208,58.805980072414044,58.871030935326,58.936081798237964,59.001132661149924,59.06618352406189,59.13123438697385,59.19628524988582,59.26133611279777,59.32638697570974,59.3914378386217,59.456488701533665,59.521539564445625,59.58659042735759,59.65164129026955,59.71669215318152,59.78174301609347,59.84679387900544,59.9118447419174,59.976895604829366,60.041946467741326,60.10699733065329,60.172048193565246,60.23709905647721,60.30214991938917,60.36720078230114,60.4322516452131,60.49730250812507,60.56235337103703,60.627404233948994,60.69245509686095,60.757505959772914,60.822556822684874,60.88760768559684,60.9526585485088,61.01770941142077,61.08276027433272,61.14781113724469,61.21286200015665,61.277912863068615,61.342963725980574,61.40801458889254,61.4730654518045,61.538116314716454,61.60316717762842,61.66821804054038,61.73326890345235,61.79831976636431,61.863370629276275,61.928421492188235,61.993472355100195,62.058523218012155,62.12357408092412,62.18862494383608,62.25367580674805,62.31872666966001,62.383777532571976,62.44882839548393,62.513879258395896,62.578930121307856,62.64398098421982,62.70903184713178,62.77408271004375,62.83913357295571,62.90418443586768,62.96923529877963,63.0342861616916,63.09933702460356,63.164387887515524,63.229438750427484,63.29448961333945,63.359540476251404,63.42459133916337,63.48964220207533,63.5546930649873,63.61974392789926,63.684794790811225,63.749845653723185,63.81489651663515,63.879947379547104,63.94499824245907,64.01004910537104,64.075099968283,64.14015083119496,64.20520169410692,64.27025255701888,64.33530341993085,64.40035428284281,64.46540514575477,64.53045600866673,64.59550687157869,64.66055773449065,64.72560859740263,64.79065946031459,64.85571032322655,64.9207611861385,64.98581204905047,65.05086291196243,65.1159137748744,65.18096463778636,65.24601550069832,65.31106636361028,65.37611722652225,65.4411680894342,65.50621895234617,65.57126981525813,65.6363206781701,65.70137154108205,65.76642240399403,65.83147326690599,65.89652412981795,65.96157499272991,66.02662585564187,66.09167671855383,66.1567275814658,66.22177844437776,66.28682930728972,66.35188017020168,66.41693103311364,66.4819818960256,66.54703275893758,66.61208362184954,66.6771344847615,66.74218534767346,66.80723621058542,66.87228707349738,66.93733793640935,67.00238879932131,67.06743966223327,67.13249052514523,67.19754138805719,67.26259225096915,67.32764311388111,67.39269397679308,67.45774483970504,67.522795702617,67.58784656552896,67.65289742844094,67.71794829135288,67.78299915426486,67.84805001717682,67.91310088008878,67.97815174300074,68.04320260591271,68.10825346882466,68.17330433173663,68.23835519464859,68.30340605756055,68.36845692047251,68.43350778338448,68.49855864629644,68.5636095092084,68.62866037212036,68.69371123503232,68.75876209794428,68.82381296085626,68.88886382376822,68.95391468668018,69.01896554959214,69.0840164125041,69.14906727541606,69.21411813832803,69.27916900123999,69.34421986415195,69.40927072706391,69.47432158997589,69.53937245288783,69.6044233157998,69.66947417871177,69.73452504162373,69.79957590453569,69.86462676744766,69.9296776303596,69.99472849327158,70.05977935618354,70.1248302190955,70.18988108200746,70.25493194491943,70.3199828078314,70.38503367074335,70.45008453365531,70.51513539656727,70.58018625947923,70.64523712239121,70.71028798530317,70.77533884821513,70.84038971112709,70.90544057403905,70.97049143695101,71.03554229986298,71.10059316277494,71.1656440256869,71.23069488859886,71.29574575151084,71.36079661442278,71.42584747733476,71.49089834024672,71.55594920315868,71.62100006607064,71.68605092898261,71.75110179189456,71.81615265480653,71.88120351771849,71.94625438063045,72.01130524354241,72.07635610645438,72.14140696936634,72.2064578322783,72.27150869519026,72.33655955810222,72.40161042101418,72.46666128392616,72.53171214683812,72.59676300975008,72.66181387266204],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146],"y":[0,0.3140497008049242,0.6280994016098485,0.9421491024147727,1.256198803219697,1.570248504024621,1.8842982048295454,2.1983479056344697,2.512397606439394,2.826447307244318,3.140497008049242,3.4545467088541666,3.7685964096590907,4.082646110464015,4.396695811268939,4.7107455120738635,5.024795212878788,5.338844913683712,5.652894614488636,5.96694431529356,6.280994016098484,6.595043716903408,6.909093417708333,7.223143118513257,7.537192819318181,7.8512425201231055,8.16529222092803,8.479341921732955,8.793391622537879,9.107441323342803,9.421491024147727,9.735540724952651,10.049590425757575,10.3636401265625,10.677689827367423,10.991739528172348,11.305789228977272,11.619838929782198,11.93388863058712,12.247938331392046,12.561988032196968,12.876037733001894,13.190087433806816,13.504137134611742,13.818186835416666,14.13223653622159,14.446286237026515,14.760335937831439,15.074385638636363,15.388435339441289,15.702485040246211,16.016534741051135,16.33058444185606,16.644634142660983,16.95868384346591,17.27273354427083,17.586783245075758,17.900832945880683,18.214882646685606,18.52893234749053,18.842982048295454,19.15703174910038,19.471081449905302,19.785131150710228,20.09918085151515,20.413230552320076,20.727280253125,21.041329953929925,21.355379654734847,21.669429355539773,21.983479056344695,22.29752875714962,22.611578457954543,22.92562815875947,23.239677859564395,23.55372756036932,23.86777726117424,24.181826961979166,24.49587666278409,24.809926363589017,25.123976064393936,25.438025765198862,25.752075466003788,26.066125166808714,26.380174867613633,26.69422456841856,27.008274269223485,27.32232397002841,27.636373670833333,27.950423371638255,28.26447307244318,28.578522773248107,28.89257247405303,29.206622174857955,29.520671875662877,29.834721576467803,30.148771277272726,30.46282097807765,30.776870678882577,31.0909203796875,31.404970080492422,31.719019781297348,32.03306948210227,32.3471191829072,32.66116888371212,32.975218584517044,33.28926828532197,33.603317986126896,33.91736768693182,34.23141738773674,34.54546708854166,34.85951678934659,35.173566490151515,35.48761619095644,35.80166589176137,36.11571559256629,36.42976529337121,36.743814994176134,37.05786469498106,37.371914395785986,37.68596409659091,38.00001379739583,38.31406349820076,38.62811319900568,38.942162899810604,39.25621260061553,39.570262301420456,39.88431200222538,40.1983617030303,40.51241140383522,40.82646110464015,41.140510805445075,41.45456050625,41.76861020705492,42.08265990785985,42.39670960866477,42.710759309469694,43.024809010274616,43.338858711079546,43.65290841188447,43.96695811268939,44.28100781349431,44.59505751429924,44.909107215104164,45.22315691590909,45.537206616714016,45.85125631751894],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126],"y":[0,0.1560026364458836,0.3120052728917672,0.4680079093376508,0.6240105457835344,0.780013182229418,0.9360158186753016,1.0920184551211851,1.2480210915670689,1.4040237280129524,1.560026364458836,1.7160290009047197,1.8720316373506032,2.0280342737964867,2.1840369102423702,2.340039546688254,2.4960421831341377,2.6520448195800213,2.8080474560259048,2.9640500924717883,3.120052728917672,3.276055365363556,3.4320580018094393,3.588060638255323,3.7440632747012064,3.90006591114709,4.056068547592973,4.212071184038857,4.3680738204847405,4.524076456930624,4.680079093376508,4.836081729822392,4.9920843662682755,5.148087002714159,5.3040896391600425,5.460092275605927,5.6160949120518096,5.772097548497694,5.928100184943577,6.084102821389461,6.240105457835344,6.396108094281228,6.552110730727112,6.708113367172995,6.864116003618879,7.020118640064762,7.176121276510646,7.33212391295653,7.488126549402413,7.644129185848297,7.80013182229418,7.956134458740064,8.112137095185947,8.268139731631832,8.424142368077714,8.5801450045236,8.736147640969481,8.892150277415366,9.048152913861248,9.204155550307133,9.360158186753017,9.5161608231989,9.672163459644784,9.828166096090667,9.984168732536551,10.140171368982436,10.296174005428318,10.452176641874201,10.608179278320085,10.76418191476597,10.920184551211854,11.076187187657736,11.232189824103619,11.388192460549504,11.544195096995388,11.700197733441271,11.856200369887153,12.012203006333039,12.168205642778922,12.324208279224806,12.480210915670687,12.636213552116573,12.792216188562456,12.94821882500834,13.104221461454223,13.260224097900108,13.41622673434599,13.572229370791874,13.728232007237757,13.884234643683643,14.040237280129524,14.196239916575408,14.352242553021291,14.508245189467177,14.66424782591306,14.820250462358942,14.976253098804825,15.13225573525071,15.288258371696594,15.444261008142478,15.60026364458836,15.756266281034245,15.912268917480128,16.06827155392601,16.224274190371894,16.38027682681778,16.536279463263664,16.692282099709544,16.848284736155428,17.004287372601315,17.1602900090472,17.316292645493082,17.472295281938962,17.62829791838485,17.784300554830732,17.940303191276616,18.096305827722496,18.252308464168383,18.408311100614267,18.56431373706015,18.720316373506034,18.876319009951917,19.0323216463978,19.188324282843684,19.344326919289568,19.50032955573545,19.656332192181335],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919],"y":[0,0.017735826593006685,0.03547165318601337,0.05320747977902006,0.07094330637202674,0.08867913296503342,0.10641495955804012,0.1241507861510468,0.14188661274405348,0.15962243933706016,0.17735826593006684,0.19509409252307353,0.21282991911608024,0.2305657457090869,0.2483015723020936,0.2660373988951003,0.28377322548810696,0.3015090520811136,0.3192448786741203,0.33698070526712703,0.3547165318601337,0.3724523584531404,0.39018818504614705,0.40792401163915376,0.42565983823216047,0.4433956648251671,0.4611314914181738,0.47886731801118054,0.4966031446041872,0.5143389711971939,0.5320747977902006,0.5498106243832072,0.5675464509762139,0.5852822775692207,0.6030181041622272,0.620753930755234,0.6384897573482406,0.6562255839412473,0.6739614105342541,0.6916972371272607,0.7094330637202674,0.727168890313274,0.7449047169062808,0.7626405434992874,0.7803763700922941,0.7981121966853009,0.8158480232783075,0.8335838498713142,0.8513196764643209,0.8690555030573275,0.8867913296503342,0.904527156243341,0.9222629828363476,0.9399988094293543,0.9577346360223611,0.9754704626153676,0.9932062892083744,1.010942115801381,1.0286779423943877,1.0464137689873945,1.0641495955804012,1.0818854221734078,1.0996212487664143,1.117357075359421,1.1350929019524278,1.1528287285454346,1.1705645551384414,1.188300381731448,1.2060362083244545,1.2237720349174612,1.241507861510468,1.2592436881034748,1.2769795146964813,1.294715341289488,1.3124511678824946,1.3301869944755014,1.3479228210685081,1.3656586476615147,1.3833944742545214,1.4011303008475282,1.4188661274405348,1.4366019540335415,1.454337780626548,1.4720736072195548,1.4898094338125616,1.5075452604055681,1.525281086998575,1.5430169135915817,1.5607527401845882,1.578488566777595,1.5962243933706017,1.6139602199636083,1.631696046556615,1.6494318731496216,1.6671676997426284,1.6849035263356351,1.7026393529286419,1.7203751795216484,1.738111006114655,1.7558468327076617,1.7735826593006685,1.7913184858936753,1.809054312486682,1.8267901390796883,1.8445259656726951,1.8622617922657019,1.8799976188587086,1.8977334454517154,1.9154692720447222,1.9332050986377285,1.9509409252307353,1.968676751823742,1.9864125784167488,2.0041484050097553,2.021884231602762,2.039620058195769,2.0573558847887754,2.0750917113817824,2.092827537974789,2.1105633645677955,2.1282991911608025,2.146035017753809,2.1637708443468155,2.181506670939822,2.1992424975328286,2.2169783241258356,2.234714150718842,2.252449977311849,2.2701858039048557,2.2879216304978622,2.3056574570908692,2.3233932836838758,2.3411291102768828,2.358864936869889,2.376600763462896,2.3943365900559024,2.412072416648909,2.429808243241916,2.4475440698349225,2.4652798964279294,2.483015723020936,2.5007515496139425,2.5184873762069495,2.5362232027999556,2.5539590293929626,2.571694855985969,2.589430682578976,2.6071665091719827,2.624902335764989,2.642638162357996,2.6603739889510027,2.6781098155440097,2.6958456421370163,2.713581468730023,2.7313172953230294,2.749053121916036,2.766788948509043,2.7845247751020494,2.8022606016950564,2.819996428288063,2.8377322548810695,2.8554680814740765,2.873203908067083,2.89093973466009,2.908675561253096,2.9264113878461027,2.9441472144391096,2.961883041032116,2.979618867625123,2.9973546942181297,3.0150905208111363,3.0328263474041433,3.05056217399715,3.068298000590157,3.0860338271831633,3.10376965377617,3.1215054803691764,3.139241306962183,3.15697713355519,3.1747129601481965,3.1924487867412035,3.21018461333421,3.2279204399272166,3.2456562665202235,3.26339209311323,3.2811279197062366,3.298863746299243,3.31659957289225,3.3343353994852567,3.3520712260782632,3.3698070526712702,3.3875428792642768,3.4052787058572838,3.4230145324502903,3.440750359043297,3.458486185636304,3.47622201222931,3.493957838822317,3.5116936654153235,3.52942949200833,3.547165318601337,3.5649011451943435,3.5826369717873505,3.600372798380357,3.618108624973364,3.6358444515663706,3.6535802781593767,3.6713161047523837,3.6890519313453902,3.706787757938397,3.7245235845314038,3.7422594111244103,3.7599952377174173,3.777731064310424,3.795466890903431,3.8132027174964374,3.8309385440894443,3.8486743706824504,3.866410197275457,3.884146023868464,3.9018818504614705,3.9196176770544775,3.937353503647484,3.9550893302404906,3.9728251568334976,3.990560983426504,4.008296810019511,4.026032636612518,4.043768463205524,4.06150428979853,4.079240116391538,4.096975942984544,4.114711769577551,4.132447596170557,4.150183422763565,4.16791924935657,4.185655075949578,4.203390902542584,4.221126729135591,4.2388625557285975,4.256598382321605,4.274334208914611,4.292070035507618,4.3098058621006246,4.327541688693631,4.345277515286638,4.363013341879644,4.380749168472652,4.398484995065657,4.416220821658665,4.433956648251671,4.451692474844679,4.469428301437684,4.487164128030692,4.504899954623698,4.522635781216704,4.540371607809711,4.558107434402718,4.5758432609957245,4.593579087588731,4.6113149141817384,4.629050740774745,4.6467865673677515,4.664522393960758,4.6822582205537655,4.699994047146771,4.717729873739778,4.735465700332785,4.753201526925792,4.770937353518798,4.788673180111805,4.806409006704812,4.824144833297818,4.841880659890825,4.859616486483832,4.8773523130768375,4.895088139669845,4.9128239662628514,4.930559792855859,4.9482956194488645,4.966031446041872,4.9837672726348785,5.001503099227885,5.019238925820892,5.036974752413899,5.054710579006906,5.072446405599911,5.090182232192919,5.107918058785925,5.125653885378932,5.143389711971938,5.161125538564946,5.178861365157952,5.196597191750959,5.214333018343965,5.232068844936973,5.249804671529978,5.267540498122985,5.285276324715992,5.303012151308998,5.3207479779020055,5.338483804495012,5.3562196310880195,5.373955457681025,5.3916912842740325,5.409427110867039,5.427162937460046,5.444898764053052,5.462634590646059,5.480370417239066,5.498106243832072,5.515842070425079,5.533577897018086,5.551313723611092,5.569049550204099,5.586785376797106,5.604521203390113,5.6222570299831185,5.639992856576126,5.6577286831691325,5.675464509762139,5.6932003363551456,5.710936162948153,5.728671989541159,5.746407816134166,5.764143642727173,5.78187946932018,5.799615295913186,5.817351122506192,5.8350869490992,5.852822775692205,5.870558602285213,5.888294428878219,5.906030255471227,5.923766082064232,5.94150190865724,5.959237735250246,5.976973561843253,5.9947093884362594,6.012445215029266,6.0301810416222725,6.047916868215279,6.0656526948082865,6.083388521401293,6.1011243479943,6.118860174587306,6.136596001180314,6.154331827773319,6.172067654366327,6.189803480959333,6.20753930755234,6.225275134145346,6.243010960738353,6.26074678733136,6.278482613924366,6.296218440517373,6.31395426711038,6.331690093703387,6.349425920296393,6.3671617468894,6.384897573482407,6.402633400075413,6.42036922666842,6.438105053261427,6.455840879854433,6.47357670644744,6.491312533040447,6.509048359633454,6.52678418622646,6.544520012819467,6.562255839412473,6.57999166600548,6.597727492598486,6.615463319191494,6.6331991457845,6.650934972377507,6.668670798970513,6.686406625563521,6.7041424521565265,6.721878278749534,6.7396141053425405,6.757349931935546,6.7750857585285535,6.79282158512156,6.8105574117145675,6.828293238307573,6.846029064900581,6.863764891493587,6.881500718086594,6.8992365446796,6.916972371272608,6.934708197865614,6.95244402445862,6.970179851051627,6.987915677644634,7.00565150423764,7.023387330830647,7.041123157423654,7.05885898401666,7.076594810609667,7.094330637202674,7.112066463795681,7.129802290388687,7.147538116981694,7.165273943574701,7.183009770167707,7.200745596760714,7.218481423353721,7.236217249946728,7.253953076539734,7.271688903132741,7.289424729725748,7.307160556318753,7.324896382911761,7.342632209504767,7.360368036097775,7.3781038626907804,7.395839689283788,7.413575515876794,7.431311342469801,7.4490471690628075,7.466782995655815,7.484518822248821,7.502254648841827,7.519990475434835,7.537726302027841,7.555462128620848,7.573197955213854,7.590933781806862,7.608669608399867,7.626405434992875,7.644141261585881,7.661877088178889,7.679612914771894,7.697348741364901,7.715084567957908,7.732820394550914,7.750556221143921,7.768292047736928,7.786027874329935,7.803763700922941,7.8214995275159485,7.839235354108955,7.8569711807019615,7.874707007294968,7.892442833887975,7.910178660480981,7.927914487073988,7.945650313666995,7.963386140260002,7.981121966853008,7.998857793446015,8.016593620039021,8.034329446632029,8.052065273225036,8.069801099818042,8.087536926411047,8.105272753004055,8.12300857959706,8.140744406190068,8.158480232783075,8.176216059376081,8.193951885969089,8.211687712562096,8.229423539155102,8.247159365748107,8.264895192341115,8.282631018934122,8.30036684552713,8.318102672120135,8.33583849871314,8.35357432530615,8.371310151899156,8.389045978492161,8.406781805085169,8.424517631678176,8.442253458271182,8.45998928486419,8.477725111457195,8.4954609380502,8.51319676464321,8.530932591236216,8.548668417829221,8.566404244422229,8.584140071015236,8.601875897608243,8.619611724201249,8.637347550794255,8.655083377387262,8.67281920398027,8.690555030573275,8.708290857166283,8.726026683759288,8.743762510352296,8.761498336945303,8.779234163538309,8.796969990131315,8.814705816724322,8.83244164331733,8.850177469910335,8.867913296503342,8.885649123096348,8.903384949689357,8.921120776282363,8.938856602875369,8.956592429468376,8.974328256061384,8.99206408265439,9.009799909247397,9.027535735840402,9.045271562433408,9.063007389026417,9.080743215619423,9.098479042212428,9.116214868805436,9.133950695398443,9.151686521991449,9.169422348584456,9.187158175177462,9.20489400177047,9.222629828363477,9.240365654956483,9.25810148154949,9.275837308142496,9.293573134735503,9.31130896132851,9.329044787921516,9.346780614514522,9.364516441107531,9.382252267700537,9.399988094293542,9.41772392088655,9.435459747479555,9.453195574072563,9.47093140066557,9.488667227258576,9.506403053851583,9.52413888044459,9.541874707037596,9.559610533630604,9.57734636022361,9.595082186816615,9.612818013409624,9.63055384000263,9.648289666595636,9.666025493188643,9.68376131978165,9.701497146374656,9.719232972967664,9.73696879956067,9.754704626153675,9.772440452746684,9.79017627933969,9.807912105932697,9.825647932525703,9.84338375911871,9.861119585711718,9.878855412304723,9.896591238897729,9.914327065490738,9.932062892083744,9.94979871867675,9.967534545269757,9.985270371862763,10.00300619845577,10.020742025048778,10.038477851641783,10.05621367823479,10.073949504827798,10.091685331420804,10.109421158013811,10.127156984606817,10.144892811199822,10.162628637792832,10.180364464385837,10.198100290978843,10.21583611757185,10.233571944164858,10.251307770757863,10.269043597350871,10.286779423943877,10.304515250536882,10.322251077129891,10.339986903722897,10.357722730315905,10.37545855690891,10.393194383501918,10.410930210094925,10.42866603668793,10.446401863280936,10.464137689873946,10.481873516466951,10.499609343059957,10.517345169652964,10.53508099624597,10.552816822838977,10.570552649431985,10.58828847602499,10.606024302617996,10.623760129211005,10.641495955804011,10.659231782397018,10.676967608990024,10.69470343558303,10.712439262176039,10.730175088769045,10.74791091536205,10.765646741955058,10.783382568548065,10.80111839514107,10.818854221734078,10.836590048327084,10.854325874920091,10.872061701513099,10.889797528106104,10.90753335469911,10.925269181292117,10.943005007885125,10.960740834478132,10.978476661071138,10.996212487664144,11.013948314257153,11.031684140850158,11.049419967443164,11.067155794036172,11.084891620629177,11.102627447222185,11.120363273815192,11.138099100408198,11.155834927001203,11.173570753594213,11.191306580187218,11.209042406780226,11.226778233373231,11.244514059966237,11.262249886559246,11.279985713152252,11.297721539745258,11.315457366338265,11.333193192931272,11.350929019524278,11.368664846117285,11.386400672710291,11.404136499303299,11.421872325896306,11.439608152489312,11.457343979082317,11.475079805675325,11.492815632268332,11.51055145886134,11.528287285454345,11.54602311204735,11.56375893864036,11.581494765233366,11.599230591826371,11.616966418419379,11.634702245012384,11.652438071605392,11.6701738981984,11.687909724791405,11.70564555138441,11.72338137797742,11.741117204570426,11.758853031163431,11.776588857756439,11.794324684349446,11.812060510942453,11.82979633753546,11.847532164128465,11.865267990721472,11.88300381731448,11.900739643907485,11.918475470500493,11.936211297093498,11.953947123686506,11.971682950279513,11.989418776872519,12.007154603465525,12.024890430058532,12.04262625665154,12.060362083244545,12.078097909837552,12.095833736430558,12.113569563023567,12.131305389616573,12.149041216209579,12.166777042802586,12.184512869395592,12.2022486959886,12.219984522581607,12.237720349174612,12.255456175767618,12.273192002360627,12.290927828953633,12.308663655546638,12.326399482139646,12.344135308732653,12.361871135325659,12.379606961918666,12.397342788511672,12.41507861510468,12.432814441697687,12.450550268290693,12.4682860948837,12.486021921476706,12.503757748069713,12.52149357466272,12.539229401255726,12.556965227848732,12.57470105444174,12.592436881034747,12.610172707627752,12.62790853422076,12.645644360813765,12.663380187406775,12.68111601399978,12.698851840592786,12.716587667185793,12.7343234937788,12.752059320371806,12.769795146964814,12.78753097355782,12.805266800150825,12.823002626743834,12.84073845333684,12.858474279929846,12.876210106522853,12.89394593311586,12.911681759708866,12.929417586301874,12.94715341289488,12.964889239487887,12.982625066080894,13.0003608926739,13.018096719266907,13.035832545859913,13.05356837245292,13.071304199045928,13.089040025638933,13.106775852231939,13.124511678824947,13.142247505417954,13.15998333201096,13.177719158603967,13.195454985196973,13.21319081178998,13.230926638382988,13.248662464975993,13.266398291569,13.284134118162008,13.301869944755014,13.319605771348021,13.337341597941027,13.355077424534032,13.372813251127042,13.390549077720047,13.408284904313053,13.42602073090606,13.443756557499068,13.461492384092073,13.479228210685081,13.496964037278087,13.514699863871092,13.532435690464101,13.550171517057107,13.567907343650115,13.58564317024312,13.603378996836128,13.621114823429135,13.63885065002214,13.656586476615146,13.674322303208156,13.692058129801161,13.709793956394167,13.727529782987174,13.74526560958018,13.763001436173187,13.780737262766195,13.7984730893592,13.816208915952206,13.833944742545215,13.851680569138221,13.869416395731228,13.887152222324234,13.90488804891724,13.922623875510249,13.940359702103255,13.95809552869626,13.975831355289268,13.993567181882275,14.01130300847528,14.029038835068288,14.046774661661294,14.0645104882543,14.082246314847309,14.099982141440314,14.11771796803332,14.135453794626327,14.153189621219335,14.170925447812342,14.188661274405348,14.206397100998354,14.224132927591363,14.241868754184368,14.259604580777374,14.277340407370382,14.295076233963387,14.312812060556395,14.330547887149402,14.348283713742408,14.366019540335413,14.383755366928423,14.401491193521428,14.419227020114436,14.436962846707441,14.454698673300447,14.472434499893456,14.490170326486462,14.507906153079468,14.525641979672475,14.543377806265482,14.561113632858488,14.578849459451495,14.596585286044501,14.614321112637507,14.632056939230516,14.649792765823522,14.667528592416527,14.685264419009535,14.703000245602542,14.72073607219555,14.738471898788555,14.756207725381561,14.77394355197457,14.791679378567576,14.809415205160581,14.827151031753589,14.844886858346594,14.862622684939602,14.88035851153261,14.898094338125615,14.91583016471862,14.93356599131163,14.951301817904636,14.969037644497641,14.986773471090649,15.004509297683654,15.022245124276663,15.03998095086967,15.057716777462675,15.075452604055682,15.09318843064869,15.110924257241695,15.128660083834703,15.146395910427708,15.164131737020716,15.181867563613723,15.199603390206729,15.217339216799735,15.235075043392742,15.25281086998575,15.270546696578755,15.288282523171763,15.306018349764768,15.323754176357777,15.341490002950783,15.359225829543789,15.376961656136796,15.394697482729802,15.41243330932281,15.430169135915817,15.447904962508822,15.465640789101828,15.483376615694837,15.501112442287843,15.518848268880848,15.536584095473856,15.554319922066862,15.57205574865987,15.589791575252876,15.607527401845882,15.62526322843889,15.642999055031897,15.660734881624903,15.67847070821791,15.696206534810916,15.713942361403923,15.73167818799693,15.749414014589936,15.767149841182942,15.78488566777595,15.802621494368957,15.820357320961962,15.83809314755497,15.855828974147975,15.873564800740985,15.89130062733399,15.909036453926996,15.926772280520003,15.944508107113009,15.962243933706016,15.979979760299024,15.99771558689203,16.015451413485035,16.033187240078043,16.05092306667105,16.068658893264057,16.08639471985706,16.104130546450072,16.121866373043076,16.139602199636084,16.15733802622909,16.175073852822095,16.192809679415102,16.21054550600811,16.228281332601117,16.24601715919412,16.263752985787132,16.281488812380136,16.299224638973143],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Ext Rule 6: Fibonacci Support Confluence</td>
                <td>UNKNOWN</td>
                <td class="positive">82.69%</td>
                <td>64.4%</td>
                <td>1108</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>25.86%</td>
                <td>82.4</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">72.66%</td>
                <td>64.7%</td>
                <td>1117</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>22.87%</td>
                <td>78.5</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">45.85%</td>
                <td>69.2%</td>
                <td>146</td>
                <td>1.43</td>
                <td>0.00</td>
                <td>12.48%</td>
                <td>69.1</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">19.66%</td>
                <td>68.3%</td>
                <td>126</td>
                <td>1.20</td>
                <td>0.00</td>
                <td>10.77%</td>
                <td>58.3</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">16.30%</td>
                <td>63.1%</td>
                <td>919</td>
                <td>1.02</td>
                <td>0.00</td>
                <td>40.68%</td>
                <td>55.5</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">14.86%</td>
                <td>68.9%</td>
                <td>74</td>
                <td>1.25</td>
                <td>0.00</td>
                <td>8.92%</td>
                <td>48.8</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                <td>UNKNOWN</td>
                <td class="positive">14.87%</td>
                <td>76.0%</td>
                <td>25</td>
                <td>2.00</td>
                <td>0.00</td>
                <td>3.70%</td>
                <td>36.2</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">10.30%</td>
                <td>80.0%</td>
                <td>15</td>
                <td>2.41</td>
                <td>0.00</td>
                <td>2.86%</td>
                <td>32.6</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">4.12%</td>
                <td>68.4%</td>
                <td>19</td>
                <td>1.27</td>
                <td>0.00</td>
                <td>3.54%</td>
                <td>27.9</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 812,700<br>
            • Backtest Range: 300 to 813,000<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
