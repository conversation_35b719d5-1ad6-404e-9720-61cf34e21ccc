
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 7 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 11:40:22</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">7</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">25.9%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">9.7%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">19.7%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">64.9%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">2,745</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["16.7%","19.7%","16.5%","7.2%","4.6%","1.8%","1.1%"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Rule 3: RSI Oversold","Rule 6: Stochastic Oversold Cross","Rule 24: MFI Oversold","Price Action Rule 3: Engulfing Pattern"],"y":[16.691177933565715,19.704830180785603,16.541328025558165,7.171356523712988,4.597928351109443,1.7608920249396216,1.099229027120011],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Rule 3: RSI Oversold","Rule 6: Stochastic Oversold Cross","Rule 24: MFI Oversold","Price Action Rule 3: Engulfing Pattern"],"y":[167,670,536,164,123,96,15],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Rule 3: RSI Oversold","Rule 6: Stochastic Oversold Cross","Rule 24: MFI Oversold","Price Action Rule 3: Engulfing Pattern"],"y":[71,371,314,86,69,54,9],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[16.691177933565715,19.704830180785603,16.541328025558165,7.171356523712988,4.597928351109443,1.7608920249396216,1.099229027120011],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Prof Rule 7","AI Rule 10","Rule 7","Rule 3","Rule 6","Rule 24","Price Action Rule 3"],"textposition":"top center","x":[4.687640699996466,9.154162099220779,12.67553995147132,7.130854197001553,6.70289682570653,7.140934329545173,2.123321193458967],"y":[16.691177933565715,19.704830180785603,16.541328025558165,7.171356523712988,4.597928351109443,1.7608920249396216,1.099229027120011],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["16.7%","19.7%","7.5%","1.1%"],"textposition":"auto","x":["PROFESSIONAL","AI_GENERATED","ORIGINAL","UNKNOWN"],"y":[16.691177933565715,19.704830180785603,7.517876231330055,1.099229027120011],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["238","1041","850","250","192","150","24"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Rule 3: RSI Oversold","Rule 6: Stochastic Oversold Cross","Rule 24: MFI Oversold","Price Action Rule 3: Engulfing Pattern"],"y":[238,1041,850,250,192,150,24],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238],"y":[0,0.07013099972086434,0.14026199944172868,0.21039299916259305,0.28052399888345736,0.35065499860432175,0.4207859983251861,0.49091699804605043,0.5610479977669147,0.6311789974877791,0.7013099972086435,0.7714409969295078,0.8415719966503722,0.9117029963712365,0.9818339960921009,1.051964995812965,1.1220959955338294,1.1922269952546938,1.2623579949755581,1.3324889946964225,1.402619994417287,1.4727509941381514,1.5428819938590157,1.61301299357988,1.6831439933007444,1.7532749930216087,1.823405992742473,1.8935369924633374,1.9636679921842017,2.033798991905066,2.10392999162593,2.1740609913467948,2.244191991067659,2.3143229907885234,2.3844539905093876,2.454584990230252,2.5247159899511162,2.594846989671981,2.664977989392845,2.7351089891137095,2.805239988834574,2.875370988555438,2.9455019882763027,3.015632987997167,3.0857639877180314,3.1558949874388955,3.22602598715976,3.296156986880624,3.3662879866014888,3.436418986322353,3.5065499860432174,3.5766809857640816,3.646811985484946,3.7169429852058107,3.787073984926675,3.8572049846475394,3.9273359843684035,3.997466984089268,4.067597983810132,4.137728983530997,4.20785998325186,4.2779909829727245,4.3481219826935895,4.418252982414454,4.488383982135318,4.558514981856182,4.628645981577047,4.698776981297911,4.768907981018775,4.83903898073964,4.909169980460504,4.979300980181368,5.0494319799022325,5.1195629796230975,5.189693979343962,5.259824979064826,5.32995597878569,5.400086978506555,5.470217978227419,5.540348977948283,5.610479977669148,5.680610977390012,5.750741977110876,5.82087297683174,5.891003976552605,5.9611349762734696,6.031265975994334,6.101396975715198,6.171527975436063,6.241658975156927,6.311789974877791,6.381920974598655,6.45205197431952,6.522182974040384,6.592313973761248,6.662444973482113,6.7325759732029775,6.802706972923842,6.872837972644706,6.942968972365571,7.013099972086435,7.083230971807299,7.153361971528163,7.223492971249028,7.293623970969892,7.363754970690756,7.433885970411621,7.5040169701324855,7.57414796985335,7.644278969574214,7.714409969295079,7.784540969015943,7.854671968736807,7.924802968457671,7.994933968178536,8.065064967899401,8.135195967620264,8.205326967341128,8.275457967061994,8.345588966782858,8.41571996650372,8.485850966224586,8.555981965945449,8.626112965666316,8.696243965387179,8.766374965108044,8.836505964828907,8.906636964549772,8.976767964270636,9.0468989639915,9.117029963712364,9.18716096343323,9.257291963154094,9.327422962874959,9.397553962595822,9.467684962316687,9.53781596203755,9.607946961758415,9.67807796147928,9.748208961200145,9.818339960921008,9.888470960641873,9.958601960362737,10.028732960083602,10.098863959804465,10.16899495952533,10.239125959246195,10.30925695896706,10.379387958687923,10.449518958408788,10.519649958129651,10.589780957850516,10.65991195757138,10.730042957292246,10.80017395701311,10.870304956733975,10.940435956454838,11.010566956175703,11.080697955896566,11.150828955617431,11.220959955338296,11.291090955059161,11.361221954780024,11.43135295450089,11.501483954221753,11.571614953942618,11.64174595366348,11.711876953384346,11.78200795310521,11.852138952826076,11.922269952546939,11.992400952267804,12.062531951988667,12.132662951709532,12.202793951430396,12.272924951151262,12.343055950872126,12.41318695059299,12.483317950313854,12.553448950034719,12.623579949755582,12.693710949476447,12.76384194919731,12.833972948918177,12.90410394863904,12.974234948359905,13.044365948080769,13.114496947801634,13.184627947522497,13.254758947243362,13.324889946964227,13.395020946685092,13.465151946405955,13.53528294612682,13.605413945847683,13.675544945568548,13.745675945289412,13.815806945010277,13.885937944731142,13.956068944452007,14.02619994417287,14.096330943893735,14.166461943614598,14.236592943335463,14.306723943056326,14.376854942777193,14.446985942498056,14.517116942218921,14.587247941939784,14.65737894166065,14.727509941381513,14.797640941102378,14.867771940823243,14.937902940544108,15.008033940264971,15.078164939985836,15.1482959397067,15.218426939427564,15.288557939148427,15.358688938869292,15.428819938590157,15.498950938311022,15.569081938031886,15.63921293775275,15.709343937473614,15.779474937194479,15.849605936915342,15.919736936636209,15.989867936357072,16.059998936077935,16.130129935798802,16.200260935519665,16.27039193524053,16.340522934961392,16.410653934682255,16.480784934403122,16.55091593412399,16.621046933844852,16.691177933565715],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041],"y":[0,0.018928751374433814,0.03785750274886763,0.05678625412330145,0.07571500549773526,0.0946437568721691,0.1135725082466029,0.1325012596210367,0.15143001099547052,0.17035876236990435,0.1892875137443382,0.20821626511877198,0.2271450164932058,0.2460737678676396,0.2650025192420734,0.28393127061650725,0.30286002199094103,0.32178877336537487,0.3407175247398087,0.3596462761142425,0.3785750274886764,0.39750377886311017,0.41643253023754395,0.4353612816119778,0.4542900329864116,0.4732187843608454,0.4921475357352792,0.5110762871097131,0.5300050384841468,0.5489337898585807,0.5678625412330145,0.5867912926074483,0.6057200439818821,0.624648795356316,0.6435775467307497,0.6625062981051836,0.6814350494796174,0.7003638008540513,0.719292552228485,0.7382213036029188,0.7571500549773528,0.7760788063517865,0.7950075577262203,0.8139363091006541,0.8328650604750879,0.8517938118495217,0.8707225632239556,0.8896513145983893,0.9085800659728231,0.9275088173472571,0.9464375687216908,0.9653663200961247,0.9842950714705584,1.0032238228449923,1.0221525742194262,1.0410813255938598,1.0600100769682936,1.0789388283427277,1.0978675797171613,1.1167963310915952,1.135725082466029,1.1546538338404628,1.1735825852148967,1.1925113365893305,1.2114400879637641,1.230368839338198,1.249297590712632,1.2682263420870656,1.2871550934614995,1.3060838448359333,1.3250125962103672,1.3439413475848008,1.3628700989592348,1.3817988503336687,1.4007276017081025,1.4196563530825363,1.43858510445697,1.4575138558314038,1.4764426072058376,1.4953713585802715,1.5143001099547055,1.5332288613291392,1.552157612703573,1.5710863640780068,1.5900151154524407,1.6089438668268743,1.6278726182013081,1.646801369575742,1.6657301209501758,1.6846588723246099,1.7035876236990435,1.7225163750734773,1.7414451264479112,1.760373877822345,1.7793026291967786,1.7982313805712125,1.8171601319456463,1.8360888833200804,1.8550176346945142,1.8739463860689478,1.8928751374433817,1.9118038888178155,1.9307326401922493,1.949661391566683,1.9685901429411168,1.9875188943155508,2.0064476456899847,2.0253763970644183,2.0443051484388524,2.063233899813286,2.0821626511877196,2.1010914025621537,2.1200201539365873,2.1389489053110213,2.1578776566854554,2.176806408059889,2.1957351594343226,2.2146639108087567,2.2335926621831903,2.252521413557624,2.271450164932058,2.2903789163064916,2.3093076676809257,2.3282364190553597,2.3471651704297933,2.366093921804227,2.385022673178661,2.4039514245530946,2.4228801759275282,2.4418089273019623,2.460737678676396,2.47966643005083,2.498595181425264,2.5175239327996977,2.5364526841741313,2.5553814355485653,2.574310186922999,2.5932389382974326,2.6121676896718666,2.6310964410463002,2.6500251924207343,2.668953943795168,2.6878826951696015,2.706811446544036,2.7257401979184697,2.7446689492929037,2.7635977006673373,2.782526452041771,2.801455203416205,2.8203839547906386,2.8393127061650727,2.8582414575395063,2.87717020891394,2.896098960288374,2.9150277116628076,2.9339564630372412,2.9528852144116753,2.971813965786109,2.990742717160543,3.0096714685349766,3.028600219909411,3.0475289712838447,3.0664577226582783,3.0853864740327124,3.104315225407146,3.1232439767815796,3.1421727281560137,3.1611014795304473,3.1800302309048813,3.198958982279315,3.2178877336537486,3.2368164850281826,3.2557452364026163,3.27467398777705,3.293602739151484,3.3125314905259176,3.3314602419003516,3.3503889932747857,3.3693177446492197,3.3882464960236534,3.407175247398087,3.426103998772521,3.4450327501469546,3.4639615015213883,3.4828902528958223,3.501819004270256,3.52074775564469,3.5396765070191236,3.5586052583935572,3.5775340097679913,3.596462761142425,3.6153915125168585,3.6343202638912926,3.653249015265726,3.6721777666401607,3.6911065180145943,3.7100352693890284,3.728964020763462,3.7478927721378956,3.7668215235123297,3.7857502748867633,3.804679026261197,3.823607777635631,3.8425365290100646,3.8614652803844987,3.8803940317589323,3.899322783133366,3.9182515345078,3.9371802858822336,3.956109037256667,3.9750377886311017,3.9939665400055353,4.012895291379969,4.031824042754403,4.050752794128837,4.06968154550327,4.088610296877705,4.107539048252138,4.126467799626572,4.145396551001006,4.164325302375439,4.183254053749874,4.202182805124307,4.221111556498741,4.2400403078731745,4.258969059247608,4.277897810622043,4.296826561996476,4.315755313370911,4.334684064745344,4.353612816119778,4.372541567494212,4.391470318868645,4.410399070243079,4.429327821617513,4.448256572991947,4.467185324366381,4.486114075740814,4.505042827115248,4.523971578489682,4.542900329864116,4.56182908123855,4.580757832612983,4.599686583987417,4.618615335361851,4.637544086736285,4.656472838110719,4.675401589485153,4.694330340859587,4.71325909223402,4.732187843608454,4.7511165949828875,4.770045346357322,4.788974097731756,4.807902849106189,4.826831600480623,4.8457603518550565,4.864689103229491,4.883617854603925,4.902546605978358,4.921475357352792,4.940404108727226,4.95933286010166,4.978261611476094,4.997190362850528,5.016119114224962,5.035047865599395,5.053976616973829,5.0729053683482626,5.091834119722696,5.110762871097131,5.129691622471564,5.148620373845998,5.1675491252204315,5.186477876594865,5.2054066279693,5.224335379343733,5.243264130718167,5.2621928820926005,5.281121633467034,5.300050384841469,5.318979136215902,5.337907887590336,5.3568366389647695,5.375765390339203,5.394694141713637,5.413622893088072,5.432551644462506,5.451480395836939,5.470409147211373,5.4893378985858075,5.508266649960241,5.527195401334675,5.546124152709108,5.565052904083542,5.583981655457976,5.60291040683241,5.621839158206844,5.640767909581277,5.659696660955711,5.678625412330145,5.697554163704579,5.716482915079013,5.735411666453446,5.75434041782788,5.7732691692023135,5.792197920576748,5.811126671951182,5.830055423325615,5.848984174700049,5.8679129260744824,5.886841677448917,5.905770428823351,5.924699180197784,5.943627931572218,5.962556682946651,5.981485434321086,6.0004141856955195,6.019342937069953,6.038271688444387,6.057200439818822,6.076129191193256,6.095057942567689,6.113986693942123,6.132915445316557,6.15184419669099,6.170772948065425,6.189701699439858,6.208630450814292,6.227559202188726,6.246487953563159,6.265416704937594,6.284345456312027,6.303274207686461,6.322202959060895,6.341131710435328,6.360060461809763,6.378989213184196,6.39791796455863,6.4168467159330635,6.435775467307497,6.454704218681931,6.473632970056365,6.492561721430799,6.5114904728052325,6.530419224179666,6.5493479755541,6.568276726928534,6.587205478302968,6.6061342296774015,6.625062981051835,6.643991732426269,6.662920483800703,6.681849235175138,6.700777986549571,6.719706737924005,6.7386354892984395,6.757564240672873,6.776492992047307,6.79542174342174,6.814350494796174,6.833279246170608,6.852207997545042,6.871136748919476,6.890065500293909,6.908994251668343,6.9279230030427765,6.946851754417211,6.965780505791645,6.984709257166078,7.003638008540512,7.0225667599149455,7.04149551128938,7.060424262663814,7.079353014038247,7.098281765412681,7.1172105167871145,7.136139268161548,7.155068019535983,7.173996770910416,7.19292552228485,7.211854273659283,7.230783025033717,7.249711776408152,7.268640527782585,7.287569279157019,7.306498030531452,7.325426781905888,7.344355533280321,7.363284284654755,7.382213036029189,7.401141787403622,7.420070538778057,7.43899929015249,7.457928041526924,7.476856792901358,7.495785544275791,7.514714295650225,7.533643047024659,7.552571798399093,7.571500549773527,7.59042930114796,7.609358052522394,7.628286803896828,7.647215555271262,7.666144306645696,7.685073058020129,7.704001809394563,7.722930560768997,7.741859312143431,7.7607880635178645,7.779716814892298,7.798645566266732,7.817574317641165,7.8365030690156,7.8554318203900335,7.874360571764467,7.893289323138901,7.912218074513334,7.931146825887769,7.950075577262203,7.969004328636637,7.987933080011071,8.006861831385505,8.025790582759939,8.044719334134372,8.063648085508806,8.08257683688324,8.101505588257673,8.120434339632107,8.13936309100654,8.158291842380976,8.17722059375541,8.196149345129843,8.215078096504277,8.23400684787871,8.252935599253144,8.271864350627578,8.290793102002011,8.309721853376445,8.328650604750878,8.347579356125312,8.366508107499747,8.385436858874181,8.404365610248615,8.423294361623048,8.442223112997482,8.461151864371915,8.480080615746349,8.499009367120783,8.517938118495216,8.53686686986965,8.555795621244085,8.574724372618519,8.593653123992953,8.612581875367386,8.631510626741822,8.650439378116255,8.669368129490689,8.688296880865122,8.707225632239556,8.72615438361399,8.745083134988423,8.764011886362857,8.78294063773729,8.801869389111724,8.820798140486158,8.839726891860593,8.858655643235027,8.87758439460946,8.896513145983894,8.915441897358328,8.934370648732761,8.953299400107195,8.972228151481628,8.991156902856062,9.010085654230496,9.02901440560493,9.047943156979365,9.066871908353798,9.085800659728232,9.104729411102666,9.1236581624771,9.142586913851533,9.161515665225966,9.1804444166004,9.199373167974834,9.218301919349269,9.237230670723703,9.256159422098136,9.27508817347257,9.294016924847003,9.312945676221439,9.331874427595872,9.350803178970306,9.36973193034474,9.388660681719173,9.407589433093607,9.42651818446804,9.445446935842474,9.464375687216908,9.483304438591341,9.502233189965775,9.52116194134021,9.540090692714644,9.559019444089078,9.577948195463511,9.596876946837945,9.615805698212379,9.634734449586812,9.653663200961246,9.67259195233568,9.691520703710113,9.710449455084547,9.729378206458982,9.748306957833416,9.76723570920785,9.786164460582283,9.805093211956716,9.82402196333115,9.842950714705584,9.861879466080017,9.880808217454453,9.899736968828885,9.91866572020332,9.937594471577754,9.956523222952187,9.97545197432662,9.994380725701056,10.013309477075488,10.032238228449923,10.051166979824355,10.07009573119879,10.089024482573222,10.107953233947658,10.126881985322091,10.145810736696525,10.164739488070959,10.183668239445392,10.202596990819828,10.221525742194261,10.240454493568695,10.259383244943129,10.278311996317564,10.297240747691996,10.316169499066431,10.335098250440863,10.354027001815298,10.37295575318973,10.391884504564166,10.4108132559386,10.429742007313033,10.448670758687467,10.4675995100619,10.486528261436334,10.50545701281077,10.524385764185201,10.543314515559636,10.562243266934068,10.581172018308504,10.600100769682937,10.61902952105737,10.637958272431804,10.656887023806238,10.675815775180672,10.694744526555107,10.713673277929539,10.732602029303974,10.751530780678406,10.770459532052842,10.789388283427273,10.808317034801709,10.827245786176144,10.846174537550576,10.865103288925011,10.884032040299445,10.902960791673879,10.921889543048312,10.940818294422746,10.95974704579718,10.978675797171615,10.997604548546047,11.016533299920482,11.035462051294914,11.05439080266935,11.073319554043781,11.092248305418217,11.11117705679265,11.130105808167084,11.149034559541517,11.167963310915953,11.186892062290385,11.20582081366482,11.224749565039252,11.243678316413687,11.26260706778812,11.281535819162555,11.300464570536988,11.319393321911422,11.338322073285855,11.35725082466029,11.376179576034723,11.395108327409158,11.41403707878359,11.432965830158025,11.45189458153246,11.470823332906892,11.489752084281328,11.50868083565576,11.527609587030195,11.546538338404627,11.565467089779062,11.584395841153496,11.60332459252793,11.622253343902363,11.641182095276799,11.66011084665123,11.679039598025666,11.697968349400098,11.716897100774533,11.735825852148965,11.7547546035234,11.773683354897834,11.792612106272268,11.811540857646701,11.830469609021135,11.849398360395568,11.868327111770004,11.887255863144436,11.906184614518871,11.925113365893303,11.944042117267738,11.962970868642172,11.981899620016605,12.000828371391039,12.019757122765473,12.038685874139906,12.057614625514342,12.076543376888774,12.095472128263209,12.114400879637644,12.133329631012076,12.152258382386512,12.171187133760943,12.190115885135379,12.20904463650981,12.227973387884246,12.24690213925868,12.265830890633113,12.284759642007547,12.30368839338198,12.322617144756414,12.34154589613085,12.360474647505281,12.379403398879717,12.398332150254149,12.417260901628584,12.436189653003016,12.455118404377451,12.474047155751885,12.492975907126318,12.511904658500752,12.530833409875187,12.54976216124962,12.568690912624055,12.587619663998487,12.606548415372922,12.625477166747354,12.64440591812179,12.663334669496223,12.682263420870656,12.70119217224509,12.720120923619525,12.739049674993959,12.757978426368393,12.776907177742826,12.79583592911726,12.814764680491695,12.833693431866127,12.852622183240562,12.871550934614994,12.89047968598943,12.909408437363862,12.928337188738297,12.94726594011273,12.966194691487164,12.985123442861598,13.004052194236033,13.022980945610465,13.0419096969849,13.060838448359332,13.079767199733768,13.0986959511082,13.117624702482635,13.136553453857069,13.155482205231502,13.174410956605936,13.19333970798037,13.212268459354803,13.231197210729238,13.25012596210367,13.269054713478106,13.287983464852537,13.306912216226973,13.325840967601406,13.34476971897584,13.363698470350275,13.382627221724707,13.401555973099143,13.420484724473576,13.43941347584801,13.458342227222444,13.477270978596879,13.49619972997131,13.515128481345746,13.534057232720178,13.552985984094613,13.571914735469045,13.59084348684348,13.609772238217914,13.628700989592348,13.647629740966781,13.666558492341215,13.685487243715649,13.704415995090084,13.723344746464516,13.742273497838951,13.761202249213383,13.780131000587819,13.79905975196225,13.817988503336686,13.83691725471112,13.855846006085553,13.874774757459987,13.893703508834422,13.912632260208854,13.93156101158329,13.950489762957721,13.969418514332157,13.988347265706592,14.007276017081024,14.02620476845546,14.045133519829891,14.064062271204326,14.08299102257876,14.101919773953194,14.120848525327627,14.13977727670206,14.158706028076494,14.17763477945093,14.196563530825362,14.215492282199797,14.234421033574229,14.253349784948664,14.272278536323096,14.291207287697532,14.310136039071965,14.329064790446399,14.347993541820832,14.366922293195268,14.3858510445697,14.404779795944135,14.423708547318567,14.442637298693002,14.461566050067434,14.48049480144187,14.499423552816303,14.518352304190737,14.53728105556517,14.556209806939604,14.575138558314038,14.594067309688473,14.612996061062905,14.63192481243734,14.650853563811776,14.669782315186207,14.688711066560643,14.707639817935075,14.72656856930951,14.745497320683942,14.764426072058377,14.783354823432811,14.802283574807245,14.821212326181678,14.840141077556114,14.859069828930545,14.87799858030498,14.896927331679413,14.915856083053848,14.93478483442828,14.953713585802715,14.972642337177149,14.991571088551582,15.010499839926016,15.02942859130045,15.048357342674883,15.067286094049319,15.08621484542375,15.105143596798186,15.124072348172618,15.143001099547053,15.161929850921487,15.18085860229592,15.199787353670354,15.218716105044788,15.237644856419221,15.256573607793657,15.27550235916809,15.294431110542524,15.313359861916958,15.332288613291391,15.351217364665827,15.370146116040258,15.389074867414694,15.408003618789126,15.426932370163561,15.445861121537995,15.464789872912428,15.483718624286862,15.502647375661295,15.521576127035729,15.540504878410164,15.559433629784596,15.578362381159032,15.597291132533464,15.616219883907899,15.63514863528233,15.654077386656766,15.6730061380312,15.691934889405633,15.710863640780067,15.729792392154502,15.748721143528934,15.76764989490337,15.786578646277801,15.805507397652237,15.824436149026669,15.843364900401104,15.862293651775538,15.881222403149971,15.900151154524407,15.919079905898839,15.938008657273274,15.956937408647708,15.975866160022141,15.994794911396575,16.01372366277101,16.032652414145442,16.051581165519877,16.07050991689431,16.089438668268745,16.108367419643177,16.127296171017612,16.146224922392044,16.16515367376648,16.18408242514091,16.203011176515346,16.22193992788978,16.240868679264214,16.25979743063865,16.27872618201308,16.297654933387516,16.31658368476195,16.335512436136383,16.35444118751082,16.37336993888525,16.392298690259686,16.411227441634118,16.430156193008553,16.449084944382985,16.46801369575742,16.486942447131852,16.505871198506288,16.52479994988072,16.543728701255155,16.56265745262959,16.581586204004022,16.600514955378458,16.61944370675289,16.638372458127325,16.657301209501757,16.676229960876192,16.695158712250624,16.71408746362506,16.733016214999495,16.751944966373927,16.770873717748362,16.789802469122797,16.80873122049723,16.827659971871665,16.846588723246096,16.865517474620532,16.884446225994964,16.9033749773694,16.92230372874383,16.941232480118266,16.960161231492698,16.979089982867134,16.998018734241565,17.016947485616,17.035876236990433,17.054804988364868,17.0737337397393,17.092662491113735,17.11159124248817,17.130519993862602,17.149448745237038,17.16837749661147,17.187306247985905,17.20623499936034,17.225163750734772,17.244092502109208,17.263021253483643,17.281950004858075,17.30087875623251,17.319807507606942,17.338736258981378,17.35766501035581,17.376593761730245,17.395522513104677,17.414451264479112,17.433380015853544,17.45230876722798,17.47123751860241,17.490166269976847,17.50909502135128,17.528023772725714,17.546952524100146,17.56588127547458,17.584810026849016,17.60373877822345,17.622667529597884,17.641596280972315,17.66052503234675,17.679453783721186,17.698382535095618,17.717311286470053,17.736240037844485,17.75516878921892,17.774097540593353,17.793026291967788,17.811955043342223,17.830883794716655,17.84981254609109,17.868741297465522,17.887670048839958,17.90659880021439,17.925527551588825,17.944456302963257,17.963385054337692,17.982313805712124,18.00124255708656,18.02017130846099,18.039100059835427,18.05802881120986,18.076957562584294,18.09588631395873,18.11481506533316,18.133743816707597,18.152672568082032,18.171601319456464,18.1905300708309,18.20945882220533,18.228387573579766,18.2473163249542,18.266245076328634,18.285173827703066,18.3041025790775,18.323031330451933,18.341960081826368,18.3608888332008,18.379817584575235,18.398746335949667,18.417675087324103,18.436603838698538,18.45553259007297,18.474461341447405,18.493390092821837,18.512318844196272,18.531247595570704,18.55017634694514,18.569105098319575,18.588033849694007,18.606962601068442,18.625891352442878,18.64482010381731,18.663748855191745,18.682677606566177,18.701606357940612,18.720535109315044,18.73946386068948,18.75839261206391,18.777321363438347,18.79625011481278,18.815178866187214,18.834107617561646,18.85303636893608,18.871965120310513,18.89089387168495,18.90982262305938,18.928751374433816,18.94768012580825,18.966608877182683,18.98553762855712,19.00446637993155,19.023395131305985,19.04232388268042,19.061252634054853,19.080181385429288,19.09911013680372,19.118038888178155,19.13696763955259,19.155896390927023,19.174825142301458,19.19375389367589,19.212682645050325,19.231611396424757,19.250540147799192,19.269468899173624,19.28839765054806,19.30732640192249,19.326255153296927,19.34518390467136,19.364112656045794,19.383041407420226,19.40197015879466,19.420898910169093,19.43982766154353,19.458756412917964,19.477685164292396,19.49661391566683,19.515542667041267,19.5344714184157,19.553400169790134,19.572328921164566,19.591257672539,19.610186423913433,19.62911517528787,19.6480439266623,19.666972678036736,19.685901429411167,19.704830180785603],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850],"y":[0,0.019460385912421368,0.038920771824842736,0.058381157737264114,0.07784154364968547,0.09730192956210686,0.11676231547452823,0.1362227013869496,0.15568308729937094,0.17514347321179233,0.1946038591242137,0.2140642450366351,0.23352463094905646,0.25298501686147784,0.2724454027738992,0.29190578868632056,0.3113661745987419,0.3308265605111633,0.35028694642358466,0.36974733233600604,0.3892077182484274,0.40866810416084876,0.4281284900732702,0.4475888759856915,0.4670492618981129,0.4865096478105343,0.5059700337229557,0.525430419635377,0.5448908055477985,0.5643511914602197,0.5838115773726411,0.6032719632850625,0.6227323491974838,0.6421927351099053,0.6616531210223267,0.6811135069347479,0.7005738928471693,0.7200342787595907,0.7394946646720121,0.7589550505844335,0.7784154364968549,0.7978758224092761,0.8173362083216975,0.836796594234119,0.8562569801465404,0.8757173660589617,0.895177751971383,0.9146381378838045,0.9340985237962258,0.9535589097086472,0.9730192956210686,0.9924796815334899,1.0119400674459114,1.0314004533583327,1.050860839270754,1.0703212251831755,1.089781611095597,1.1092419970080183,1.1287023829204395,1.1481627688328608,1.1676231547452822,1.1870835406577036,1.206543926570125,1.2260043124825464,1.2454646983949675,1.2649250843073891,1.2843854702198105,1.303845856132232,1.3233062420446533,1.3427666279570747,1.3622270138694959,1.3816873997819172,1.4011477856943386,1.42060817160676,1.4400685575191814,1.459528943431603,1.4789893293440242,1.4984497152564455,1.517910101168867,1.5373704870812883,1.5568308729937097,1.576291258906131,1.5957516448185523,1.6152120307309736,1.634672416643395,1.6541328025558166,1.673593188468238,1.6930535743806594,1.7125139602930808,1.731974346205502,1.7514347321179233,1.7708951180303447,1.790355503942766,1.8098158898551875,1.829276275767609,1.8487366616800303,1.8681970475924516,1.887657433504873,1.9071178194172944,1.9265782053297158,1.9460385912421372,1.9654989771545583,1.9849593630669797,2.004419748979401,2.0238801348918227,2.043340520804244,2.0628009067166655,2.0822612926290867,2.101721678541508,2.1211820644539294,2.140642450366351,2.160102836278772,2.179563222191194,2.199023608103615,2.2184839940160366,2.2379443799284577,2.257404765840879,2.2768651517533,2.2963255376657217,2.3157859235781433,2.3352463094905644,2.354706695402986,2.374167081315407,2.393627467227829,2.41308785314025,2.4325482390526716,2.4520086249650928,2.4714690108775144,2.490929396789935,2.5103897827023567,2.5298501686147783,2.5493105545271995,2.568770940439621,2.5882313263520422,2.607691712264464,2.627152098176885,2.6466124840893066,2.6660728700017278,2.6855332559141494,2.704993641826571,2.7244540277389917,2.7439144136514133,2.7633747995638345,2.782835185476256,2.8022955713886772,2.821755957301099,2.84121634321352,2.8606767291259416,2.880137115038363,2.8995975009507844,2.919057886863206,2.938518272775627,2.9579786586880483,2.9774390446004695,2.996899430512891,3.0163598164253123,3.035820202337734,3.055280588250155,3.0747409741625766,3.0942013600749982,3.1136617459874194,3.133122131899841,3.152582517812262,3.172042903724684,3.1915032896371045,3.210963675549526,3.2304240614619473,3.249884447374369,3.26934483328679,3.2888052191992116,3.3082656051116333,3.3277259910240544,3.347186376936476,3.366646762848897,3.386107148761319,3.40556753467374,3.4250279205861616,3.4444883064985823,3.463948692411004,3.4834090783234255,3.5028694642358467,3.5223298501482683,3.5417902360606894,3.561250621973111,3.580711007885532,3.600171393797954,3.619631779710375,3.6390921656227966,3.658552551535218,3.678012937447639,3.6974733233600605,3.7169337092724817,3.7363940951849033,3.7558544810973244,3.775314867009746,3.794775252922167,3.814235638834589,3.83369602474701,3.8531564106594316,3.872616796571853,3.8920771824842744,3.9115375683966955,3.9309979543091167,3.9504583402215383,3.9699187261339595,3.989379112046381,4.008839497958802,4.028299883871224,4.0477602697836454,4.067220655696066,4.086681041608488,4.106141427520909,4.125601813433331,4.145062199345753,4.164522585258173,4.183982971170595,4.203443357083016,4.222903742995438,4.242364128907859,4.26182451482028,4.281284900732702,4.300745286645123,4.320205672557544,4.339666058469965,4.359126444382388,4.378586830294808,4.39804721620723,4.417507602119651,4.436967988032073,4.456428373944494,4.4758887598569155,4.495349145769337,4.514809531681758,4.534269917594179,4.5537303035066,4.573190689419023,4.592651075331443,4.612111461243865,4.631571847156287,4.651032233068708,4.670492618981129,4.6899530048935505,4.709413390805972,4.728873776718393,4.748334162630814,4.767794548543235,4.787254934455658,4.806715320368078,4.8261757062805,4.845636092192922,4.865096478105343,4.884556864017764,4.9040172499301855,4.923477635842607,4.942938021755029,4.962398407667449,4.98185879357987,5.001319179492293,5.020779565404713,5.040239951317135,5.059700337229557,5.079160723141978,5.098621109054399,5.1180814949668205,5.137541880879242,5.157002266791664,5.1764626527040845,5.195923038616507,5.215383424528928,5.234843810441348,5.25430419635377,5.273764582266192,5.293224968178613,5.312685354091034,5.3321457400034555,5.351606125915877,5.371066511828299,5.3905268977407195,5.409987283653142,5.429447669565563,5.448908055477983,5.468368441390405,5.487828827302827,5.507289213215248,5.526749599127669,5.5462099850400906,5.565670370952512,5.585130756864934,5.6045911427773545,5.624051528689777,5.643511914602198,5.662972300514619,5.68243268642704,5.701893072339462,5.721353458251883,5.740813844164304,5.760274230076726,5.779734615989147,5.799195001901569,5.8186553878139895,5.838115773726412,5.857576159638833,5.877036545551254,5.896496931463675,5.915957317376097,5.935417703288518,5.954878089200939,5.9743384751133615,5.993798861025782,6.013259246938204,6.0327196328506245,6.052180018763047,6.071640404675468,6.091100790587889,6.11056117650031,6.1300215624127325,6.149481948325153,6.168942334237574,6.1884027201499965,6.207863106062417,6.227323491974839,6.2467838778872595,6.266244263799682,6.285704649712103,6.305165035624524,6.324625421536945,6.344085807449368,6.363546193361788,6.383006579274209,6.4024669651866315,6.421927351099052,6.441387737011474,6.4608481229238945,6.480308508836317,6.499768894748738,6.519229280661159,6.53868966657358,6.558150052486003,6.577610438398423,6.597070824310845,6.6165312102232665,6.635991596135687,6.655451982048109,6.67491236796053,6.694372753872952,6.713833139785373,6.733293525697794,6.752753911610216,6.772214297522638,6.791674683435058,6.81113506934748,6.8305954552599015,6.850055841172323,6.869516227084744,6.888976612997165,6.908436998909587,6.927897384822008,6.947357770734429,6.966818156646851,6.986278542559273,7.005738928471693,7.025199314384115,7.0446597002965365,7.064120086208958,7.083580472121379,7.1030408580338,7.122501243946222,7.141961629858643,7.161422015771064,7.180882401683486,7.200342787595908,7.219803173508328,7.23926355942075,7.258723945333172,7.278184331245593,7.297644717158014,7.317105103070436,7.336565488982857,7.356025874895278,7.375486260807699,7.394946646720121,7.414407032632543,7.433867418544963,7.453327804457385,7.472788190369807,7.492248576282228,7.511708962194649,7.531169348107071,7.550629734019492,7.570090119931913,7.589550505844334,7.609010891756756,7.628471277669178,7.647931663581598,7.66739204949402,7.686852435406442,7.706312821318863,7.725773207231284,7.745233593143706,7.764693979056127,7.784154364968549,7.8036147508809695,7.823075136793391,7.842535522705813,7.861995908618233,7.881456294530655,7.900916680443077,7.920377066355498,7.939837452267919,7.959297838180341,7.978758224092762,7.998218610005184,8.017678995917604,8.037139381830027,8.056599767742448,8.076060153654868,8.095520539567291,8.114980925479712,8.134441311392132,8.153901697304555,8.173362083216976,8.192822469129398,8.212282855041819,8.23174324095424,8.251203626866662,8.270664012779083,8.290124398691505,8.309584784603924,8.329045170516347,8.34850555642877,8.36796594234119,8.38742632825361,8.406886714166031,8.426347100078454,8.445807485990876,8.465267871903295,8.484728257815718,8.50418864372814,8.52364902964056,8.543109415552982,8.562569801465404,8.582030187377825,8.601490573290246,8.620950959202666,8.640411345115089,8.659871731027511,8.67933211693993,8.698792502852353,8.718252888764775,8.737713274677194,8.757173660589617,8.77663404650204,8.79609443241446,8.81555481832688,8.835015204239301,8.854475590151724,8.873935976064146,8.893396361976565,8.912856747888988,8.93231713380141,8.951777519713831,8.971237905626252,8.990698291538674,9.010158677451095,9.029619063363516,9.049079449275938,9.068539835188359,9.088000221100781,9.1074606070132,9.126920992925623,9.146381378838045,9.165841764750466,9.185302150662887,9.20476253657531,9.22422292248773,9.24368330840015,9.263143694312573,9.282604080224994,9.302064466137416,9.321524852049835,9.340985237962258,9.36044562387468,9.379906009787101,9.399366395699522,9.418826781611944,9.438287167524365,9.457747553436786,9.477207939349208,9.496668325261629,9.516128711174051,9.53558909708647,9.555049482998893,9.574509868911315,9.593970254823736,9.613430640736157,9.63289102664858,9.652351412561,9.671811798473422,9.691272184385843,9.710732570298264,9.730192956210686,9.749653342123105,9.769113728035528,9.78857411394795,9.808034499860371,9.827494885772792,9.846955271685214,9.866415657597635,9.885876043510057,9.905336429422478,9.924796815334899,9.944257201247321,9.96371758715974,9.983177973072163,10.002638358984585,10.022098744897006,10.041559130809427,10.06101951672185,10.08047990263427,10.099940288546692,10.119400674459113,10.138861060371534,10.158321446283956,10.177781832196375,10.197242218108798,10.21670260402122,10.236162989933641,10.255623375846062,10.275083761758484,10.294544147670905,10.314004533583327,10.333464919495748,10.352925305408169,10.372385691320591,10.391846077233014,10.411306463145433,10.430766849057855,10.450227234970276,10.469687620882697,10.48914800679512,10.50860839270754,10.528068778619962,10.547529164532383,10.566989550444804,10.586449936357226,10.605910322269649,10.625370708182068,10.64483109409449,10.664291480006911,10.683751865919332,10.703212251831754,10.722672637744175,10.742133023656598,10.761593409569018,10.781053795481439,10.800514181393861,10.819974567306284,10.839434953218703,10.858895339131125,10.878355725043546,10.897816110955967,10.91727649686839,10.93673688278081,10.956197268693233,10.975657654605653,10.995118040518074,11.014578426430496,11.034038812342919,11.053499198255338,11.07295958416776,11.092419970080181,11.111880355992602,11.131340741905024,11.150801127817445,11.170261513729868,11.189721899642288,11.209182285554709,11.228642671467131,11.248103057379554,11.267563443291973,11.287023829204395,11.306484215116816,11.325944601029239,11.34540498694166,11.36486537285408,11.384325758766503,11.403786144678923,11.423246530591344,11.442706916503766,11.462167302416189,11.481627688328608,11.50108807424103,11.520548460153451,11.540008846065874,11.559469231978294,11.578929617890715,11.598390003803138,11.617850389715558,11.637310775627979,11.656771161540401,11.676231547452824,11.695691933365243,11.715152319277665,11.734612705190088,11.754073091102509,11.77353347701493,11.79299386292735,11.812454248839773,11.831914634752193,11.851375020664614,11.870835406577037,11.890295792489459,11.909756178401878,11.9292165643143,11.948676950226723,11.968137336139144,11.987597722051564,12.007058107963985,12.026518493876408,12.04597887978883,12.065439265701249,12.084899651613672,12.104360037526094,12.123820423438513,12.143280809350935,12.162741195263358,12.182201581175779,12.2016619670882,12.22112235300062,12.240582738913043,12.260043124825465,12.279503510737884,12.298963896650307,12.318424282562729,12.337884668475148,12.35734505438757,12.376805440299993,12.396265826212414,12.415726212124834,12.435186598037255,12.454646983949678,12.4741073698621,12.493567755774519,12.513028141686942,12.532488527599364,12.551948913511783,12.571409299424205,12.590869685336628,12.610330071249049,12.62979045716147,12.64925084307389,12.668711228986313,12.688171614898735,12.707632000811154,12.727092386723577,12.746552772635999,12.766013158548418,12.78547354446084,12.804933930373263,12.824394316285684,12.843854702198104,12.863315088110525,12.882775474022948,12.90223585993537,12.921696245847789,12.941156631760212,12.960617017672634,12.980077403585055,12.999537789497476,13.018998175409898,13.038458561322319,13.05791894723474,13.07737933314716,13.096839719059583,13.116300104972005,13.135760490884424,13.155220876796847,13.174681262709269,13.19414164862169,13.21360203453411,13.233062420446533,13.252522806358954,13.271983192271374,13.291443578183797,13.310903964096218,13.33036435000864,13.34982473592106,13.369285121833482,13.388745507745904,13.408205893658325,13.427666279570746,13.447126665483168,13.466587051395589,13.48604743730801,13.505507823220432,13.524968209132853,13.544428595045275,13.563888980957694,13.583349366870117,13.60280975278254,13.62227013869496,13.64173052460738,13.661190910519803,13.680651296432224,13.700111682344646,13.719572068257067,13.739032454169488,13.75849284008191,13.77795322599433,13.797413611906752,13.816873997819174,13.836334383731595,13.855794769644016,13.875255155556438,13.894715541468859,13.914175927381281,13.933636313293702,13.953096699206123,13.972557085118545,13.992017471030964,14.011477856943387,14.03093824285581,14.05039862876823,14.06985901468065,14.089319400593073,14.108779786505494,14.128240172417916,14.147700558330337,14.167160944242758,14.18662133015518,14.2060817160676,14.225542101980022,14.245002487892444,14.264462873804865,14.283923259717286,14.303383645629708,14.322844031542129,14.342304417454551,14.361764803366972,14.381225189279393,14.400685575191815,14.420145961104234,14.439606347016657,14.45906673292908,14.4785271188415,14.49798750475392,14.517447890666343,14.536908276578764,14.556368662491186,14.575829048403607,14.595289434316028,14.61474982022845,14.634210206140873,14.653670592053292,14.673130977965714,14.692591363878135,14.712051749790556,14.731512135702978,14.750972521615399,14.770432907527821,14.789893293440242,14.809353679352663,14.828814065265085,14.848274451177508,14.867734837089927,14.88719522300235,14.90665560891477,14.92611599482719,14.945576380739613,14.965036766652034,14.984497152564456,15.003957538476877,15.023417924389298,15.04287831030172,15.062338696214143,15.081799082126562,15.101259468038984,15.120719853951405,15.140180239863826,15.159640625776248,15.179101011688669,15.198561397601091,15.218021783513512,15.237482169425933,15.256942555338355,15.276402941250778,15.295863327163197,15.31532371307562,15.33478409898804,15.354244484900462,15.373704870812883,15.393165256725304,15.412625642637726,15.432086028550147,15.451546414462568,15.47100680037499,15.490467186287413,15.509927572199832,15.529387958112254,15.548848344024675,15.568308729937097,15.587769115849518,15.607229501761939,15.626689887674361,15.646150273586782,15.665610659499203,15.685071045411625,15.704531431324048,15.723991817236467,15.74345220314889,15.76291258906131,15.782372974973732,15.801833360886153,15.821293746798574,15.840754132710996,15.860214518623417,15.879674904535838,15.89913529044826,15.918595676360683,15.938056062273102,15.957516448185524,15.976976834097947,15.996437220010367,16.015897605922788,16.03535799183521,16.05481837774763,16.074278763660054,16.093739149572475,16.113199535484895,16.132659921397316,16.152120307309737,16.17158069322216,16.191041079134582,16.210501465047003,16.229961850959423,16.249422236871844,16.268882622784265,16.28834300869669,16.30780339460911,16.32726378052153,16.34672416643395,16.366184552346372,16.385644938258796,16.405105324171217,16.424565710083638,16.44402609599606,16.46348648190848,16.4829468678209,16.502407253733324,16.521867639645745,16.541328025558165],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Rule 3","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250],"y":[0,0.028685426094851953,0.057370852189703905,0.08605627828455585,0.11474170437940781,0.14342713047425976,0.1721125565691117,0.20079798266396368,0.22948340875881562,0.25816883485366754,0.2868542609485195,0.31553968704337143,0.3442251131382234,0.3729105392330754,0.40159596532792735,0.43028139142277927,0.45896681751763124,0.4876522436124832,0.5163376697073351,0.5450230958021871,0.573708521897039,0.602393947991891,0.6310793740867429,0.6597648001815949,0.6884502262764468,0.7171356523712988,0.7458210784661508,0.7745065045610027,0.8031919306558547,0.8318773567507066,0.8605627828455585,0.8892482089404105,0.9179336350352625,0.9466190611301144,0.9753044872249664,1.0039899133198185,1.0326753394146702,1.061360765509522,1.0900461916043742,1.1187316176992261,1.147417043794078,1.17610246988893,1.204787895983782,1.2334733220786338,1.2621587481734857,1.2908441742683379,1.3195296003631898,1.3482150264580417,1.3769004525528936,1.4055858786477458,1.4342713047425977,1.4629567308374494,1.4916421569323015,1.5203275830271534,1.5490130091220053,1.5776984352168573,1.6063838613117094,1.6350692874065613,1.6637547135014132,1.6924401395962652,1.721125565691117,1.749810991785969,1.778496417880821,1.807181843975673,1.835867270070525,1.8645526961653769,1.8932381222602288,1.921923548355081,1.9506089744499329,1.9792944005447848,2.007979826639637,2.036665252734488,2.0653506788293403,2.0940361049241925,2.122721531019044,2.1514069571138963,2.1800923832087484,2.2087778093036,2.2374632353984523,2.2661486614933044,2.294834087588156,2.3235195136830082,2.35220493977786,2.380890365872712,2.409575791967564,2.438261218062416,2.4669466441572676,2.4956320702521197,2.5243174963469714,2.5530029224418236,2.5816883485366757,2.6103737746315274,2.6390592007263796,2.6677446268212317,2.6964300529160834,2.7251154790109355,2.7538009051057872,2.7824863312006394,2.8111717572954915,2.839857183390343,2.8685426094851953,2.8972280355800475,2.9259134616748987,2.954598887769751,2.983284313864603,3.0119697399594547,3.040655166054307,3.0693405921491586,3.0980260182440107,3.126711444338863,3.1553968704337145,3.1840822965285667,3.212767722623419,3.2414531487182705,3.2701385748131226,3.298824000907975,3.3275094270028265,3.3561948530976786,3.3848802791925303,3.413565705287382,3.442251131382234,3.470936557477086,3.499621983571938,3.52830740966679,3.556992835761642,3.585678261856494,3.614363687951346,3.643049114046198,3.67173454014105,3.700419966235902,3.7291053923307538,3.757790818425606,3.7864762445204576,3.8151616706153098,3.843847096710162,3.8725325228050136,3.9012179488998657,3.929903374994718,3.9585888010895696,3.9872742271844217,4.015959653279274,4.044645079374125,4.073330505468976,4.1020159315638285,4.130701357658681,4.159386783753533,4.188072209848385,4.216757635943237,4.245443062038088,4.27412848813294,4.302813914227793,4.331499340322645,4.360184766417497,4.388870192512348,4.4175556186072,4.446241044702052,4.4749264707969045,4.503611896891757,4.532297322986609,4.56098274908146,4.589668175176312,4.618353601271164,4.6470390273660165,4.675724453460869,4.70440987955572,4.733095305650572,4.761780731745424,4.790466157840276,4.819151583935128,4.847837010029981,4.876522436124832,4.905207862219684,4.933893288314535,4.962578714409387,4.9912641405042395,5.019949566599092,5.048634992693943,5.077320418788795,5.106005844883647,5.134691270978499,5.1633766970733515,5.192062123168203,5.220747549263055,5.249432975357907,5.278118401452759,5.306803827547611,5.335489253642463,5.364174679737315,5.392860105832167,5.421545531927019,5.450230958021871,5.478916384116723,5.5076018102115745,5.536287236306427,5.564972662401279,5.593658088496131,5.622343514590983,5.651028940685835,5.679714366780686,5.708399792875539,5.737085218970391,5.765770645065243,5.794456071160095,5.823141497254946,5.8518269233497975,5.88051234944465,5.909197775539502,5.937883201634354,5.966568627729206,5.995254053824057,6.023939479918909,6.052624906013762,6.081310332108614,6.109995758203466,6.138681184298317,6.167366610393169,6.196052036488021,6.2247374625828735,6.253422888677726,6.282108314772578,6.310793740867429,6.339479166962281,6.368164593057133,6.3968500191519855,6.425535445246838,6.454220871341689,6.482906297436541,6.511591723531393,6.540277149626245,6.568962575721097,6.59764800181595,6.626333427910801,6.655018854005653,6.683704280100505,6.712389706195357,6.7410751322902085,6.769760558385061,6.798445984479912,6.827131410574764,6.855816836669616,6.884502262764468,6.9131876888593204,6.941873114954172,6.970558541049024,6.999243967143876,7.027929393238728,7.05661481933358,7.085300245428432,7.113985671523284,7.142671097618136,7.171356523712988],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192],"y":[0,0.023947543495361684,0.04789508699072337,0.07184263048608505,0.09579017398144674,0.11973771747680842,0.1436852609721701,0.1676328044675318,0.19158034796289347,0.21552789145825516,0.23947543495361684,0.2634229784489785,0.2873705219443402,0.31131806543970186,0.3352656089350636,0.3592131524304253,0.38316069592578694,0.40710823942114865,0.4310557829165103,0.45500332641187197,0.4789508699072337,0.5028984134025953,0.526845956897957,0.5507935003933188,0.5747410438886804,0.5986885873840422,0.6226361308794037,0.6465836743747655,0.6705312178701271,0.6944787613654888,0.7184263048608506,0.7423738483562122,0.7663213918515739,0.7902689353469355,0.8142164788422973,0.8381640223376589,0.8621115658330206,0.8860591093283824,0.9100066528237439,0.9339541963191057,0.9579017398144674,0.981849283309829,1.0057968268051907,1.0297443703005524,1.053691913795914,1.0776394572912757,1.1015870007866375,1.125534544281999,1.1494820877773608,1.1734296312727224,1.1973771747680844,1.221324718263446,1.2452722617588075,1.2692198052541694,1.293167348749531,1.3171148922448925,1.3410624357402543,1.365009979235616,1.3889575227309776,1.4129050662263394,1.4368526097217011,1.4608001532170627,1.4847476967124245,1.508695240207786,1.5326427837031478,1.5565903271985095,1.580537870693871,1.6044854141892326,1.6284329576845946,1.6523805011799562,1.6763280446753177,1.7002755881706797,1.7242231316660412,1.7481706751614028,1.7721182186567648,1.7960657621521263,1.8200133056474879,1.8439608491428499,1.8679083926382114,1.891855936133573,1.9158034796289347,1.9397510231242965,1.963698566619658,1.9876461101150198,2.0115936536103813,2.035541197105743,2.059488740601105,2.0834362840964666,2.107383827591828,2.13133137108719,2.1552789145825515,2.1792264580779133,2.203174001573275,2.227121545068637,2.251069088563998,2.27501663205936,2.2989641755547217,2.3229117190500834,2.3468592625454447,2.3708068060408065,2.3947543495361687,2.41870189303153,2.442649436526892,2.4665969800222536,2.490544523517615,2.5144920670129767,2.538439610508339,2.5623871540037,2.586334697499062,2.6102822409944237,2.634229784489785,2.658177327985147,2.6821248714805086,2.70607241497587,2.730019958471232,2.753967501966594,2.7779150454619552,2.801862588957317,2.8258101324526788,2.84975767594804,2.8737052194434023,2.897652762938764,2.9216003064341254,2.945547849929487,2.969495393424849,2.9934429369202102,3.017390480415572,3.041338023910934,3.0652855674062955,3.0892331109016573,3.113180654397019,3.1371281978923804,3.161075741387742,3.185023284883104,3.2089708283784653,3.2329183718738275,3.2568659153691892,3.2808134588645506,3.3047610023599123,3.328708545855274,3.3526560893506354,3.3766036328459976,3.4005511763413594,3.4244987198367207,3.4484462633320825,3.4723938068274443,3.4963413503228056,3.5202888938181673,3.5442364373135296,3.568183980808891,3.5921315243042526,3.6160790677996144,3.6400266112949757,3.6639741547903375,3.6879216982856997,3.711869241781061,3.735816785276423,3.7597643287717846,3.783711872267146,3.8076594157625077,3.8316069592578694,3.8555545027532308,3.879502046248593,3.9034495897439547,3.927397133239316,3.951344676734678,3.9752922202300396,3.999239763725401,4.023187307220763,4.047134850716125,4.071082394211486,4.0950299377068475,4.11897748120221,4.142925024697571,4.166872568192933,4.190820111688295,4.214767655183656,4.238715198679018,4.26266274217438,4.286610285669742,4.310557829165103,4.334505372660465,4.3584529161558265,4.382400459651188,4.40634800314655,4.430295546641911,4.454243090137274,4.478190633632635,4.502138177127996,4.5260857206233585,4.55003326411872,4.573980807614081,4.597928351109443],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">16.69%</td>
                <td>70.2%</td>
                <td>238</td>
                <td>1.31</td>
                <td>0.00</td>
                <td>4.69%</td>
                <td>57.7</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">19.70%</td>
                <td>64.5%</td>
                <td>1041</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>9.15%</td>
                <td>57.2</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">16.54%</td>
                <td>63.1%</td>
                <td>850</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>12.68%</td>
                <td>55.5</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Rule 3: RSI Oversold</td>
                <td>ORIGINAL</td>
                <td class="positive">7.17%</td>
                <td>65.6%</td>
                <td>250</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>7.13%</td>
                <td>52.5</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">4.60%</td>
                <td>64.1%</td>
                <td>192</td>
                <td>1.10</td>
                <td>0.00</td>
                <td>6.70%</td>
                <td>51.1</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Rule 24: MFI Oversold</td>
                <td>ORIGINAL</td>
                <td class="positive">1.76%</td>
                <td>64.7%</td>
                <td>150</td>
                <td>1.05</td>
                <td>0.00</td>
                <td>7.14%</td>
                <td>50.1</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">1.10%</td>
                <td>62.5%</td>
                <td>24</td>
                <td>1.18</td>
                <td>0.00</td>
                <td>2.12%</td>
                <td>26.4</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
