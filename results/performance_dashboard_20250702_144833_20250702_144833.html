
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 16 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 14:48:33</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">16</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">55.2%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">14.8%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">26.5%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">64.4%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">12,020</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["20.1%","24.1%","25.2%","16.0%","6.9%","8.2%","26.5%","12.1%","15.2%","8.2%"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","Rule 28: Volume Breakout","AI Rule 8: Momentum Divergence Reversal","Professional Rule 10: CCI Reversal Enhanced","Professional Rule 7: Chaikin Money Flow Reversal","Volatility Rule 2: ATR Expansion Signal","Volume Rule 4: Volume Breakout Confirmation"],"y":[20.081585123704638,24.06034792915104,25.192648942806507,15.988301762969126,6.871312242260386,8.23929524564364,26.504688247782603,12.050831430345909,15.1858020306614,8.15314613194098],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","Rule 28: Volume Breakout","AI Rule 8: Momentum Divergence Reversal","Professional Rule 10: CCI Reversal Enhanced","Professional Rule 7: Chaikin Money Flow Reversal","Volatility Rule 2: ATR Expansion Signal","Volume Rule 4: Volume Breakout Confirmation"],"y":[697,659,682,503,307,471,514,543,274,321],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","Rule 28: Volume Breakout","AI Rule 8: Momentum Divergence Reversal","Professional Rule 10: CCI Reversal Enhanced","Professional Rule 7: Chaikin Money Flow Reversal","Volatility Rule 2: ATR Expansion Signal","Volume Rule 4: Volume Breakout Confirmation"],"y":[388,362,370,277,176,267,270,308,147,182],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[20.081585123704638,24.06034792915104,25.192648942806507,15.988301762969126,6.871312242260386,8.23929524564364,26.504688247782603,12.050831430345909,15.1858020306614,8.15314613194098,9.859451046817291,18.751354825075506,10.032981620479763,16.703530479619676,11.022107241418944,8.665283155345627],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["AI Rule 10","Rule 7","Ext Rule 6","Prof Rule 7","Rule 28","AI Rule 8","Professional Rule 10","Professional Rule 7","Volatility Rule 2","Volume Rule 4","Rule 10","Momentum Rule 2","Rule 27","Rule 6","Volume Rule 3","Price Action Rule 3"],"textposition":"top center","x":[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0],"y":[20.081585123704638,24.06034792915104,25.192648942806507,15.988301762969126,6.871312242260386,8.23929524564364,26.504688247782603,12.050831430345909,15.1858020306614,8.15314613194098,9.859451046817291,18.751354825075506,10.032981620479763,16.703530479619676,11.022107241418944,8.665283155345627],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["14.2%","13.5%","15.7%","16.0%"],"textposition":"auto","x":["AI_GENERATED","ORIGINAL","UNKNOWN","PROFESSIONAL"],"y":[14.16044018467414,13.50552466366563,15.690732750672183,15.988301762969126],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["1085","1021","1052","780","483","738","784","851","421","503"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","Rule 28: Volume Breakout","AI Rule 8: Momentum Divergence Reversal","Professional Rule 10: CCI Reversal Enhanced","Professional Rule 7: Chaikin Money Flow Reversal","Volatility Rule 2: ATR Expansion Signal","Volume Rule 4: Volume Breakout Confirmation"],"y":[1085,1021,1052,780,483,738,784,851,421,503],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085],"y":[0,0.01850837338590289,0.03701674677180578,0.05552512015770868,0.07403349354361156,0.09254186692951447,0.11105024031541735,0.12955861370132024,0.14806698708722313,0.16657536047312604,0.18508373385902893,0.20359210724493182,0.2221004806308347,0.24060885401673762,0.2591172274026405,0.2776256007885434,0.29613397417444626,0.3146423475603492,0.3331507209462521,0.3516590943321549,0.37016746771805786,0.38867584110396075,0.40718421448986364,0.4256925878757665,0.4442009612616694,0.4627093346475723,0.48121770803347524,0.4997260814193781,0.518234454805281,0.5367428281911839,0.5552512015770869,0.5737595749629897,0.5922679483488925,0.6107763217347955,0.6292846951206984,0.6477930685066012,0.6663014418925042,0.684809815278407,0.7033181886643098,0.7218265620502129,0.7403349354361157,0.7588433088220186,0.7773516822079215,0.7958600555938243,0.8143684289797273,0.8328768023656302,0.851385175751533,0.869893549137436,0.8884019225233388,0.9069102959092417,0.9254186692951446,0.9439270426810474,0.9624354160669505,0.9809437894528533,0.9994521628387562,1.017960536224659,1.036468909610562,1.0549772829964648,1.0734856563823678,1.0919940297682706,1.1105024031541737,1.1290107765400765,1.1475191499259794,1.1660275233118822,1.184535896697785,1.2030442700836879,1.221552643469591,1.2400610168554937,1.2585693902413968,1.2770777636272994,1.2955861370132025,1.3140945103991053,1.3326028837850084,1.3511112571709112,1.369619630556814,1.388128003942717,1.4066363773286197,1.4251447507145227,1.4436531241004258,1.4621614974863284,1.4806698708722315,1.4991782442581343,1.5176866176440371,1.53619499102994,1.554703364415843,1.5732117378017456,1.5917201111876487,1.6102284845735517,1.6287368579594546,1.6472452313453574,1.6657536047312604,1.6842619781171633,1.702770351503066,1.721278724888969,1.739787098274872,1.7582954716607746,1.7768038450466777,1.7953122184325807,1.8138205918184833,1.8323289652043864,1.8508373385902892,1.869345711976192,1.8878540853620949,1.906362458747998,1.924870832133901,1.9433792055198036,1.9618875789057066,1.9803959522916097,1.9989043256775123,2.0174126990634154,2.035921072449318,2.054429445835221,2.072937819221124,2.091446192607027,2.1099545659929295,2.128462939378833,2.1469713127647356,2.1654796861506385,2.1839880595365413,2.202496432922444,2.2210048063083474,2.23951317969425,2.258021553080153,2.276529926466056,2.2950382998519587,2.3135466732378616,2.3320550466237644,2.3505634200096672,2.36907179339557,2.3875801667814733,2.4060885401673757,2.424596913553279,2.443105286939182,2.4616136603250847,2.4801220337109875,2.4986304070968903,2.5171387804827936,2.535647153868696,2.554155527254599,2.572663900640502,2.591172274026405,2.6096806474123078,2.6281890207982106,2.6466973941841134,2.6652057675700167,2.6837141409559195,2.7022225143418224,2.720730887727725,2.739239261113628,2.757747634499531,2.776256007885434,2.794764381271337,2.8132727546572394,2.8317811280431426,2.8502895014290455,2.8687978748149483,2.8873062482008516,2.905814621586754,2.924322994972657,2.94283136835856,2.961339741744463,2.9798481151303653,2.9983564885162686,3.0168648619021714,3.0353732352880742,3.0538816086739775,3.07238998205988,3.0908983554457827,3.109406728831686,3.127915102217589,3.1464234756034912,3.1649318489893945,3.1834402223752973,3.2019485957612,3.2204569691471034,3.2389653425330063,3.257473715918909,3.275982089304812,3.2944904626907148,3.312998836076618,3.331507209462521,3.3500155828484233,3.3685239562343265,3.3870323296202294,3.405540703006132,3.4240490763920355,3.442557449777938,3.4610658231638407,3.479574196549744,3.498082569935647,3.516590943321549,3.5350993167074525,3.5536076900933553,3.572116063479258,3.5906244368651614,3.609132810251064,3.6276411836369666,3.64614955702287,3.6646579304087727,3.683166303794675,3.7016746771805784,3.7201830505664812,3.738691423952384,3.7571997973382874,3.7757081707241897,3.7942165441100926,3.812724917495996,3.8312332908818987,3.849741664267802,3.868250037653705,3.886758411039607,3.9052667844255105,3.9237751578114133,3.942283531197316,3.9607919045832194,3.979300277969122,3.9978086513550246,4.016317024740927,4.034825398126831,4.053333771512733,4.071842144898636,4.090350518284539,4.108858891670442,4.127367265056345,4.145875638442248,4.164384011828151,4.182892385214054,4.201400758599957,4.219909131985859,4.238417505371762,4.256925878757666,4.275434252143568,4.293942625529471,4.312450998915374,4.330959372301277,4.34946774568718,4.367976119073083,4.386484492458985,4.404992865844888,4.4235012392307915,4.442009612616695,4.460517986002597,4.4790263593885,4.497534732774403,4.516043106160306,4.5345514795462085,4.553059852932112,4.571568226318014,4.5900765997039175,4.608584973089821,4.627093346475723,4.6456017198616255,4.664110093247529,4.682618466633432,4.7011268400193345,4.719635213405238,4.73814358679114,4.756651960177043,4.775160333562947,4.793668706948849,4.8121770803347514,4.830685453720655,4.849193827106558,4.86770220049246,4.886210573878364,4.904718947264266,4.923227320650169,4.941735694036073,4.960244067421975,4.978752440807877,4.997260814193781,5.015769187579684,5.034277560965587,5.052785934351489,5.071294307737392,5.089802681123295,5.108311054509198,5.126819427895101,5.145327801281004,5.163836174666907,5.18234454805281,5.200852921438713,5.2193612948246155,5.237869668210518,5.256378041596421,5.274886414982324,5.293394788368227,5.31190316175413,5.330411535140033,5.348919908525936,5.367428281911839,5.385936655297742,5.404445028683645,5.422953402069548,5.44146177545545,5.459970148841353,5.478478522227256,5.496986895613159,5.515495268999062,5.534003642384965,5.552512015770868,5.571020389156771,5.589528762542674,5.608037135928577,5.626545509314479,5.645053882700382,5.663562256086285,5.682070629472188,5.700579002858091,5.719087376243994,5.737595749629897,5.7561041230158,5.774612496401703,5.793120869787605,5.811629243173508,5.830137616559411,5.848645989945314,5.867154363331217,5.88566273671712,5.9041711101030225,5.922679483488926,5.941187856874829,5.959696230260731,5.978204603646634,5.996712977032537,6.0152213504184395,6.033729723804343,6.052238097190246,6.0707464705761485,6.089254843962052,6.107763217347955,6.1262715907338565,6.14477996411976,6.163288337505663,6.1817967108915655,6.200305084277469,6.218813457663372,6.237321831049274,6.255830204435178,6.274338577821081,6.2928469512069825,6.311355324592886,6.329863697978789,6.348372071364691,6.366880444750595,6.385388818136498,6.4038971915224,6.422405564908304,6.440913938294207,6.459422311680109,6.4779306850660126,6.496439058451915,6.514947431837818,6.533455805223721,6.551964178609624,6.570472551995527,6.5889809253814295,6.607489298767333,6.625997672153236,6.6445060455391385,6.663014418925042,6.681522792310945,6.7000311656968465,6.71853953908275,6.737047912468653,6.7555562858545555,6.774064659240459,6.792573032626362,6.811081406012264,6.829589779398168,6.848098152784071,6.8666065261699725,6.885114899555876,6.903623272941779,6.922131646327681,6.940640019713585,6.959148393099488,6.97765676648539,6.996165139871294,7.014673513257197,7.033181886643098,7.051690260029002,7.070198633414905,7.088707006800807,7.107215380186711,7.125723753572614,7.144232126958516,7.16274050034442,7.181248873730323,7.199757247116224,7.218265620502128,7.236773993888031,7.255282367273933,7.2737907406598366,7.29229911404574,7.310807487431642,7.3293158608175455,7.347824234203449,7.36633260758935,7.3848409809752535,7.403349354361157,7.421857727747059,7.4403661011329625,7.458874474518866,7.477382847904768,7.495891221290671,7.514399594676575,7.532907968062476,7.5514163414483795,7.569924714834283,7.588433088220185,7.606941461606088,7.625449834991992,7.643958208377894,7.662466581763797,7.680974955149701,7.699483328535604,7.717991701921506,7.73650007530741,7.755008448693312,7.773516822079214,7.792025195465118,7.810533568851021,7.829041942236923,7.847550315622827,7.86605868900873,7.884567062394632,7.9030754357805355,7.921583809166439,7.94009218255234,7.958600555938244,7.977108929324147,7.995617302710049,8.014125676095952,8.032634049481855,8.051142422867757,8.069650796253661,8.088159169639564,8.106667543025466,8.12517591641137,8.143684289797273,8.162192663183175,8.180701036569078,8.199209409954982,8.217717783340884,8.236226156726786,8.25473453011269,8.273242903498593,8.291751276884495,8.3102596502704,8.328768023656302,8.347276397042204,8.365784770428109,8.38429314381401,8.402801517199913,8.421309890585816,8.439818263971718,8.458326637357622,8.476835010743525,8.495343384129427,8.513851757515331,8.532360130901234,8.550868504287136,8.569376877673038,8.587885251058943,8.606393624444845,8.624901997830747,8.643410371216651,8.661918744602554,8.680427117988456,8.69893549137436,8.717443864760261,8.735952238146165,8.754460611532068,8.77296898491797,8.791477358303874,8.809985731689777,8.828494105075679,8.847002478461583,8.865510851847485,8.88401922523339,8.90252759861929,8.921035972005194,8.939544345391097,8.958052718777,8.976561092162903,8.995069465548806,9.013577838934708,9.032086212320612,9.050594585706515,9.069102959092417,9.08761133247832,9.106119705864224,9.124628079250126,9.143136452636028,9.161644826021933,9.180153199407835,9.198661572793737,9.217169946179641,9.235678319565542,9.254186692951446,9.272695066337349,9.291203439723251,9.309711813109155,9.328220186495058,9.34672855988096,9.365236933266864,9.383745306652767,9.402253680038669,9.420762053424571,9.439270426810475,9.457778800196378,9.47628717358228,9.494795546968184,9.513303920354087,9.53181229373999,9.550320667125893,9.568829040511796,9.587337413897698,9.605845787283602,9.624354160669503,9.642862534055407,9.66137090744131,9.679879280827212,9.698387654213116,9.716896027599018,9.73540440098492,9.753912774370825,9.772421147756727,9.79092952114263,9.809437894528532,9.827946267914436,9.846454641300339,9.864963014686241,9.883471388072145,9.901979761458048,9.92048813484395,9.938996508229854,9.957504881615755,9.976013255001659,9.994521628387561,10.013030001773464,10.031538375159368,10.05004674854527,10.068555121931174,10.087063495317077,10.105571868702977,10.124080242088882,10.142588615474784,10.161096988860688,10.17960536224659,10.198113735632495,10.216622109018395,10.2351304824043,10.253638855790202,10.272147229176106,10.290655602562008,10.309163975947913,10.327672349333813,10.346180722719716,10.36468909610562,10.383197469491522,10.401705842877426,10.420214216263329,10.438722589649231,10.457230963035133,10.475739336421036,10.49424770980694,10.512756083192842,10.531264456578747,10.549772829964647,10.568281203350551,10.586789576736454,10.605297950122358,10.62380632350826,10.642314696894164,10.660823070280067,10.679331443665967,10.697839817051872,10.716348190437774,10.734856563823678,10.75336493720958,10.771873310595485,10.790381683981385,10.80889005736729,10.827398430753192,10.845906804139096,10.864415177524998,10.8829235509109,10.901431924296803,10.919940297682706,10.93844867106861,10.956957044454512,10.975465417840416,10.993973791226319,11.01248216461222,11.030990537998123,11.049498911384026,11.06800728476993,11.086515658155832,11.105024031541737,11.123532404927637,11.142040778313541,11.160549151699444,11.179057525085348,11.19756589847125,11.216074271857154,11.234582645243055,11.253091018628957,11.271599392014862,11.290107765400764,11.308616138786668,11.32712451217257,11.345632885558471,11.364141258944375,11.382649632330278,11.401158005716182,11.419666379102084,11.438174752487988,11.456683125873889,11.475191499259793,11.493699872645696,11.5122082460316,11.530716619417502,11.549224992803406,11.567733366189307,11.58624173957521,11.604750112961113,11.623258486347016,11.64176685973292,11.660275233118822,11.678783606504725,11.697291979890627,11.71580035327653,11.734308726662434,11.752817100048336,11.77132547343424,11.789833846820141,11.808342220206045,11.826850593591947,11.845358966977852,11.863867340363754,11.882375713749658,11.90088408713556,11.919392460521461,11.937900833907365,11.956409207293268,11.974917580679172,11.993425954065074,12.011934327450978,12.030442700836879,12.048951074222783,12.067459447608686,12.08596782099459,12.104476194380492,12.122984567766396,12.141492941152297,12.1600013145382,12.178509687924103,12.197018061310006,12.21552643469591,12.234034808081812,12.252543181467713,12.271051554853617,12.28955992823952,12.308068301625424,12.326576675011326,12.34508504839723,12.363593421783131,12.382101795169035,12.400610168554937,12.419118541940842,12.437626915326744,12.456135288712648,12.474643662098549,12.493152035484451,12.511660408870355,12.530168782256258,12.548677155642162,12.567185529028064,12.585693902413965,12.604202275799869,12.622710649185771,12.641219022571676,12.659727395957578,12.678235769343482,12.696744142729383,12.715252516115287,12.73376088950119,12.752269262887094,12.770777636272996,12.7892860096589,12.8077943830448,12.826302756430703,12.844811129816607,12.86331950320251,12.881827876588414,12.900336249974316,12.918844623360219,12.937352996746121,12.955861370132025,12.974369743517927,12.99287811690383,13.011386490289734,13.029894863675636,13.048403237061539,13.066911610447441,13.085419983833345,13.103928357219248,13.122436730605152,13.140945103991054,13.159453477376955,13.177961850762859,13.196470224148761,13.214978597534666,13.233486970920568,13.251995344306472,13.270503717692373,13.289012091078277,13.30752046446418,13.326028837850084,13.344537211235986,13.36304558462189,13.38155395800779,13.400062331393693,13.418570704779597,13.4370790781655,13.455587451551404,13.474095824937306,13.492604198323207,13.511112571709111,13.529620945095013,13.548129318480918,13.56663769186682,13.585146065252724,13.603654438638625,13.622162812024529,13.640671185410431,13.659179558796335,13.677687932182238,13.696196305568142,13.714704678954043,13.733213052339945,13.751721425725849,13.770229799111751,13.788738172497656,13.807246545883558,13.825754919269459,13.844263292655363,13.862771666041265,13.88128003942717,13.899788412813072,13.918296786198976,13.936805159584877,13.95531353297078,13.973821906356683,13.992330279742587,14.01083865312849,14.029347026514394,14.047855399900294,14.066363773286197,14.084872146672101,14.103380520058003,14.121888893443908,14.14039726682981,14.158905640215712,14.177414013601615,14.195922386987519,14.214430760373421,14.232939133759324,14.251447507145228,14.26995588053113,14.288464253917033,14.306972627302935,14.32548100068884,14.343989374074742,14.362497747460646,14.381006120846548,14.399514494232449,14.418022867618353,14.436531241004255,14.45503961439016,14.473547987776062,14.492056361161966,14.510564734547867,14.52907310793377,14.547581481319673,14.566089854705577,14.58459822809148,14.603106601477384,14.621614974863284,14.640123348249187,14.658631721635091,14.677140095020993,14.695648468406898,14.7141568417928,14.7326652151787,14.751173588564605,14.769681961950507,14.788190335336411,14.806698708722314,14.825207082108218,14.843715455494118,14.862223828880023,14.880732202265925,14.89924057565183,14.917748949037732,14.936257322423636,14.954765695809536,14.973274069195439,14.991782442581343,15.010290815967245,15.02879918935315,15.047307562739052,15.065815936124952,15.084324309510857,15.102832682896759,15.121341056282663,15.139849429668566,15.15835780305447,15.17686617644037,15.195374549826274,15.213882923212177,15.232391296598081,15.250899669983983,15.269408043369888,15.287916416755788,15.30642479014169,15.324933163527595,15.343441536913497,15.361949910299401,15.380458283685304,15.398966657071208,15.417475030457108,15.435983403843013,15.454491777228915,15.47300015061482,15.491508524000722,15.510016897386624,15.528525270772526,15.547033644158429,15.565542017544333,15.584050390930235,15.60255876431614,15.621067137702042,15.639575511087942,15.658083884473847,15.676592257859749,15.695100631245653,15.713609004631556,15.73211737801746,15.75062575140336,15.769134124789264,15.787642498175167,15.806150871561071,15.824659244946973,15.843167618332878,15.861675991718778,15.88018436510468,15.898692738490585,15.917201111876487,15.935709485262391,15.954217858648294,15.972726232034194,15.991234605420098,16.009742978806003,16.028251352191905,16.046759725577807,16.06526809896371,16.083776472349612,16.102284845735515,16.12079321912142,16.139301592507323,16.157809965893225,16.176318339279128,16.19482671266503,16.213335086050932,16.231843459436835,16.25035183282274,16.268860206208643,16.287368579594546,16.305876952980448,16.32438532636635,16.342893699752253,16.361402073138155,16.37991044652406,16.398418819909963,16.416927193295866,16.435435566681768,16.45394394006767,16.472452313453573,16.49096068683948,16.50946906022538,16.52797743361128,16.546485806997186,16.56499418038309,16.58350255376899,16.602010927154893,16.6205193005408,16.6390276739267,16.657536047312604,16.676044420698506,16.69455279408441,16.71306116747031,16.731569540856217,16.75007791424212,16.76858628762802,16.787094661013924,16.805603034399827,16.82411140778573,16.84261978117163,16.861128154557537,16.879636527943436,16.89814490132934,16.916653274715244,16.935161648101147,16.95367002148705,16.97217839487295,16.990686768258854,17.009195141644756,17.027703515030662,17.046211888416565,17.064720261802467,17.08322863518837,17.101737008574272,17.120245381960174,17.138753755346077,17.157262128731983,17.175770502117885,17.194278875503787,17.21278724888969,17.231295622275592,17.249803995661495,17.268312369047397,17.286820742433303,17.305329115819205,17.323837489205108,17.34234586259101,17.360854235976912,17.379362609362815,17.39787098274872,17.416379356134623,17.434887729520522,17.453396102906428,17.47190447629233,17.490412849678233,17.508921223064135,17.52742959645004,17.54593796983594,17.564446343221846,17.58295471660775,17.60146308999365,17.619971463379553,17.638479836765455,17.656988210151358,17.67549658353726,17.694004956923166,17.71251333030907,17.73102170369497,17.749530077080873,17.76803845046678,17.786546823852678,17.80505519723858,17.823563570624486,17.84207194401039,17.86058031739629,17.879088690782194,17.897597064168096,17.916105437554,17.934613810939904,17.953122184325807,17.97163055771171,17.99013893109761,18.008647304483514,18.027155677869416,18.04566405125532,18.064172424641225,18.082680798027127,18.10118917141303,18.11969754479893,18.138205918184834,18.156714291570736,18.17522266495664,18.193731038342545,18.212239411728447,18.23074778511435,18.249256158500252,18.267764531886154,18.286272905272057,18.304781278657963,18.323289652043865,18.341798025429764,18.36030639881567,18.378814772201572,18.397323145587475,18.415831518973377,18.434339892359283,18.452848265745182,18.471356639131084,18.48986501251699,18.508373385902892,18.526881759288795,18.545390132674697,18.5638985060606,18.582406879446502,18.600915252832408,18.61942362621831,18.637931999604213,18.656440372990115,18.674948746376018,18.69345711976192,18.711965493147822,18.73047386653373,18.74898223991963,18.767490613305533,18.785998986691435,18.804507360077338,18.82301573346324,18.841524106849143,18.86003248023505,18.87854085362095,18.897049227006853,18.915557600392756,18.934065973778658,18.95257434716456,18.971082720550466,18.98959109393637,19.00809946732227,19.026607840708174,19.045116214094076,19.06362458747998,19.08213296086588,19.100641334251787,19.11914970763769,19.13765808102359,19.156166454409494,19.174674827795396,19.1931832011813,19.211691574567205,19.230199947953107,19.248708321339006,19.26721669472491,19.285725068110814,19.304233441496716,19.32274181488262,19.341250188268525,19.359758561654424,19.378266935040326,19.396775308426232,19.415283681812134,19.433792055198037,19.45230042858394,19.47080880196984,19.489317175355744,19.50782554874165,19.526333922127552,19.544842295513455,19.563350668899357,19.58185904228526,19.600367415671162,19.618875789057064,19.63738416244297,19.655892535828873,19.674400909214775,19.692909282600677,19.71141765598658,19.729926029372482,19.748434402758384,19.76694277614429,19.785451149530193,19.803959522916095,19.822467896301998,19.8409762696879,19.859484643073802,19.87799301645971,19.89650138984561,19.91500976323151,19.933518136617415,19.952026510003318,19.97053488338922,19.989043256775123,20.00755163016103,20.026060003546927,20.044568376932833,20.063076750318736,20.081585123704638],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021],"y":[0,0.023565472996230206,0.04713094599246041,0.07069641898869061,0.09426189198492083,0.11782736498115103,0.14139283797738122,0.16495831097361144,0.18852378396984165,0.21208925696607187,0.23565472996230205,0.2592202029585322,0.28278567595476245,0.30635114895099264,0.3299166219472229,0.35348209494345306,0.3770475679396833,0.4006130409359135,0.42417851393214373,0.4477439869283739,0.4713094599246041,0.49487493292083423,0.5184404059170644,0.5420058789132947,0.5655713519095249,0.5891368249057551,0.6127022979019853,0.6362677708982155,0.6598332438944458,0.683398716890676,0.7069641898869061,0.7305296628831364,0.7540951358793666,0.7776606088755967,0.801226081871827,0.8247915548680571,0.8483570278642875,0.8719225008605176,0.8954879738567478,0.919053446852978,0.9426189198492082,0.9661843928454383,0.9897498658416685,1.0133153388378988,1.0368808118341288,1.0604462848303593,1.0840117578265893,1.1075772308228196,1.1311427038190498,1.15470817681528,1.1782736498115103,1.2018391228077405,1.2254045958039705,1.248970068800201,1.272535541796431,1.2961010147926613,1.3196664877888915,1.3432319607851215,1.366797433781352,1.390362906777582,1.4139283797738122,1.4374938527700425,1.4610593257662727,1.4846247987625028,1.5081902717587332,1.5317557447549632,1.5553212177511935,1.578886690747424,1.602452163743654,1.6260176367398842,1.6495831097361142,1.6731485827323445,1.696714055728575,1.720279528724805,1.7438450017210352,1.7674104747172652,1.7909759477134957,1.814541420709726,1.838106893705956,1.861672366702186,1.8852378396984164,1.9088033126946466,1.9323687856908767,1.955934258687107,1.979499731683337,2.0030652046795674,2.0266306776757976,2.050196150672028,2.0737616236682577,2.0973270966644884,2.1208925696607186,2.1444580426569484,2.1680235156531786,2.1915889886494093,2.215154461645639,2.2387199346418694,2.2622854076380996,2.28585088063433,2.30941635363056,2.3329818266267903,2.3565472996230206,2.3801127726192504,2.403678245615481,2.4272437186117113,2.450809191607941,2.4743746646041713,2.497940137600402,2.521505610596632,2.545071083592862,2.5686365565890923,2.5922020295853225,2.6157675025815528,2.639332975577783,2.6628984485740133,2.686463921570243,2.7100293945664737,2.733594867562704,2.7571603405589338,2.780725813555164,2.8042912865513943,2.8278567595476245,2.8514222325438547,2.874987705540085,2.8985531785363152,2.9221186515325455,2.9456841245287757,2.9692495975250055,2.9928150705212357,3.0163805435174664,3.0399460165136962,3.0635114895099265,3.087076962506157,3.110642435502387,3.134207908498617,3.157773381494848,3.1813388544910777,3.204904327487308,3.2284698004835377,3.2520352734797684,3.2756007464759986,3.2991662194722284,3.322731692468459,3.346297165464689,3.369862638460919,3.39342811145715,3.4169935844533796,3.44055905744961,3.4641245304458406,3.4876900034420704,3.5112554764383006,3.5348209494345304,3.558386422430761,3.5819518954269913,3.605517368423221,3.629082841419452,3.6526483144156816,3.676213787411912,3.6997792604081425,3.723344733404372,3.7469102064006026,3.770475679396833,3.7940411523930626,3.8176066253892933,3.841172098385523,3.8647375713817533,3.888303044377984,3.911868517374214,3.935433990370444,3.958999463366674,3.9825649363629045,4.006130409359135,4.029695882355365,4.053261355351595,4.0768268283478255,4.100392301344056,4.123957774340286,4.147523247336515,4.1710887203327465,4.194654193328977,4.218219666325206,4.241785139321437,4.265350612317667,4.288916085313897,4.312481558310128,4.336047031306357,4.3596125043025875,4.383177977298819,4.406743450295048,4.430308923291278,4.4538743962875085,4.477439869283739,4.501005342279969,4.524570815276199,4.548136288272429,4.57170176126866,4.59526723426489,4.61883270726112,4.64239818025735,4.665963653253581,4.689529126249811,4.713094599246041,4.736660072242271,4.760225545238501,4.783791018234732,4.807356491230962,4.8309219642271914,4.854487437223423,4.878052910219653,4.901618383215882,4.925183856212113,4.948749329208343,4.972314802204573,4.995880275200804,5.019445748197033,5.043011221193264,5.066576694189494,5.090142167185724,5.113707640181954,5.137273113178185,5.160838586174415,5.184404059170645,5.207969532166875,5.2315350051631055,5.255100478159336,5.278665951155566,5.302231424151796,5.3257968971480265,5.349362370144257,5.372927843140486,5.396493316136717,5.4200587891329475,5.443624262129177,5.467189735125408,5.490755208121638,5.5143206811178676,5.537886154114099,5.561451627110328,5.585017100106558,5.6085825731027885,5.632148046099019,5.655713519095249,5.679278992091479,5.7028444650877095,5.72640993808394,5.74997541108017,5.7735408840764,5.7971063570726304,5.820671830068861,5.844237303065091,5.86780277606132,5.891368249057551,5.914933722053782,5.938499195050011,5.962064668046242,5.9856301410424715,6.009195614038702,6.032761087034933,6.056326560031163,6.0798920330273925,6.103457506023623,6.127022979019853,6.150588452016083,6.174153925012314,6.1977193980085445,6.221284871004774,6.244850344001004,6.268415816997234,6.291981289993465,6.315546762989696,6.339112235985924,6.362677708982155,6.386243181978386,6.409808654974616,6.433374127970846,6.456939600967075,6.480505073963306,6.504070546959537,6.527636019955767,6.551201492951997,6.574766965948227,6.598332438944457,6.621897911940687,6.645463384936918,6.6690288579331485,6.692594330929378,6.716159803925608,6.739725276921838,6.763290749918069,6.7868562229143,6.81042169591053,6.833987168906759,6.8575526419029895,6.88111811489922,6.90468358789545,6.928249060891681,6.95181453388791,6.975380006884141,6.998945479880371,7.022510952876601,7.0460764258728315,7.069641898869061,7.093207371865291,7.116772844861522,7.140338317857752,7.163903790853983,7.187469263850212,7.211034736846442,7.2346002098426725,7.258165682838904,7.281731155835134,7.305296628831363,7.3288621018275935,7.352427574823824,7.375993047820054,7.399558520816285,7.423123993812515,7.446689466808744,7.470254939804975,7.493820412801205,7.517385885797435,7.540951358793666,7.564516831789895,7.588082304786125,7.611647777782356,7.635213250778587,7.658778723774817,7.682344196771046,7.705909669767276,7.729475142763507,7.753040615759738,7.776606088755968,7.800171561752197,7.823737034748428,7.847302507744658,7.870867980740888,7.894433453737119,7.917998926733348,7.941564399729579,7.965129872725809,7.988695345722039,8.01226081871827,8.0358262917145,8.05939176471073,8.08295723770696,8.10652271070319,8.13008818369942,8.153653656695651,8.177219129691881,8.200784602688111,8.224350075684342,8.247915548680572,8.271481021676802,8.29504649467303,8.318611967669263,8.342177440665493,8.365742913661723,8.389308386657953,8.412873859654182,8.436439332650412,8.460004805646644,8.483570278642874,8.507135751639105,8.530701224635335,8.554266697631563,8.577832170627794,8.601397643624026,8.624963116620256,8.648528589616486,8.672094062612715,8.695659535608945,8.719225008605175,8.742790481601407,8.766355954597637,8.789921427593866,8.813486900590096,8.837052373586326,8.860617846582556,8.884183319578788,8.907748792575017,8.931314265571247,8.954879738567477,8.978445211563708,9.002010684559938,9.025576157556168,9.049141630552398,9.072707103548629,9.096272576544859,9.11983804954109,9.14340352253732,9.16696899553355,9.19053446852978,9.21409994152601,9.23766541452224,9.26123088751847,9.2847963605147,9.308361833510931,9.331927306507161,9.355492779503392,9.379058252499622,9.40262372549585,9.426189198492082,9.449754671488312,9.473320144484543,9.496885617480773,9.520451090477001,9.544016563473232,9.567582036469464,9.591147509465694,9.614712982461924,9.638278455458153,9.661843928454383,9.685409401450613,9.708974874446845,9.732540347443075,9.756105820439306,9.779671293435534,9.803236766431764,9.826802239427995,9.850367712424227,9.873933185420457,9.897498658416685,9.921064131412916,9.944629604409146,9.968195077405376,9.991760550401608,10.015326023397837,10.038891496394067,10.062456969390297,10.086022442386527,10.109587915382757,10.133153388378988,10.156718861375218,10.180284334371448,10.203849807367678,10.227415280363909,10.250980753360139,10.27454622635637,10.2981116993526,10.32167717234883,10.34524264534506,10.36880811834129,10.39237359133752,10.41593906433375,10.43950453732998,10.463070010326211,10.486635483322441,10.510200956318672,10.533766429314902,10.557331902311132,10.580897375307362,10.604462848303593,10.628028321299821,10.651593794296053,10.675159267292283,10.698724740288514,10.722290213284744,10.745855686280972,10.769421159277202,10.792986632273434,10.816552105269665,10.840117578265895,10.863683051262125,10.887248524258354,10.910813997254584,10.934379470250816,10.957944943247046,10.981510416243276,11.005075889239505,11.028641362235735,11.052206835231965,11.075772308228197,11.099337781224428,11.122903254220656,11.146468727216886,11.170034200213117,11.193599673209347,11.217165146205577,11.240730619201807,11.264296092198038,11.287861565194268,11.311427038190498,11.334992511186728,11.358557984182958,11.382123457179189,11.405688930175419,11.42925440317165,11.45281987616788,11.47638534916411,11.49995082216034,11.52351629515657,11.5470817681528,11.57064724114903,11.594212714145261,11.617778187141491,11.641343660137721,11.664909133133952,11.688474606130182,11.712040079126412,11.73560555212264,11.759171025118873,11.782736498115103,11.806301971111333,11.829867444107563,11.853432917103792,11.876998390100022,11.900563863096254,11.924129336092484,11.947694809088715,11.971260282084943,11.994825755081173,12.018391228077403,12.041956701073635,12.065522174069866,12.089087647066096,12.112653120062326,12.136218593058556,12.159784066054785,12.183349539051015,12.206915012047245,12.230480485043476,12.254045958039706,12.277611431035936,12.301176904032166,12.324742377028398,12.348307850024629,12.371873323020859,12.395438796017089,12.419004269013318,12.442569742009548,12.466135215005778,12.489700688002008,12.513266160998239,12.536831633994469,12.560397106990699,12.58396257998693,12.607528052983161,12.631093525979392,12.654658998975618,12.678224471971848,12.70178994496808,12.72535541796431,12.748920890960541,12.772486363956771,12.796051836953001,12.819617309949232,12.843182782945462,12.866748255941692,12.89031372893792,12.91387920193415,12.937444674930381,12.961010147926611,12.984575620922842,13.008141093919074,13.031706566915304,13.055272039911534,13.078837512907764,13.102402985903995,13.125968458900225,13.149533931896453,13.173099404892683,13.196664877888914,13.220230350885144,13.243795823881374,13.267361296877604,13.290926769873836,13.314492242870067,13.338057715866297,13.361623188862527,13.385188661858756,13.408754134854986,13.432319607851216,13.455885080847446,13.479450553843677,13.503016026839907,13.526581499836137,13.550146972832367,13.5737124458286,13.59727791882483,13.62084339182106,13.644408864817287,13.667974337813519,13.691539810809749,13.715105283805979,13.73867075680221,13.76223622979844,13.78580170279467,13.8093671757909,13.83293264878713,13.856498121783362,13.880063594779589,13.90362906777582,13.92719454077205,13.950760013768281,13.974325486764512,13.997890959760742,14.021456432756972,14.045021905753202,14.068587378749433,14.092152851745663,14.115718324741893,14.139283797738122,14.162849270734352,14.186414743730582,14.209980216726812,14.233545689723044,14.257111162719275,14.280676635715505,14.304242108711735,14.327807581707965,14.351373054704196,14.374938527700424,14.398504000696654,14.422069473692885,14.445634946689115,14.469200419685345,14.492765892681575,14.516331365677807,14.539896838674037,14.563462311670268,14.587027784666498,14.610593257662726,14.634158730658957,14.657724203655187,14.681289676651417,14.704855149647647,14.728420622643878,14.751986095640108,14.775551568636338,14.79911704163257,14.8226825146288,14.84624798762503,14.869813460621257,14.893378933617488,14.91694440661372,14.94050987960995,14.96407535260618,14.98764082560241,15.01120629859864,15.03477177159487,15.058337244591101,15.081902717587331,15.10546819058356,15.12903366357979,15.15259913657602,15.17616460957225,15.199730082568482,15.223295555564713,15.246861028560943,15.270426501557173,15.293991974553403,15.317557447549634,15.341122920545864,15.364688393542092,15.388253866538323,15.411819339534553,15.435384812530783,15.458950285527013,15.482515758523245,15.506081231519476,15.529646704515706,15.553212177511936,15.576777650508166,15.600343123504395,15.623908596500625,15.647474069496855,15.671039542493086,15.694605015489316,15.718170488485546,15.741735961481776,15.765301434478008,15.788866907474238,15.812432380470469,15.835997853466695,15.859563326462927,15.883128799459158,15.906694272455388,15.930259745451618,15.953825218447848,15.977390691444079,16.00095616444031,16.02452163743654,16.04808711043277,16.071652583429,16.095218056425228,16.11878352942146,16.14234900241769,16.16591447541392,16.18947994841015,16.21304542140638,16.236610894402613,16.26017636739884,16.283741840395074,16.307307313391302,16.33087278638753,16.354438259383762,16.37800373237999,16.401569205376223,16.42513467837245,16.448700151368683,16.472265624364912,16.495831097361144,16.519396570357376,16.542962043353604,16.566527516349836,16.59009298934606,16.613658462342293,16.637223935338525,16.660789408334754,16.684354881330986,16.707920354327214,16.731485827323446,16.755051300319675,16.778616773315907,16.802182246312135,16.825747719308364,16.849313192304596,16.872878665300824,16.896444138297056,16.92000961129329,16.943575084289517,16.96714055728575,16.990706030281977,17.01427150327821,17.037836976274438,17.06140244927067,17.084967922266898,17.108533395263127,17.13209886825936,17.155664341255587,17.17922981425182,17.20279528724805,17.22636076024428,17.24992623324051,17.27349170623674,17.297057179232972,17.3206226522292,17.34418812522543,17.36775359822166,17.39131907121789,17.41488454421412,17.43845001721035,17.462015490206582,17.485580963202814,17.509146436199043,17.532711909195275,17.5562773821915,17.57984285518773,17.603408328183964,17.626973801180192,17.650539274176424,17.674104747172652,17.697670220168884,17.721235693165113,17.744801166161345,17.768366639157577,17.791932112153805,17.815497585150034,17.839063058146262,17.862628531142494,17.886194004138726,17.909759477134955,17.933324950131187,17.956890423127415,17.980455896123647,18.004021369119876,18.027586842116108,18.051152315112336,18.074717788108565,18.098283261104797,18.121848734101025,18.145414207097257,18.16897968009349,18.192545153089718,18.21611062608595,18.23967609908218,18.26324157207841,18.28680704507464,18.310372518070867,18.3339379910671,18.357503464063328,18.38106893705956,18.404634410055788,18.42819988305202,18.451765356048252,18.47533082904448,18.498896302040713,18.52246177503694,18.54602724803317,18.5695927210294,18.59315819402563,18.616723667021862,18.64028914001809,18.663854613014323,18.68742008601055,18.710985559006783,18.734551032003015,18.758116504999244,18.781681977995476,18.8052474509917,18.828812923987932,18.852378396984165,18.875943869980393,18.899509342976625,18.923074815972853,18.946640288969085,18.970205761965314,18.993771234961546,19.017336707957778,19.040902180954003,19.064467653950235,19.088033126946463,19.111598599942695,19.135164072938927,19.158729545935156,19.182295018931388,19.205860491927616,19.22942596492385,19.252991437920077,19.276556910916305,19.300122383912537,19.323687856908766,19.347253329904998,19.370818802901226,19.39438427589746,19.41794974889369,19.44151522188992,19.46508069488615,19.48864616788238,19.51221164087861,19.53577711387484,19.559342586871068,19.5829080598673,19.60647353286353,19.63003900585976,19.65360447885599,19.67716995185222,19.700735424848453,19.72430089784468,19.747866370840914,19.771431843837142,19.79499731683337,19.818562789829603,19.84212826282583,19.865693735822063,19.88925920881829,19.912824681814524,19.936390154810752,19.959955627806984,19.983521100803216,20.007086573799445,20.030652046795673,20.054217519791905,20.077782992788133,20.101348465784366,20.124913938780594,20.148479411776826,20.172044884773054,20.195610357769286,20.219175830765515,20.242741303761747,20.266306776757975,20.289872249754204,20.313437722750436,20.337003195746668,20.360568668742896,20.38413414173913,20.407699614735357,20.43126508773159,20.454830560727817,20.47839603372405,20.501961506720278,20.525526979716506,20.54909245271274,20.572657925708967,20.5962233987052,20.619788871701427,20.64335434469766,20.66691981769389,20.69048529069012,20.714050763686352,20.73761623668258,20.76118170967881,20.78474718267504,20.80831265567127,20.8318781286675,20.85544360166373,20.87900907465996,20.90257454765619,20.926140020652422,20.949705493648654,20.973270966644883,20.99683643964111,21.020401912637343,21.04396738563357,21.067532858629804,21.091098331626032,21.114663804622264,21.138229277618493,21.161794750614725,21.185360223610953,21.208925696607185,21.232491169603417,21.256056642599642,21.279622115595874,21.303187588592106,21.326753061588335,21.350318534584567,21.373884007580795,21.397449480577027,21.421014953573255,21.444580426569487,21.468145899565716,21.491711372561944,21.515276845558176,21.538842318554405,21.562407791550637,21.58597326454687,21.609538737543097,21.63310421053933,21.656669683535558,21.68023515653179,21.70380062952802,21.72736610252425,21.75093157552048,21.774497048516707,21.79806252151294,21.821627994509168,21.8451934675054,21.868758940501632,21.89232441349786,21.915889886494092,21.93945535949032,21.963020832486553,21.98658630548278,22.01015177847901,22.03371725147524,22.05728272447147,22.080848197467702,22.10441367046393,22.127979143460163,22.151544616456395,22.175110089452623,22.198675562448855,22.22224103544508,22.245806508441312,22.269371981437544,22.292937454433773,22.316502927430005,22.340068400426233,22.363633873422465,22.387199346418694,22.410764819414926,22.434330292411154,22.457895765407386,22.481461238403615,22.505026711399843,22.528592184396075,22.552157657392307,22.575723130388536,22.599288603384768,22.622854076380996,22.646419549377228,22.669985022373456,22.69355049536969,22.717115968365917,22.740681441362145,22.764246914358377,22.787812387354606,22.811377860350838,22.83494333334707,22.8585088063433,22.88207427933953,22.90563975233576,22.92920522533199,22.95277069832822,22.976336171324448,22.99990164432068,23.02346711731691,23.04703259031314,23.07059806330937,23.0941635363056,23.117729009301833,23.14129448229806,23.164859955294293,23.188425428290522,23.21199090128675,23.235556374282982,23.25912184727921,23.282687320275443,23.30625279327167,23.329818266267903,23.35338373926413,23.376949212260364,23.400514685256596,23.424080158252824,23.447645631249053,23.47121110424528,23.494776577241513,23.518342050237745,23.541907523233974,23.565472996230206,23.589038469226434,23.612603942222666,23.636169415218895,23.659734888215127,23.68330036121136,23.706865834207584,23.730431307203816,23.753996780200044,23.777562253196276,23.801127726192508,23.824693199188737,23.84825867218497,23.871824145181197,23.89538961817743,23.918955091173657,23.942520564169886,23.966086037166118,23.989651510162346,24.01321698315858,24.036782456154807,24.06034792915104],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Ext Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052],"y":[0,0.023947384926622156,0.04789476985324431,0.07184215477986647,0.09578953970648862,0.11973692463311077,0.14368430955973294,0.16763169448635507,0.19157907941297725,0.2155264643395994,0.23947384926622153,0.2634212341928437,0.2873686191194659,0.311316004046088,0.33526338897271013,0.3592107738993323,0.3831581588259545,0.4071055437525766,0.4310529286791988,0.4550003136058209,0.47894769853244307,0.5028950834590653,0.5268424683856874,0.5507898533123096,0.5747372382389317,0.5986846231655538,0.622632008092176,0.6465793930187982,0.6705267779454203,0.6944741628720424,0.7184215477986646,0.7423689327252868,0.766316317651909,0.7902637025785311,0.8142110875051533,0.8381584724317754,0.8621058573583976,0.8860532422850198,0.9100006272116418,0.933948012138264,0.9578953970648861,0.9818427819915083,1.0057901669181306,1.0297375518447527,1.0536849367713748,1.0776323216979968,1.1015797066246191,1.1255270915512412,1.1494744764778635,1.1734218614044856,1.1973692463311076,1.22131663125773,1.245264016184352,1.2692114011109743,1.2931587860375964,1.3171061709642187,1.3410535558908405,1.3650009408174628,1.3889483257440849,1.4128957106707072,1.4368430955973293,1.4607904805239516,1.4847378654505736,1.5086852503771957,1.532632635303818,1.55658002023044,1.5805274051570621,1.6044747900836844,1.6284221750103065,1.6523695599369288,1.6763169448635509,1.700264329790173,1.7242117147167952,1.7481590996434173,1.7721064845700396,1.7960538694966617,1.8200012544232835,1.843948639349906,1.867896024276528,1.8918434092031504,1.9157907941297723,1.9397381790563948,1.9636855639830166,1.9876329489096387,2.0115803338362612,2.035527718762883,2.0594751036895054,2.083422488616127,2.1073698735427495,2.131317258469372,2.1552646433959937,2.1792120283226164,2.2031594132492383,2.2271067981758605,2.2510541831024824,2.2750015680291047,2.298948952955727,2.322896337882349,2.346843722808971,2.3707911077355934,2.3947384926622153,2.4186858775888376,2.44263326251546,2.466580647442082,2.490528032368704,2.5144754172953263,2.5384228022219486,2.5623701871485705,2.5863175720751927,2.6102649570018146,2.6342123419284373,2.658159726855059,2.682107111781681,2.7060544967083033,2.7300018816349256,2.753949266561548,2.7778966514881698,2.801844036414792,2.8257914213414144,2.849738806268036,2.8736861911946585,2.897633576121281,2.921580961047903,2.945528345974525,2.9694757309011472,2.9934231158277695,3.0173705007543914,3.0413178856810137,3.065265270607636,3.089212655534258,3.11316004046088,3.137107425387502,3.1610548103141243,3.1850021952407466,3.208949580167369,3.2328969650939907,3.256844350020613,3.2807917349472353,3.3047391198738576,3.3286865048004795,3.3526338897271017,3.376581274653724,3.400528659580346,3.424476044506968,3.4484234294335905,3.4723708143602128,3.4963181992868346,3.520265584213457,3.544212969140079,3.5681603540667006,3.5921077389933234,3.6160551239199457,3.640002508846567,3.6639498937731894,3.687897278699812,3.7118446636264344,3.735792048553056,3.759739433479678,3.783686818406301,3.8076342033329222,3.8315815882595445,3.855528973186167,3.8794763581127896,3.903423743039411,3.9273711279660333,3.9513185128926556,3.9752658978192774,3.9992132827458997,4.0231606676725225,4.047108052599144,4.071055437525766,4.095002822452389,4.118950207379011,4.142897592305633,4.166844977232254,4.190792362158877,4.214739747085499,4.238687132012121,4.262634516938744,4.286581901865366,4.310529286791987,4.33447667171861,4.358424056645233,4.382371441571854,4.4063188264984765,4.430266211425098,4.454213596351721,4.478160981278343,4.502108366204965,4.5260557511315875,4.550003136058209,4.573950520984831,4.597897905911454,4.621845290838076,4.645792675764698,4.66974006069132,4.693687445617942,4.717634830544564,4.741582215471187,4.765529600397809,4.789476985324431,4.813424370251053,4.837371755177675,4.861319140104298,4.88526652503092,4.909213909957542,4.933161294884164,4.957108679810786,4.981056064737408,5.005003449664031,5.028950834590653,5.0528982195172745,5.076845604443897,5.100792989370519,5.124740374297141,5.148687759223764,5.1726351441503855,5.196582529077007,5.220529914003629,5.244477298930252,5.268424683856875,5.292372068783496,5.316319453710118,5.340266838636741,5.364214223563362,5.388161608489985,5.412108993416607,5.436056378343229,5.460003763269851,5.483951148196473,5.507898533123096,5.531845918049718,5.5557933029763396,5.579740687902962,5.603688072829584,5.627635457756206,5.651582842682829,5.675530227609451,5.699477612536072,5.723424997462695,5.747372382389317,5.771319767315939,5.795267152242562,5.8192145371691835,5.843161922095806,5.867109307022428,5.89105669194905,5.915004076875673,5.9389514618022945,5.962898846728916,5.986846231655539,6.010793616582161,6.034741001508783,6.0586883864354055,6.082635771362027,6.106583156288649,6.130530541215272,6.154477926141894,6.178425311068516,6.2023726959951375,6.22632008092176,6.250267465848383,6.274214850775004,6.298162235701627,6.3221096206282485,6.346057005554871,6.370004390481493,6.393951775408115,6.417899160334738,6.44184654526136,6.465793930187981,6.489741315114604,6.513688700041226,6.537636084967849,6.561583469894471,6.5855308548210925,6.609478239747715,6.633425624674337,6.657373009600959,6.681320394527582,6.7052677794542035,6.729215164380825,6.753162549307448,6.77710993423407,6.801057319160692,6.8250047040873145,6.848952089013936,6.872899473940557,6.896846858867181,6.920794243793803,6.9447416287204256,6.968689013647047,6.992636398573669,7.016583783500292,7.040531168426914,7.064478553353535,7.088425938280158,7.11237332320678,7.136320708133401,7.160268093060025,7.184215477986647,7.208162862913268,7.232110247839891,7.256057632766512,7.280005017693134,7.303952402619758,7.327899787546379,7.351847172473002,7.375794557399624,7.399741942326245,7.423689327252869,7.44763671217949,7.471584097106112,7.495531482032735,7.519478866959356,7.543426251885978,7.567373636812602,7.591321021739223,7.6152684066658445,7.639215791592467,7.663163176519089,7.687110561445713,7.711057946372334,7.7350053312989555,7.758952716225579,7.7829001011522,7.806847486078822,7.830794871005445,7.8547422559320665,7.878689640858688,7.902637025785311,7.926584410711933,7.950531795638555,7.974479180565178,7.998426565491799,8.022373950418421,8.046321335345045,8.070268720271667,8.094216105198289,8.11816349012491,8.142110875051532,8.166058259978156,8.190005644904778,8.213953029831398,8.237900414758021,8.261847799684643,8.285795184611265,8.309742569537889,8.333689954464509,8.35763733939113,8.381584724317754,8.405532109244376,8.429479494170998,8.453426879097622,8.477374264024242,8.501321648950865,8.525269033877487,8.549216418804109,8.573163803730733,8.597111188657353,8.621058573583975,8.645005958510598,8.66895334343722,8.692900728363842,8.716848113290466,8.740795498217086,8.764742883143708,8.788690268070331,8.812637652996953,8.836585037923575,8.860532422850197,8.884479807776819,8.908427192703442,8.932374577630064,8.956321962556686,8.980269347483308,9.00421673240993,9.028164117336551,9.052111502263175,9.076058887189797,9.100006272116419,9.12395365704304,9.147901041969662,9.171848426896284,9.195795811822908,9.21974319674953,9.243690581676152,9.267637966602774,9.291585351529395,9.315532736456019,9.33948012138264,9.363427506309263,9.387374891235885,9.411322276162506,9.435269661089128,9.459217046015752,9.483164430942374,9.507111815868996,9.531059200795617,9.55500658572224,9.578953970648861,9.602901355575485,9.626848740502107,9.650796125428728,9.67474351035535,9.698690895281972,9.722638280208596,9.746585665135218,9.77053305006184,9.794480434988461,9.818427819915083,9.842375204841705,9.866322589768329,9.89026997469495,9.914217359621572,9.938164744548194,9.962112129474816,9.986059514401438,10.010006899328062,10.033954284254683,10.057901669181305,10.081849054107927,10.105796439034549,10.129743823961173,10.153691208887794,10.177638593814414,10.201585978741038,10.22553336366766,10.249480748594282,10.273428133520905,10.297375518447527,10.321322903374147,10.345270288300771,10.369217673227393,10.393165058154015,10.417112443080638,10.441059828007258,10.465007212933882,10.488954597860504,10.512901982787126,10.53684936771375,10.560796752640371,10.584744137566991,10.608691522493615,10.632638907420237,10.656586292346859,10.680533677273482,10.704481062200102,10.728428447126724,10.752375832053348,10.77632321697997,10.800270601906591,10.824217986833213,10.848165371759835,10.872112756686459,10.89606014161308,10.920007526539703,10.943954911466326,10.967902296392946,10.991849681319568,11.015797066246192,11.039744451172814,11.063691836099435,11.087639221026057,11.111586605952679,11.135533990879301,11.159481375805925,11.183428760732546,11.207376145659168,11.23132353058579,11.255270915512412,11.279218300439036,11.303165685365657,11.32711307029228,11.351060455218901,11.375007840145523,11.398955225072145,11.422902609998768,11.44684999492539,11.470797379852012,11.494744764778634,11.518692149705256,11.542639534631878,11.566586919558501,11.590534304485123,11.614481689411745,11.638429074338367,11.662376459264989,11.686323844191612,11.710271229118234,11.734218614044856,11.758165998971478,11.7821133838981,11.806060768824722,11.830008153751345,11.853955538677967,11.877902923604589,11.90185030853121,11.925797693457833,11.949745078384455,11.973692463311078,11.9976398482377,12.021587233164322,12.045534618090944,12.069482003017566,12.09342938794419,12.117376772870811,12.141324157797433,12.165271542724055,12.189218927650677,12.213166312577298,12.237113697503922,12.261061082430544,12.285008467357164,12.308955852283788,12.33290323721041,12.356850622137031,12.380798007063655,12.404745391990275,12.428692776916899,12.45264016184352,12.476587546770142,12.500534931696766,12.524482316623388,12.548429701550008,12.572377086476632,12.596324471403253,12.620271856329877,12.644219241256497,12.668166626183119,12.692114011109743,12.716061396036363,12.740008780962986,12.76395616588961,12.78790355081623,12.811850935742852,12.835798320669475,12.859745705596096,12.88369309052272,12.907640475449343,12.931587860375963,12.955535245302585,12.979482630229208,13.003430015155832,13.027377400082452,13.051324785009074,13.075272169935698,13.099219554862318,13.123166939788941,13.147114324715565,13.171061709642185,13.195009094568807,13.21895647949543,13.24290386442205,13.266851249348674,13.290798634275298,13.314746019201918,13.33869340412854,13.362640789055163,13.386588173981783,13.410535558908407,13.434482943835029,13.45843032876165,13.482377713688273,13.506325098614896,13.530272483541516,13.55421986846814,13.578167253394762,13.602114638321384,13.626062023248005,13.650009408174629,13.67395679310125,13.697904178027873,13.721851562954495,13.745798947881115,13.769746332807738,13.793693717734362,13.817641102660984,13.841588487587606,13.865535872514227,13.889483257440851,13.913430642367471,13.937378027294095,13.961325412220717,13.985272797147339,14.00922018207396,14.033167567000584,14.057114951927204,14.081062336853828,14.10500972178045,14.12895710670707,14.152904491633693,14.176851876560317,14.200799261486937,14.22474664641356,14.248694031340182,14.272641416266802,14.296588801193426,14.32053618612005,14.34448357104667,14.368430955973293,14.392378340899915,14.416325725826535,14.440273110753159,14.464220495679783,14.488167880606403,14.512115265533025,14.536062650459648,14.560010035386268,14.583957420312892,14.607904805239516,14.631852190166137,14.655799575092757,14.679746960019381,14.703694344946005,14.727641729872625,14.751589114799248,14.77553649972587,14.79948388465249,14.823431269579114,14.847378654505738,14.871326039432358,14.89527342435898,14.919220809285603,14.943168194212223,14.967115579138847,14.99106296406547,15.01501034899209,15.038957733918712,15.062905118845336,15.086852503771956,15.11079988869858,15.134747273625203,15.158694658551823,15.182642043478445,15.206589428405069,15.230536813331689,15.254484198258313,15.278431583184934,15.302378968111556,15.326326353038178,15.350273737964802,15.374221122891425,15.398168507818045,15.422115892744667,15.446063277671291,15.470010662597911,15.493958047524535,15.517905432451158,15.541852817377778,15.5658002023044,15.589747587231024,15.613694972157644,15.637642357084268,15.66158974201089,15.685537126937511,15.709484511864133,15.733431896790757,15.757379281717377,15.781326666644,15.805274051570622,15.829221436497244,15.853168821423866,15.87711620635049,15.90106359127711,15.925010976203733,15.948958361130355,15.972905746056975,15.996853130983599,16.02080051591022,16.044747900836843,16.068695285763464,16.09264267069009,16.116590055616708,16.140537440543334,16.164484825469955,16.188432210396577,16.2123795953232,16.23632698024982,16.260274365176443,16.284221750103065,16.308169135029686,16.332116519956312,16.35606390488293,16.380011289809556,16.403958674736177,16.427906059662796,16.45185344458942,16.475800829516043,16.499748214442665,16.523695599369287,16.54764298429591,16.57159036922253,16.595537754149152,16.619485139075778,16.643432524002396,16.667379908929018,16.691327293855643,16.71527467878226,16.739222063708887,16.76316944863551,16.78711683356213,16.811064218488752,16.835011603415374,16.858958988341996,16.882906373268618,16.906853758195243,16.930801143121865,16.954748528048484,16.97869591297511,17.00264329790173,17.026590682828353,17.050538067754974,17.074485452681596,17.098432837608218,17.12238022253484,17.146327607461465,17.170274992388084,17.194222377314706,17.21816976224133,17.24211714716795,17.266064532094575,17.290011917021197,17.31395930194782,17.33790668687444,17.361854071801062,17.385801456727684,17.409748841654306,17.43369622658093,17.45764361150755,17.48159099643417,17.505538381360797,17.529485766287415,17.55343315121404,17.577380536140662,17.601327921067284,17.625275305993906,17.649222690920528,17.67317007584715,17.69711746077377,17.721064845700393,17.74501223062702,17.768959615553637,17.792907000480263,17.816854385406884,17.840801770333506,17.864749155260128,17.88869654018675,17.912643925113372,17.936591310039994,17.960538694966615,17.984486079893237,18.00843346481986,18.032380849746485,18.056328234673103,18.08027561959973,18.10422300452635,18.12817038945297,18.152117774379594,18.176065159306216,18.200012544232838,18.22395992915946,18.24790731408608,18.271854699012703,18.295802083939325,18.31974946886595,18.34369685379257,18.367644238719194,18.391591623645816,18.415539008572434,18.43948639349906,18.46343377842568,18.487381163352303,18.511328548278925,18.535275933205547,18.559223318132172,18.58317070305879,18.607118087985416,18.631065472912038,18.655012857838656,18.67896024276528,18.702907627691904,18.726855012618525,18.750802397545147,18.77474978247177,18.79869716739839,18.822644552325013,18.846591937251638,18.870539322178256,18.894486707104882,18.918434092031504,18.942381476958122,18.966328861884747,18.99027624681137,19.01422363173799,19.038171016664613,19.062118401591235,19.086065786517857,19.11001317144448,19.133960556371104,19.157907941297722,19.181855326224344,19.20580271115097,19.229750096077588,19.253697481004213,19.277644865930835,19.301592250857457,19.32553963578408,19.3494870207107,19.373434405637326,19.397381790563944,19.421329175490566,19.44527656041719,19.46922394534381,19.493171330270435,19.517118715197057,19.54106610012368,19.5650134850503,19.588960869976923,19.612908254903545,19.636855639830166,19.66080302475679,19.68475040968341,19.708697794610032,19.732645179536657,19.756592564463276,19.7805399493899,19.804487334316523,19.828434719243145,19.852382104169767,19.87632948909639,19.90027687402301,19.924224258949632,19.948171643876254,19.972119028802876,19.996066413729498,20.020013798656123,20.04396118358274,20.067908568509367,20.09185595343599,20.11580333836261,20.139750723289232,20.163698108215854,20.187645493142476,20.211592878069098,20.23554026299572,20.259487647922345,20.283435032848963,20.30738241777559,20.33132980270221,20.35527718762883,20.379224572555454,20.403171957482076,20.427119342408698,20.45106672733532,20.475014112261942,20.498961497188564,20.522908882115185,20.54685626704181,20.57080365196843,20.594751036895055,20.618698421821676,20.642645806748295,20.66659319167492,20.690540576601542,20.714487961528164,20.738435346454786,20.762382731381408,20.78633011630803,20.81027750123465,20.834224886161277,20.858172271087895,20.882119656014517,20.906067040941142,20.930014425867764,20.953961810794386,20.977909195721008,21.00185658064763,21.02580396557425,21.049751350500873,21.0736987354275,21.097646120354117,21.121593505280742,21.145540890207364,21.169488275133983,21.193435660060608,21.21738304498723,21.24133042991385,21.265277814840474,21.289225199767095,21.313172584693717,21.33711996962034,21.361067354546964,21.385014739473583,21.408962124400205,21.43290950932683,21.45685689425345,21.480804279180074,21.504751664106696,21.528699049033317,21.55264643395994,21.57659381888656,21.600541203813183,21.624488588739805,21.648435973666427,21.672383358593052,21.69633074351967,21.720278128446296,21.744225513372918,21.76817289829954,21.79212028322616,21.816067668152783,21.840015053079405,21.863962438006027,21.887909822932652,21.91185720785927,21.935804592785892,21.959751977712518,21.983699362639136,22.00764674756576,22.031594132492383,22.055541517419005,22.079488902345627,22.10343628727225,22.12738367219887,22.151331057125493,22.175278442052115,22.199225826978736,22.223173211905358,22.247120596831984,22.271067981758602,22.295015366685227,22.31896275161185,22.342910136538467,22.366857521465093,22.390804906391715,22.414752291318337,22.43869967624496,22.46264706117158,22.486594446098206,22.510541831024824,22.53448921595145,22.55843660087807,22.58238398580469,22.606331370731315,22.630278755657937,22.65422614058456,22.67817352551118,22.702120910437802,22.726068295364424,22.750015680291046,22.77396306521767,22.79791045014429,22.821857835070915,22.845805219997537,22.869752604924155,22.89369998985078,22.917647374777403,22.941594759704024,22.965542144630646,22.989489529557268,23.01343691448389,23.037384299410512,23.061331684337137,23.085279069263756,23.109226454190377,23.133173839117003,23.15712122404362,23.181068608970246,23.20501599389687,23.22896337882349,23.252910763750112,23.276858148676734,23.30080553360336,23.324752918529978,23.348700303456603,23.372647688383225,23.396595073309843,23.42054245823647,23.44448984316309,23.468437228089712,23.492384613016334,23.516331997942956,23.540279382869578,23.5642267677962,23.588174152722825,23.612121537649443,23.636068922576065,23.66001630750269,23.68396369242931,23.707911077355934,23.731858462282556,23.755805847209178,23.7797532321358,23.80370061706242,23.827648001989044,23.851595386915665,23.875542771842287,23.89949015676891,23.92343754169553,23.947384926622156,23.971332311548775,23.9952796964754,24.019227081402022,24.043174466328644,24.067121851255266,24.091069236181887,24.115016621108513,24.13896400603513,24.162911390961753,24.18685877588838,24.210806160814997,24.234753545741622,24.258700930668244,24.282648315594866,24.306595700521488,24.33054308544811,24.35449047037473,24.378437855301353,24.402385240227975,24.426332625154597,24.45028001008122,24.474227395007844,24.498174779934462,24.522122164861088,24.54606954978771,24.570016934714328,24.593964319640953,24.617911704567575,24.641859089494197,24.66580647442082,24.68975385934744,24.713701244274063,24.737648629200685,24.76159601412731,24.78554339905393,24.80949078398055,24.833438168907175,24.857385553833797,24.88133293876042,24.90528032368704,24.929227708613663,24.953175093540285,24.977122478466907,25.001069863393532,25.02501724832015,25.048964633246776,25.072912018173398,25.096859403100016,25.12080678802664,25.144754172953263,25.168701557879885,25.192648942806507],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780],"y":[0,0.02049782277303734,0.04099564554607468,0.061493468319112024,0.08199129109214937,0.1024891138651867,0.12298693663822405,0.14348475941126138,0.16398258218429873,0.18448040495733609,0.2049782277303734,0.22547605050341074,0.2459738732764481,0.26647169604948545,0.28696951882252275,0.3074673415955601,0.32796516436859746,0.34846298714163476,0.36896080991467217,0.38945863268770947,0.4099564554607468,0.4304542782337842,0.4509521010068215,0.47144992377985884,0.4919477465528962,0.5124455693259334,0.5329433920989709,0.5534412148720083,0.5739390376450455,0.5944368604180829,0.6149346831911202,0.6354325059641576,0.6559303287371949,0.6764281515102323,0.6969259742832695,0.717423797056307,0.7379216198293443,0.7584194426023816,0.7789172653754189,0.7994150881484563,0.8199129109214937,0.840410733694531,0.8609085564675684,0.8814063792406056,0.901904202013643,0.9224020247866804,0.9428998475597177,0.963397670332755,0.9838954931057924,1.0043933158788296,1.0248911386518669,1.0453889614249043,1.0658867841979418,1.086384606970979,1.1068824297440165,1.1273802525170538,1.147878075290091,1.1683758980631285,1.1888737208361657,1.209371543609203,1.2298693663822404,1.250367189155278,1.2708650119283151,1.2913628347013526,1.3118606574743898,1.332358480247427,1.3528563030204646,1.3733541257935018,1.393851948566539,1.4143497713395765,1.434847594112614,1.4553454168856512,1.4758432396586887,1.496341062431726,1.5168388852047632,1.5373367079778006,1.5578345307508379,1.5783323535238751,1.5988301762969126,1.61932799906995,1.6398258218429873,1.6603236446160248,1.680821467389062,1.7013192901620993,1.7218171129351367,1.742314935708174,1.7628127584812112,1.7833105812542487,1.803808404027286,1.8243062268003234,1.8448040495733609,1.865301872346398,1.8857996951194353,1.9062975178924728,1.92679534066551,1.9472931634385473,1.9677909862115848,1.988288808984622,2.0087866317576593,2.0292844545306967,2.0497822773037337,2.0702801000767717,2.0907779228498087,2.111275745622846,2.1317735683958836,2.1522713911689206,2.172769213941958,2.1932670367149956,2.213764859488033,2.23426268226107,2.2547605050341075,2.2752583278071445,2.295756150580182,2.31625397335322,2.336751796126257,2.3572496188992944,2.3777474416723314,2.398245264445369,2.418743087218406,2.439240909991444,2.459738732764481,2.4802365555375183,2.500734378310556,2.521232201083593,2.5417300238566303,2.5622278466296677,2.582725669402705,2.6032234921757422,2.6237213149487797,2.6442191377218167,2.664716960494854,2.6852147832678916,2.705712606040929,2.7262104288139666,2.7467082515870036,2.767206074360041,2.787703897133078,2.8082017199061156,2.828699542679153,2.8491973654521905,2.869695188225228,2.890193010998265,2.9106908337713024,2.9311886565443395,2.9516864793173774,2.9721843020904144,2.992682124863452,3.013179947636489,3.0336777704095264,3.054175593182564,3.0746734159556013,3.0951712387286388,3.1156690615016758,3.1361668842747132,3.1566647070477503,3.1771625298207877,3.197660352593825,3.2181581753668627,3.2386559981399,3.259153820912937,3.2796516436859746,3.3001494664590116,3.3206472892320495,3.3411451120050866,3.361642934778124,3.382140757551161,3.4026385803241985,3.4231364030972355,3.4436342258702735,3.464132048643311,3.484629871416348,3.5051276941893854,3.5256255169624224,3.54612333973546,3.5666211625084974,3.587118985281535,3.607616808054572,3.6281146308276093,3.648612453600647,3.669110276373684,3.6896080991467217,3.7101059219197587,3.730603744692796,3.7511015674658332,3.7715993902388707,3.7920972130119077,3.8125950357849456,3.833092858557983,3.85359068133102,3.8740885041040576,3.8945863268770946,3.915084149650132,3.9355819724231695,3.956079795196207,3.976577617969244,3.9970754407422815,4.0175732635153185,4.038071086288356,4.0585689090613934,4.0790667318344305,4.0995645546074675,4.120062377380505,4.140560200153543,4.16105802292658,4.181555845699617,4.202053668472655,4.222551491245692,4.243049314018729,4.263547136791767,4.284044959564804,4.304542782337841,4.325040605110878,4.345538427883916,4.366036250656953,4.386534073429991,4.407031896203029,4.427529718976066,4.448027541749103,4.46852536452214,4.489023187295178,4.509521010068215,4.530018832841252,4.550516655614289,4.571014478387327,4.591512301160364,4.612010123933401,4.63250794670644,4.653005769479477,4.673503592252514,4.694001415025551,4.714499237798589,4.734997060571626,4.755494883344663,4.775992706117701,4.796490528890738,4.816988351663775,4.837486174436812,4.85798399720985,4.878481819982888,4.898979642755925,4.919477465528962,4.939975288302,4.960473111075037,4.980970933848074,5.001468756621112,5.021966579394149,5.042464402167186,5.062962224940223,5.0834600477132605,5.103957870486298,5.1244556932593355,5.144953516032373,5.16545133880541,5.185949161578447,5.2064469843514845,5.226944807124522,5.247442629897559,5.267940452670596,5.288438275443633,5.308936098216671,5.329433920989708,5.349931743762745,5.370429566535783,5.390927389308821,5.411425212081858,5.431923034854895,5.452420857627933,5.47291868040097,5.493416503174007,5.513914325947044,5.534412148720082,5.554909971493119,5.575407794266156,5.595905617039194,5.616403439812231,5.636901262585269,5.657399085358306,5.677896908131344,5.698394730904381,5.718892553677418,5.739390376450456,5.759888199223493,5.78038602199653,5.800883844769567,5.821381667542605,5.841879490315642,5.862377313088679,5.882875135861717,5.903372958634755,5.923870781407792,5.944368604180829,5.964866426953867,5.985364249726904,6.005862072499941,6.026359895272978,6.046857718046016,6.067355540819053,6.08785336359209,6.108351186365128,6.1288490091381655,6.149346831911203,6.16984465468424,6.1903424774572775,6.2108403002303145,6.2313381230033515,6.251835945776389,6.2723337685494265,6.2928315913224635,6.3133294140955005,6.333827236868538,6.3543250596415755,6.374822882414613,6.39532070518765,6.415818527960688,6.436316350733725,6.456814173506762,6.4773119962798,6.497809819052837,6.518307641825874,6.538805464598911,6.559303287371949,6.579801110144986,6.600298932918023,6.620796755691061,6.641294578464099,6.661792401237136,6.682290224010173,6.702788046783211,6.723285869556248,6.743783692329285,6.764281515102322,6.78477933787536,6.805277160648397,6.825774983421434,6.846272806194471,6.866770628967509,6.887268451740547,6.907766274513584,6.928264097286622,6.948761920059659,6.969259742832696,6.989757565605733,7.010255388378771,7.030753211151808,7.051251033924845,7.071748856697883,7.09224667947092,7.112744502243957,7.133242325016995,7.153740147790033,7.17423797056307,7.194735793336107,7.215233616109144,7.235731438882182,7.256229261655219,7.276727084428256,7.297224907201294,7.317722729974331,7.338220552747368,7.358718375520405,7.379216198293443,7.3997140210664805,7.4202118438395175,7.440709666612555,7.461207489385592,7.481705312158629,7.5022031349316665,7.522700957704704,7.543198780477741,7.563696603250778,7.584194426023815,7.604692248796853,7.625190071569891,7.645687894342928,7.666185717115966,7.686683539889003,7.70718136266204,7.727679185435077,7.748177008208115,7.768674830981152,7.789172653754189,7.809670476527227,7.830168299300264,7.850666122073301,7.871163944846339,7.891661767619377,7.912159590392414,7.932657413165451,7.953155235938488,7.973653058711526,7.994150881484563,8.014648704257601,8.035146527030637,8.055644349803675,8.076142172576713,8.096639995349749,8.117137818122787,8.137635640895823,8.158133463668861,8.178631286441899,8.199129109214935,8.219626931987973,8.24012475476101,8.260622577534049,8.281120400307087,8.301618223080125,8.32211604585316,8.342613868626199,8.363111691399235,8.383609514172273,8.40410733694531,8.424605159718347,8.445102982491385,8.465600805264422,8.486098628037459,8.506596450810497,8.527094273583534,8.54759209635657,8.568089919129608,8.588587741902646,8.609085564675683,8.62958338744872,8.650081210221757,8.670579032994794,8.691076855767832,8.711574678540869,8.732072501313906,8.752570324086946,8.773068146859982,8.79356596963302,8.814063792406058,8.834561615179094,8.855059437952132,8.875557260725168,8.896055083498206,8.916552906271244,8.93705072904428,8.957548551817318,8.978046374590356,8.998544197363392,9.01904202013643,9.039539842909468,9.060037665682504,9.080535488455542,9.101033311228578,9.121531134001616,9.142028956774654,9.16252677954769,9.183024602320728,9.203522425093766,9.224020247866802,9.244518070639842,9.26501589341288,9.285513716185916,9.306011538958954,9.32650936173199,9.347007184505028,9.367505007278066,9.388002830051102,9.40850065282414,9.428998475597178,9.449496298370214,9.469994121143252,9.49049194391629,9.510989766689326,9.531487589462364,9.551985412235402,9.572483235008438,9.592981057781476,9.613478880554512,9.63397670332755,9.654474526100588,9.674972348873624,9.695470171646662,9.7159679944197,9.736465817192736,9.756963639965775,9.777461462738813,9.79795928551185,9.818457108284887,9.838954931057923,9.859452753830961,9.879950576604,9.900448399377035,9.920946222150073,9.941444044923111,9.961941867696147,9.982439690469185,10.002937513242223,10.02343533601526,10.043933158788297,10.064430981561333,10.084928804334371,10.10542662710741,10.125924449880445,10.146422272653483,10.166920095426521,10.187417918199557,10.207915740972595,10.228413563745633,10.248911386518671,10.269409209291709,10.289907032064747,10.310404854837783,10.33090267761082,10.351400500383857,10.371898323156895,10.392396145929933,10.412893968702969,10.433391791476007,10.453889614249045,10.47438743702208,10.494885259795119,10.515383082568157,10.535880905341193,10.55637872811423,10.576876550887267,10.597374373660305,10.617872196433343,10.638370019206379,10.658867841979417,10.679365664752455,10.69986348752549,10.720361310298529,10.740859133071567,10.761356955844604,10.781854778617642,10.802352601390679,10.822850424163716,10.843348246936754,10.86384606970979,10.884343892482828,10.904841715255866,10.925339538028902,10.94583736080194,10.966335183574978,10.986833006348014,11.007330829121052,11.027828651894088,11.048326474667126,11.068824297440164,11.0893221202132,11.109819942986238,11.130317765759276,11.150815588532312,11.17131341130535,11.191811234078388,11.212309056851424,11.232806879624462,11.253304702397502,11.273802525170538,11.294300347943576,11.314798170716612,11.33529599348965,11.355793816262688,11.376291639035724,11.396789461808762,11.4172872845818,11.437785107354836,11.458282930127874,11.478780752900912,11.499278575673948,11.519776398446986,11.540274221220022,11.56077204399306,11.581269866766098,11.601767689539134,11.622265512312172,11.64276333508521,11.663261157858246,11.683758980631284,11.704256803404322,11.724754626177358,11.745252448950398,11.765750271723434,11.786248094496472,11.80674591726951,11.827243740042546,11.847741562815584,11.868239385588621,11.888737208361658,11.909235031134696,11.929732853907733,11.95023067668077,11.970728499453807,11.991226322226844,12.011724144999882,12.03222196777292,12.052719790545956,12.073217613318993,12.093715436092031,12.114213258865068,12.134711081638105,12.155208904411143,12.17570672718418,12.196204549957217,12.216702372730255,12.237200195503291,12.257698018276331,12.278195841049367,12.298693663822405,12.319191486595443,12.33968930936848,12.360187132141517,12.380684954914555,12.401182777687591,12.421680600460629,12.442178423233667,12.462676246006703,12.483174068779741,12.503671891552777,12.524169714325815,12.544667537098853,12.565165359871889,12.585663182644927,12.606161005417965,12.626658828191001,12.647156650964039,12.667654473737077,12.688152296510113,12.708650119283151,12.729147942056187,12.749645764829227,12.770143587602265,12.7906414103753,12.811139233148339,12.831637055921377,12.852134878694413,12.87263270146745,12.893130524240489,12.913628347013525,12.934126169786563,12.9546239925596,12.975121815332637,12.995619638105675,13.01611746087871,13.036615283651749,13.057113106424787,13.077610929197823,13.09810875197086,13.118606574743898,13.139104397516935,13.159602220289973,13.18010004306301,13.200597865836047,13.221095688609084,13.241593511382122,13.26209133415516,13.282589156928198,13.303086979701234,13.323584802474272,13.34408262524731,13.364580448020346,13.385078270793384,13.405576093566422,13.426073916339458,13.446571739112496,13.467069561885532,13.48756738465857,13.508065207431608,13.528563030204644,13.549060852977682,13.56955867575072,13.590056498523756,13.610554321296794,13.631052144069832,13.651549966842868,13.672047789615906,13.692545612388942,13.71304343516198,13.733541257935018,13.754039080708056,13.774536903481094,13.795034726254132,13.815532549027168,13.836030371800206,13.856528194573244,13.87702601734628,13.897523840119318,13.918021662892356,13.938519485665392,13.95901730843843,13.979515131211466,14.000012953984504,14.020510776757542,14.041008599530578,14.061506422303616,14.082004245076654,14.10250206784969,14.122999890622728,14.143497713395766,14.163995536168802,14.18449335894184,14.204991181714876,14.225489004487914,14.245986827260953,14.26648465003399,14.286982472807027,14.307480295580065,14.327978118353101,14.34847594112614,14.368973763899177,14.389471586672213,14.409969409445251,14.430467232218287,14.450965054991325,14.471462877764363,14.4919607005374,14.512458523310437,14.532956346083475,14.553454168856511,14.57395199162955,14.594449814402587,14.614947637175623,14.635445459948661,14.655943282721699,14.676441105494735,14.696938928267773,14.71743675104081,14.737934573813847,14.758432396586887,14.778930219359923,14.799428042132961,14.819925864905999,14.840423687679035,14.860921510452073,14.88141933322511,14.901917155998147,14.922414978771185,14.942912801544221,14.963410624317259,14.983908447090297,15.004406269863333,15.02490409263637,15.045401915409409,15.065899738182445,15.086397560955483,15.10689538372852,15.127393206501557,15.147891029274595,15.16838885204763,15.188886674820669,15.209384497593707,15.229882320366743,15.250380143139783,15.27087796591282,15.291375788685857,15.311873611458894,15.332371434231932,15.352869257004969,15.373367079778006,15.393864902551043,15.41436272532408,15.434860548097118,15.455358370870155,15.475856193643192,15.49635401641623,15.516851839189266,15.537349661962304,15.557847484735342,15.578345307508378,15.598843130281416,15.619340953054454,15.63983877582749,15.660336598600528,15.680834421373564,15.701332244146602,15.72183006691964,15.742327889692678,15.762825712465716,15.783323535238754,15.80382135801179,15.824319180784828,15.844817003557866,15.865314826330902,15.88581264910394,15.906310471876976,15.926808294650014,15.947306117423052,15.967803940196088,15.988301762969126],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Rule 28","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483],"y":[0,0.014226319342154008,0.028452638684308015,0.04267895802646202,0.05690527736861603,0.07113159671077003,0.08535791605292405,0.09958423539507805,0.11381055473723206,0.12803687407938608,0.14226319342154006,0.15648951276369408,0.1707158321058481,0.1849421514480021,0.1991684707901561,0.2133947901323101,0.22762110947446412,0.24184742881661814,0.25607374815877215,0.27030006750092617,0.2845263868430801,0.29875270618523414,0.31297902552738815,0.32720534486954217,0.3414316642116962,0.3556579835538502,0.3698843028960042,0.38411062223815823,0.3983369415803122,0.4125632609224662,0.4267895802646202,0.4410158996067743,0.45524221894892825,0.46946853829108226,0.4836948576332363,0.4979211769753903,0.5121474963175443,0.5263738156596983,0.5406001350018523,0.5548264543440062,0.5690527736861603,0.5832790930283143,0.5975054123704683,0.6117317317126223,0.6259580510547763,0.6401843703969303,0.6544106897390843,0.6686370090812384,0.6828633284233924,0.6970896477655464,0.7113159671077004,0.7255422864498544,0.7397686057920084,0.7539949251341624,0.7682212444763165,0.7824475638184705,0.7966738831606244,0.8109002025027784,0.8251265218449324,0.8393528411870864,0.8535791605292404,0.8678054798713944,0.8820317992135486,0.8962581185557025,0.9104844378978565,0.9247107572400105,0.9389370765821645,0.9531633959243184,0.9673897152664725,0.9816160346086265,0.9958423539507806,1.0100686732929345,1.0242949926350886,1.0385213119772425,1.0527476313193966,1.0669739506615505,1.0812002700037047,1.0954265893458586,1.1096529086880125,1.1238792280301666,1.1381055473723205,1.1523318667144746,1.1665581860566285,1.1807845053987827,1.1950108247409366,1.2092371440830907,1.2234634634252446,1.2376897827673987,1.2519161021095526,1.2661424214517067,1.2803687407938606,1.2945950601360148,1.3088213794781687,1.3230476988203228,1.3372740181624767,1.3515003375046306,1.3657266568467847,1.3799529761889386,1.3941792955310928,1.4084056148732467,1.4226319342154008,1.4368582535575547,1.4510845728997088,1.4653108922418627,1.4795372115840169,1.4937635309261708,1.5079898502683249,1.5222161696104788,1.536442488952633,1.5506688082947868,1.564895127636941,1.5791214469790948,1.5933477663212487,1.6075740856634029,1.6218004050055568,1.6360267243477107,1.6502530436898648,1.664479363032019,1.6787056823741728,1.692932001716327,1.7071583210584809,1.721384640400635,1.735610959742789,1.7498372790849428,1.7640635984270971,1.778289917769251,1.792516237111405,1.8067425564535589,1.820968875795713,1.8351951951378669,1.849421514480021,1.863647833822175,1.877874153164329,1.892100472506483,1.9063267918486368,1.9205531111907908,1.934779430532945,1.949005749875099,1.963232069217253,1.9774583885594073,1.9916847079015612,2.005911027243715,2.020137346585869,2.0343636659280233,2.048589985270177,2.062816304612331,2.077042623954485,2.0912689432966394,2.1054952626387933,2.119721581980947,2.133947901323101,2.1481742206652554,2.1624005400074093,2.1766268593495632,2.190853178691717,2.205079498033871,2.219305817376025,2.233532136718179,2.247758456060333,2.261984775402487,2.276211094744641,2.290437414086795,2.3046637334289493,2.318890052771103,2.333116372113257,2.347342691455411,2.3615690107975653,2.375795330139719,2.390021649481873,2.404247968824027,2.4184742881661814,2.4327006075083353,2.446926926850489,2.461153246192643,2.4753795655347974,2.4896058848769513,2.5038322042191052,2.5180585235612596,2.5322848429034135,2.5465111622455674,2.5607374815877213,2.5749638009298756,2.5891901202720295,2.6034164396141835,2.6176427589563374,2.6318690782984917,2.6460953976406456,2.6603217169827995,2.6745480363249534,2.6887743556671073,2.7030006750092612,2.717226994351415,2.7314533136935695,2.7456796330357234,2.7599059523778773,2.774132271720031,2.7883585910621855,2.8025849104043394,2.8168112297464933,2.8310375490886472,2.8452638684308016,2.8594901877729555,2.8737165071151094,2.8879428264572633,2.9021691457994176,2.9163954651415716,2.9306217844837255,2.9448481038258794,2.9590744231680337,2.9733007425101876,2.9875270618523415,3.0017533811944954,3.0159797005366498,3.0302060198788037,3.0444323392209576,3.058658658563112,3.072884977905266,3.0871112972474197,3.1013376165895736,3.115563935931728,3.129790255273882,3.144016574616036,3.1582428939581897,3.1724692133003436,3.1866955326424975,3.200921851984652,3.2151481713268057,3.2293744906689597,3.2436008100111136,3.2578271293532675,3.2720534486954214,3.2862797680375757,3.3005060873797296,3.3147324067218835,3.328958726064038,3.3431850454061918,3.3574113647483457,3.3716376840904996,3.385864003432654,3.400090322774808,3.4143166421169617,3.4285429614591156,3.44276928080127,3.4569956001434234,3.471221919485578,3.485448238827732,3.4996745581698856,3.51390087751204,3.5281271968541943,3.5423535161963478,3.556579835538502,3.5708061548806556,3.58503247422281,3.5992587935649643,3.6134851129071177,3.627711432249272,3.641937751591426,3.65616407093358,3.6703903902757338,3.684616709617888,3.698843028960042,3.713069348302196,3.72729566764435,3.7415219869865037,3.755748306328658,3.7699746256708115,3.784200945012966,3.7984272643551202,3.8126535836972737,3.826879903039428,3.8411062223815815,3.855332541723736,3.86955886106589,3.8837851804080437,3.898011499750198,3.9122378190923524,3.926464138434506,3.94069045777666,3.9549167771188145,3.969143096460968,3.9833694158031223,3.9975957351452758,4.01182205448743,4.0260483738295845,4.040274693171738,4.054501012513892,4.068727331856047,4.0829536511982,4.097179970540354,4.111406289882508,4.125632609224662,4.139858928566817,4.15408524790897,4.168311567251124,4.182537886593279,4.196764205935432,4.2109905252775865,4.225216844619741,4.239443163961894,4.253669483304049,4.267895802646202,4.2821221219883565,4.296348441330511,4.310574760672664,4.324801080014819,4.339027399356973,4.3532537186991265,4.367480038041281,4.381706357383434,4.395932676725589,4.410158996067742,4.424385315409896,4.43861163475205,4.452837954094204,4.467064273436358,4.481290592778512,4.495516912120666,4.50974323146282,4.523969550804974,4.538195870147128,4.552422189489282,4.566648508831436,4.58087482817359,4.595101147515744,4.6093274668578985,4.623553786200052,4.637780105542206,4.65200642488436,4.666232744226514,4.6804590635686685,4.694685382910822,4.708911702252976,4.723138021595131,4.737364340937284,4.751590660279438,4.765816979621593,4.780043298963746,4.794269618305901,4.808495937648054,4.822722256990208,4.836948576332363,4.851174895674516,4.8654012150166706,4.879627534358825,4.893853853700978,4.908080173043133,4.922306492385286,4.9365328117274405,4.950759131069595,4.964985450411748,4.979211769753903,4.993438089096057,5.0076644084382105,5.021890727780365,5.036117047122519,5.050343366464673,5.064569685806827,5.07879600514898,5.093022324491135,5.107248643833289,5.121474963175443,5.135701282517597,5.149927601859751,5.164153921201905,5.178380240544059,5.192606559886213,5.206832879228367,5.221059198570521,5.235285517912675,5.249511837254829,5.263738156596983,5.277964475939137,5.292190795281291,5.306417114623446,5.320643433965599,5.334869753307753,5.349096072649907,5.363322391992061,5.377548711334215,5.391775030676369,5.4060013500185224,5.420227669360677,5.43445398870283,5.448680308044985,5.462906627387139,5.477132946729292,5.491359266071447,5.5055855854136,5.519811904755755,5.534038224097909,5.548264543440062,5.562490862782217,5.576717182124371,5.5909435014665245,5.605169820808679,5.619396140150832,5.633622459492987,5.647848778835141,5.6620750981772945,5.676301417519449,5.690527736861603,5.704754056203757,5.718980375545911,5.7332066948880644,5.747433014230219,5.761659333572373,5.775885652914527,5.790111972256681,5.804338291598835,5.818564610940989,5.832790930283143,5.8470172496252975,5.861243568967451,5.875469888309605,5.889696207651759,5.903922526993913,5.918148846336067,5.932375165678221,5.946601485020375,5.96082780436253,5.975054123704683,5.989280443046837,6.003506762388991,6.017733081731145,6.0319594010732995,6.046185720415453,6.060412039757607,6.074638359099762,6.088864678441915,6.1030909977840695,6.117317317126224,6.131543636468377,6.145769955810532,6.159996275152685,6.1742225944948395,6.188448913836994,6.202675233179147,6.216901552521302,6.231127871863456,6.245354191205609,6.259580510547764,6.273806829889917,6.288033149232072,6.302259468574226,6.316485787916379,6.330712107258534,6.344938426600687,6.3591647459428415,6.373391065284995,6.387617384627149,6.401843703969304,6.416070023311457,6.4302963426536115,6.444522661995765,6.458748981337919,6.472975300680073,6.487201620022227,6.5014279393643815,6.515654258706535,6.529880578048689,6.544106897390843,6.558333216732997,6.572559536075151,6.586785855417305,6.601012174759459,6.615238494101614,6.629464813443767,6.643691132785921,6.657917452128076,6.672143771470229,6.6863700908123835,6.700596410154537,6.714822729496691,6.729049048838846,6.743275368180999,6.7575016875231535,6.771728006865308,6.785954326207461,6.800180645549616,6.814406964891769,6.8286332842339235,6.842859603576078,6.857085922918231,6.871312242260386],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">20.08%</td>
                <td>64.2%</td>
                <td>1085</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>31.0</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">24.06%</td>
                <td>64.5%</td>
                <td>1021</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>32.3</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Ext Rule 6: Fibonacci Support Confluence</td>
                <td>UNKNOWN</td>
                <td class="positive">25.19%</td>
                <td>64.8%</td>
                <td>1052</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>32.7</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">15.99%</td>
                <td>64.5%</td>
                <td>780</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>29.9</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Rule 28: Volume Breakout</td>
                <td>ORIGINAL</td>
                <td class="positive">6.87%</td>
                <td>63.6%</td>
                <td>483</td>
                <td>1.06</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>26.9</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">8.24%</td>
                <td>63.8%</td>
                <td>738</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>27.3</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">26.50%</td>
                <td>65.6%</td>
                <td>784</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>33.3</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">12.05%</td>
                <td>63.8%</td>
                <td>851</td>
                <td>1.06</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>28.5</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Volatility Rule 2: ATR Expansion Signal</td>
                <td>UNKNOWN</td>
                <td class="positive">15.19%</td>
                <td>65.3%</td>
                <td>421</td>
                <td>1.14</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>29.9</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Volume Rule 4: Volume Breakout Confirmation</td>
                <td>UNKNOWN</td>
                <td class="positive">8.15%</td>
                <td>64.0%</td>
                <td>503</td>
                <td>1.06</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>27.4</td>
            </tr>
            
            <tr>
                <td>11</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">9.86%</td>
                <td>63.8%</td>
                <td>985</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>27.8</td>
            </tr>
            
            <tr>
                <td>12</td>
                <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                <td>UNKNOWN</td>
                <td class="positive">18.75%</td>
                <td>65.3%</td>
                <td>608</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>30.9</td>
            </tr>
            
            <tr>
                <td>13</td>
                <td>Rule 27: Structure Break Up</td>
                <td>ORIGINAL</td>
                <td class="positive">10.03%</td>
                <td>63.5%</td>
                <td>853</td>
                <td>1.05</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>27.8</td>
            </tr>
            
            <tr>
                <td>14</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">16.70%</td>
                <td>64.4%</td>
                <td>855</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>30.1</td>
            </tr>
            
            <tr>
                <td>15</td>
                <td>Volume Rule 3: Dark Pool Activity</td>
                <td>UNKNOWN</td>
                <td class="positive">11.02%</td>
                <td>64.7%</td>
                <td>411</td>
                <td>1.11</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>28.5</td>
            </tr>
            
            <tr>
                <td>16</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">8.67%</td>
                <td>63.9%</td>
                <td>590</td>
                <td>1.06</td>
                <td>0.00</td>
                <td>0.00%</td>
                <td>27.5</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 198,420<br>
            • Backtest Range: 300 to 198,720<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
