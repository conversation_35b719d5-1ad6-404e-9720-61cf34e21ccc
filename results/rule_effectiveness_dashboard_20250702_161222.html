
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 16:12:24 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">-841.92%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">96,340</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">61.3%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.07</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>53.61%</strong></td>
                    <td>63.7%</td>
                    <td>5286</td>
                    <td>1.03</td>
                    <td class="negative">40.51%</td>
                    <td class="positive"><strong>1.0041</strong></td>
                    <td class="negative">+0.86% / -1.45%</td>
                    <td>3h1m<br><small>(1.0m - 49h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>6.09%</strong></td>
                    <td>72.5%</td>
                    <td>51</td>
                    <td>1.59</td>
                    <td class="neutral">3.50%</td>
                    <td class="positive"><strong>0.5873</strong></td>
                    <td class="negative">+0.85% / -1.39%</td>
                    <td>3h21m<br><small>(4.0m - 18h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="positive"><strong>3.46%</strong></td>
                    <td>77.8%</td>
                    <td>9</td>
                    <td>3.42</td>
                    <td class="positive">0.74%</td>
                    <td class="positive"><strong>0.6271</strong></td>
                    <td class="negative">+1.38% / -1.39%</td>
                    <td>2.9m<br><small>(1.0m - 5.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>SMC Rule 2: Fair Value Gap Fill</td>
                    <td class="positive"><strong>0.46%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.91% / 0.00%</td>
                    <td>1h7m</td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>New Rule 4: Ultimate Oscillator Breakout</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 1: MA Alignment with RSI Oversold</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="negative"><strong>-3.21%</strong></td>
                    <td>53.8%</td>
                    <td>39</td>
                    <td>0.79</td>
                    <td class="negative">6.50%</td>
                    <td class="positive"><strong>0.3550</strong></td>
                    <td class="negative">+1.18% / -1.73%</td>
                    <td>5.9m<br><small>(1.0m - 22.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="negative"><strong>-3.32%</strong></td>
                    <td>62.1%</td>
                    <td>612</td>
                    <td>0.98</td>
                    <td class="negative">18.50%</td>
                    <td class="positive"><strong>0.6688</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>4h31m<br><small>(3.0m - 41h22m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-5.12%</strong></td>
                    <td>62.6%</td>
                    <td>7940</td>
                    <td>1.00</td>
                    <td class="negative">61.76%</td>
                    <td class="positive"><strong>0.8594</strong></td>
                    <td class="negative">+0.88% / -1.46%</td>
                    <td>2h41m</td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="negative"><strong>-7.54%</strong></td>
                    <td>57.9%</td>
                    <td>178</td>
                    <td>0.86</td>
                    <td class="negative">10.71%</td>
                    <td class="positive"><strong>0.5152</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>6h12m<br><small>(1.0m - 50h38m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="negative"><strong>-14.91%</strong></td>
                    <td>53.6%</td>
                    <td>209</td>
                    <td>0.79</td>
                    <td class="negative">17.39%</td>
                    <td class="positive"><strong>0.4820</strong></td>
                    <td class="negative">+1.05% / -1.53%</td>
                    <td>1h2m<br><small>(1.0m - 13h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-19.93%</strong></td>
                    <td>59.8%</td>
                    <td>987</td>
                    <td>0.92</td>
                    <td class="negative">31.91%</td>
                    <td class="positive"><strong>0.6420</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>4h34m<br><small>(1.0m - 49h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-28.99%</strong></td>
                    <td>62.1%</td>
                    <td>4285</td>
                    <td>0.97</td>
                    <td class="negative">40.47%</td>
                    <td class="positive"><strong>0.7637</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>3h28m<br><small>(1.0m - 48h38m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-30.65%</strong></td>
                    <td>62.2%</td>
                    <td>4630</td>
                    <td>0.97</td>
                    <td class="negative">49.02%</td>
                    <td class="positive"><strong>0.7547</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>3h11m<br><small>(1.0m - 53h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-32.71%</strong></td>
                    <td>61.9%</td>
                    <td>4822</td>
                    <td>0.97</td>
                    <td class="negative">43.34%</td>
                    <td class="positive"><strong>0.7619</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>3h21m</td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="negative"><strong>-35.72%</strong></td>
                    <td>62.3%</td>
                    <td>8713</td>
                    <td>0.98</td>
                    <td class="negative">64.09%</td>
                    <td class="positive"><strong>0.7849</strong></td>
                    <td class="negative">+0.88% / -1.46%</td>
                    <td>2h35m</td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="negative"><strong>-36.75%</strong></td>
                    <td>61.5%</td>
                    <td>3682</td>
                    <td>0.95</td>
                    <td class="negative">44.78%</td>
                    <td class="positive"><strong>0.7192</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>3h30m</td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="negative"><strong>-40.03%</strong></td>
                    <td>60.6%</td>
                    <td>2706</td>
                    <td>0.93</td>
                    <td class="negative">45.51%</td>
                    <td class="positive"><strong>0.6736</strong></td>
                    <td class="negative">+0.88% / -1.44%</td>
                    <td>4h0m<br><small>(1.0m - 49h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-41.52%</strong></td>
                    <td>60.8%</td>
                    <td>2171</td>
                    <td>0.91</td>
                    <td class="negative">47.61%</td>
                    <td class="positive"><strong>0.6371</strong></td>
                    <td class="negative">+0.87% / -1.46%</td>
                    <td>4h5m<br><small>(1.0m - 49h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-46.74%</strong></td>
                    <td>61.9%</td>
                    <td>8582</td>
                    <td>0.98</td>
                    <td class="negative">70.05%</td>
                    <td class="positive"><strong>0.7465</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>2h43m</td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="negative"><strong>-51.98%</strong></td>
                    <td>61.0%</td>
                    <td>4221</td>
                    <td>0.94</td>
                    <td class="negative">54.32%</td>
                    <td class="positive"><strong>0.6763</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>3h27m</td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="negative"><strong>-53.15%</strong></td>
                    <td>58.1%</td>
                    <td>1794</td>
                    <td>0.86</td>
                    <td class="negative">55.05%</td>
                    <td class="positive"><strong>0.5675</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>4h12m<br><small>(1.0m - 58h15m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-53.72%</strong></td>
                    <td>61.6%</td>
                    <td>5881</td>
                    <td>0.96</td>
                    <td class="negative">64.29%</td>
                    <td class="positive"><strong>0.6943</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>3h11m</td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="negative"><strong>-55.98%</strong></td>
                    <td>59.6%</td>
                    <td>3578</td>
                    <td>0.92</td>
                    <td class="negative">59.87%</td>
                    <td class="positive"><strong>0.6387</strong></td>
                    <td class="negative">+0.90% / -1.42%</td>
                    <td>3h5m<br><small>(1.0m - 59h56m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="negative"><strong>-56.98%</strong></td>
                    <td>61.2%</td>
                    <td>5791</td>
                    <td>0.95</td>
                    <td class="negative">62.61%</td>
                    <td class="positive"><strong>0.6863</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>3h14m</td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="negative"><strong>-57.00%</strong></td>
                    <td>58.9%</td>
                    <td>2537</td>
                    <td>0.89</td>
                    <td class="negative">60.32%</td>
                    <td class="positive"><strong>0.5925</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>3h30m<br><small>(1.0m - 51h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="negative"><strong>-71.00%</strong></td>
                    <td>60.1%</td>
                    <td>6900</td>
                    <td>0.93</td>
                    <td class="negative">74.20%</td>
                    <td class="positive"><strong>0.6475</strong></td>
                    <td class="negative">+0.90% / -1.43%</td>
                    <td>2h44m<br><small>(1.0m - 59h56m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#28</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="negative"><strong>-73.30%</strong></td>
                    <td>58.6%</td>
                    <td>3692</td>
                    <td>0.87</td>
                    <td class="negative">73.60%</td>
                    <td class="positive"><strong>0.5648</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>3h0m<br><small>(1.0m - 59h56m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#29</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-85.27%</strong></td>
                    <td>60.0%</td>
                    <td>7043</td>
                    <td>0.90</td>
                    <td class="negative">85.73%</td>
                    <td class="positive"><strong>0.5871</strong></td>
                    <td class="negative">+0.88% / -1.44%</td>
                    <td>2h57m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>SMC Rule 2: Fair Value Gap Fill</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">0.46%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.91% / 0.00%</td>
                    <td>1h7m</td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="positive"><strong>77.8%</strong></td>
                    <td class="positive">3.46%</td>
                    <td>9</td>
                    <td>3.42</td>
                    <td class="positive">0.74%</td>
                    <td class="positive"><strong>0.6271</strong></td>
                    <td class="negative">+1.38% / -1.39%</td>
                    <td>2.9m<br><small>(1.0m - 5.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>72.5%</strong></td>
                    <td class="positive">6.09%</td>
                    <td>51</td>
                    <td>1.59</td>
                    <td class="neutral">3.50%</td>
                    <td class="positive"><strong>0.5873</strong></td>
                    <td class="negative">+0.85% / -1.39%</td>
                    <td>3h21m<br><small>(4.0m - 18h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>63.7%</strong></td>
                    <td class="positive">53.61%</td>
                    <td>5286</td>
                    <td>1.03</td>
                    <td class="negative">40.51%</td>
                    <td class="positive"><strong>1.0041</strong></td>
                    <td class="negative">+0.86% / -1.45%</td>
                    <td>3h1m<br><small>(1.0m - 49h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>62.6%</strong></td>
                    <td class="negative">-5.12%</td>
                    <td>7940</td>
                    <td>1.00</td>
                    <td class="negative">61.76%</td>
                    <td class="positive"><strong>0.8594</strong></td>
                    <td class="negative">+0.88% / -1.46%</td>
                    <td>2h41m</td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>62.3%</strong></td>
                    <td class="negative">-35.72%</td>
                    <td>8713</td>
                    <td>0.98</td>
                    <td class="negative">64.09%</td>
                    <td class="positive"><strong>0.7849</strong></td>
                    <td class="negative">+0.88% / -1.46%</td>
                    <td>2h35m</td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>62.2%</strong></td>
                    <td class="negative">-30.65%</td>
                    <td>4630</td>
                    <td>0.97</td>
                    <td class="negative">49.02%</td>
                    <td class="positive"><strong>0.7547</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>3h11m<br><small>(1.0m - 53h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>62.1%</strong></td>
                    <td class="negative">-28.99%</td>
                    <td>4285</td>
                    <td>0.97</td>
                    <td class="negative">40.47%</td>
                    <td class="positive"><strong>0.7637</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>3h28m<br><small>(1.0m - 48h38m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>62.1%</strong></td>
                    <td class="negative">-3.32%</td>
                    <td>612</td>
                    <td>0.98</td>
                    <td class="negative">18.50%</td>
                    <td class="positive"><strong>0.6688</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>4h31m<br><small>(3.0m - 41h22m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>61.9%</strong></td>
                    <td class="negative">-32.71%</td>
                    <td>4822</td>
                    <td>0.97</td>
                    <td class="negative">43.34%</td>
                    <td class="positive"><strong>0.7619</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>3h21m</td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>61.9%</strong></td>
                    <td class="negative">-46.74%</td>
                    <td>8582</td>
                    <td>0.98</td>
                    <td class="negative">70.05%</td>
                    <td class="positive"><strong>0.7465</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>2h43m</td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>61.6%</strong></td>
                    <td class="negative">-53.72%</td>
                    <td>5881</td>
                    <td>0.96</td>
                    <td class="negative">64.29%</td>
                    <td class="positive"><strong>0.6943</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>3h11m</td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>61.5%</strong></td>
                    <td class="negative">-36.75%</td>
                    <td>3682</td>
                    <td>0.95</td>
                    <td class="negative">44.78%</td>
                    <td class="positive"><strong>0.7192</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>3h30m</td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>61.2%</strong></td>
                    <td class="negative">-56.98%</td>
                    <td>5791</td>
                    <td>0.95</td>
                    <td class="negative">62.61%</td>
                    <td class="positive"><strong>0.6863</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>3h14m</td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>61.0%</strong></td>
                    <td class="negative">-51.98%</td>
                    <td>4221</td>
                    <td>0.94</td>
                    <td class="negative">54.32%</td>
                    <td class="positive"><strong>0.6763</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>3h27m</td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>60.8%</strong></td>
                    <td class="negative">-41.52%</td>
                    <td>2171</td>
                    <td>0.91</td>
                    <td class="negative">47.61%</td>
                    <td class="positive"><strong>0.6371</strong></td>
                    <td class="negative">+0.87% / -1.46%</td>
                    <td>4h5m<br><small>(1.0m - 49h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>60.6%</strong></td>
                    <td class="negative">-40.03%</td>
                    <td>2706</td>
                    <td>0.93</td>
                    <td class="negative">45.51%</td>
                    <td class="positive"><strong>0.6736</strong></td>
                    <td class="negative">+0.88% / -1.44%</td>
                    <td>4h0m<br><small>(1.0m - 49h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>60.1%</strong></td>
                    <td class="negative">-71.00%</td>
                    <td>6900</td>
                    <td>0.93</td>
                    <td class="negative">74.20%</td>
                    <td class="positive"><strong>0.6475</strong></td>
                    <td class="negative">+0.90% / -1.43%</td>
                    <td>2h44m<br><small>(1.0m - 59h56m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="negative">-85.27%</td>
                    <td>7043</td>
                    <td>0.90</td>
                    <td class="negative">85.73%</td>
                    <td class="positive"><strong>0.5871</strong></td>
                    <td class="negative">+0.88% / -1.44%</td>
                    <td>2h57m</td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>59.8%</strong></td>
                    <td class="negative">-19.93%</td>
                    <td>987</td>
                    <td>0.92</td>
                    <td class="negative">31.91%</td>
                    <td class="positive"><strong>0.6420</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>4h34m<br><small>(1.0m - 49h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>59.6%</strong></td>
                    <td class="negative">-55.98%</td>
                    <td>3578</td>
                    <td>0.92</td>
                    <td class="negative">59.87%</td>
                    <td class="positive"><strong>0.6387</strong></td>
                    <td class="negative">+0.90% / -1.42%</td>
                    <td>3h5m<br><small>(1.0m - 59h56m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>58.9%</strong></td>
                    <td class="negative">-57.00%</td>
                    <td>2537</td>
                    <td>0.89</td>
                    <td class="negative">60.32%</td>
                    <td class="positive"><strong>0.5925</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>3h30m<br><small>(1.0m - 51h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>58.6%</strong></td>
                    <td class="negative">-73.30%</td>
                    <td>3692</td>
                    <td>0.87</td>
                    <td class="negative">73.60%</td>
                    <td class="positive"><strong>0.5648</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>3h0m<br><small>(1.0m - 59h56m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>58.1%</strong></td>
                    <td class="negative">-53.15%</td>
                    <td>1794</td>
                    <td>0.86</td>
                    <td class="negative">55.05%</td>
                    <td class="positive"><strong>0.5675</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>4h12m<br><small>(1.0m - 58h15m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>57.9%</strong></td>
                    <td class="negative">-7.54%</td>
                    <td>178</td>
                    <td>0.86</td>
                    <td class="negative">10.71%</td>
                    <td class="positive"><strong>0.5152</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>6h12m<br><small>(1.0m - 50h38m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>53.8%</strong></td>
                    <td class="negative">-3.21%</td>
                    <td>39</td>
                    <td>0.79</td>
                    <td class="negative">6.50%</td>
                    <td class="positive"><strong>0.3550</strong></td>
                    <td class="negative">+1.18% / -1.73%</td>
                    <td>5.9m<br><small>(1.0m - 22.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>53.6%</strong></td>
                    <td class="negative">-14.91%</td>
                    <td>209</td>
                    <td>0.79</td>
                    <td class="negative">17.39%</td>
                    <td class="positive"><strong>0.4820</strong></td>
                    <td class="negative">+1.05% / -1.53%</td>
                    <td>1h2m<br><small>(1.0m - 13h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#28</strong></td>
                    <td>New Rule 4: Ultimate Oscillator Breakout</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#29</strong></td>
                    <td>Rule 1: MA Alignment with RSI Oversold</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['New Rule 4: Ultimate Oscillato...', 'Rule 1: MA Alignment with RSI ...', 'SMC Rule 2: Fair Value Gap Fil...', 'Prof Rule 7: Mean Reversion Vo...', 'Momentum Rule 5: Momentum Brea...', 'Rule 21: Gap Up', 'Volume Rule 3: Dark Pool Activ...', 'Rule 7: Bollinger Band Bounce', 'Acad Rule 2: Mean Reversion Fa...', 'Acad Rule 3: Volatility Breako...', 'Rule 2: Golden Cross', 'SMC Rule 5: Institutional Cand...', 'Volume Rule 5: Smart Money Vol...', 'AI Rule 8: Momentum Divergence...', 'Professional Rule 10: CCI Reve...'],
                y: [0, 0, 100.0, 63.658721150208095, 77.77777777777779, 72.54901960784314, 62.091503267973856, 62.619647355163735, 53.84615384615385, 57.865168539325836, 59.77710233029382, 53.588516746411486, 62.147024504084015, 62.20302375809935, 61.8830360846122],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['New Rule 4: Ultimate Oscillato...', 'Rule 1: MA Alignment with RSI ...', 'SMC Rule 2: Fair Value Gap Fil...', 'Prof Rule 7: Mean Reversion Vo...', 'Momentum Rule 5: Momentum Brea...', 'Rule 21: Gap Up', 'Volume Rule 3: Dark Pool Activ...', 'Rule 7: Bollinger Band Bounce', 'Acad Rule 2: Mean Reversion Fa...', 'Acad Rule 3: Volatility Breako...', 'Rule 2: Golden Cross', 'SMC Rule 5: Institutional Cand...', 'Volume Rule 5: Smart Money Vol...', 'AI Rule 8: Momentum Divergence...', 'Professional Rule 10: CCI Reve...'],
                y: [0.0, 0.0, np.float64(0.4572606382978739), np.float64(53.60827998730011), np.float64(3.455214427516884), np.float64(6.089519520524389), np.float64(-3.3153968391796407), np.float64(-5.12186050492458), np.float64(-3.2084303326219366), np.float64(-7.544444924431415), np.float64(-19.932734649556483), np.float64(-14.914651377215925), np.float64(-28.988133646928794), np.float64(-30.65181126259995), np.float64(-32.70874781021339)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
