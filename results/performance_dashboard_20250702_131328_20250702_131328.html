
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 7 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 13:13:28</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">7</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">43.8%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">15.3%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">46.9%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">67.0%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">2,016</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["46.9%","33.8%","6.0%","7.0%","4.2%","5.2%","3.8%"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Momentum Rule 2: Momentum Divergence Recovery","Professional Rule 7: Chaikin Money Flow Reversal","Volume Rule 5: Smart Money Volume","Rule 10: Volume Spike","Rule 2: Golden Cross"],"y":[46.86544786881127,33.80790075845665,6.020606467576232,7.025232971012753,4.249405806962808,5.1506542935684125,3.8212936820889736],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Momentum Rule 2: Momentum Divergence Recovery","Professional Rule 7: Chaikin Money Flow Reversal","Volume Rule 5: Smart Money Volume","Rule 10: Volume Spike","Rule 2: Golden Cross"],"y":[199,951,56,30,27,11,12],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Momentum Rule 2: Momentum Divergence Recovery","Professional Rule 7: Chaikin Money Flow Reversal","Volume Rule 5: Smart Money Volume","Rule 10: Volume Spike","Rule 2: Golden Cross"],"y":[93,565,33,16,14,4,5],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[46.86544786881127,33.80790075845665,6.020606467576232,7.025232971012753,4.249405806962808,5.1506542935684125,3.8212936820889736],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Prof Rule 7","AI Rule 10","Momentum Rule 2","Professional Rule 7","Volume Rule 5","Rule 10","Rule 2"],"textposition":"top center","x":[11.757630926302285,36.617251827572325,11.363232434840832,5.361775158080069,11.399792930717336,3.844779572452708,4.047315931757763],"y":[46.86544786881127,33.80790075845665,6.020606467576232,7.025232971012753,4.249405806962808,5.1506542935684125,3.8212936820889736],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["46.9%","33.8%","5.8%","4.5%"],"textposition":"auto","x":["PROFESSIONAL","AI_GENERATED","UNKNOWN","ORIGINAL"],"y":[46.86544786881127,33.80790075845665,5.7650817485172645,4.485973987828693],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["292","1516","89","46","41","15","17"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Momentum Rule 2: Momentum Divergence Recovery","Professional Rule 7: Chaikin Money Flow Reversal","Volume Rule 5: Smart Money Volume","Rule 10: Volume Spike","Rule 2: Golden Cross"],"y":[292,1516,89,46,41,15,17],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292],"y":[0,0.16049810913976462,0.32099621827952923,0.4814943274192939,0.6419924365590585,0.8024905456988232,0.9629886548385878,1.1234867639783523,1.283984873118117,1.4444829822578815,1.6049810913976463,1.765479200537411,1.9259773096771755,2.0864754188169403,2.2469735279567047,2.4074716370964695,2.567969746236234,2.7284678553759987,2.888965964515763,3.049464073655528,3.2099621827952927,3.370460291935057,3.530958401074822,3.691456510214586,3.851954619354351,4.012452728494115,4.172950837633881,4.333448946773645,4.493947055913409,4.654445165053174,4.814943274192939,4.975441383332703,5.135939492472468,5.296437601612233,5.456935710751997,5.617433819891762,5.777931929031526,5.938430038171292,6.098928147311056,6.259426256450821,6.419924365590585,6.580422474730351,6.740920583870114,6.901418693009879,7.061916802149644,7.222414911289409,7.382913020429172,7.543411129568938,7.703909238708702,7.864407347848467,8.02490545698823,8.185403566127997,8.345901675267761,8.506399784407526,8.66689789354729,8.827396002687054,8.987894111826819,9.148392220966585,9.308890330106347,9.469388439246114,9.629886548385878,9.790384657525642,9.950882766665407,10.111380875805173,10.271878984944935,10.432377094084702,10.592875203224466,10.75337331236423,10.913871421503995,11.074369530643759,11.234867639783523,11.39536574892329,11.555863858063052,11.716361967202818,11.876860076342584,12.037358185482349,12.197856294622111,12.358354403761878,12.518852512901642,12.679350622041408,12.83984873118117,13.000346840320935,13.160844949460701,13.321343058600466,13.481841167740228,13.642339276879994,13.802837386019759,13.963335495159525,14.123833604299287,14.284331713439052,14.444829822578818,14.605327931718582,14.765826040858345,14.926324149998111,15.086822259137875,15.247320368277641,15.407818477417404,15.568316586557168,15.728814695696935,15.8893128048367,16.04981091397646,16.210309023116228,16.370807132255994,16.53130524139576,16.691803350535523,16.852301459675285,17.01279956881505,17.173297677954817,17.33379578709458,17.494293896234346,17.65479200537411,17.815290114513875,17.975788223653637,18.136286332793404,18.29678444193317,18.457282551072932,18.617780660212695,18.77827876935246,18.938776878492227,19.099274987631993,19.259773096771756,19.42027120591152,19.580769315051285,19.74126742419105,19.901765533330813,20.06226364247058,20.222761751610346,20.38325986075011,20.54375796988987,20.704256079029637,20.864754188169403,21.02525229730917,21.185750406448932,21.346248515588695,21.50674662472846,21.667244733868227,21.82774284300799,21.988240952147756,22.148739061287518,22.309237170427284,22.469735279567047,22.630233388706813,22.79073149784658,22.95122960698634,23.111727716126104,23.27222582526587,23.432723934405637,23.593222043545403,23.75372015268517,23.91421826182493,24.074716370964698,24.235214480104457,24.395712589244223,24.55621069838399,24.716708807523755,24.877206916663518,25.037705025803284,25.19820313494305,25.358701244082816,25.519199353222575,25.67969746236234,25.840195571502104,26.00069368064187,26.161191789781636,26.321689898921402,26.482188008061165,26.64268611720093,26.80318422634069,26.963682335480456,27.124180444620222,27.28467855375999,27.445176662899755,27.605674772039517,27.766172881179283,27.92667099031905,28.08716909945881,28.247667208598575,28.40816531773834,28.568663426878103,28.72916153601787,28.889659645157636,29.050157754297402,29.210655863437164,29.371153972576927,29.53165208171669,29.692150190856456,29.852648299996222,30.013146409135988,30.17364451827575,30.334142627415517,30.494640736555283,30.655138845695042,30.815636954834808,30.976135063974574,31.136633173114337,31.297131282254103,31.45762939139387,31.618127500533635,31.7786256096734,31.93912371881316,32.09962182795292,32.26011993709269,32.420618046232455,32.58111615537222,32.74161426451199,32.902112373651754,33.06261048279152,33.22310859193128,33.383606701071045,33.544104810210804,33.70460291935057,33.865101028490336,34.0255991376301,34.18609724676987,34.346595355909635,34.507093465049394,34.66759157418916,34.828089683328926,34.98858779246869,35.14908590160846,35.30958401074822,35.470082119887984,35.63058022902775,35.791078338167516,35.951576447307275,36.11207455644704,36.27257266558681,36.43307077472657,36.59356888386634,36.754066993006106,36.914565102145865,37.07506321128563,37.23556132042539,37.396059429565156,37.55655753870492,37.71705564784469,37.877553756984454,38.03805186612422,38.19854997526399,38.35904808440375,38.51954619354351,38.68004430268328,38.84054241182304,39.0010405209628,39.16153863010257,39.322036739242336,39.4825348483821,39.64303295752187,39.80353106666163,39.96402917580139,40.12452728494116,40.285025394080925,40.44552350322069,40.60602161236045,40.76651972150022,40.92701783063998,41.08751593977974,41.24801404891951,41.408512158059274,41.56901026719904,41.729508376338806,41.89000648547857,42.05050459461834,42.2110027037581,42.371500812897864,42.53199892203762,42.69249703117739,42.852995140317155,43.01349324945692,43.17399135859669,43.334489467736454,43.49498757687622,43.65548568601598,43.815983795155745,43.97648190429551,44.13698001343528,44.297478122575036,44.4579762317148,44.61847434085457,44.778972449994335,44.939470559134094,45.09996866827386,45.260466777413626,45.42096488655339,45.58146299569316,45.741961104832924,45.90245921397268,46.06295732311245,46.22345543225221,46.383953541391975,46.54445165053174,46.70494975967151,46.86544786881127],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516],"y":[0,0.022300726093968763,0.044601452187937526,0.0669021782819063,0.08920290437587505,0.11150363046984382,0.1338043565638126,0.15610508265778136,0.1784058087517501,0.20070653484571888,0.22300726093968765,0.24530798703365642,0.2676087131276252,0.2899094392215939,0.3122101653155627,0.3345108914095315,0.3568116175035002,0.379112343597469,0.40141306969143775,0.4237137957854065,0.4460145218793753,0.468315247973344,0.49061597406731283,0.5129167001612815,0.5352174262552504,0.5575181523492191,0.5798188784431878,0.6021196045371567,0.6244203306311255,0.6467210567250942,0.669021782819063,0.6913225089130317,0.7136232350070004,0.7359239611009692,0.758224687194938,0.7805254132889068,0.8028261393828755,0.8251268654768443,0.847427591570813,0.8697283176647818,0.8920290437587506,0.9143297698527193,0.936630495946688,0.958931222040657,0.9812319481346257,1.0035326742285944,1.025833400322563,1.048134126416532,1.0704348525105007,1.0927355786044695,1.1150363046984382,1.137337030792407,1.1596377568863756,1.1819384829803445,1.2042392090743135,1.226539935168282,1.248840661262251,1.2711413873562196,1.2934421134501883,1.315742839544157,1.338043565638126,1.3603442917320945,1.3826450178260634,1.4049457439200324,1.4272464700140008,1.4495471961079698,1.4718479222019385,1.4941486482959072,1.516449374389876,1.5387501004838449,1.5610508265778136,1.5833515526717823,1.605652278765751,1.6279530048597197,1.6502537309536887,1.6725544570476572,1.694855183141626,1.7171559092355948,1.7394566353295635,1.7617573614235325,1.7840580875175012,1.8063588136114699,1.8286595397054386,1.8509602657994075,1.873260991893376,1.895561717987345,1.917862444081314,1.9401631701752824,1.9624638962692513,1.98476462236322,2.0070653484571888,2.0293660745511577,2.051666800645126,2.073967526739095,2.096268252833064,2.1185689789270326,2.1408697050210015,2.16317043111497,2.185471157208939,2.207771883302908,2.2300726093968763,2.252373335490845,2.274674061584814,2.2969747876787827,2.319275513772751,2.3415762398667206,2.363876965960689,2.3861776920546576,2.408478418148627,2.4307791442425954,2.453079870336564,2.475380596430533,2.497681322524502,2.5199820486184703,2.5422827747124392,2.5645835008064077,2.5868842269003767,2.6091849529943456,2.631485679088314,2.653786405182283,2.676087131276252,2.6983878573702205,2.720688583464189,2.7429893095581583,2.765290035652127,2.7875907617460953,2.8098914878400647,2.832192213934033,2.8544929400280017,2.8767936661219706,2.8990943922159396,2.921395118309908,2.943695844403877,2.965996570497846,2.9882972965918144,3.0105980226857834,3.032898748779752,3.055199474873721,3.0775002009676897,3.099800927061658,3.122101653155627,3.144402379249596,3.1667031053435646,3.189003831437533,3.211304557531502,3.233605283625471,3.2559060097194394,3.278206735813408,3.3005074619073773,3.322808188001346,3.3451089140953143,3.3674096401892837,3.389710366283252,3.4120110923772207,3.4343118184711896,3.4566125445651585,3.478913270659127,3.501213996753096,3.523514722847065,3.5458154489410334,3.5681161750350023,3.590416901128971,3.6127176272229398,3.6350183533169087,3.657319079410877,3.679619805504846,3.701920531598815,3.7242212576927836,3.746521983786752,3.7688227098807214,3.79112343597469,3.8134241620686584,3.835724888162628,3.8580256142565963,3.880326340350565,3.9026270664445337,3.9249277925385027,3.947228518632471,3.96952924472644,3.991829970820409,4.0141306969143775,4.036431423008347,4.058732149102315,4.081032875196284,4.103333601290252,4.125634327384222,4.14793505347819,4.170235779572159,4.192536505666128,4.214837231760097,4.237137957854065,4.259438683948034,4.281739410042003,4.3040401361359715,4.32634086222994,4.348641588323909,4.370942314417878,4.393243040511846,4.415543766605816,4.437844492699784,4.460145218793753,4.482445944887722,4.50474667098169,4.527047397075659,4.549348123169628,4.571648849263596,4.593949575357565,4.616250301451535,4.638551027545502,4.660851753639472,4.683152479733441,4.705453205827409,4.727753931921378,4.7500546580153475,4.772355384109315,4.7946561102032845,4.816956836297254,4.8392575623912215,4.861558288485191,4.883859014579159,4.906159740673128,4.928460466767097,4.950761192861066,4.973061918955034,4.995362645049004,5.017663371142972,5.039964097236941,5.06226482333091,5.0845655494248785,5.106866275518847,5.1291670016128155,5.151467727706785,5.173768453800753,5.196069179894722,5.218369905988691,5.24067063208266,5.262971358176628,5.285272084270598,5.307572810364566,5.329873536458535,5.352174262552504,5.374474988646472,5.396775714740441,5.41907644083441,5.441377166928378,5.463677893022347,5.485978619116317,5.508279345210284,5.530580071304254,5.552880797398223,5.575181523492191,5.59748224958616,5.619782975680129,5.642083701774097,5.664384427868066,5.686685153962036,5.708985880056003,5.731286606149973,5.753587332243941,5.77588805833791,5.798188784431879,5.820489510525848,5.842790236619816,5.8650909627137855,5.887391688807754,5.9096924149017225,5.931993140995692,5.95429386708966,5.976594593183629,5.998895319277597,6.021196045371567,6.043496771465535,6.065797497559504,6.088098223653473,6.110398949747442,6.13269967584141,6.1550004019353795,6.177301128029348,6.199601854123316,6.221902580217286,6.244203306311254,6.266504032405223,6.288804758499192,6.31110548459316,6.333406210687129,6.355706936781098,6.378007662875066,6.4003083889690355,6.422609115063004,6.4449098411569725,6.467210567250942,6.48951129334491,6.511812019438879,6.534112745532848,6.556413471626816,6.578714197720785,6.601014923814755,6.623315649908722,6.645616376002692,6.667917102096661,6.690217828190629,6.712518554284598,6.734819280378567,6.757120006472535,6.779420732566504,6.801721458660474,6.824022184754441,6.846322910848411,6.868623636942379,6.890924363036348,6.913225089130317,6.935525815224286,6.957826541318254,6.9801272674122234,7.002427993506192,7.02472871960016,7.04702944569413,7.069330171788098,7.091630897882067,7.113931623976036,7.136232350070005,7.158533076163973,7.180833802257942,7.203134528351911,7.2254352544458795,7.247735980539848,7.270036706633817,7.292337432727786,7.314638158821754,7.336938884915724,7.359239611009692,7.381540337103661,7.40384106319763,7.426141789291598,7.448442515385567,7.4707432414795365,7.493043967573504,7.5153446936674735,7.537645419761443,7.5599461458554105,7.58224687194938,7.604547598043349,7.626848324137317,7.649149050231286,7.671449776325256,7.693750502419223,7.716051228513193,7.738351954607161,7.76065268070113,7.782953406795099,7.805254132889067,7.827554858983036,7.849855585077005,7.872156311170974,7.894457037264942,7.916757763358912,7.93905848945288,7.961359215546849,7.983659941640818,8.005960667734787,8.028261393828755,8.050562119922724,8.072862846016694,8.09516357211066,8.11746429820463,8.1397650242986,8.162065750392568,8.184366476486536,8.206667202580505,8.228967928674473,8.251268654768444,8.273569380862412,8.29587010695638,8.318170833050349,8.340471559144317,8.362772285238286,8.385073011332256,8.407373737426225,8.429674463520193,8.451975189614162,8.47427591570813,8.4965766418021,8.518877367896067,8.541178093990036,8.563478820084006,8.585779546177974,8.608080272271943,8.630380998365913,8.65268172445988,8.674982450553848,8.697283176647819,8.719583902741787,8.741884628835756,8.764185354929726,8.786486081023693,8.808786807117661,8.831087533211631,8.8533882593056,8.875688985399568,8.897989711493537,8.920290437587505,8.942591163681474,8.964891889775444,8.987192615869413,9.00949334196338,9.03179406805735,9.054094794151318,9.076395520245287,9.098696246339257,9.120996972433225,9.143297698527192,9.165598424621162,9.18789915071513,9.2101998768091,9.23250060290307,9.254801328997038,9.277102055091005,9.299402781184975,9.321703507278944,9.344004233372912,9.366304959466882,9.388605685560849,9.410906411654818,9.433207137748788,9.455507863842756,9.477808589936725,9.500109316030695,9.522410042124662,9.54471076821863,9.5670114943126,9.589312220406569,9.611612946500538,9.633913672594508,9.656214398688475,9.678515124782443,9.700815850876413,9.723116576970382,9.74541730306435,9.767718029158319,9.790018755252287,9.812319481346256,9.834620207440226,9.856920933534195,9.879221659628163,9.901522385722132,9.9238231118161,9.946123837910068,9.968424564004039,9.990725290098007,10.013026016191974,10.035326742285944,10.057627468379913,10.079928194473881,10.102228920567851,10.12452964666182,10.146830372755787,10.169131098849757,10.191431824943725,10.213732551037694,10.236033277131664,10.258334003225631,10.2806347293196,10.30293545541357,10.325236181507538,10.347536907601507,10.369837633695477,10.392138359789444,10.414439085883412,10.436739811977382,10.459040538071351,10.48134126416532,10.50364199025929,10.525942716353256,10.548243442447225,10.570544168541195,10.592844894635164,10.615145620729132,10.6374463468231,10.65974707291707,10.682047799011038,10.704348525105008,10.726649251198976,10.748949977292945,10.771250703386913,10.793551429480882,10.81585215557485,10.83815288166882,10.86045360776279,10.882754333856756,10.905055059950726,10.927355786044695,10.949656512138663,10.971957238232633,10.994257964326602,11.016558690420569,11.038859416514539,11.061160142608507,11.083460868702476,11.105761594796446,11.128062320890413,11.150363046984381,11.172663773078352,11.19496449917232,11.217265225266289,11.239565951360259,11.261866677454226,11.284167403548194,11.306468129642164,11.328768855736133,11.351069581830101,11.373370307924072,11.395671034018038,11.417971760112007,11.440272486205977,11.462573212299946,11.484873938393914,11.507174664487883,11.529475390581851,11.55177611667582,11.57407684276979,11.596377568863758,11.618678294957727,11.640979021051695,11.663279747145664,11.685580473239632,11.707881199333602,11.730181925427571,11.752482651521538,11.774783377615508,11.797084103709476,11.819384829803445,11.841685555897415,11.863986281991384,11.88628700808535,11.90858773417932,11.93088846027329,11.953189186367258,11.975489912461228,11.997790638555195,12.020091364649163,12.042392090743133,12.064692816837102,12.08699354293107,12.10929426902504,12.131594995119007,12.153895721212976,12.176196447306946,12.198497173400915,12.220797899494883,12.243098625588853,12.26539935168282,12.287700077776789,12.310000803870759,12.332301529964727,12.354602256058696,12.376902982152664,12.399203708246633,12.421504434340601,12.443805160434572,12.46610588652854,12.488406612622509,12.510707338716477,12.533008064810446,12.555308790904414,12.577609516998384,12.599910243092353,12.62221096918632,12.64451169528029,12.666812421374258,12.689113147468227,12.711413873562195,12.733714599656166,12.756015325750132,12.7783160518441,12.800616777938071,12.82291750403204,12.845218230126008,12.867518956219977,12.889819682313945,12.912120408407914,12.934421134501884,12.956721860595852,12.97902258668982,13.00132331278379,13.023624038877758,13.045924764971726,13.068225491065697,13.090526217159665,13.112826943253632,13.135127669347602,13.15742839544157,13.179729121535539,13.20202984762951,13.224330573723478,13.246631299817444,13.268932025911415,13.291232752005383,13.313533478099352,13.335834204193322,13.35813493028729,13.380435656381257,13.402736382475227,13.425037108569196,13.447337834663164,13.469638560757135,13.491939286851101,13.51424001294507,13.53654073903904,13.558841465133009,13.581142191226977,13.603442917320947,13.625743643414914,13.648044369508883,13.670345095602853,13.692645821696821,13.71494654779079,13.737247273884758,13.759547999978727,13.781848726072695,13.804149452166666,13.826450178260634,13.848750904354603,13.871051630448571,13.89335235654254,13.915653082636508,13.937953808730478,13.960254534824447,13.982555260918414,14.004855987012384,14.027156713106352,14.04945743920032,14.071758165294291,14.09405889138826,14.116359617482226,14.138660343576197,14.160961069670165,14.183261795764134,14.205562521858104,14.227863247952072,14.250163974046039,14.27246470014001,14.294765426233978,14.317066152327946,14.339366878421917,14.361667604515883,14.383968330609852,14.406269056703822,14.42856978279779,14.450870508891759,14.47317123498573,14.495471961079696,14.517772687173665,14.540073413267635,14.562374139361603,14.584674865455572,14.60697559154954,14.629276317643509,14.651577043737477,14.673877769831448,14.696178495925416,14.718479222019385,14.740779948113353,14.763080674207322,14.78538140030129,14.80768212639526,14.829982852489229,14.852283578583195,14.874584304677166,14.896885030771134,14.919185756865103,14.941486482959073,14.963787209053041,14.986087935147008,15.008388661240978,15.030689387334947,15.052990113428915,15.075290839522886,15.097591565616854,15.119892291710821,15.142193017804791,15.16449374389876,15.186794469992728,15.209095196086698,15.231395922180665,15.253696648274634,15.275997374368604,15.298298100462572,15.320598826556541,15.342899552650511,15.365200278744478,15.387501004838446,15.409801730932417,15.432102457026385,15.454403183120354,15.476703909214322,15.49900463530829,15.52130536140226,15.54360608749623,15.565906813590198,15.588207539684166,15.610508265778135,15.632808991872103,15.655109717966072,15.677410444060042,15.69971117015401,15.722011896247977,15.744312622341948,15.766613348435916,15.788914074529885,15.811214800623855,15.833515526717823,15.85581625281179,15.87811697890576,15.900417704999729,15.922718431093697,15.945019157187668,15.967319883281636,15.989620609375603,16.011921335469573,16.03422206156354,16.05652278765751,16.07882351375148,16.101124239845447,16.123424965939417,16.145725692033388,16.168026418127354,16.19032714422132,16.21262787031529,16.23492859640926,16.25722932250323,16.2795300485972,16.301830774691165,16.324131500785136,16.346432226879106,16.368732952973073,16.391033679067043,16.41333440516101,16.43563513125498,16.457935857348946,16.480236583442917,16.502537309536887,16.524838035630854,16.547138761724824,16.56943948781879,16.59174021391276,16.61404094000673,16.636341666100698,16.658642392194665,16.680943118288635,16.703243844382605,16.725544570476572,16.747845296570542,16.770146022664512,16.79244674875848,16.81474747485245,16.837048200946416,16.859348927040386,16.881649653134357,16.903950379228323,16.92625110532229,16.94855183141626,16.970852557510227,16.9931532836042,17.015454009698168,17.037754735792134,17.060055461886105,17.08235618798007,17.10465691407404,17.126957640168012,17.149258366261982,17.17155909235595,17.193859818449916,17.216160544543886,17.238461270637853,17.260761996731826,17.283062722825793,17.30536344891976,17.32766417501373,17.349964901107697,17.372265627201667,17.394566353295637,17.416867079389604,17.439167805483574,17.46146853157754,17.48376925767151,17.506069983765478,17.528370709859452,17.55067143595342,17.572972162047385,17.595272888141356,17.617573614235322,17.639874340329293,17.662175066423263,17.68447579251723,17.7067765186112,17.729077244705167,17.751377970799137,17.773678696893104,17.795979422987074,17.818280149081044,17.84058087517501,17.86288160126898,17.885182327362948,17.907483053456914,17.92978377955089,17.952084505644855,17.974385231738825,17.996685957832792,18.01898668392676,18.04128741002073,18.0635881361147,18.08588886220867,18.108189588302636,18.130490314396607,18.152791040490573,18.17509176658454,18.197392492678514,18.21969321877248,18.24199394486645,18.264294670960417,18.286595397054384,18.308896123148354,18.331196849242325,18.353497575336295,18.37579830143026,18.39809902752423,18.4203997536182,18.442700479712165,18.46500120580614,18.487301931900106,18.509602657994076,18.531903384088043,18.55420411018201,18.57650483627598,18.59880556236995,18.62110628846392,18.643407014557887,18.665707740651854,18.688008466745824,18.71030919283979,18.732609918933765,18.75491064502773,18.777211371121698,18.79951209721567,18.821812823309635,18.844113549403605,18.866414275497576,18.888715001591546,18.911015727685513,18.93331645377948,18.95561717987345,18.977917905967416,19.00021863206139,19.022519358155357,19.044820084249324,19.067120810343294,19.08942153643726,19.11172226253123,19.1340229886252,19.156323714719168,19.178624440813138,19.200925166907105,19.223225893001075,19.245526619095042,19.267827345189016,19.290128071282982,19.31242879737695,19.33472952347092,19.357030249564886,19.379330975658856,19.401631701752827,19.423932427846793,19.446233153940764,19.46853388003473,19.4908346061287,19.513135332222667,19.535436058316638,19.557736784410608,19.580037510504575,19.602338236598545,19.62463896269251,19.64693968878648,19.669240414880452,19.69154114097442,19.71384186706839,19.736142593162356,19.758443319256326,19.780744045350293,19.803044771444263,19.825345497538233,19.8476462236322,19.86994694972617,19.892247675820137,19.914548401914104,19.936849128008078,19.959149854102044,19.981450580196014,20.00375130628998,20.026052032383948,20.048352758477918,20.07065348457189,20.09295421066586,20.115254936759825,20.137555662853792,20.159856388947762,20.18215711504173,20.204457841135703,20.22675856722967,20.24905929332364,20.271360019417607,20.293660745511573,20.315961471605544,20.338262197699514,20.360562923793484,20.38286364988745,20.405164375981418,20.427465102075388,20.449765828169355,20.47206655426333,20.494367280357295,20.516668006451262,20.538968732545232,20.5612694586392,20.58357018473317,20.60587091082714,20.62817163692111,20.650472363015076,20.672773089109043,20.695073815203013,20.71737454129698,20.739675267390954,20.76197599348492,20.784276719578887,20.806577445672858,20.828878171766824,20.851178897860795,20.873479623954765,20.89578035004873,20.918081076142702,20.94038180223667,20.96268252833064,20.984983254424606,21.00728398051858,21.029584706612546,21.051885432706513,21.074186158800483,21.09648688489445,21.11878761098842,21.14108833708239,21.163389063176357,21.185689789270327,21.207990515364294,21.230291241458264,21.25259196755223,21.2748926936462,21.29719341974017,21.31949414583414,21.34179487192811,21.364095598022075,21.386396324116042,21.408697050210016,21.430997776303983,21.453298502397953,21.47559922849192,21.49789995458589,21.520200680679856,21.542501406773827,21.564802132867797,21.587102858961764,21.609403585055734,21.6317043111497,21.654005037243667,21.67630576333764,21.698606489431608,21.72090721552558,21.743207941619545,21.76550866771351,21.787809393807482,21.810110119901452,21.832410845995422,21.85471157208939,21.877012298183356,21.899313024277326,21.921613750371293,21.943914476465267,21.966215202559233,21.988515928653204,22.01081665474717,22.033117380841137,22.055418106935107,22.077718833029078,22.100019559123048,22.122320285217015,22.14462101131098,22.16692173740495,22.18922246349892,22.211523189592892,22.23382391568686,22.256124641780826,22.278425367874796,22.300726093968763,22.323026820062733,22.345327546156703,22.367628272250673,22.38992899834464,22.412229724438607,22.434530450532577,22.456831176626544,22.479131902720518,22.501432628814484,22.52373335490845,22.54603408100242,22.568334807096388,22.59063553319036,22.61293625928433,22.635236985378295,22.657537711472266,22.679838437566232,22.702139163660203,22.72443988975417,22.746740615848143,22.76904134194211,22.791342068036077,22.813642794130047,22.835943520224014,22.858244246317984,22.880544972411954,22.90284569850592,22.92514642459989,22.947447150693858,22.969747876787828,22.992048602881795,23.014349328975765,23.036650055069735,23.058950781163702,23.081251507257672,23.10355223335164,23.125852959445606,23.14815368553958,23.170454411633546,23.192755137727517,23.215055863821483,23.237356589915453,23.25965731600942,23.28195804210339,23.30425876819736,23.326559494291327,23.348860220385298,23.371160946479264,23.39346167257323,23.415762398667205,23.43806312476117,23.460363850855142,23.48266457694911,23.504965303043075,23.527266029137046,23.549566755231016,23.571867481324986,23.594168207418953,23.61646893351292,23.63876965960689,23.661070385700857,23.68337111179483,23.705671837888797,23.727972563982767,23.750273290076734,23.7725740161707,23.79487474226467,23.81717546835864,23.83947619445261,23.86177692054658,23.884077646640545,23.906378372734515,23.928679098828482,23.950979824922456,23.973280551016423,23.99558127711039,24.01788200320436,24.040182729298326,24.062483455392297,24.084784181486267,24.107084907580237,24.129385633674204,24.15168635976817,24.17398708586214,24.196287811956108,24.21858853805008,24.240889264144048,24.263189990238015,24.285490716331985,24.307791442425952,24.330092168519922,24.352392894613892,24.37469362070786,24.39699434680183,24.419295072895796,24.441595798989766,24.463896525083733,24.486197251177707,24.508497977271674,24.53079870336564,24.55309942945961,24.575400155553577,24.597700881647548,24.620001607741518,24.642302333835485,24.664603059929455,24.68690378602342,24.70920451211739,24.73150523821136,24.75380596430533,24.7761066903993,24.798407416493266,24.820708142587236,24.843008868681203,24.86530959477517,24.887610320869143,24.90991104696311,24.93221177305708,24.954512499151047,24.976813225245017,24.999113951338984,25.021414677432954,25.043715403526924,25.06601612962089,25.08831685571486,25.110617581808828,25.132918307902795,25.15521903399677,25.177519760090735,25.199820486184706,25.222121212278672,25.24442193837264,25.26672266446661,25.28902339056058,25.31132411665455,25.333624842748517,25.355925568842487,25.378226294936454,25.40052702103042,25.42282774712439,25.44512847321836,25.46742919931233,25.489729925406298,25.512030651500265,25.534331377594235,25.5566321036882,25.578932829782175,25.601233555876142,25.62353428197011,25.64583500806408,25.668135734158046,25.690436460252016,25.712737186345986,25.735037912439953,25.757338638533923,25.77963936462789,25.80194009072186,25.824240816815827,25.8465415429098,25.868842269003768,25.891142995097734,25.913443721191705,25.93574444728567,25.95804517337964,25.980345899473612,26.00264662556758,26.02494735166155,26.047248077755516,26.069548803849486,26.091849529943453,26.114150256037423,26.136450982131393,26.15875170822536,26.18105243431933,26.203353160413297,26.225653886507263,26.247954612601237,26.270255338695204,26.292556064789174,26.31485679088314,26.33715751697711,26.359458243071078,26.38175896916505,26.40405969525902,26.426360421352985,26.448661147446956,26.470961873540922,26.49326259963489,26.515563325728863,26.53786405182283,26.5601647779168,26.582465504010766,26.604766230104733,26.627066956198703,26.649367682292674,26.671668408386644,26.69396913448061,26.71626986057458,26.738570586668548,26.760871312762514,26.78317203885649,26.805472764950455,26.827773491044425,26.850074217138392,26.87237494323236,26.89467566932633,26.9169763954203,26.93927712151427,26.961577847608236,26.983878573702203,27.006179299796173,27.02848002589014,27.050780751984114,27.07308147807808,27.09538220417205,27.117682930266017,27.139983656359984,27.162284382453954,27.184585108547925,27.206885834641895,27.22918656073586,27.25148728682983,27.2737880129238,27.296088739017765,27.31838946511174,27.340690191205706,27.362990917299673,27.385291643393643,27.40759236948761,27.42989309558158,27.45219382167555,27.474494547769517,27.496795273863487,27.519095999957454,27.541396726051424,27.56369745214539,27.585998178239365,27.60829890433333,27.630599630427298,27.65290035652127,27.675201082615235,27.697501808709205,27.719802534803176,27.742103260897142,27.764403986991113,27.78670471308508,27.80900543917905,27.831306165273016,27.853606891366987,27.875907617460957,27.898208343554924,27.920509069648894,27.94280979574286,27.965110521836827,27.9874112479308,28.009711974024768,28.032012700118738,28.054313426212705,28.076614152306675,28.09891487840064,28.121215604494612,28.143516330588582,28.16581705668255,28.18811778277652,28.210418508870486,28.232719234964453,28.255019961058427,28.277320687152393,28.299621413246363,28.32192213934033,28.344222865434297,28.366523591528267,28.388824317622237,28.411125043716208,28.433425769810174,28.455726495904145,28.47802722199811,28.500327948092078,28.522628674186052,28.54492940028002,28.56723012637399,28.589530852467956,28.611831578561922,28.634132304655893,28.656433030749863,28.678733756843833,28.7010344829378,28.723335209031767,28.745635935125737,28.767936661219704,28.790237387313677,28.812538113407644,28.834838839501614,28.85713956559558,28.879440291689548,28.901741017783518,28.92404174387749,28.94634246997146,28.968643196065425,28.990943922159392,29.013244648253362,29.03554537434733,29.057846100441303,29.08014682653527,29.102447552629236,29.124748278723207,29.147049004817173,29.169349730911144,29.191650457005114,29.21395118309908,29.23625190919305,29.258552635287018,29.280853361380988,29.303154087474955,29.32545481356893,29.347755539662895,29.370056265756862,29.392356991850832,29.4146577179448,29.43695844403877,29.45925917013274,29.481559896226706,29.503860622320676,29.526161348414643,29.548462074508613,29.57076280060258,29.59306352669655,29.61536425279052,29.637664978884487,29.659965704978458,29.682266431072424,29.70456715716639,29.726867883260365,29.74916860935433,29.7714693354483,29.79377006154227,29.81607078763624,29.838371513730205,29.860672239824176,29.882972965918146,29.905273692012113,29.927574418106083,29.94987514420005,29.972175870294016,29.99447659638799,30.016777322481957,30.039078048575927,30.061378774669894,30.08367950076386,30.10598022685783,30.1282809529518,30.15058167904577,30.172882405139738,30.19518313123371,30.217483857327675,30.239784583421642,30.262085309515616,30.284386035609582,30.306686761703553,30.32898748779752,30.351288213891486,30.373588939985456,30.395889666079427,30.418190392173397,30.440491118267364,30.46279184436133,30.4850925704553,30.507393296549267,30.52969402264324,30.551994748737208,30.574295474831178,30.596596200925145,30.61889692701911,30.641197653113082,30.663498379207052,30.685799105301022,30.70809983139499,30.730400557488956,30.752701283582926,30.775002009676893,30.797302735770867,30.819603461864833,30.8419041879588,30.86420491405277,30.886505640146737,30.908806366240707,30.931107092334678,30.953407818428644,30.975708544522615,30.99800927061658,31.02030999671055,31.04261072280452,31.064911448898492,31.08721217499246,31.109512901086426,31.131813627180396,31.154114353274363,31.176415079368333,31.198715805462303,31.22101653155627,31.24331725765024,31.265617983744207,31.287918709838177,31.310219435932144,31.332520162026114,31.354820888120084,31.37712161421405,31.39942234030802,31.421723066401988,31.444023792495955,31.46632451858993,31.488625244683895,31.510925970777866,31.533226696871832,31.555527422965802,31.57782814905977,31.60012887515374,31.62242960124771,31.644730327341676,31.667031053435647,31.689331779529613,31.71163250562358,31.733933231717554,31.75623395781152,31.77853468390549,31.800835409999458,31.823136136093424,31.845436862187395,31.867737588281365,31.890038314375335,31.912339040469302,31.934639766563272,31.95694049265724,31.979241218751206,32.00154194484518,32.023842670939146,32.04614339703311,32.06844412312708,32.09074484922105,32.11304557531502,32.135346301408994,32.15764702750296,32.17994775359693,32.202248479690894,32.22454920578486,32.246849931878835,32.2691506579728,32.291451384066775,32.31375211016074,32.33605283625471,32.358353562348675,32.38065428844264,32.402955014536616,32.42525574063058,32.44755646672455,32.46985719281852,32.49215791891249,32.51445864500646,32.53675937110043,32.5590600971944,32.581360823288364,32.60366154938233,32.625962275476304,32.64826300157027,32.670563727664245,32.69286445375821,32.71516517985218,32.737465905946145,32.75976663204011,32.782067358134086,32.80436808422805,32.82666881032202,32.84896953641599,32.87127026250996,32.893570988603926,32.91587171469789,32.93817244079187,32.96047316688583,32.9827738929798,33.005074619073774,33.02737534516774,33.04967607126171,33.07197679735568,33.09427752344965,33.116578249543615,33.13887897563758,33.161179701731555,33.18348042782552,33.20578115391949,33.22808188001346,33.25038260610743,33.272683332201396,33.29498405829536,33.31728478438933,33.3395855104833,33.36188623657727,33.384186962671244,33.40648768876521,33.42878841485918,33.451089140953144,33.47338986704712,33.495690593141084,33.51799131923505,33.540292045329025,33.56259277142299,33.58489349751696,33.60719422361093,33.6294949497049,33.651795675798866,33.67409640189283,33.6963971279868,33.71869785408077,33.74099858017474,33.76329930626871,33.78560003236268,33.80790075845665],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Momentum Rule 2","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89],"y":[0,0.06764726368063181,0.13529452736126363,0.20294179104189544,0.27058905472252726,0.3382363184031591,0.4058835820837909,0.4735308457644227,0.5411781094450545,0.6088253731256863,0.6764726368063182,0.74411990048695,0.8117671641675818,0.8794144278482137,0.9470616915288455,1.0147089552094772,1.082356218890109,1.1500034825707408,1.2176507462513726,1.2852980099320046,1.3529452736126364,1.4205925372932682,1.4882398009739,1.555887064654532,1.6235343283351635,1.6911815920157955,1.7588288556964273,1.8264761193770591,1.894123383057691,1.961770646738323,2.0294179104189545,2.0970651740995865,2.164712437780218,2.23235970146085,2.3000069651414816,2.3676542288221136,2.435301492502745,2.502948756183377,2.570596019864009,2.638243283544641,2.705890547225273,2.773537810905905,2.8411850745865364,2.9088323382671684,2.9764796019478,3.044126865628432,3.111774129309064,3.179421392989695,3.247068656670327,3.314715920350959,3.382363184031591,3.4500104477122226,3.5176577113928547,3.5853049750734867,3.6529522387541182,3.72059950243475,3.788246766115382,3.855894029796014,3.923541293476646,3.991188557157277,4.058835820837909,4.126483084518541,4.194130348199173,4.261777611879804,4.329424875560436,4.397072139241068,4.4647194029217,4.532366666602331,4.600013930282963,4.667661193963595,4.735308457644227,4.802955721324859,4.87060298500549,4.938250248686122,5.005897512366754,5.073544776047386,5.141192039728018,5.20883930340865,5.276486567089282,5.3441338307699136,5.411781094450546,5.479428358131178,5.54707562181181,5.614722885492441,5.682370149173073,5.750017412853705,5.817664676534337,5.885311940214968,5.9529592038956,6.020606467576232],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Professional Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46],"y":[0,0.15272245589158157,0.30544491178316313,0.45816736767474475,0.6108898235663263,0.7636122794579079,0.9163347353494895,1.0690571912410711,1.2217796471326525,1.3745021030242344,1.5272245589158158,1.6799470148073974,1.832669470698979,1.9853919265905604,2.1381143824821423,2.290836838373724,2.443559294265305,2.5962817501568867,2.7490042060484687,2.90172666194005,3.0544491178316315,3.207171573723213,3.3598940296147948,3.5126164855063764,3.665338941397958,3.818061397289539,3.970783853181121,4.123506309072703,4.2762287649642845,4.428951220855866,4.581673676747448,4.734396132639029,4.88711858853061,5.039841044422192,5.192563500313773,5.345285956205355,5.4980084120969375,5.650730867988519,5.8034533238801,5.956175779771682,6.108898235663263,6.261620691554844,6.414343147446426,6.567065603338008,6.7197880592295895,6.872510515121172,7.025232971012753],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Volume Rule 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41],"y":[0,0.10364404407226362,0.20728808814452723,0.31093213221679084,0.41457617628905447,0.518220220361318,0.6218642644335817,0.7255083085058454,0.8291523525781089,0.9327963966503725,1.036440440722636,1.1400844847948999,1.2437285288671633,1.347372572939427,1.4510166170116907,1.5546606610839542,1.6583047051562179,1.7619487492284813,1.865592793300745,1.9692368373730087,2.072880881445272,2.176524925517536,2.2801689695897998,2.3838130136620634,2.4874570577343267,2.5911011018065904,2.694745145878854,2.7983891899511177,2.9020332340233814,3.0056772780956447,3.1093213221679084,3.212965366240172,3.3166094103124357,3.4202534543846994,3.5238974984569627,3.6275415425292263,3.73118558660149,3.8348296306737537,3.9384736747460174,4.042117718818281,4.145761762890544,4.249405806962808],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">46.87%</td>
                <td>68.2%</td>
                <td>292</td>
                <td>1.27</td>
                <td>0.00</td>
                <td>11.76%</td>
                <td>69.2</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">33.81%</td>
                <td>62.8%</td>
                <td>1516</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>36.62%</td>
                <td>62.4</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                <td>UNKNOWN</td>
                <td class="positive">6.02%</td>
                <td>62.9%</td>
                <td>89</td>
                <td>1.10</td>
                <td>0.00</td>
                <td>11.36%</td>
                <td>48.0</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">7.03%</td>
                <td>65.2%</td>
                <td>46</td>
                <td>1.27</td>
                <td>0.00</td>
                <td>5.36%</td>
                <td>36.2</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">4.25%</td>
                <td>65.9%</td>
                <td>41</td>
                <td>1.16</td>
                <td>0.00</td>
                <td>11.40%</td>
                <td>33.8</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">5.15%</td>
                <td>73.3%</td>
                <td>15</td>
                <td>1.72</td>
                <td>0.00</td>
                <td>3.84%</td>
                <td>28.6</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Rule 2: Golden Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">3.82%</td>
                <td>70.6%</td>
                <td>17</td>
                <td>1.43</td>
                <td>0.00</td>
                <td>4.05%</td>
                <td>27.8</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
