
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 7 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 16:08:58</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">7</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">43.8%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">9.8%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">20.6%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">68.0%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">1,858</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["20.6%","19.1%","3.0%","8.1%","3.5%","8.4%","5.6%"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Rule 6: Stochastic Oversold Cross","Volume Rule 5: Smart Money Volume","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[20.58151536879575,19.058810492686067,2.9938026302837244,8.133433005205545,3.5156451872715406,8.420381604790645,5.5902201037222845],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Rule 6: Stochastic Oversold Cross","Volume Rule 5: Smart Money Volume","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[118,850,95,46,37,13,23],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Rule 6: Stochastic Oversold Cross","Volume Rule 5: Smart Money Volume","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[53,509,54,21,22,4,13],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[20.58151536879575,19.058810492686067,2.9938026302837244,8.133433005205545,3.5156451872715406,8.420381604790645,5.5902201037222845],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Prof Rule 7","AI Rule 10","Rule 6","Volume Rule 5","Professional Rule 10","Price Action Rule 3","Professional Rule 7"],"textposition":"top center","x":[12.12804621210469,32.19507808303527,16.454617763263382,5.614942971352814,5.55051670085763,1.6253892558966672,5.3881169424265565],"y":[20.58151536879575,19.058810492686067,2.9938026302837244,8.133433005205545,3.5156451872715406,8.420381604790645,5.5902201037222845],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["20.6%","19.1%","3.0%","6.4%"],"textposition":"auto","x":["PROFESSIONAL","AI_GENERATED","ORIGINAL","UNKNOWN"],"y":[20.58151536879575,19.058810492686067,2.9938026302837244,6.4149199752475035],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["171","1359","149","67","59","17","36"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 10: Composite Sentiment Reversal","Rule 6: Stochastic Oversold Cross","Volume Rule 5: Smart Money Volume","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[171,1359,149,67,59,17,36],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171],"y":[0,0.12035973899880555,0.2407194779976111,0.36107921699641665,0.4814389559952222,0.6017986949940277,0.7221584339928333,0.8425181729916388,0.9628779119904444,1.0832376509892498,1.2035973899880554,1.323957128986861,1.4443168679856666,1.564676606984472,1.6850363459832776,1.8053960849820831,1.9257558239808887,2.0461155629796943,2.1664753019784997,2.2868350409773055,2.407194779976111,2.5275545189749162,2.647914257973722,2.7682739969725274,2.888633735971333,3.0089934749701386,3.129353213968944,3.2497129529677498,3.370072691966555,3.490432430965361,3.6107921699641663,3.7311519089629717,3.8515116479617775,3.971871386960583,4.092231125959389,4.2125908649581945,4.332950603956999,4.453310342955805,4.573670081954611,4.694029820953416,4.814389559952222,4.9347492989510275,5.0551090379498325,5.175468776948638,5.295828515947444,5.41618825494625,5.536547993945055,5.656907732943861,5.777267471942666,5.897627210941471,6.017986949940277,6.138346688939083,6.258706427937888,6.379066166936694,6.4994259059354995,6.619785644934305,6.74014538393311,6.860505122931916,6.980864861930722,7.101224600929527,7.221584339928333,7.341944078927138,7.462303817925943,7.582663556924749,7.703023295923555,7.823383034922361,7.943742773921166,8.064102512919971,8.184462251918777,8.304821990917583,8.425181729916389,8.545541468915193,8.665901207913999,8.786260946912805,8.90662068591161,9.026980424910416,9.147340163909222,9.267699902908026,9.388059641906832,9.508419380905638,9.628779119904443,9.74913885890325,9.869498597902055,9.989858336900861,10.110218075899665,10.23057781489847,10.350937553897277,10.471297292896084,10.591657031894888,10.712016770893694,10.8323765098925,10.952736248891306,11.07309598789011,11.193455726888917,11.313815465887721,11.434175204886527,11.554534943885333,11.674894682884139,11.795254421882943,11.91561416088175,12.035973899880554,12.15633363887936,12.276693377878166,12.397053116876972,12.517412855875776,12.637772594874583,12.758132333873387,12.878492072872195,12.998851811870999,13.119211550869805,13.23957128986861,13.359931028867416,13.48029076786622,13.600650506865028,13.721010245863832,13.841369984862638,13.961729723861444,14.08208946286025,14.202449201859054,14.322808940857861,14.443168679856665,14.563528418855471,14.683888157854277,14.804247896853083,14.924607635851887,15.044967374850694,15.165327113849498,15.285686852848306,15.40604659184711,15.526406330845916,15.646766069844722,15.767125808843527,15.887485547842331,16.00784528684114,16.128205025839943,16.24856476483875,16.368924503837555,16.489284242836362,16.609643981835166,16.73000372083397,16.850363459832778,16.970723198831582,17.091082937830386,17.211442676829193,17.331802415827998,17.452162154826805,17.57252189382561,17.692881632824417,17.81324137182322,17.93360111082203,18.053960849820832,18.174320588819636,18.294680327818444,18.415040066817248,18.535399805816052,18.65575954481486,18.776119283813664,18.89647902281247,19.016838761811275,19.137198500810083,19.257558239808887,19.377917978807695,19.4982777178065,19.618637456805306,19.73899719580411,19.859356934802914,19.979716673801722,20.100076412800526,20.22043615179933,20.340795890798137,20.46115562979694,20.58151536879575],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359],"y":[0,0.014024143114559285,0.02804828622911857,0.04207242934367785,0.05609657245823714,0.07012071557279642,0.0841448586873557,0.09816900180191498,0.11219314491647428,0.12621728803103355,0.14024143114559284,0.15426557426015214,0.1682897173747114,0.1823138604892707,0.19633800360382997,0.2103621467183893,0.22438628983294856,0.23841043294750786,0.2524345760620671,0.2664587191766264,0.2804828622911857,0.294507005405745,0.3085311485203043,0.3225552916348636,0.3365794347494228,0.3506035778639821,0.3646277209785414,0.3786518640931007,0.39267600720765994,0.40670015032221923,0.4207242934367786,0.4347484365513378,0.4487725796658971,0.46279672278045636,0.4768208658950157,0.49084500900957495,0.5048691521241342,0.5188932952386935,0.5329174383532528,0.5469415814678121,0.5609657245823714,0.5749898676969306,0.58901401081149,0.6030381539260492,0.6170622970406086,0.6310864401551678,0.6451105832697271,0.6591347263842864,0.6731588694988456,0.6871830126134049,0.7012071557279642,0.7152312988425235,0.7292554419570828,0.7432795850716422,0.7573037281862014,0.7713278713007606,0.7853520144153199,0.7993761575298792,0.8134003006444385,0.8274244437589977,0.8414485868735572,0.8554727299881164,0.8694968731026756,0.8835210162172349,0.8975451593317942,0.9115693024463535,0.9255934455609127,0.939617588675472,0.9536417317900314,0.9676658749045907,0.9816900180191499,0.9957141611337093,1.0097383042482684,1.0237624473628277,1.037786590477387,1.0518107335919462,1.0658348767065056,1.079859019821065,1.0938831629356243,1.1079073060501834,1.1219314491647427,1.135955592279302,1.1499797353938612,1.1640038785084206,1.17802802162298,1.192052164737539,1.2060763078520984,1.2201004509666575,1.234124594081217,1.2481487371957765,1.2621728803103356,1.276197023424895,1.2902211665394543,1.3042453096540134,1.3182694527685728,1.3322935958831321,1.3463177389976912,1.3603418821122506,1.3743660252268097,1.388390168341369,1.4024143114559284,1.4164384545704876,1.430462597685047,1.4444867407996063,1.4585108839141656,1.472535027028725,1.4865591701432843,1.5005833132578434,1.5146074563724028,1.528631599486962,1.5426557426015213,1.5566798857160806,1.5707040288306398,1.584728171945199,1.5987523150597585,1.6127764581743176,1.626800601288877,1.640824744403436,1.6548488875179954,1.6688730306325548,1.6828971737471143,1.6969213168616735,1.7109454599762328,1.724969603090792,1.7389937462053513,1.7530178893199106,1.7670420324344698,1.7810661755490291,1.7950903186635885,1.8091144617781476,1.823138604892707,1.837162748007266,1.8511868911218254,1.8652110342363848,1.879235177350944,1.8932593204655033,1.9072834635800628,1.921307606694622,1.9353317498091813,1.9493558929237407,1.9633800360382998,1.9774041791528592,1.9914283222674185,2.005452465381978,2.0194766084965368,2.033500751611096,2.0475248947256555,2.061549037840215,2.075573180954774,2.089597324069333,2.1036214671838924,2.1176456102984518,2.131669753413011,2.1456938965275705,2.15971803964213,2.173742182756689,2.1877663258712485,2.201790468985808,2.215814612100367,2.229838755214926,2.2438628983294855,2.257887041444045,2.271911184558604,2.285935327673163,2.2999594707877224,2.313983613902282,2.328007757016841,2.3420319001314005,2.35605604324596,2.370080186360519,2.384104329475078,2.398128472589638,2.412152615704197,2.426176758818756,2.440200901933315,2.454225045047875,2.468249188162434,2.482273331276993,2.496297474391553,2.510321617506112,2.524345760620671,2.5383699037352305,2.55239404684979,2.566418189964349,2.5804423330789086,2.5944664761934675,2.608490619308027,2.622514762422586,2.6365389055371455,2.6505630486517044,2.6645871917662642,2.678611334880823,2.6926354779953825,2.7066596211099423,2.720683764224501,2.7347079073390606,2.7487320504536195,2.7627561935681793,2.776780336682738,2.7908044797972975,2.804828622911857,2.818852766026416,2.832876909140975,2.846901052255535,2.860925195370094,2.874949338484653,2.8889734815992125,2.902997624713772,2.9170217678283312,2.9310459109428906,2.94507005405745,2.959094197172009,2.9731183402865686,2.9871424834011275,3.001166626515687,3.015190769630246,3.0292149127448056,3.0432390558593645,3.057263198973924,3.071287342088483,3.0853114852030425,3.0993356283176015,3.1133597714321612,3.12738391454672,3.1414080576612795,3.1554322007758393,3.169456343890398,3.1834804870049576,3.197504630119517,3.2115287732340763,3.225552916348635,3.239577059463195,3.253601202577754,3.267625345692313,3.281649488806872,3.295673631921432,3.309697775035991,3.3237219181505506,3.3377460612651095,3.351770204379669,3.3657943474942287,3.3798184906087876,3.393842633723347,3.407866776837906,3.4218909199524656,3.4359150630670245,3.449939206181584,3.4639633492961432,3.4779874924107026,3.4920116355252615,3.5060357786398213,3.52005992175438,3.5340840648689396,3.548108207983499,3.5621323510980583,3.576156494212617,3.590180637327177,3.6042047804417363,3.618228923556295,3.632253066670855,3.646277209785414,3.6603013528999733,3.674325496014532,3.688349639129092,3.702373782243651,3.7163979253582102,3.7304220684727696,3.744446211587329,3.758470354701888,3.7724944978164476,3.7865186409310065,3.800542784045566,3.8145669271601257,3.8285910702746846,3.842615213389244,3.8566393565038033,3.8706634996183626,3.8846876427329216,3.8987117858474813,3.9127359289620403,3.9267600720765996,3.9407842151911585,3.9548083583057183,3.968832501420277,3.982856644534837,3.996880787649396,4.010904930763956,4.024929073878514,4.0389532169930735,4.052977360107634,4.067001503222192,4.081025646336752,4.095049789451311,4.10907393256587,4.12309807568043,4.137122218794989,4.151146361909548,4.165170505024108,4.179194648138666,4.193218791253226,4.207242934367785,4.221267077482344,4.2352912205969035,4.249315363711463,4.263339506826022,4.277363649940582,4.291387793055141,4.3054119361697,4.31943607928426,4.333460222398819,4.347484365513378,4.361508508627938,4.375532651742497,4.3895567948570555,4.403580937971616,4.417605081086174,4.431629224200734,4.445653367315293,4.459677510429852,4.473701653544412,4.487725796658971,4.50174993977353,4.51577408288809,4.529798226002649,4.543822369117208,4.557846512231768,4.571870655346326,4.585894798460886,4.599918941575445,4.613943084690004,4.627967227804564,4.641991370919123,4.656015514033682,4.670039657148242,4.684063800262801,4.69808794337736,4.71211208649192,4.726136229606479,4.740160372721038,4.754184515835597,4.768208658950156,4.7822328020647165,4.796256945179276,4.810281088293834,4.824305231408394,4.838329374522954,4.852353517637512,4.866377660752072,4.88040180386663,4.89442594698119,4.90845009009575,4.922474233210308,4.936498376324868,4.950522519439428,4.964546662553986,4.978570805668546,4.992594948783106,5.006619091897664,5.020643235012224,5.034667378126783,5.048691521241342,5.062715664355902,5.076739807470461,5.0907639505850195,5.10478809369958,5.118812236814139,5.132836379928698,5.146860523043257,5.160884666157817,5.174908809272376,5.188932952386935,5.202957095501495,5.216981238616054,5.231005381730613,5.245029524845172,5.259053667959732,5.273077811074291,5.28710195418885,5.301126097303409,5.315150240417969,5.3291743835325285,5.343198526647087,5.357222669761646,5.3712468128762065,5.385270955990765,5.399295099105324,5.413319242219885,5.427343385334443,5.441367528449002,5.455391671563561,5.469415814678121,5.4834399577926805,5.497464100907239,5.511488244021798,5.5255123871363585,5.539536530250917,5.553560673365476,5.567584816480036,5.581608959594595,5.595633102709154,5.609657245823714,5.623681388938273,5.637705532052832,5.651729675167392,5.66575381828195,5.6797779613965105,5.69380210451107,5.707826247625628,5.721850390740188,5.735874533854748,5.749898676969306,5.763922820083866,5.777946963198425,5.791971106312984,5.805995249427544,5.820019392542103,5.8340435356566624,5.848067678771222,5.862091821885781,5.87611596500034,5.8901401081149,5.904164251229459,5.918188394344018,5.932212537458577,5.946236680573137,5.960260823687696,5.974284966802255,5.9883091099168135,6.002333253031374,6.016357396145933,6.030381539260492,6.044405682375051,6.058429825489611,6.07245396860417,6.086478111718729,6.100502254833289,6.114526397947848,6.128550541062407,6.142574684176966,6.156598827291527,6.170622970406085,6.1846471135206444,6.198671256635203,6.212695399749763,6.2267195428643225,6.240743685978881,6.25476782909344,6.2687919722080006,6.282816115322559,6.296840258437118,6.310864401551679,6.324888544666237,6.338912687780796,6.352936830895356,6.366960974009915,6.3809851171244745,6.395009260239034,6.409033403353592,6.4230575464681525,6.437081689582712,6.45110583269727,6.46512997581183,6.47915411892639,6.493178262040948,6.507202405155508,6.521226548270068,6.535250691384626,6.549274834499186,6.563298977613744,6.5773231207283045,6.591347263842864,6.605371406957423,6.619395550071982,6.633419693186542,6.647443836301101,6.66146797941566,6.675492122530219,6.689516265644779,6.703540408759338,6.717564551873897,6.731588694988457,6.745612838103016,6.759636981217575,6.773661124332134,6.787685267446694,6.801709410561253,6.815733553675812,6.829757696790371,6.843781839904931,6.85780598301949,6.871830126134049,6.885854269248608,6.899878412363168,6.913902555477727,6.9279266985922865,6.941950841706845,6.955974984821405,6.9699991279359645,6.984023271050523,6.998047414165083,7.012071557279643,7.026095700394201,7.04011984350876,7.054143986623321,7.068168129737879,7.0821922728524385,7.096216415966998,7.110240559081557,7.1242647021961165,7.138288845310676,7.152312988425234,7.166337131539795,7.180361274654354,7.194385417768912,7.208409560883473,7.222433703998032,7.23645784711259,7.25048199022715,7.26450613334171,7.2785302764562685,7.292554419570828,7.306578562685386,7.3206027057999465,7.334626848914506,7.348650992029064,7.362675135143624,7.376699278258184,7.390723421372742,7.404747564487302,7.418771707601862,7.4327958507164205,7.44681999383098,7.460844136945539,7.4748682800600985,7.488892423174658,7.502916566289217,7.516940709403776,7.530964852518336,7.544988995632895,7.559013138747454,7.573037281862013,7.587061424976573,7.601085568091132,7.615109711205691,7.629133854320251,7.64315799743481,7.657182140549369,7.6712062836639285,7.685230426778488,7.699254569893047,7.713278713007607,7.727302856122165,7.741326999236725,7.755351142351285,7.769375285465843,7.7833994285804025,7.797423571694963,7.811447714809521,7.8254718579240805,7.839496001038639,7.853520144153199,7.867544287267759,7.881568430382317,7.895592573496877,7.909616716611437,7.923640859725995,7.937665002840554,7.951689145955115,7.965713289069674,7.9797374321842325,7.993761575298792,8.007785718413352,8.021809861527911,8.035834004642469,8.049858147757028,8.06388229087159,8.077906433986147,8.091930577100706,8.105954720215268,8.119978863329825,8.134003006444384,8.148027149558944,8.162051292673503,8.176075435788063,8.190099578902622,8.204123722017181,8.21814786513174,8.2321720082463,8.24619615136086,8.260220294475419,8.274244437589978,8.288268580704537,8.302292723819097,8.316316866933656,8.330341010048215,8.344365153162775,8.358389296277332,8.372413439391893,8.386437582506453,8.40046172562101,8.41448586873557,8.42851001185013,8.442534154964688,8.456558298079248,8.470582441193807,8.484606584308366,8.498630727422926,8.512654870537485,8.526679013652045,8.540703156766604,8.554727299881163,8.568751442995723,8.582775586110282,8.596799729224841,8.6108238723394,8.62484801545396,8.63887215856852,8.652896301683079,8.666920444797638,8.680944587912197,8.694968731026757,8.708992874141316,8.723017017255875,8.737041160370433,8.751065303484994,8.765089446599553,8.779113589714111,8.793137732828672,8.807161875943232,8.821186019057789,8.835210162172348,8.84923430528691,8.863258448401467,8.877282591516026,8.891306734630586,8.905330877745145,8.919355020859705,8.933379163974264,8.947403307088823,8.961427450203383,8.975451593317942,8.989475736432501,9.00349987954706,9.01752402266162,9.03154816577618,9.045572308890739,9.059596452005298,9.073620595119857,9.087644738234417,9.101668881348974,9.115693024463535,9.129717167578095,9.143741310692652,9.157765453807212,9.171789596921773,9.18581374003633,9.19983788315089,9.213862026265451,9.227886169380008,9.241910312494568,9.255934455609127,9.269958598723687,9.283982741838246,9.298006884952805,9.312031028067365,9.326055171181924,9.340079314296483,9.354103457411043,9.368127600525602,9.382151743640161,9.39617588675472,9.41020002986928,9.42422417298384,9.438248316098399,9.452272459212958,9.466296602327516,9.480320745442077,9.494344888556636,9.508369031671194,9.522393174785753,9.536417317900312,9.550441461014874,9.564465604129433,9.57848974724399,9.592513890358552,9.60653803347311,9.620562176587669,9.63458631970223,9.648610462816787,9.662634605931347,9.676658749045908,9.690682892160465,9.704707035275025,9.718731178389586,9.732755321504143,9.746779464618703,9.76080360773326,9.774827750847821,9.78885189396238,9.802876037076938,9.8169001801915,9.830924323306059,9.844948466420616,9.858972609535178,9.872996752649737,9.887020895764294,9.901045038878856,9.915069181993413,9.929093325107972,9.943117468222534,9.957141611337091,9.97116575445165,9.985189897566212,9.99921404068077,10.013238183795329,10.027262326909888,10.041286470024447,10.055310613139007,10.069334756253566,10.083358899368125,10.097383042482685,10.111407185597244,10.125431328711803,10.139455471826363,10.153479614940922,10.167503758055481,10.181527901170039,10.1955520442846,10.20957618739916,10.223600330513717,10.237624473628278,10.251648616742838,10.265672759857395,10.279696902971956,10.293721046086514,10.307745189201073,10.321769332315634,10.335793475430192,10.349817618544751,10.363841761659312,10.37786590477387,10.39189004788843,10.40591419100299,10.419938334117548,10.433962477232107,10.447986620346667,10.462010763461226,10.476034906575785,10.490059049690345,10.504083192804904,10.518107335919463,10.532131479034023,10.546155622148582,10.560179765263142,10.5742039083777,10.58822805149226,10.602252194606818,10.616276337721379,10.630300480835938,10.644324623950496,10.658348767065057,10.672372910179616,10.686397053294174,10.700421196408735,10.714445339523293,10.728469482637852,10.742493625752413,10.75651776886697,10.77054191198153,10.784566055096091,10.798590198210649,10.812614341325208,10.82663848443977,10.840662627554327,10.854686770668886,10.868710913783444,10.882735056898005,10.896759200012564,10.910783343127122,10.924807486241683,10.938831629356242,10.9528557724708,10.966879915585361,10.980904058699918,10.994928201814478,11.008952344929039,11.022976488043597,11.037000631158156,11.051024774272717,11.065048917387275,11.079073060501834,11.093097203616395,11.107121346730953,11.121145489845512,11.135169632960071,11.14919377607463,11.16321791918919,11.17724206230375,11.191266205418309,11.205290348532868,11.219314491647427,11.233338634761987,11.247362777876546,11.261386920991105,11.275411064105665,11.289435207220222,11.303459350334784,11.317483493449343,11.3315076365639,11.345531779678462,11.359555922793021,11.373580065907579,11.38760420902214,11.401628352136697,11.415652495251257,11.429676638365818,11.443700781480375,11.457724924594935,11.471749067709496,11.485773210824053,11.499797353938613,11.513821497053174,11.527845640167731,11.54186978328229,11.55589392639685,11.56991806951141,11.583942212625969,11.597966355740528,11.611990498855087,11.626014641969647,11.640038785084206,11.654062928198766,11.668087071313325,11.682111214427884,11.696135357542444,11.710159500657001,11.724183643771562,11.738207786886122,11.75223193000068,11.76625607311524,11.7802802162298,11.794304359344357,11.808328502458918,11.822352645573476,11.836376788688035,11.850400931802596,11.864425074917154,11.878449218031713,11.892473361146275,11.906497504260832,11.920521647375391,11.934545790489953,11.94856993360451,11.96259407671907,11.976618219833627,11.990642362948188,12.004666506062748,12.018690649177305,12.032714792291866,12.046738935406426,12.060763078520983,12.074787221635544,12.088811364750102,12.102835507864661,12.116859650979222,12.13088379409378,12.14490793720834,12.1589320803229,12.172956223437458,12.186980366552017,12.201004509666578,12.215028652781136,12.229052795895695,12.243076939010255,12.257101082124814,12.271125225239375,12.285149368353933,12.299173511468492,12.313197654583053,12.32722179769761,12.34124594081217,12.355270083926731,12.369294227041289,12.383318370155848,12.397342513270406,12.411366656384967,12.425390799499526,12.439414942614084,12.453439085728645,12.467463228843204,12.481487371957762,12.495511515072323,12.50953565818688,12.52355980130144,12.537583944416001,12.551608087530559,12.565632230645118,12.57965637375968,12.593680516874237,12.607704659988796,12.621728803103357,12.635752946217915,12.649777089332474,12.663801232447033,12.677825375561593,12.691849518676152,12.705873661790712,12.719897804905271,12.73392194801983,12.74794609113439,12.761970234248949,12.775994377363507,12.790018520478068,12.804042663592627,12.818066806707185,12.832090949821746,12.846115092936305,12.860139236050863,12.874163379165424,12.888187522279983,12.90221166539454,12.916235808509102,12.93025995162366,12.944284094738219,12.95830823785278,12.972332380967337,12.986356524081897,13.000380667196458,13.014404810311015,13.028428953425575,13.042453096540136,13.056477239654694,13.070501382769253,13.08452552588381,13.098549668998372,13.112573812112931,13.126597955227489,13.14062209834205,13.154646241456609,13.168670384571168,13.182694527685728,13.196718670800285,13.210742813914846,13.224766957029406,13.238791100143963,13.252815243258524,13.266839386373084,13.280863529487641,13.294887672602202,13.308911815716762,13.32293595883132,13.33696010194588,13.350984245060438,13.365008388174997,13.379032531289559,13.393056674404116,13.407080817518676,13.421104960633237,13.435129103747794,13.449153246862354,13.463177389976915,13.477201533091472,13.491225676206032,13.50524981932059,13.51927396243515,13.53329810554971,13.547322248664267,13.561346391778828,13.575370534893388,13.589394678007945,13.603418821122506,13.617442964237064,13.631467107351623,13.645491250466184,13.659515393580742,13.673539536695301,13.687563679809863,13.70158782292442,13.71561196603898,13.72963610915354,13.743660252268098,13.757684395382658,13.771708538497217,13.785732681611776,13.799756824726336,13.813780967840895,13.827805110955454,13.841829254070014,13.855853397184573,13.869877540299132,13.88390168341369,13.897925826528251,13.91194996964281,13.925974112757368,13.939998255871929,13.954022398986488,13.968046542101046,13.982070685215607,13.996094828330166,14.010118971444724,14.024143114559285,14.038167257673843,14.052191400788402,14.066215543902963,14.08023968701752,14.09426383013208,14.108287973246641,14.122312116361199,14.136336259475758,14.15036040259032,14.164384545704877,14.178408688819436,14.192432831933996,14.206456975048555,14.220481118163114,14.234505261277674,14.248529404392233,14.262553547506792,14.276577690621352,14.290601833735911,14.304625976850469,14.31865011996503,14.33267426307959,14.346698406194147,14.360722549308708,14.374746692423267,14.388770835537825,14.402794978652386,14.416819121766945,14.430843264881503,14.444867407996064,14.458891551110622,14.47291569422518,14.486939837339742,14.5009639804543,14.514988123568859,14.52901226668342,14.543036409797978,14.557060552912537,14.571084696027095,14.585108839141656,14.599132982256215,14.613157125370773,14.627181268485334,14.641205411599893,14.65522955471445,14.669253697829012,14.683277840943571,14.697301984058129,14.71132612717269,14.725350270287247,14.739374413401807,14.753398556516368,14.767422699630925,14.781446842745485,14.795470985860046,14.809495128974604,14.823519272089163,14.837543415203724,14.851567558318282,14.865591701432841,14.8796158445474,14.89363998766196,14.907664130776519,14.921688273891078,14.935712417005638,14.949736560120197,14.963760703234756,14.977784846349316,14.991808989463873,15.005833132578434,15.019857275692994,15.033881418807551,15.047905561922112,15.061929705036672,15.07595384815123,15.08997799126579,15.10400213438035,15.118026277494907,15.132050420609469,15.146074563724026,15.160098706838586,15.174122849953147,15.188146993067704,15.202171136182264,15.216195279296825,15.230219422411382,15.244243565525942,15.258267708640503,15.27229185175506,15.28631599486962,15.300340137984179,15.314364281098738,15.328388424213298,15.342412567327857,15.356436710442416,15.370460853556976,15.384484996671535,15.398509139786094,15.412533282900652,15.426557426015213,15.440581569129773,15.45460571224433,15.468629855358891,15.48265399847345,15.496678141588008,15.51070228470257,15.524726427817129,15.538750570931686,15.552774714046247,15.566798857160805,15.580823000275364,15.594847143389925,15.608871286504483,15.622895429619042,15.636919572733603,15.650943715848161,15.66496785896272,15.678992002077278,15.693016145191839,15.707040288306398,15.721064431420956,15.735088574535517,15.749112717650076,15.763136860764634,15.777161003879195,15.791185146993755,15.805209290108312,15.819233433222873,15.83325757633743,15.84728171945199,15.861305862566551,15.875330005681109,15.88935414879567,15.90337829191023,15.917402435024787,15.931426578139348,15.945450721253907,15.959474864368465,15.973499007483026,15.987523150597584,16.001547293712143,16.015571436826704,16.02959557994126,16.043619723055823,16.05764386617038,16.071668009284938,16.0856921523995,16.099716295514057,16.113740438628618,16.12776458174318,16.141788724857737,16.155812867972294,16.169837011086855,16.183861154201413,16.197885297315974,16.211909440430535,16.225933583545093,16.23995772665965,16.253981869774208,16.26800601288877,16.28203015600333,16.296054299117888,16.31007844223245,16.324102585347006,16.338126728461567,16.352150871576125,16.366175014690683,16.380199157805244,16.394223300919805,16.408247444034362,16.422271587148924,16.43629573026348,16.45031987337804,16.4643440164926,16.47836815960716,16.49239230272172,16.50641644583628,16.520440588950837,16.534464732065395,16.548488875179956,16.562513018294514,16.576537161409075,16.590561304523636,16.604585447638193,16.61860959075275,16.632633733867312,16.64665787698187,16.66068202009643,16.67470616321099,16.68873030632555,16.702754449440107,16.716778592554665,16.730802735669226,16.744826878783787,16.758851021898344,16.772875165012906,16.786899308127463,16.80092345124202,16.814947594356582,16.82897173747114,16.8429958805857,16.85702002370026,16.87104416681482,16.885068309929377,16.899092453043938,16.913116596158495,16.927140739273057,16.941164882387614,16.955189025502175,16.969213168616733,16.983237311731294,16.99726145484585,17.011285597960413,17.02530974107497,17.03933388418953,17.05335802730409,17.06738217041865,17.081406313533208,17.095430456647765,17.109454599762326,17.123478742876888,17.137502885991445,17.151527029106006,17.165551172220564,17.17957531533512,17.193599458449683,17.20762360156424,17.2216477446788,17.235671887793362,17.24969603090792,17.263720174022477,17.27774431713704,17.291768460251596,17.305792603366157,17.31981674648072,17.333840889595276,17.347865032709834,17.361889175824395,17.375913318938952,17.389937462053513,17.40396160516807,17.417985748282632,17.43200989139719,17.44603403451175,17.46005817762631,17.474082320740866,17.488106463855427,17.50213060696999,17.516154750084546,17.530178893199107,17.544203036313665,17.558227179428222,17.572251322542783,17.586275465657344,17.600299608771902,17.614323751886463,17.62834789500102,17.642372038115578,17.65639618123014,17.670420324344697,17.684444467459258,17.69846861057382,17.712492753688377,17.726516896802934,17.740541039917495,17.754565183032053,17.768589326146614,17.78261346926117,17.796637612375733,17.81066175549029,17.824685898604848,17.83871004171941,17.85273418483397,17.866758327948528,17.88078247106309,17.894806614177647,17.908830757292204,17.922854900406765,17.936879043521323,17.950903186635884,17.964927329750445,17.978951472865003,17.99297561597956,18.00699975909412,18.02102390220868,18.03504804532324,18.049072188437798,18.06309633155236,18.077120474666916,18.091144617781477,18.105168760896035,18.119192904010596,18.133217047125154,18.147241190239715,18.161265333354272,18.175289476468834,18.18931361958339,18.20333776269795,18.21736190581251,18.23138604892707,18.24541019204163,18.25943433515619,18.273458478270747,18.287482621385305,18.301506764499866,18.315530907614423,18.329555050728985,18.343579193843546,18.357603336958103,18.37162748007266,18.385651623187222,18.39967576630178,18.41369990941634,18.427724052530902,18.44174819564546,18.455772338760017,18.469796481874578,18.483820624989136,18.497844768103697,18.511868911218254,18.525893054332816,18.539917197447373,18.553941340561934,18.567965483676492,18.58198962679105,18.59601376990561,18.61003791302017,18.62406205613473,18.63808619924929,18.652110342363848,18.666134485478405,18.680158628592967,18.694182771707528,18.708206914822085,18.722231057936646,18.736255201051204,18.75027934416576,18.764303487280323,18.77832763039488,18.79235177350944,18.806375916624003,18.82040005973856,18.834424202853118,18.84844834596768,18.862472489082236,18.876496632196798,18.890520775311355,18.904544918425916,18.918569061540474,18.93259320465503,18.946617347769592,18.960641490884154,18.97466563399871,18.988689777113272,19.00271392022783,19.016738063342387,19.03076220645695,19.044786349571506,19.058810492686067],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149],"y":[0,0.02009263510257533,0.04018527020515066,0.06027790530772599,0.08037054041030132,0.10046317551287666,0.12055581061545198,0.14064844571802732,0.16074108082060265,0.18083371592317798,0.20092635102575332,0.22101898612832865,0.24111162123090396,0.2612042563334793,0.28129689143605463,0.30138952653862994,0.3214821616412053,0.34157479674378066,0.36166743184635597,0.38176006694893133,0.40185270205150664,0.42194533715408195,0.4420379722566573,0.4621306073592326,0.4822232424618079,0.5023158775643833,0.5224085126669586,0.542501147769534,0.5625937828721093,0.5826864179746846,0.6027790530772599,0.6228716881798353,0.6429643232824106,0.663056958384986,0.6831495934875613,0.7032422285901366,0.7233348636927119,0.7434274987952872,0.7635201338978627,0.783612769000438,0.8037054041030133,0.8237980392055886,0.8438906743081639,0.8639833094107393,0.8840759445133146,0.9041685796158899,0.9242612147184652,0.9443538498210405,0.9644464849236158,0.9845391200261914,1.0046317551287667,1.0247243902313419,1.0448170253339173,1.0649096604364925,1.085002295539068,1.1050949306416433,1.1251875657442185,1.145280200846794,1.1653728359493691,1.1854654710519446,1.2055581061545197,1.2256507412570954,1.2457433763596706,1.2658360114622458,1.2859286465648212,1.3060212816673964,1.326113916769972,1.3462065518725472,1.3662991869751226,1.3863918220776978,1.4064844571802733,1.4265770922828485,1.4466697273854239,1.4667623624879993,1.4868549975905745,1.50694763269315,1.5270402677957253,1.5471329028983005,1.567225538000876,1.5873181731034511,1.6074108082060266,1.6275034433086017,1.6475960784111772,1.6676887135137524,1.6877813486163278,1.707873983718903,1.7279666188214786,1.748059253924054,1.7681518890266292,1.7882445241292046,1.8083371592317798,1.8284297943343553,1.8485224294369305,1.8686150645395059,1.888707699642081,1.9088003347446565,1.9288929698472317,1.948985604949807,1.9690782400523827,1.989170875154958,2.0092635102575334,2.0293561453601083,2.0494487804626838,2.069541415565259,2.0896340506678346,2.1097266857704096,2.129819320872985,2.1499119559755604,2.170004591078136,2.1900972261807112,2.2101898612832866,2.230282496385862,2.250375131488437,2.2704677665910125,2.290560401693588,2.3106530367961633,2.3307456718987383,2.3508383070013137,2.370930942103889,2.3910235772064645,2.4111162123090395,2.4312088474116154,2.4513014825141908,2.4713941176167658,2.491486752719341,2.5115793878219166,2.5316720229244916,2.551764658027067,2.5718572931296424,2.591949928232218,2.612042563334793,2.632135198437368,2.652227833539944,2.672320468642519,2.6924131037450945,2.71250573884767,2.7325983739502453,2.7526910090528203,2.7727836441553957,2.792876279257971,2.8129689143605465,2.8330615494631215,2.853154184565697,2.8732468196682723,2.8933394547708478,2.913432089873423,2.9335247249759986,2.953617360078574,2.973709995181149,2.9938026302837244],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Volume Rule 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],"y":[0,0.1213945224657544,0.2427890449315088,0.36418356739726315,0.4855780898630176,0.606972612328772,0.7283671347945263,0.8497616572602807,0.9711561797260352,1.0925507021917895,1.213945224657544,1.3353397471232984,1.4567342695890526,1.5781287920548073,1.6995233145205615,1.820917836986316,1.9423123594520704,2.063706881917825,2.185101404383579,2.3064959268493332,2.427890449315088,2.5492849717808426,2.670679494246597,2.792074016712351,2.913468539178105,3.03486306164386,3.1562575841096145,3.2776521065753688,3.399046629041123,3.520441151506877,3.641835673972632,3.7632301964383865,3.8846247189041407,4.006019241369895,4.12741376383565,4.248808286301403,4.370202808767158,4.491597331232913,4.6129918536986665,4.734386376164421,4.855780898630176,4.9771754210959305,5.098569943561685,5.219964466027439,5.341358988493194,5.462753510958948,5.584148033424702,5.705542555890457,5.82693707835621,5.948331600821965,6.06972612328772,6.191120645753474,6.312515168219229,6.433909690684983,6.5553042131507375,6.676698735616492,6.798093258082246,6.919487780548001,7.040882303013754,7.162276825479509,7.283671347945264,7.4050658704110175,7.526460392876773,7.647854915342528,7.7692494378082815,7.890643960274036,8.01203848273979,8.133433005205545],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Professional Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59],"y":[0,0.05958720656392442,0.11917441312784884,0.17876161969177326,0.23834882625569767,0.29793603281962205,0.3575232393835465,0.41711044594747093,0.47669765251139534,0.5362848590753198,0.5958720656392441,0.6554592722031686,0.715046478767093,0.7746336853310174,0.8342208918949419,0.8938080984588662,0.9533953050227907,1.012982511586715,1.0725697181506395,1.1321569247145638,1.1917441312784882,1.2513313378424127,1.3109185444063371,1.3705057509702616,1.430092957534186,1.4896801640981103,1.5492673706620348,1.6088545772259593,1.6684417837898837,1.7280289903538082,1.7876161969177324,1.8472034034816571,1.9067906100455814,1.9663778166095056,2.02596502317343,2.0855522297373543,2.145139436301279,2.2047266428652033,2.2643138494291275,2.323901055993052,2.3834882625569764,2.443075469120901,2.5026626756848254,2.5622498822487496,2.6218370888126743,2.6814242953765985,2.7410115019405232,2.8005987085044475,2.860185915068372,2.9197731216322964,2.9793603281962207,3.0389475347601453,3.0985347413240696,3.1581219478879943,3.2177091544519185,3.2772963610158428,3.3368835675797675,3.3964707741436917,3.4560579807076164,3.5156451872715406],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">20.58%</td>
                <td>69.0%</td>
                <td>171</td>
                <td>1.27</td>
                <td>0.00</td>
                <td>12.13%</td>
                <td>58.9</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">19.06%</td>
                <td>62.5%</td>
                <td>1359</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>32.20%</td>
                <td>56.4</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">2.99%</td>
                <td>63.8%</td>
                <td>149</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>16.45%</td>
                <td>50.3</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">8.13%</td>
                <td>68.7%</td>
                <td>67</td>
                <td>1.27</td>
                <td>0.00</td>
                <td>5.61%</td>
                <td>44.0</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">3.52%</td>
                <td>62.7%</td>
                <td>59</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>5.55%</td>
                <td>37.9</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">8.42%</td>
                <td>82.4%</td>
                <td>17</td>
                <td>3.24</td>
                <td>0.00</td>
                <td>1.63%</td>
                <td>33.2</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">5.59%</td>
                <td>66.7%</td>
                <td>36</td>
                <td>1.37</td>
                <td>0.00</td>
                <td>5.39%</td>
                <td>33.0</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
