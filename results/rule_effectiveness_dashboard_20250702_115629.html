
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 11:56:29 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">11.36%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">7,941</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.0%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1.10</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>30.70%</strong></td>
                    <td>66.2%</td>
                    <td>337</td>
                    <td>1.14</td>
                    <td class="negative">15.31%</td>
                    <td class="positive"><strong>0.7343</strong></td>
                    <td class="negative">+0.88% / -1.49%</td>
                    <td>2h6m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>22.70%</strong></td>
                    <td>73.0%</td>
                    <td>63</td>
                    <td>1.76</td>
                    <td class="negative">5.18%</td>
                    <td class="positive"><strong>0.6819</strong></td>
                    <td class="negative">+0.92% / -1.33%</td>
                    <td>2h32m<br><small>(1.0m - 19h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>14.61%</strong></td>
                    <td>64.6%</td>
                    <td>99</td>
                    <td>1.24</td>
                    <td class="negative">11.06%</td>
                    <td class="positive"><strong>0.6032</strong></td>
                    <td class="negative">+0.88% / -1.37%</td>
                    <td>3h34m<br><small>(7.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>14.16%</strong></td>
                    <td>63.7%</td>
                    <td>328</td>
                    <td>1.07</td>
                    <td class="negative">19.30%</td>
                    <td class="positive"><strong>0.6704</strong></td>
                    <td class="negative">+0.88% / -1.44%</td>
                    <td>1h40m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>8.39%</strong></td>
                    <td>64.4%</td>
                    <td>188</td>
                    <td>1.07</td>
                    <td class="negative">20.71%</td>
                    <td class="positive"><strong>0.6022</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h20m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>8.37%</strong></td>
                    <td>65.9%</td>
                    <td>41</td>
                    <td>1.35</td>
                    <td class="negative">7.68%</td>
                    <td class="positive"><strong>0.5317</strong></td>
                    <td class="negative">+0.98% / -1.39%</td>
                    <td>1h54m<br><small>(4.0m - 15h6m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>6.92%</strong></td>
                    <td>64.8%</td>
                    <td>122</td>
                    <td>1.08</td>
                    <td class="negative">13.34%</td>
                    <td class="positive"><strong>0.5632</strong></td>
                    <td class="negative">+0.87% / -1.47%</td>
                    <td>2h49m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>4.40%</strong></td>
                    <td>75.0%</td>
                    <td>16</td>
                    <td>1.58</td>
                    <td class="neutral">4.56%</td>
                    <td class="positive"><strong>0.4756</strong></td>
                    <td class="negative">+0.90% / -1.37%</td>
                    <td>2h10m<br><small>(3.0m - 11h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>4.05%</strong></td>
                    <td>65.1%</td>
                    <td>43</td>
                    <td>1.15</td>
                    <td class="negative">7.59%</td>
                    <td class="positive"><strong>0.4635</strong></td>
                    <td class="negative">+0.82% / -1.52%</td>
                    <td>2h45m<br><small>(6.0m - 14h29m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>3.37%</strong></td>
                    <td>63.6%</td>
                    <td>11</td>
                    <td>1.47</td>
                    <td class="neutral">2.54%</td>
                    <td class="positive"><strong>0.4172</strong></td>
                    <td class="negative">+1.23% / -1.54%</td>
                    <td>2.7m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>2.81%</strong></td>
                    <td>80.0%</td>
                    <td>5</td>
                    <td>2.69</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>0.4649</strong></td>
                    <td class="negative">+0.86% / -1.47%</td>
                    <td>1h8m<br><small>(13.0m - 3h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>2.48%</strong></td>
                    <td>60.0%</td>
                    <td>30</td>
                    <td>1.12</td>
                    <td class="negative">7.76%</td>
                    <td class="positive"><strong>0.4335</strong></td>
                    <td class="negative">+0.85% / -1.30%</td>
                    <td>3h9m<br><small>(13.0m - 16h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>1.27%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.91% / 0.00%</td>
                    <td>5h23m</td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-11.28%</strong></td>
                    <td>45.8%</td>
                    <td>24</td>
                    <td>0.53</td>
                    <td class="negative">17.79%</td>
                    <td class="positive"><strong>0.1896</strong></td>
                    <td class="negative">+0.95% / -1.35%</td>
                    <td>1h50m<br><small>(13.0m - 7h10m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="negative"><strong>-19.84%</strong></td>
                    <td>61.7%</td>
                    <td>2976</td>
                    <td>0.99</td>
                    <td class="negative">61.69%</td>
                    <td class="positive"><strong>0.7258</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>2h31m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-22.45%</strong></td>
                    <td>61.4%</td>
                    <td>2248</td>
                    <td>0.99</td>
                    <td class="negative">55.00%</td>
                    <td class="positive"><strong>0.6982</strong></td>
                    <td class="negative">+0.88% / -1.43%</td>
                    <td>2h25m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="negative"><strong>-59.31%</strong></td>
                    <td>60.8%</td>
                    <td>1409</td>
                    <td>0.94</td>
                    <td class="negative">72.30%</td>
                    <td class="positive"><strong>0.5235</strong></td>
                    <td class="negative">+0.87% / -1.41%</td>
                    <td>2h34m<br><small>(1.0m - 32h36m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.27%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.91% / 0.00%</td>
                    <td>5h23m</td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>80.0%</strong></td>
                    <td class="positive">2.81%</td>
                    <td>5</td>
                    <td>2.69</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>0.4649</strong></td>
                    <td class="negative">+0.86% / -1.47%</td>
                    <td>1h8m<br><small>(13.0m - 3h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">4.40%</td>
                    <td>16</td>
                    <td>1.58</td>
                    <td class="neutral">4.56%</td>
                    <td class="positive"><strong>0.4756</strong></td>
                    <td class="negative">+0.90% / -1.37%</td>
                    <td>2h10m<br><small>(3.0m - 11h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>73.0%</strong></td>
                    <td class="positive">22.70%</td>
                    <td>63</td>
                    <td>1.76</td>
                    <td class="negative">5.18%</td>
                    <td class="positive"><strong>0.6819</strong></td>
                    <td class="negative">+0.92% / -1.33%</td>
                    <td>2h32m<br><small>(1.0m - 19h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>66.2%</strong></td>
                    <td class="positive">30.70%</td>
                    <td>337</td>
                    <td>1.14</td>
                    <td class="negative">15.31%</td>
                    <td class="positive"><strong>0.7343</strong></td>
                    <td class="negative">+0.88% / -1.49%</td>
                    <td>2h6m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>65.9%</strong></td>
                    <td class="positive">8.37%</td>
                    <td>41</td>
                    <td>1.35</td>
                    <td class="negative">7.68%</td>
                    <td class="positive"><strong>0.5317</strong></td>
                    <td class="negative">+0.98% / -1.39%</td>
                    <td>1h54m<br><small>(4.0m - 15h6m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>65.1%</strong></td>
                    <td class="positive">4.05%</td>
                    <td>43</td>
                    <td>1.15</td>
                    <td class="negative">7.59%</td>
                    <td class="positive"><strong>0.4635</strong></td>
                    <td class="negative">+0.82% / -1.52%</td>
                    <td>2h45m<br><small>(6.0m - 14h29m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>64.8%</strong></td>
                    <td class="positive">6.92%</td>
                    <td>122</td>
                    <td>1.08</td>
                    <td class="negative">13.34%</td>
                    <td class="positive"><strong>0.5632</strong></td>
                    <td class="negative">+0.87% / -1.47%</td>
                    <td>2h49m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>64.6%</strong></td>
                    <td class="positive">14.61%</td>
                    <td>99</td>
                    <td>1.24</td>
                    <td class="negative">11.06%</td>
                    <td class="positive"><strong>0.6032</strong></td>
                    <td class="negative">+0.88% / -1.37%</td>
                    <td>3h34m<br><small>(7.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>64.4%</strong></td>
                    <td class="positive">8.39%</td>
                    <td>188</td>
                    <td>1.07</td>
                    <td class="negative">20.71%</td>
                    <td class="positive"><strong>0.6022</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h20m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>63.7%</strong></td>
                    <td class="positive">14.16%</td>
                    <td>328</td>
                    <td>1.07</td>
                    <td class="negative">19.30%</td>
                    <td class="positive"><strong>0.6704</strong></td>
                    <td class="negative">+0.88% / -1.44%</td>
                    <td>1h40m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>63.6%</strong></td>
                    <td class="positive">3.37%</td>
                    <td>11</td>
                    <td>1.47</td>
                    <td class="neutral">2.54%</td>
                    <td class="positive"><strong>0.4172</strong></td>
                    <td class="negative">+1.23% / -1.54%</td>
                    <td>2.7m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>61.7%</strong></td>
                    <td class="negative">-19.84%</td>
                    <td>2976</td>
                    <td>0.99</td>
                    <td class="negative">61.69%</td>
                    <td class="positive"><strong>0.7258</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>2h31m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>61.4%</strong></td>
                    <td class="negative">-22.45%</td>
                    <td>2248</td>
                    <td>0.99</td>
                    <td class="negative">55.00%</td>
                    <td class="positive"><strong>0.6982</strong></td>
                    <td class="negative">+0.88% / -1.43%</td>
                    <td>2h25m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>60.8%</strong></td>
                    <td class="negative">-59.31%</td>
                    <td>1409</td>
                    <td>0.94</td>
                    <td class="negative">72.30%</td>
                    <td class="positive"><strong>0.5235</strong></td>
                    <td class="negative">+0.87% / -1.41%</td>
                    <td>2h34m<br><small>(1.0m - 32h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="positive">2.48%</td>
                    <td>30</td>
                    <td>1.12</td>
                    <td class="negative">7.76%</td>
                    <td class="positive"><strong>0.4335</strong></td>
                    <td class="negative">+0.85% / -1.30%</td>
                    <td>3h9m<br><small>(13.0m - 16h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="neutral"><strong>45.8%</strong></td>
                    <td class="negative">-11.28%</td>
                    <td>24</td>
                    <td>0.53</td>
                    <td class="negative">17.79%</td>
                    <td class="positive"><strong>0.1896</strong></td>
                    <td class="negative">+0.95% / -1.35%</td>
                    <td>1h50m<br><small>(13.0m - 7h10m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'Professional Rule 10: CCI Reve...', 'AI Rule 8: Momentum Divergence...', 'Rule 6: Stochastic Oversold Cr...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 7: Chaikin M...', 'AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Momentum Rule 2: Momentum Dive...', 'Rule 10: Volume Spike', 'Volume Rule 3: Dark Pool Activ...', 'Rule 2: Golden Cross', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'AI Rule 3: Smart Money Flow Di...'],
                y: [66.17210682492582, 64.64646464646465, 63.71951219512195, 64.36170212765957, 64.75409836065575, 73.01587301587301, 61.693548387096776, 61.43238434163701, 65.85365853658537, 65.11627906976744, 100.0, 75.0, 60.0, 80.0, 60.75230660042583],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'Professional Rule 10: CCI Reve...', 'AI Rule 8: Momentum Divergence...', 'Rule 6: Stochastic Oversold Cr...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 7: Chaikin M...', 'AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Momentum Rule 2: Momentum Dive...', 'Rule 10: Volume Spike', 'Volume Rule 3: Dark Pool Activ...', 'Rule 2: Golden Cross', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'AI Rule 3: Smart Money Flow Di...'],
                y: [np.float64(30.703521305006376), np.float64(14.60551085804512), np.float64(14.160603717813983), np.float64(8.38907238491699), np.float64(6.92206159235457), np.float64(22.702462946339217), np.float64(-19.844285911128157), np.float64(-22.448890204655616), np.float64(8.369112675996613), np.float64(4.050066017518126), np.float64(1.2664928541927947), np.float64(4.3967875769759415), np.float64(2.48079840090072), np.float64(2.8149453465135594), np.float64(-59.3074691376079)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
