
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 11 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 01:15:35</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">11</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">40.7%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">7.1%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">15.1%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">64.2%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">3,939</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["15.1%","13.7%","12.1%","11.8%","2.8%","2.6%","1.2%","10.0%","3.2%","3.8%"],"textposition":"auto","x":["Acad Rule 2: Mean Reversion Factor","AI Rule 3: Smart Money Flow Divergence","Rule 6: Stochastic Oversold Cross","AI Rule 10: Composite Sentiment Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Rule 7: Bollinger Band Bounce","Volatility Rule 2: ATR Expansion Signal","Volume Rule 5: Smart Money Volume","Advanced Rule 7: DMI ADX Filter","SMC Rule 5: Institutional Candle Pattern"],"y":[15.145075345860626,13.667620190409629,12.111143298087294,11.849334788563008,2.817098491696146,2.6319336923255032,1.2109347761705576,10.04988755953715,3.177316121762545,3.7994569103021347],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Acad Rule 2: Mean Reversion Factor","AI Rule 3: Smart Money Flow Divergence","Rule 6: Stochastic Oversold Cross","AI Rule 10: Composite Sentiment Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Rule 7: Bollinger Band Bounce","Volatility Rule 2: ATR Expansion Signal","Volume Rule 5: Smart Money Volume","Advanced Rule 7: DMI ADX Filter","SMC Rule 5: Institutional Candle Pattern"],"y":[496,359,93,695,143,452,143,54,22,15],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Acad Rule 2: Mean Reversion Factor","AI Rule 3: Smart Money Flow Divergence","Rule 6: Stochastic Oversold Cross","AI Rule 10: Composite Sentiment Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Rule 7: Bollinger Band Bounce","Volatility Rule 2: ATR Expansion Signal","Volume Rule 5: Smart Money Volume","Advanced Rule 7: DMI ADX Filter","SMC Rule 5: Institutional Candle Pattern"],"y":[289,199,52,411,86,277,90,25,12,9],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[15.145075345860626,13.667620190409629,12.111143298087294,11.849334788563008,2.817098491696146,2.6319336923255032,1.2109347761705576,10.04988755953715,3.177316121762545,3.7994569103021347,1.9126863625719852],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Acad Rule 2","AI Rule 3","Rule 6","AI Rule 10","Professional Rule 7","Rule 7","Volatility Rule 2","Volume Rule 5","Advanced Rule 7","SMC Rule 5","Volume Rule 3"],"textposition":"top center","x":[28.451009265410253,14.984394949776233,8.017239224747579,33.34140938314597,13.174637045670748,25.537657148699,14.749512547005638,4.796989420809617,4.392947197535993,3.7050771306257184,2.745704332069317],"y":[15.145075345860626,13.667620190409629,12.111143298087294,11.849334788563008,2.817098491696146,2.6319336923255032,1.2109347761705576,10.04988755953715,3.177316121762545,3.7994569103021347,1.9126863625719852],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["15.1%","12.8%","7.4%","3.8%"],"textposition":"auto","x":["ACADEMIC","AI_GENERATED","ORIGINAL","UNKNOWN"],"y":[15.145075345860626,12.758477489486317,7.371538495206399,3.8278967036734195],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["785","558","145","1106","229","729","233","79","34","24"],"textposition":"auto","x":["Acad Rule 2: Mean Reversion Factor","AI Rule 3: Smart Money Flow Divergence","Rule 6: Stochastic Oversold Cross","AI Rule 10: Composite Sentiment Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Rule 7: Bollinger Band Bounce","Volatility Rule 2: ATR Expansion Signal","Volume Rule 5: Smart Money Volume","Advanced Rule 7: DMI ADX Filter","SMC Rule 5: Institutional Candle Pattern"],"y":[785,558,145,1106,229,729,233,79,34,24],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Acad Rule 2","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785],"y":[0,0.019293089612561307,0.03858617922512261,0.05787926883768392,0.07717235845024523,0.09646544806280653,0.11575853767536784,0.13505162728792913,0.15434471690049045,0.17363780651305175,0.19293089612561307,0.2122239857381744,0.23151707535073568,0.250810164963297,0.27010325457585826,0.2893963441884196,0.3086894338009809,0.32798252341354217,0.3472756130261035,0.36656870263866487,0.38586179225122613,0.40515488186378745,0.4244479714763488,0.44374106108891004,0.46303415070147136,0.4823272403140327,0.501620329926594,0.5209134195391553,0.5402065091517165,0.5594995987642779,0.5787926883768392,0.5980857779894004,0.6173788676019618,0.6366719572145231,0.6559650468270843,0.6752581364396457,0.694551226052207,0.7138443156647684,0.7331374052773297,0.752430494889891,0.7717235845024523,0.7910166741150136,0.8103097637275749,0.8296028533401362,0.8488959429526975,0.8681890325652588,0.8874821221778201,0.9067752117903815,0.9260683014029427,0.945361391015504,0.9646544806280654,0.9839475702406266,1.003240659853188,1.0225337494657492,1.0418268390783105,1.061119928690872,1.080413018303433,1.0997061079159944,1.1189991975285558,1.138292287141117,1.1575853767536783,1.1768784663662397,1.1961715559788009,1.2154646455913622,1.2347577352039236,1.2540508248164848,1.2733439144290462,1.2926370040416075,1.3119300936541687,1.33122318326673,1.3505162728792914,1.3698093624918526,1.389102452104414,1.4083955417169753,1.4276886313295367,1.446981720942098,1.4662748105546595,1.4855679001672206,1.504860989779782,1.5241540793923434,1.5434471690049045,1.562740258617466,1.5820333482300273,1.6013264378425884,1.6206195274551498,1.6399126170677112,1.6592057066802723,1.6784987962928337,1.697791885905395,1.7170849755179562,1.7363780651305176,1.755671154743079,1.7749642443556402,1.7942573339682015,1.813550423580763,1.832843513193324,1.8521366028058854,1.8714296924184468,1.890722782031008,1.9100158716435693,1.9293089612561307,1.9486020508686919,1.9678951404812532,1.9871882300938146,2.006481319706376,2.025774409318937,2.0450674989314983,2.06436058854406,2.083653678156621,2.102946767769182,2.122239857381744,2.141532946994305,2.160826036606866,2.1801191262194277,2.199412215831989,2.21870530544455,2.2379983950571116,2.2572914846696728,2.276584574282234,2.2958776638947955,2.3151707535073567,2.334463843119918,2.3537569327324794,2.3730500223450406,2.3923431119576017,2.4116362015701633,2.4309292911827245,2.4502223807952856,2.4695154704078472,2.4888085600204084,2.5081016496329696,2.527394739245531,2.5466878288580923,2.5659809184706535,2.585274008083215,2.604567097695776,2.6238601873083374,2.643153276920899,2.66244636653346,2.6817394561460213,2.701032545758583,2.720325635371144,2.739618724983705,2.7589118145962668,2.778204904208828,2.797497993821389,2.8167910834339507,2.836084173046512,2.8553772626590734,2.874670352271635,2.893963441884196,2.9132565314967573,2.932549621109319,2.95184271072188,2.9711358003344412,2.990428889947003,3.009721979559564,3.029015069172125,3.0483081587846867,3.067601248397248,3.086894338009809,3.1061874276223707,3.125480517234932,3.144773606847493,3.1640666964600546,3.1833597860726157,3.202652875685177,3.2219459652977385,3.2412390549102996,3.2605321445228608,3.2798252341354224,3.2991183237479835,3.3184114133605447,3.3377045029731063,3.3569975925856674,3.3762906821982286,3.39558377181079,3.4148768614233513,3.4341699510359125,3.453463040648474,3.4727561302610352,3.4920492198735964,3.511342309486158,3.530635399098719,3.5499284887112803,3.569221578323842,3.588514667936403,3.607807757548964,3.627100847161526,3.646393936774087,3.665687026386648,3.6849801159992097,3.704273205611771,3.723566295224332,3.7428593848368936,3.7621524744494548,3.781445564062016,3.8007386536745775,3.8200317432871387,3.8393248328997,3.8586179225122614,3.8779110121248226,3.8972041017373837,3.9164971913499453,3.9357902809625065,3.9550833705750676,3.9743764601876292,3.9936695498001904,4.012962639412752,4.032255729025313,4.051548818637874,4.0708419082504355,4.090134997862997,4.109428087475559,4.12872117708812,4.148014266700681,4.167307356313242,4.186600445925803,4.205893535538364,4.2251866251509265,4.244479714763488,4.263772804376049,4.28306589398861,4.302358983601171,4.321652073213732,4.340945162826294,4.360238252438855,4.379531342051417,4.398824431663978,4.418117521276539,4.4374106108891,4.456703700501662,4.475996790114223,4.495289879726784,4.5145829693393456,4.533876058951907,4.553169148564468,4.57246223817703,4.591755327789591,4.611048417402152,4.630341507014713,4.6496345966272745,4.668927686239836,4.688220775852398,4.707513865464959,4.72680695507752,4.746100044690081,4.765393134302642,4.7846862239152035,4.8039793135277655,4.823272403140327,4.842565492752888,4.861858582365449,4.88115167197801,4.900444761590571,4.919737851203133,4.9390309408156945,4.958324030428256,4.977617120040817,4.996910209653378,5.016203299265939,5.035496388878501,5.054789478491062,5.0740825681036235,5.093375657716185,5.112668747328746,5.131961836941307,5.151254926553869,5.17054801616643,5.189841105778991,5.209134195391552,5.228427285004114,5.247720374616675,5.267013464229237,5.286306553841798,5.305599643454359,5.32489273306692,5.344185822679481,5.3634789122920425,5.382772001904605,5.402065091517166,5.421358181129727,5.440651270742288,5.459944360354849,5.47923744996741,5.498530539579972,5.5178236291925336,5.537116718805095,5.556409808417656,5.575702898030217,5.594995987642778,5.61428907725534,5.633582166867901,5.6528752564804625,5.672168346093024,5.691461435705586,5.710754525318147,5.730047614930709,5.74934070454327,5.768633794155831,5.787926883768392,5.8072199733809535,5.826513062993515,5.845806152606077,5.865099242218638,5.884392331831199,5.90368542144376,5.922978511056321,5.9422716006688825,5.9615646902814445,5.980857779894006,6.000150869506567,6.019443959119128,6.038737048731689,6.05803013834425,6.077323227956812,6.0966163175693735,6.115909407181935,6.135202496794496,6.154495586407057,6.173788676019618,6.19308176563218,6.212374855244741,6.2316679448573025,6.250961034469864,6.270254124082425,6.289547213694986,6.308840303307548,6.328133392920109,6.34742648253267,6.366719572145231,6.386012661757793,6.405305751370354,6.424598840982916,6.443891930595477,6.463185020208038,6.482478109820599,6.50177119943316,6.5210642890457216,6.540357378658284,6.559650468270845,6.578943557883406,6.598236647495967,6.617529737108528,6.636822826721089,6.656115916333651,6.675409005946213,6.694702095558774,6.713995185171335,6.733288274783896,6.752581364396457,6.771874454009019,6.79116754362158,6.8104606332341415,6.829753722846703,6.849046812459264,6.868339902071825,6.887632991684387,6.906926081296948,6.926219170909509,6.9455122605220705,6.964805350134632,6.984098439747193,7.003391529359755,7.022684618972316,7.041977708584877,7.061270798197438,7.0805638878099995,7.099856977422561,7.119150067035123,7.138443156647684,7.157736246260245,7.177029335872806,7.196322425485367,7.215615515097928,7.2349086047104905,7.254201694323052,7.273494783935613,7.292787873548174,7.312080963160735,7.331374052773296,7.350667142385858,7.369960231998419,7.389253321610981,7.408546411223542,7.427839500836103,7.447132590448664,7.466425680061226,7.485718769673787,7.505011859286348,7.5243049488989096,7.543598038511471,7.562891128124032,7.582184217736594,7.601477307349155,7.620770396961716,7.640063486574277,7.6593565761868385,7.6786496657994,7.697942755411962,7.717235845024523,7.736528934637084,7.755822024249645,7.775115113862206,7.7944082034747675,7.8137012930873295,7.832994382699891,7.852287472312452,7.871580561925013,7.890873651537574,7.910166741150135,7.929459830762697,7.9487529203752585,7.96804600998782,7.987339099600381,8.006632189212942,8.025925278825504,8.045218368438064,8.064511458050626,8.083804547663187,8.103097637275749,8.12239072688831,8.141683816500871,8.160976906113433,8.180269995725993,8.199563085338555,8.218856174951117,8.238149264563678,8.25744235417624,8.2767354437888,8.296028533401362,8.315321623013922,8.334614712626484,8.353907802239046,8.373200891851607,8.392493981464169,8.411787071076729,8.431080160689291,8.450373250301853,8.469666339914413,8.488959429526975,8.508252519139536,8.527545608752098,8.546838698364658,8.56613178797722,8.585424877589782,8.604717967202342,8.624011056814904,8.643304146427464,8.662597236040027,8.681890325652589,8.701183415265149,8.72047650487771,8.739769594490271,8.759062684102833,8.778355773715393,8.797648863327955,8.816941952940518,8.836235042553078,8.85552813216564,8.8748212217782,8.894114311390762,8.913407401003324,8.932700490615884,8.951993580228446,8.971286669841007,8.990579759453569,9.009872849066129,9.029165938678691,9.048459028291253,9.067752117903813,9.087045207516375,9.106338297128936,9.125631386741498,9.14492447635406,9.16421756596662,9.183510655579182,9.202803745191742,9.222096834804304,9.241389924416865,9.260683014029427,9.279976103641989,9.299269193254549,9.318562282867111,9.337855372479671,9.357148462092233,9.376441551704795,9.395734641317356,9.415027730929918,9.434320820542478,9.45361391015504,9.4729069997676,9.492200089380162,9.511493178992724,9.530786268605285,9.550079358217847,9.569372447830407,9.588665537442969,9.607958627055531,9.627251716668091,9.646544806280653,9.665837895893214,9.685130985505776,9.704424075118336,9.723717164730898,9.74301025434346,9.76230334395602,9.781596433568582,9.800889523181143,9.820182612793705,9.839475702406267,9.858768792018827,9.878061881631389,9.89735497124395,9.916648060856511,9.935941150469072,9.955234240081634,9.974527329694196,9.993820419306756,10.013113508919318,10.032406598531878,10.05169968814444,10.070992777757002,10.090285867369563,10.109578956982125,10.128872046594685,10.148165136207247,10.167458225819807,10.18675131543237,10.206044405044931,10.225337494657492,10.244630584270054,10.263923673882614,10.283216763495176,10.302509853107738,10.321802942720298,10.34109603233286,10.36038912194542,10.379682211557983,10.398975301170543,10.418268390783105,10.437561480395667,10.456854570008227,10.47614765962079,10.49544074923335,10.514733838845912,10.534026928458474,10.553320018071034,10.572613107683596,10.591906197296156,10.611199286908718,10.630492376521278,10.64978546613384,10.669078555746403,10.688371645358963,10.707664734971525,10.726957824584085,10.746250914196647,10.76554400380921,10.78483709342177,10.804130183034331,10.823423272646892,10.842716362259454,10.862009451872014,10.881302541484576,10.900595631097138,10.919888720709698,10.93918181032226,10.95847489993482,10.977767989547383,10.997061079159945,11.016354168772505,11.035647258385067,11.054940347997627,11.07423343761019,11.093526527222751,11.112819616835312,11.132112706447874,11.151405796060434,11.170698885672996,11.189991975285556,11.209285064898118,11.22857815451068,11.24787124412324,11.267164333735803,11.286457423348363,11.305750512960925,11.325043602573487,11.344336692186047,11.363629781798611,11.382922871411171,11.402215961023733,11.421509050636294,11.440802140248856,11.460095229861418,11.479388319473978,11.49868140908654,11.5179744986991,11.537267588311662,11.556560677924223,11.575853767536785,11.595146857149347,11.614439946761907,11.633733036374469,11.65302612598703,11.672319215599591,11.691612305212153,11.710905394824714,11.730198484437276,11.749491574049836,11.768784663662398,11.788077753274958,11.80737084288752,11.826663932500082,11.845957022112643,11.865250111725205,11.884543201337765,11.903836290950327,11.923129380562889,11.94242247017545,11.961715559788011,11.981008649400572,12.000301739013134,12.019594828625694,12.038887918238256,12.058181007850818,12.077474097463378,12.09676718707594,12.1160602766885,12.135353366301063,12.154646455913625,12.173939545526185,12.193232635138747,12.212525724751307,12.23181881436387,12.25111190397643,12.270404993588992,12.289698083201554,12.308991172814114,12.328284262426676,12.347577352039236,12.366870441651798,12.38616353126436,12.40545662087692,12.424749710489483,12.444042800102043,12.463335889714605,12.482628979327165,12.501922068939727,12.52121515855229,12.54050824816485,12.559801337777412,12.579094427389972,12.598387517002534,12.617680606615096,12.636973696227656,12.656266785840218,12.675559875452779,12.69485296506534,12.7141460546779,12.733439144290463,12.752732233903025,12.772025323515585,12.791318413128147,12.810611502740707,12.82990459235327,12.849197681965832,12.868490771578392,12.887783861190954,12.907076950803514,12.926370040416076,12.945663130028636,12.964956219641198,12.98424930925376,13.00354239886632,13.022835488478883,13.042128578091443,13.061421667704005,13.080714757316567,13.100007846929127,13.11930093654169,13.13859402615425,13.157887115766812,13.177180205379372,13.196473294991934,13.215766384604496,13.235059474217056,13.254352563829618,13.273645653442179,13.29293874305474,13.312231832667303,13.331524922279863,13.350818011892425,13.370111101504985,13.389404191117547,13.408697280730108,13.42799037034267,13.447283459955232,13.466576549567792,13.485869639180354,13.505162728792914,13.524455818405476,13.543748908018038,13.563041997630599,13.58233508724316,13.601628176855721,13.620921266468283,13.640214356080843,13.659507445693405,13.678800535305967,13.698093624918528,13.71738671453109,13.73667980414365,13.755972893756212,13.775265983368774,13.794559072981334,13.813852162593896,13.833145252206457,13.852438341819019,13.871731431431579,13.891024521044141,13.910317610656703,13.929610700269263,13.948903789881825,13.968196879494386,13.987489969106948,14.00678305871951,14.02607614833207,14.045369237944632,14.064662327557192,14.083955417169754,14.103248506782315,14.122541596394877,14.141834686007439,14.161127775619999,14.180420865232561,14.199713954845121,14.219007044457683,14.238300134070245,14.257593223682806,14.276886313295368,14.296179402907928,14.31547249252049,14.33476558213305,14.354058671745612,14.373351761358174,14.392644850970735,14.411937940583297,14.431231030195857,14.450524119808419,14.469817209420981,14.489110299033541,14.508403388646103,14.527696478258664,14.546989567871226,14.566282657483786,14.585575747096348,14.60486883670891,14.62416192632147,14.643455015934032,14.662748105546592,14.682041195159155,14.701334284771717,14.720627374384277,14.739920463996839,14.7592135536094,14.778506643221961,14.797799732834521,14.817092822447083,14.836385912059646,14.855679001672206,14.874972091284768,14.894265180897328,14.91355827050989,14.932851360122452,14.952144449735012,14.971437539347574,14.990730628960135,15.010023718572697,15.029316808185257,15.048609897797819,15.067902987410381,15.087196077022941,15.106489166635503,15.125782256248064,15.145075345860626],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 3","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558],"y":[0,0.024493942993565644,0.04898788598713129,0.07348182898069694,0.09797577197426258,0.12246971496782821,0.14696365796139388,0.17145760095495952,0.19595154394852515,0.2204454869420908,0.24493942993565643,0.26943337292922204,0.29392731592278776,0.31842125891635337,0.34291520190991903,0.36740914490348464,0.3919030878970503,0.4163970308906159,0.4408909738841816,0.46538491687774725,0.48987885987131286,0.5143728028648785,0.5388667458584441,0.5633606888520098,0.5878546318455755,0.6123485748391411,0.6368425178327067,0.6613364608262724,0.6858304038198381,0.7103243468134036,0.7348182898069693,0.759312232800535,0.7838061757941006,0.8083001187876663,0.8327940617812318,0.8572880047747975,0.8817819477683632,0.9062758907619288,0.9307698337554945,0.95526377674906,0.9797577197426257,1.0042516627361913,1.028745605729757,1.0532395487233228,1.0777334917168881,1.102227434710454,1.1267213777040197,1.1512153206975853,1.175709263691151,1.2002032066847164,1.2246971496782821,1.249191092671848,1.2736850356654135,1.298178978658979,1.3226729216525448,1.3471668646461104,1.3716608076396761,1.3961547506332417,1.4206486936268072,1.445142636620373,1.4696365796139386,1.4941305226075043,1.51862446560107,1.5431184085946354,1.5676123515882012,1.5921062945817668,1.6166002375753326,1.641094180568898,1.6655881235624637,1.6900820665560294,1.714576009549595,1.7390699525431608,1.7635638955367263,1.788057838530292,1.8125517815238577,1.8370457245174232,1.861539667510989,1.8860336105045545,1.91052755349812,1.9350214964916859,1.9595154394852514,1.9840093824788172,2.0085033254723825,2.032997268465948,2.057491211459514,2.0819851544530796,2.1064790974466456,2.1309730404402107,2.1554669834337763,2.1799609264273423,2.204454869420908,2.228948812414474,2.2534427554080394,2.2779366984016045,2.3024306413951705,2.326924584388736,2.351418527382302,2.3759124703758676,2.4004064133694327,2.4249003563629987,2.4493942993565643,2.47388824235013,2.498382185343696,2.522876128337261,2.547370071330827,2.5718640143243925,2.596357957317958,2.620851900311524,2.6453458433050896,2.669839786298655,2.6943337292922207,2.7188276722857863,2.7433216152793523,2.767815558272918,2.7923095012664834,2.816803444260049,2.8412973872536145,2.8657913302471805,2.890285273240746,2.914779216234311,2.939273159227877,2.9637671022214427,2.9882610452150087,3.0127549882085742,3.03724893120214,3.0617428741957053,3.086236817189271,3.110730760182837,3.1352247031764024,3.159718646169968,3.1842125891635336,3.208706532157099,3.233200475150665,3.2576944181442307,3.282188361137796,3.3066823041313618,3.3311762471249273,3.3556701901184933,3.380164133112059,3.4046580761056244,3.42915201909919,3.4536459620927555,3.4781399050863215,3.5026338480798866,3.5271277910734526,3.551621734067018,3.576115677060584,3.6006096200541493,3.6251035630477153,3.6495975060412813,3.6740914490348464,3.698585392028412,3.723079335021978,3.747573278015543,3.772067221009109,3.7965611640026746,3.82105510699624,3.8455490499898057,3.8700429929833717,3.8945369359769377,3.919030878970503,3.9435248219640684,3.9680187649576344,3.9925127079511995,4.017006650944765,4.0415005939383315,4.065994536931896,4.090488479925463,4.114982422919028,4.139476365912594,4.163970308906159,4.188464251899725,4.212958194893291,4.237452137886856,4.2619460808804215,4.286440023873988,4.310933966867553,4.335427909861119,4.359921852854685,4.38441579584825,4.408909738841816,4.433403681835381,4.457897624828948,4.482391567822512,4.506885510816079,4.531379453809644,4.555873396803209,4.5803673397967755,4.604861282790341,4.629355225783907,4.653849168777472,4.678343111771038,4.702837054764604,4.727330997758169,4.751824940751735,4.776318883745301,4.8008128267388654,4.825306769732432,4.849800712725997,4.874294655719563,4.8987885987131286,4.923282541706694,4.94777648470026,4.972270427693825,4.996764370687392,5.021258313680957,5.045752256674522,5.070246199668088,5.094740142661654,5.119234085655219,5.143728028648785,5.1682219716423505,5.192715914635916,5.217209857629482,5.241703800623048,5.266197743616613,5.290691686610179,5.315185629603745,5.33967957259731,5.364173515590876,5.388667458584441,5.413161401578007,5.4376553445715725,5.462149287565138,5.4866432305587045,5.511137173552269,5.535631116545836,5.560125059539401,5.584619002532967,5.609112945526532,5.633606888520098,5.658100831513664,5.682594774507229,5.7070887175007945,5.731582660494361,5.756076603487926,5.780570546481492,5.805064489475058,5.829558432468622,5.854052375462189,5.878546318455754,5.903040261449321,5.927534204442885,5.952028147436451,5.976522090430017,6.001016033423582,6.0255099764171485,6.050003919410714,6.07449786240428,6.098991805397845,6.123485748391411,6.147979691384977,6.172473634378542,6.196967577372107,6.221461520365674,6.2459554633592385,6.270449406352805,6.2949433493463705,6.319437292339936,6.343931235333502,6.368425178327067,6.392919121320633,6.417413064314198,6.441907007307765,6.46640095030133,6.490894893294895,6.515388836288461,6.539882779282027,6.564376722275592,6.588870665269158,6.6133646082627235,6.637858551256289,6.662352494249855,6.686846437243421,6.711340380236987,6.735834323230551,6.760328266224118,6.784822209217683,6.809316152211249,6.833810095204814,6.85830403819838,6.882797981191946,6.907291924185511,6.931785867179077,6.956279810172643,6.980773753166208,7.005267696159773,7.02976163915334,7.054255582146905,7.07874952514047,7.103243468134036,7.127737411127602,7.152231354121168,7.176725297114734,7.201219240108299,7.225713183101865,7.250207126095431,7.274701069088995,7.299195012082563,7.323688955076127,7.348182898069693,7.372676841063259,7.397170784056824,7.4216647270503895,7.446158670043956,7.4706526130375215,7.495146556031086,7.519640499024653,7.544134442018218,7.568628385011783,7.593122328005349,7.617616270998915,7.64211021399248,7.666604156986047,7.6910980999796115,7.715592042973178,7.7400859859667435,7.764579928960308,7.7890738719538755,7.81356781494744,7.838061757941006,7.862555700934572,7.887049643928137,7.911543586921702,7.936037529915269,7.960531472908834,7.985025415902399,8.009519358895966,8.03401330188953,8.058507244883096,8.083001187876663,8.107495130870229,8.131989073863792,8.15648301685736,8.180976959850925,8.205470902844489,8.229964845838056,8.254458788831622,8.278952731825187,8.303446674818753,8.327940617812319,8.352434560805884,8.37692850379945,8.401422446793015,8.425916389786583,8.450410332780146,8.474904275773712,8.49939821876728,8.523892161760843,8.548386104754409,8.572880047747976,8.597373990741541,8.621867933735105,8.646361876728673,8.670855819722238,8.695349762715802,8.71984370570937,8.744337648702935,8.7688315916965,8.793325534690066,8.817819477683631,8.842313420677199,8.866807363670762,8.891301306664328,8.915795249657895,8.94028919265146,8.964783135645025,8.989277078638592,9.013771021632158,9.038264964625721,9.062758907619289,9.087252850612854,9.111746793606418,9.136240736599985,9.160734679593551,9.185228622587115,9.209722565580682,9.234216508574248,9.258710451567813,9.283204394561379,9.307698337554944,9.33219228054851,9.356686223542075,9.381180166535641,9.405674109529208,9.430168052522772,9.454661995516338,9.479155938509905,9.50364988150347,9.528143824497034,9.552637767490602,9.577131710484167,9.601625653477731,9.626119596471298,9.650613539464864,9.67510748245843,9.699601425451995,9.72409536844556,9.748589311439126,9.773083254432692,9.797577197426257,9.822071140419823,9.846565083413388,9.871059026406954,9.89555296940052,9.920046912394085,9.94454085538765,9.969034798381218,9.993528741374783,10.018022684368347,10.042516627361914,10.06701057035548,10.091504513349044,10.115998456342611,10.140492399336177,10.164986342329742,10.189480285323308,10.213974228316873,10.238468171310439,10.262962114304004,10.28745605729757,10.311950000291136,10.336443943284701,10.360937886278267,10.385431829271832,10.4099257722654,10.434419715258963,10.458913658252529,10.483407601246096,10.50790154423966,10.532395487233225,10.556889430226793,10.581383373220358,10.605877316213924,10.63037125920749,10.654865202201055,10.67935914519462,10.703853088188186,10.728347031181752,10.752840974175317,10.777334917168883,10.801828860162448,10.826322803156014,10.85081674614958,10.875310689143145,10.899804632136712,10.924298575130276,10.948792518123842,10.973286461117409,10.997780404110973,11.022274347104538,11.046768290098106,11.071262233091671,11.095756176085235,11.120250119078802,11.144744062072368,11.169238005065933,11.193731948059499,11.218225891053065,11.24271983404663,11.267213777040196,11.291707720033761,11.316201663027329,11.340695606020892,11.365189549014458,11.389683492008025,11.414177435001589,11.438671377995155,11.463165320988722,11.487659263982286,11.512153206975851,11.536647149969419,11.561141092962984,11.585635035956548,11.610128978950115,11.63462292194368,11.659116864937245,11.683610807930812,11.708104750924377,11.732598693917943,11.757092636911509,11.781586579905074,11.806080522898641,11.830574465892205,11.85506840888577,11.879562351879338,11.904056294872902,11.928550237866467,11.953044180860035,11.9775381238536,12.002032066847164,12.026526009840731,12.051019952834297,12.07551389582786,12.100007838821428,12.124501781814994,12.14899572480856,12.173489667802125,12.19798361079569,12.222477553789256,12.246971496782821,12.271465439776387,12.295959382769954,12.320453325763518,12.344947268757084,12.369441211750651,12.393935154744215,12.41842909773778,12.442923040731348,12.467416983724913,12.491910926718477,12.516404869712044,12.54089881270561,12.565392755699174,12.589886698692741,12.614380641686306,12.638874584679872,12.663368527673438,12.687862470667003,12.712356413660569,12.736850356654134,12.7613442996477,12.785838242641265,12.810332185634831,12.834826128628396,12.859320071621964,12.88381401461553,12.908307957609093,12.93280190060266,12.957295843596226,12.98178978658979,13.006283729583357,13.030777672576923,13.055271615570486,13.079765558564054,13.10425950155762,13.128753444551185,13.15324738754475,13.177741330538316,13.202235273531882,13.226729216525447,13.251223159519013,13.275717102512578,13.300211045506144,13.32470498849971,13.349198931493275,13.373692874486842,13.398186817480406,13.422680760473973,13.447174703467539,13.471668646461103,13.49616258945467,13.520656532448236,13.545150475441801,13.569644418435367,13.594138361428932,13.618632304422498,13.643126247416063,13.667620190409629],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145],"y":[0,0.08352512619370547,0.16705025238741095,0.2505753785811164,0.3341005047748219,0.41762563096852734,0.5011507571622328,0.5846758833559383,0.6682010095496438,0.7517261357433492,0.8352512619370547,0.9187763881307602,1.0023015143244656,1.0858266405181711,1.1693517667118767,1.252876892905582,1.3364020190992876,1.4199271452929931,1.5034522714866985,1.5869773976804042,1.6705025238741094,1.7540276500678151,1.8375527762615205,1.921077902455226,2.004603028648931,2.088128154842637,2.1716532810363423,2.255178407230048,2.3387035334237534,2.422228659617459,2.505753785811164,2.58927891200487,2.672804038198575,2.7563291643922807,2.8398542905859863,2.923379416779692,3.006904542973397,3.0904296691671025,3.1739547953608085,3.257479921554513,3.3410050477482187,3.4245301739419247,3.5080553001356303,3.5915804263293354,3.675105552523041,3.7586306787167465,3.842155804910452,3.925680931104157,4.009206057297862,4.092731183491568,4.176256309685274,4.2597814358789785,4.3433065620726845,4.4268316882663905,4.510356814460096,4.593881940653801,4.677407066847507,4.760932193041212,4.844457319234918,4.927982445428623,5.011507571622328,5.095032697816034,5.17855782400974,5.262082950203444,5.34560807639715,5.429133202590856,5.512658328784561,5.5961834549782665,5.6797085811719725,5.763233707365678,5.846758833559384,5.930283959753089,6.013809085946794,6.0973342121405,6.180859338334205,6.264384464527911,6.347909590721617,6.431434716915321,6.514959843109026,6.598484969302732,6.682010095496437,6.765535221690143,6.849060347883849,6.9325854740775545,7.0161106002712605,7.099635726464965,7.183160852658671,7.266685978852376,7.350211105046082,7.433736231239787,7.517261357433493,7.600786483627198,7.684311609820904,7.767836736014608,7.851361862208314,7.9348869884020194,8.018412114595725,8.10193724078943,8.185462366983137,8.268987493176843,8.352512619370549,8.436037745564253,8.519562871757957,8.603087997951663,8.686613124145369,8.770138250339075,8.853663376532781,8.937188502726485,9.020713628920191,9.104238755113895,9.187763881307601,9.271289007501307,9.354814133695013,9.43833925988872,9.521864386082424,9.60538951227613,9.688914638469836,9.77243976466354,9.855964890857246,9.939490017050952,10.023015143244656,10.106540269438362,10.190065395632068,10.273590521825774,10.35711564801948,10.440640774213184,10.524165900406889,10.607691026600595,10.6912161527943,10.774741278988007,10.858266405181713,10.941791531375417,11.025316657569123,11.108841783762827,11.192366909956533,11.275892036150239,11.359417162343945,11.44294228853765,11.526467414731355,11.609992540925061,11.693517667118767,11.777042793312472,11.860567919506178,11.944093045699882,12.027618171893588,12.111143298087294],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106],"y":[0,0.010713684257290242,0.021427368514580485,0.03214105277187072,0.04285473702916097,0.053568421286451215,0.06428210554374145,0.07499578980103169,0.08570947405832194,0.09642315831561218,0.10713684257290243,0.11785052683019266,0.1285642110874829,0.13927789534477314,0.14999157960206339,0.16070526385935363,0.17141894811664388,0.1821326323739341,0.19284631663122437,0.20356000088851461,0.21427368514580486,0.22498736940309508,0.23570105366038532,0.24641473791767557,0.2571284221749658,0.26784210643225603,0.2785557906895463,0.2892694749468365,0.29998315920412677,0.310696843461417,0.32141052771870726,0.3321242119759975,0.34283789623328775,0.353551580490578,0.3642652647478682,0.3749789490051585,0.38569263326244874,0.396406317519739,0.40712000177702923,0.4178336860343195,0.4285473702916097,0.4392610545488999,0.44997473880619016,0.4606884230634804,0.47140210732077065,0.4821157915780609,0.49282947583535114,0.5035431600926413,0.5142568443499316,0.5249705286072218,0.5356842128645121,0.5463978971218023,0.5571115813790926,0.5678252656363828,0.578538949893673,0.5892526341509633,0.5999663184082535,0.6106800026655438,0.621393686922834,0.6321073711801243,0.6428210554374145,0.6535347396947048,0.664248423951995,0.6749621082092853,0.6856757924665755,0.6963894767238658,0.707103160981156,0.7178168452384461,0.7285305294957364,0.7392442137530266,0.749957898010317,0.7606715822676071,0.7713852665248975,0.7820989507821876,0.792812635039478,0.8035263192967681,0.8142400035540585,0.8249536878113486,0.835667372068639,0.8463810563259291,0.8570947405832194,0.8678084248405096,0.8785221090977998,0.8892357933550901,0.8999494776123803,0.9106631618696706,0.9213768461269608,0.9320905303842509,0.9428042146415413,0.9535178988988314,0.9642315831561218,0.9749452674134119,0.9856589516707023,0.9963726359279924,1.0070863201852827,1.017800004442573,1.0285136886998631,1.0392273729571533,1.0499410572144436,1.0606547414717338,1.0713684257290241,1.0820821099863145,1.0927957942436046,1.103509478500895,1.1142231627581851,1.1249368470154755,1.1356505312727656,1.146364215530056,1.157077899787346,1.1677915840446365,1.1785052683019266,1.189218952559217,1.199932636816507,1.2106463210737974,1.2213600053310876,1.232073689588378,1.242787373845668,1.2535010581029584,1.2642147423602486,1.274928426617539,1.285642110874829,1.2963557951321194,1.3070694793894095,1.3177831636467,1.32849684790399,1.3392105321612804,1.3499242164185705,1.3606379006758607,1.371351584933151,1.3820652691904411,1.3927789534477315,1.4034926377050216,1.414206321962312,1.4249200062196021,1.4356336904768923,1.4463473747341826,1.4570610589914728,1.4677747432487631,1.4784884275060532,1.4892021117633434,1.499915796020634,1.510629480277924,1.5213431645352142,1.5320568487925044,1.542770533049795,1.553484217307085,1.5641979015643752,1.5749115858216653,1.585625270078956,1.596338954336246,1.6070526385935362,1.6177663228508268,1.628480007108117,1.639193691365407,1.6499073756226972,1.6606210598799878,1.671334744137278,1.682048428394568,1.6927621126518582,1.7034757969091485,1.7141894811664389,1.724903165423729,1.7356168496810191,1.7463305339383095,1.7570442181955996,1.76775790245289,1.7784715867101801,1.7891852709674705,1.7998989552247606,1.8106126394820508,1.8213263237393411,1.8320400079966315,1.8427536922539216,1.8534673765112117,1.8641810607685019,1.8748947450257925,1.8856084292830826,1.8963221135403727,1.9070357977976629,1.9177494820549534,1.9284631663122436,1.9391768505695337,1.9498905348268238,1.9606042190841144,1.9713179033414046,1.9820315875986947,1.9927452718559848,2.0034589561132754,2.0141726403705653,2.0248863246278557,2.035600008885146,2.0463136931424364,2.0570273773997263,2.0677410616570167,2.0784547459143066,2.0891684301715974,2.0998821144288873,2.1105957986861776,2.1213094829434676,2.1320231672007584,2.1427368514580483,2.1534505357153386,2.164164219972629,2.1748779042299193,2.1855915884872092,2.1963052727444996,2.20701895700179,2.2177326412590803,2.2284463255163702,2.2391600097736606,2.249873694030951,2.2605873782882413,2.271301062545531,2.2820147468028216,2.292728431060112,2.3034421153174023,2.314155799574692,2.3248694838319826,2.335583168089273,2.346296852346563,2.357010536603853,2.3677242208611435,2.378437905118434,2.389151589375724,2.399865273633014,2.4105789578903045,2.421292642147595,2.432006326404885,2.442720010662175,2.453433694919465,2.464147379176756,2.4748610634340458,2.485574747691336,2.496288431948626,2.507002116205917,2.5177158004632068,2.528429484720497,2.539143168977787,2.549856853235078,2.5605705374923677,2.571284221749658,2.581997906006948,2.592711590264239,2.6034252745215287,2.614138958778819,2.624852643036109,2.6355663272934,2.6462800115506897,2.65699369580798,2.66770738006527,2.6784210643225608,2.6891347485798507,2.699848432837141,2.7105621170944314,2.7212758013517213,2.7319894856090117,2.742703169866302,2.7534168541235924,2.7641305383808823,2.7748442226381727,2.785557906895463,2.7962715911527534,2.8069852754100433,2.8176989596673336,2.828412643924624,2.8391263281819143,2.8498400124392043,2.8605536966964946,2.8712673809537845,2.8819810652110753,2.8926947494683652,2.9034084337256556,2.9141221179829455,2.9248358022402363,2.9355494864975262,2.9462631707548166,2.9569768550121065,2.9676905392693973,2.9784042235266868,2.9891179077839776,2.999831592041268,3.010545276298558,3.021258960555848,3.0319726448131386,3.0426863290704285,3.0534000133277193,3.0641136975850087,3.0748273818422995,3.08554106609959,3.09625475035688,3.10696843461417,3.1176821188714605,3.1283958031287504,3.139109487386041,3.1498231716433307,3.1605368559006215,3.171250540157912,3.1819642244152018,3.192677908672492,3.2033915929297825,3.2141052771870724,3.2248189614443628,3.2355326457016536,3.246246329958943,3.256960014216234,3.2676736984735237,3.278387382730814,3.2891010669881044,3.2998147512453944,3.3105284355026847,3.3212421197599755,3.331955804017265,3.342669488274556,3.3533831725318453,3.364096856789136,3.3748105410464264,3.3855242253037163,3.3962379095610067,3.406951593818297,3.417665278075587,3.4283789623328778,3.4390926465901672,3.449806330847458,3.4605200151047484,3.4712336993620383,3.4819473836193287,3.492661067876619,3.503374752133909,3.5140884363911993,3.524802120648489,3.53551580490578,3.5462294891630703,3.5569431734203603,3.5676568576776506,3.578370541934941,3.589084226192231,3.5997979104495212,3.610511594706811,3.6212252789641015,3.6319389632213923,3.6426526474786822,3.6533663317359726,3.664080015993263,3.674793700250553,3.685507384507843,3.696221068765133,3.7069347530224235,3.7176484372797143,3.7283621215370037,3.7390758057942945,3.749789490051585,3.760503174308875,3.771216858566165,3.7819305428234555,3.7926442270807454,3.8033579113380362,3.8140715955953257,3.8247852798526165,3.835498964109907,3.846212648367197,3.856926332624487,3.8676400168817775,3.8783537011390674,3.8890673853963578,3.8997810696536477,3.9104947539109385,3.921208438168229,3.9319221224255188,3.942635806682809,3.9533494909400995,3.9640631751973894,3.9747768594546797,3.9854905437119696,3.99620422796926,4.006917912226551,4.017631596483841,4.028345280741131,4.039058964998421,4.049772649255711,4.060486333513002,4.071200017770292,4.081913702027582,4.092627386284873,4.103341070542163,4.114054754799453,4.124768439056743,4.135482123314033,4.146195807571324,4.156909491828613,4.167623176085904,4.178336860343195,4.189050544600485,4.199764228857775,4.210477913115065,4.221191597372355,4.231905281629646,4.242618965886935,4.253332650144226,4.264046334401517,4.274760018658807,4.2854737029160965,4.296187387173387,4.306901071430677,4.317614755687967,4.328328439945258,4.339042124202548,4.349755808459839,4.360469492717129,4.3711831769744185,4.381896861231709,4.392610545488999,4.403324229746289,4.41403791400358,4.42475159826087,4.435465282518161,4.4461789667754505,4.4568926510327405,4.467606335290031,4.478320019547321,4.489033703804611,4.499747388061902,4.510461072319192,4.521174756576483,4.531888440833772,4.542602125091062,4.553315809348353,4.564029493605643,4.574743177862933,4.585456862120224,4.596170546377514,4.606884230634805,4.617597914892094,4.628311599149384,4.639025283406675,4.649738967663965,4.660452651921255,4.671166336178546,4.681880020435836,4.692593704693126,4.703307388950416,4.714021073207706,4.724734757464997,4.735448441722287,4.746162125979577,4.756875810236868,4.767589494494158,4.778303178751448,4.7890168630087375,4.799730547266028,4.810444231523319,4.821157915780609,4.831871600037899,4.84258528429519,4.85329896855248,4.86401265280977,4.87472633706706,4.88544002132435,4.896153705581641,4.90686738983893,4.917581074096221,4.928294758353512,4.939008442610802,4.9497221268680915,4.960435811125382,4.971149495382672,4.981863179639963,4.992576863897252,5.003290548154543,5.014004232411834,5.024717916669124,5.0354316009264135,5.046145285183704,5.056858969440994,5.067572653698284,5.078286337955574,5.089000022212865,5.099713706470156,5.110427390727446,5.1211410749847355,5.131854759242026,5.142568443499316,5.153282127756606,5.163995812013896,5.174709496271187,5.185423180528478,5.1961368647857675,5.206850549043057,5.217564233300348,5.228277917557638,5.238991601814928,5.249705286072218,5.260418970329509,5.2711326545868,5.281846338844089,5.292560023101379,5.30327370735867,5.31398739161596,5.32470107587325,5.33541476013054,5.346128444387831,5.3568421286451215,5.367555812902411,5.378269497159701,5.388983181416992,5.399696865674282,5.410410549931572,5.421124234188863,5.431837918446153,5.442551602703443,5.4532652869607325,5.463978971218023,5.474692655475314,5.485406339732604,5.496120023989894,5.506833708247185,5.517547392504475,5.528261076761765,5.5389747610190545,5.549688445276345,5.560402129533636,5.571115813790926,5.581829498048216,5.592543182305507,5.603256866562797,5.613970550820087,5.6246842350773765,5.635397919334667,5.646111603591958,5.656825287849248,5.667538972106538,5.678252656363829,5.688966340621119,5.6996800248784085,5.710393709135698,5.721107393392989,5.73182107765028,5.742534761907569,5.75324844616486,5.763962130422151,5.774675814679441,5.7853894989367305,5.79610318319402,5.806816867451311,5.817530551708601,5.828244235965891,5.838957920223182,5.849671604480473,5.8603852887377625,5.8710989729950525,5.881812657252342,5.892526341509633,5.903240025766923,5.913953710024213,5.924667394281504,5.935381078538795,5.9460947627960845,5.9568084470533735,5.967522131310664,5.978235815567955,5.988949499825245,5.999663184082536,6.010376868339827,6.021090552597116,6.0318042368544065,6.042517921111696,6.053231605368987,6.063945289626277,6.074658973883567,6.085372658140857,6.096086342398148,6.1068000266554385,6.117513710912728,6.1282273951700175,6.138941079427308,6.149654763684599,6.160368447941889,6.17108213219918,6.181795816456471,6.19250950071376,6.2032231849710495,6.21393686922834,6.224650553485631,6.235364237742921,6.246077922000211,6.256791606257501,6.267505290514792,6.278218974772082,6.288932659029372,6.299646343286661,6.310360027543952,6.321073711801243,6.331787396058533,6.342501080315824,6.353214764573114,6.3639284488304035,6.3746421330876935,6.385355817344984,6.396069501602275,6.406783185859565,6.417496870116854,6.428210554374145,6.438924238631436,6.4496379228887255,6.460351607146016,6.471065291403307,6.481778975660596,6.492492659917886,6.503206344175177,6.513920028432468,6.524633712689758,6.5353473969470475,6.546061081204337,6.556774765461628,6.567488449718919,6.578202133976209,6.588915818233498,6.599629502490789,6.6103431867480795,6.621056871005369,6.63177055526266,6.642484239519951,6.65319792377724,6.66391160803453,6.674625292291821,6.685338976549112,6.6960526608064015,6.7067663450636905,6.717480029320981,6.728193713578272,6.738907397835562,6.749621082092853,6.760334766350142,6.771048450607433,6.7817621348647235,6.792475819122013,6.803189503379304,6.813903187636594,6.824616871893884,6.835330556151174,6.846044240408465,6.8567579246657555,6.867471608923045,6.8781852931803344,6.888898977437625,6.899612661694916,6.910326345952206,6.921040030209497,6.931753714466786,6.942467398724077,6.9531810829813665,6.963894767238657,6.974608451495948,6.985322135753238,6.996035820010528,7.006749504267818,7.017463188525109,7.0281768727823986,7.038890557039689,7.049604241296978,7.060317925554269,7.07103160981156,7.08174529406885,7.092458978326141,7.1031726625834315,7.1138863468407205,7.12460003109801,7.135313715355301,7.146027399612592,7.156741083869882,7.167454768127171,7.178168452384462,7.188882136641753,7.1995958208990425,7.210309505156333,7.221023189413622,7.231736873670913,7.242450557928203,7.253164242185494,7.263877926442785,7.2745916107000745,7.2853052949573645,7.296018979214654,7.306732663471945,7.317446347729236,7.328160031986526,7.338873716243815,7.349587400501106,7.3603010847583965,7.371014769015686,7.381728453272977,7.392442137530266,7.403155821787557,7.413869506044847,7.424583190302138,7.435296874559429,7.4460105588167185,7.4567242430740075,7.467437927331298,7.478151611588589,7.488865295845879,7.49957898010317,7.510292664360459,7.52100634861775,7.53172003287504,7.54243371713233,7.553147401389621,7.563861085646911,7.574574769904201,7.585288454161491,7.596002138418782,7.6067158226760725,7.617429506933362,7.628143191190651,7.638856875447942,7.649570559705233,7.660284243962523,7.670997928219814,7.681711612477103,7.692425296734394,7.7031389809916835,7.713852665248974,7.724566349506265,7.735280033763555,7.745993718020845,7.756707402278135,7.767421086535426,7.7781347707927155,7.788848455050006,7.799562139307295,7.810275823564586,7.820989507821877,7.831703192079167,7.842416876336458,7.853130560593747,7.8638442448510375,7.874557929108327,7.885271613365618,7.895985297622909,7.906698981880199,7.917412666137488,7.928126350394779,7.9388400346520696,7.9495537189093595,7.96026740316665,7.970981087423939,7.98169477168123,7.99240845593852,8.003122140195812,8.013835824453102,8.02454950871039,8.035263192967681,8.045976877224971,8.056690561482261,8.067404245739553,8.078117929996843,8.088831614254133,8.099545298511423,8.110258982768713,8.120972667026004,8.131686351283294,8.142400035540584,8.153113719797874,8.163827404055164,8.174541088312456,8.185254772569746,8.195968456827035,8.206682141084325,8.217395825341615,8.228109509598905,8.238823193856197,8.249536878113487,8.260250562370777,8.270964246628067,8.281677930885357,8.292391615142648,8.303105299399938,8.313818983657226,8.324532667914518,8.335246352171808,8.345960036429098,8.35667372068639,8.36738740494368,8.37810108920097,8.38881477345826,8.39952845771555,8.41024214197284,8.42095582623013,8.43166951048742,8.44238319474471,8.453096879002,8.463810563259292,8.474524247516582,8.48523793177387,8.495951616031162,8.506665300288452,8.517378984545742,8.528092668803033,8.538806353060323,8.549520037317613,8.560233721574903,8.570947405832193,8.581661090089485,8.592374774346775,8.603088458604065,8.613802142861354,8.624515827118644,8.635229511375934,8.645943195633226,8.656656879890516,8.667370564147806,8.678084248405096,8.688797932662386,8.699511616919677,8.710225301176967,8.720938985434257,8.731652669691547,8.742366353948837,8.753080038206129,8.763793722463419,8.774507406720707,8.785221090977998,8.795934775235288,8.806648459492578,8.81736214374987,8.82807582800716,8.83878951226445,8.84950319652174,8.86021688077903,8.870930565036321,8.881644249293611,8.892357933550901,8.903071617808191,8.913785302065481,8.924498986322773,8.935212670580063,8.94592635483735,8.956640039094642,8.967353723351932,8.978067407609222,8.988781091866514,8.999494776123804,9.010208460381094,9.020922144638384,9.031635828895674,9.042349513152965,9.053063197410255,9.063776881667543,9.074490565924835,9.085204250182125,9.095917934439415,9.106631618696706,9.117345302953995,9.128058987211286,9.138772671468576,9.149486355725866,9.160200039983158,9.170913724240448,9.181627408497738,9.192341092755028,9.203054777012317,9.21376846126961,9.224482145526899,9.235195829784187,9.245909514041479,9.256623198298769,9.267336882556059,9.27805056681335,9.28876425107064,9.29947793532793,9.31019161958522,9.32090530384251,9.331618988099802,9.342332672357092,9.353046356614382,9.363760040871671,9.374473725128961,9.385187409386251,9.395901093643543,9.406614777900831,9.417328462158123,9.428042146415413,9.438755830672703,9.449469514929994,9.460183199187284,9.470896883444574,9.481610567701864,9.492324251959154,9.503037936216446,9.513751620473736,9.524465304731024,9.535178988988315,9.545892673245605,9.556606357502895,9.567320041760187,9.578033726017475,9.588747410274767,9.599461094532057,9.610174778789347,9.620888463046638,9.631602147303928,9.642315831561218,9.653029515818508,9.663743200075798,9.67445688433309,9.68517056859038,9.695884252847668,9.70659793710496,9.71731162136225,9.72802530561954,9.73873898987683,9.74945267413412,9.76016635839141,9.7708800426487,9.78159372690599,9.792307411163282,9.803021095420572,9.81373477967786,9.824448463935152,9.835162148192442,9.845875832449732,9.856589516707023,9.867303200964312,9.878016885221603,9.888730569478893,9.899444253736183,9.910157937993475,9.920871622250765,9.931585306508055,9.942298990765345,9.953012675022634,9.963726359279926,9.974440043537216,9.985153727794504,9.995867412051796,10.006581096309086,10.017294780566376,10.028008464823667,10.038722149080956,10.049435833338247,10.060149517595537,10.070863201852827,10.081576886110119,10.092290570367409,10.103004254624699,10.113717938881988,10.124431623139278,10.135145307396568,10.14585899165386,10.156572675911148,10.16728636016844,10.17800004442573,10.18871372868302,10.199427412940311,10.2101410971976,10.220854781454891,10.231568465712181,10.242282149969471,10.252995834226763,10.263709518484053,10.27442320274134,10.285136886998632,10.295850571255922,10.306564255513212,10.317277939770504,10.327991624027792,10.338705308285084,10.349418992542374,10.360132676799664,10.370846361056955,10.381560045314245,10.392273729571535,10.402987413828825,10.413701098086115,10.424414782343407,10.435128466600696,10.445842150857985,10.456555835115276,10.467269519372566,10.477983203629856,10.488696887887148,10.499410572144436,10.510124256401728,10.520837940659018,10.531551624916307,10.5422653091736,10.552978993430889,10.563692677688177,10.574406361945469,10.585120046202759,10.595833730460049,10.60654741471734,10.617261098974629,10.62797478323192,10.63868846748921,10.6494021517465,10.660115836003792,10.67082952026108,10.681543204518372,10.692256888775661,10.702970573032951,10.713684257290243,10.724397941547533,10.735111625804821,10.745825310062113,10.756538994319403,10.767252678576693,10.777966362833984,10.788680047091272,10.799393731348564,10.810107415605854,10.820821099863144,10.831534784120436,10.842248468377726,10.852962152635016,10.863675836892305,10.874389521149595,10.885103205406885,10.895816889664177,10.906530573921465,10.917244258178757,10.927957942436047,10.938671626693337,10.949385310950628,10.960098995207916,10.970812679465208,10.981526363722498,10.992240047979788,11.00295373223708,11.01366741649437,11.024381100751658,11.03509478500895,11.04580846926624,11.05652215352353,11.06723583778082,11.077949522038109,11.0886632062954,11.09937689055269,11.11009057480998,11.120804259067272,11.13151794332456,11.142231627581852,11.152945311839142,11.163658996096432,11.174372680353724,11.185086364611013,11.195800048868302,11.206513733125593,11.217227417382883,11.227941101640173,11.238654785897465,11.249368470154753,11.260082154412045,11.270795838669335,11.281509522926624,11.292223207183916,11.302936891441204,11.313650575698496,11.324364259955786,11.335077944213076,11.345791628470366,11.356505312727657,11.367218996984946,11.377932681242237,11.388646365499527,11.399360049756817,11.410073734014109,11.420787418271397,11.431501102528689,11.442214786785978,11.452928471043268,11.46364215530056,11.47435583955785,11.485069523815138,11.49578320807243,11.50649689232972,11.51721057658701,11.527924260844301,11.53863794510159,11.549351629358881,11.560065313616171,11.570778997873461,11.581492682130753,11.59220636638804,11.602920050645332,11.613633734902622,11.624347419159912,11.635061103417202,11.645774787674494,11.656488471931782,11.667202156189074,11.677915840446364,11.688629524703654,11.699343208960945,11.710056893218233,11.720770577475525,11.731484261732815,11.742197945990105,11.752911630247397,11.763625314504685,11.774338998761975,11.785052683019266,11.795766367276556,11.806480051533846,11.817193735791138,11.827907420048426,11.838621104305718,11.849334788563008],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Professional Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229],"y":[0,0.012301740138411118,0.024603480276822236,0.03690522041523335,0.04920696055364447,0.061508700692055586,0.0738104408304667,0.08611218096887782,0.09841392110728894,0.11071566124570006,0.12301740138411117,0.1353191415225223,0.1476208816609334,0.15992262179934455,0.17222436193775564,0.18452610207616676,0.19682784221457789,0.20912958235298904,0.22143132249140013,0.23373306262981125,0.24603480276822234,0.2583365429066335,0.2706382830450446,0.28294002318345574,0.2952417633218668,0.307543503460278,0.3198452435986891,0.33214698373710017,0.3444487238755113,0.3567504640139224,0.36905220415233353,0.3813539442907447,0.39365568442915577,0.4059574245675669,0.41825916470597807,0.43056090484438914,0.44286264498280026,0.4551643851212113,0.4674661252596225,0.4797678653980336,0.4920696055364447,0.5043713456748559,0.516673085813267,0.5289748259516781,0.5412765660900892,0.5535783062285002,0.5658800463669115,0.5781817865053226,0.5904835266437336,0.6027852667821448,0.615087006920556,0.627388747058967,0.6396904871973782,0.6519922273357893,0.6642939674742003,0.6765957076126116,0.6888974477510226,0.7011991878894337,0.7135009280278448,0.7258026681662559,0.7381044083046671,0.7504061484430783,0.7627078885814894,0.7750096287199004,0.7873113688583115,0.7996131089967227,0.8119148491351338,0.824216589273545,0.8365183294119561,0.8488200695503672,0.8611218096887783,0.8734235498271894,0.8857252899656005,0.8980270301040117,0.9103287702424226,0.9226305103808339,0.934932250519245,0.9472339906576561,0.9595357307960672,0.9718374709344785,0.9841392110728894,0.9964409512113006,1.0087426913497117,1.0210444314881229,1.033346171626534,1.045647911764945,1.0579496519033562,1.0702513920417673,1.0825531321801785,1.0948548723185896,1.1071566124570005,1.1194583525954118,1.131760092733823,1.144061832872234,1.1563635730106452,1.1686653131490563,1.1809670532874672,1.1932687934258785,1.2055705335642897,1.2178722737027008,1.230174013841112,1.2424757539795228,1.254777494117934,1.2670792342563453,1.2793809743947564,1.2916827145331675,1.3039844546715786,1.3162861948099895,1.3285879349484007,1.340889675086812,1.3531914152252231,1.3654931553636342,1.3777948955020451,1.3900966356404563,1.4023983757788674,1.4147001159172785,1.4270018560556896,1.4393035961941008,1.4516053363325119,1.463907076470923,1.4762088166093341,1.4885105567477455,1.5008122968861566,1.5131140370245677,1.5254157771629788,1.53771751730139,1.5500192574398008,1.562320997578212,1.574622737716623,1.5869244778550342,1.5992262179934453,1.6115279581318565,1.6238296982702676,1.636131438408679,1.64843317854709,1.6607349186855012,1.6730366588239123,1.6853383989623232,1.6976401391007343,1.7099418792391454,1.7222436193775565,1.7345453595159677,1.7468470996543788,1.75914883979279,1.771450579931201,1.7837523200696124,1.7960540602080235,1.8083558003464346,1.8206575404848453,1.8329592806232566,1.8452610207616678,1.8575627609000789,1.86986450103849,1.8821662411769011,1.8944679813153122,1.9067697214537234,1.9190714615921345,1.9313732017305458,1.943674941868957,1.9559766820073676,1.9682784221457787,1.98058016228419,1.9928819024226012,2.005183642561012,2.0174853826994235,2.029787122837835,2.0420888629762457,2.054390603114657,2.066692343253068,2.0789940833914793,2.09129582352989,2.103597563668301,2.1158993038067124,2.1282010439451233,2.1405027840835347,2.1528045242219456,2.165106264360357,2.1774080044987683,2.189709744637179,2.2020114847755905,2.214313224914001,2.2266149650524123,2.2389167051908236,2.2512184453292345,2.263520185467646,2.275821925606057,2.288123665744468,2.300425405882879,2.3127271460212904,2.3250288861597013,2.3373306262981126,2.3496323664365235,2.3619341065749344,2.3742358467133458,2.386537586851757,2.398839326990168,2.4111410671285793,2.4234428072669902,2.4357445474054016,2.4480462875438125,2.460348027682224,2.4726497678206347,2.4849515079590456,2.497253248097457,2.509554988235868,2.521856728374279,2.5341584685126906,2.5464602086511015,2.558761948789513,2.5710636889279237,2.583365429066335,2.595667169204746,2.6079689093431573,2.620270649481568,2.632572389619979,2.6448741297583904,2.6571758698968013,2.6694776100352127,2.681779350173624,2.694081090312035,2.7063828304504463,2.718684570588857,2.7309863107272685,2.7432880508656794,2.7555897910040903,2.7678915311425016,2.7801932712809125,2.792495011419324,2.804796751557735,2.817098491696146],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Acad Rule 2: Mean Reversion Factor</td>
                <td>ACADEMIC</td>
                <td class="positive">15.15%</td>
                <td>63.3%</td>
                <td>785</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>28.45%</td>
                <td>55.1</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 3: Smart Money Flow Divergence</td>
                <td>AI_GENERATED</td>
                <td class="positive">13.67%</td>
                <td>64.3%</td>
                <td>558</td>
                <td>1.05</td>
                <td>0.00</td>
                <td>14.98%</td>
                <td>54.8</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">12.11%</td>
                <td>64.8%</td>
                <td>145</td>
                <td>1.17</td>
                <td>0.00</td>
                <td>8.02%</td>
                <td>54.3</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">11.85%</td>
                <td>62.9%</td>
                <td>1106</td>
                <td>1.02</td>
                <td>0.00</td>
                <td>33.34%</td>
                <td>53.6</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">2.82%</td>
                <td>62.4%</td>
                <td>229</td>
                <td>1.02</td>
                <td>0.00</td>
                <td>13.17%</td>
                <td>49.9</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">2.63%</td>
                <td>62.1%</td>
                <td>729</td>
                <td>1.01</td>
                <td>0.00</td>
                <td>25.54%</td>
                <td>49.7</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Volatility Rule 2: ATR Expansion Signal</td>
                <td>UNKNOWN</td>
                <td class="positive">1.21%</td>
                <td>61.4%</td>
                <td>233</td>
                <td>1.01</td>
                <td>0.00</td>
                <td>14.75%</td>
                <td>48.9</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">10.05%</td>
                <td>68.4%</td>
                <td>79</td>
                <td>1.29</td>
                <td>0.00</td>
                <td>4.80%</td>
                <td>48.2</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Advanced Rule 7: DMI ADX Filter</td>
                <td>UNKNOWN</td>
                <td class="positive">3.18%</td>
                <td>64.7%</td>
                <td>34</td>
                <td>1.20</td>
                <td>0.00</td>
                <td>4.39%</td>
                <td>30.9</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>SMC Rule 5: Institutional Candle Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">3.80%</td>
                <td>66.7%</td>
                <td>24</td>
                <td>1.39</td>
                <td>0.00</td>
                <td>3.71%</td>
                <td>28.7</td>
            </tr>
            
            <tr>
                <td>11</td>
                <td>Volume Rule 3: Dark Pool Activity</td>
                <td>UNKNOWN</td>
                <td class="positive">1.91%</td>
                <td>64.7%</td>
                <td>17</td>
                <td>1.24</td>
                <td>0.00</td>
                <td>2.75%</td>
                <td>25.3</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
