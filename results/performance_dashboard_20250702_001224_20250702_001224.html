
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 17 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 00:12:24</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">17</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">38.6%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">12.1%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">44.7%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">63.3%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">8,037</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["44.7%","36.9%","27.8%","25.9%","21.5%","7.1%","9.1%","5.1%","7.0%","4.9%"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","AI Rule 3: Smart Money Flow Divergence","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 8: Momentum Divergence Reversal","Rule 28: Volume Breakout","Professional Rule 7: Chaikin Money Flow Reversal","Professional Rule 10: CCI Reversal Enhanced","Volatility Rule 2: ATR Expansion Signal"],"y":[44.66682365028413,36.931957058926436,27.824114311927083,25.92882184034674,21.52794101954303,7.057058775990164,9.103930920360733,5.068806276779061,7.001867067751507,4.852539353677752],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","AI Rule 3: Smart Money Flow Divergence","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 8: Momentum Divergence Reversal","Rule 28: Volume Breakout","Professional Rule 7: Chaikin Money Flow Reversal","Professional Rule 10: CCI Reversal Enhanced","Volatility Rule 2: ATR Expansion Signal"],"y":[1113,796,509,775,287,140,171,118,62,187],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","AI Rule 3: Smart Money Flow Divergence","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 8: Momentum Divergence Reversal","Rule 28: Volume Breakout","Professional Rule 7: Chaikin Money Flow Reversal","Professional Rule 10: CCI Reversal Enhanced","Volatility Rule 2: ATR Expansion Signal"],"y":[603,425,275,424,145,77,110,67,30,125],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[44.66682365028413,36.931957058926436,27.824114311927083,25.92882184034674,21.52794101954303,7.057058775990164,9.103930920360733,5.068806276779061,7.001867067751507,4.852539353677752,2.0867078477275354,1.1786307858325271,2.2274973610304696,1.2692720585476853,6.011256541596897,1.9881881306003852,1.098597389526134],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["AI Rule 10","Rule 7","AI Rule 3","Ext Rule 6","Prof Rule 7","AI Rule 8","Rule 28","Professional Rule 7","Professional Rule 10","Volatility Rule 2","Rule 10","Rule 6","Volume Rule 4","Rule 27","Volume Rule 5","Momentum Rule 2","Advanced Rule 7"],"textposition":"top center","x":[23.754105629922954,10.302293779448538,11.363149666495534,15.566786241210167,5.37180864851428,7.333885741907897,6.010604774434702,4.6095150819708195,3.1713154415171556,5.759582459247839,11.31649082644239,5.444336592807394,7.041837366318779,10.548874542611475,2.2195712724271766,4.502148596081202,2.6360364007666224],"y":[44.66682365028413,36.931957058926436,27.824114311927083,25.92882184034674,21.52794101954303,7.057058775990164,9.103930920360733,5.068806276779061,7.001867067751507,4.852539353677752,2.0867078477275354,1.1786307858325271,2.2274973610304696,1.2692720585476853,6.011256541596897,1.9881881306003852,1.098597389526134],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["26.5%","10.1%","6.8%","21.5%"],"textposition":"auto","x":["AI_GENERATED","ORIGINAL","UNKNOWN","PROFESSIONAL"],"y":[26.51599891273379,10.114099734278984,6.772196745163618,21.52794101954303],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["1716","1221","784","1199","432","217","281","185","92","312"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","AI Rule 3: Smart Money Flow Divergence","Ext Rule 6: Fibonacci Support Confluence","Prof Rule 7: Mean Reversion Volatility Filter","AI Rule 8: Momentum Divergence Reversal","Rule 28: Volume Breakout","Professional Rule 7: Chaikin Money Flow Reversal","Professional Rule 10: CCI Reversal Enhanced","Volatility Rule 2: ATR Expansion Signal"],"y":[1716,1221,784,1199,432,217,281,185,92,312],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716],"y":[0,0.026029617511820587,0.052059235023641175,0.07808885253546176,0.10411847004728235,0.13014808755910295,0.15617770507092352,0.1822073225827441,0.2082369400945647,0.23426655760638532,0.2602961751182059,0.28632579263002644,0.31235541014184703,0.3383850276536677,0.3644146451654882,0.39044426267730886,0.4164738801891294,0.44250349770095,0.46853311521277063,0.49456273272459117,0.5205923502364118,0.5466219677482324,0.5726515852600529,0.5986812027718735,0.6247108202836941,0.6507404377955147,0.6767700553073354,0.702799672819156,0.7288292903309764,0.7548589078427971,0.7808885253546177,0.8069181428664381,0.8329477603782588,0.8589773778900794,0.8850069954019,0.9110366129137206,0.9370662304255413,0.9630958479373617,0.9891254654491823,1.015155082961003,1.0411847004728236,1.067214317984644,1.0932439354964647,1.1192735530082853,1.1453031705201058,1.1713327880319264,1.197362405543747,1.2233920230555677,1.2494216405673881,1.2754512580792088,1.3014808755910294,1.32751049310285,1.3535401106146707,1.3795697281264911,1.405599345638312,1.4316289631501322,1.4576585806619529,1.4836881981737735,1.5097178156855942,1.5357474331974148,1.5617770507092354,1.5878066682210557,1.6138362857328763,1.639865903244697,1.6658955207565176,1.6919251382683382,1.7179547557801589,1.7439843732919795,1.7700139908038,1.7960436083156206,1.8220732258274412,1.848102843339262,1.8741324608510825,1.9001620783629032,1.9261916958747234,1.952221313386544,1.9782509308983647,2.0042805484101853,2.030310165922006,2.0563397834338266,2.0823694009456473,2.1083990184574675,2.134428635969288,2.1604582534811088,2.1864878709929294,2.21251748850475,2.2385471060165707,2.264576723528391,2.2906063410402115,2.316635958552032,2.342665576063853,2.3686951935756735,2.394724811087494,2.420754428599315,2.4467840461111354,2.4728136636229556,2.4988432811347763,2.524872898646597,2.5509025161584176,2.576932133670238,2.602961751182059,2.6289913686938795,2.6550209862057,2.681050603717521,2.7070802212293414,2.733109838741162,2.7591394562529823,2.785169073764803,2.811198691276624,2.8372283087884442,2.8632579263002644,2.8892875438120855,2.9153171613239057,2.941346778835727,2.967376396347547,2.993406013859368,3.0194356313711883,3.0454652488830085,3.0714948663948296,3.09752448390665,3.123554101418471,3.149583718930291,3.1756133364421113,3.2016429539539324,3.2276725714657526,3.2537021889775737,3.279731806489394,3.305761424001215,3.331791041513035,3.357820659024856,3.3838502765366765,3.409879894048497,3.4359095115603178,3.461939129072138,3.487968746583959,3.5139983640957793,3.5400279816076,3.5660575991194206,3.592087216631241,3.618116834143062,3.6441464516548825,3.6701760691667027,3.696205686678524,3.722235304190344,3.748264921702165,3.7742945392139853,3.8003241567258064,3.8263537742376266,3.8523833917494468,3.878413009261268,3.904442626773088,3.930472244284909,3.9565018617967294,3.9825314793085504,4.008561096820371,4.034590714332191,4.060620331844012,4.086649949355833,4.112679566867653,4.138709184379474,4.1647388018912945,4.190768419403115,4.216798036914935,4.2428276544267565,4.268857271938576,4.294886889450397,4.3209165069622175,4.346946124474038,4.372975741985859,4.3990053594976795,4.4250349770095,4.451064594521321,4.477094212033141,4.503123829544962,4.529153447056782,4.555183064568603,4.581212682080423,4.607242299592245,4.633271917104064,4.659301534615886,4.685331152127706,4.711360769639526,4.737390387151347,4.763420004663168,4.789449622174988,4.815479239686809,4.84150885719863,4.86753847471045,4.893568092222271,4.9195977097340915,4.945627327245911,4.971656944757733,4.9976865622695525,5.023716179781373,5.049745797293194,5.0757754148050145,5.101805032316835,5.127834649828656,5.153864267340476,5.179893884852297,5.205923502364118,5.231953119875938,5.257982737387759,5.28401235489958,5.3100419724114,5.33607158992322,5.362101207435042,5.388130824946861,5.414160442458683,5.440190059970503,5.466219677482324,5.492249294994144,5.518278912505965,5.544308530017785,5.570338147529606,5.5963677650414265,5.622397382553248,5.648427000065067,5.6744566175768885,5.700486235088709,5.726515852600529,5.7525454701123495,5.778575087624171,5.804604705135992,5.8306343226478115,5.856663940159632,5.882693557671454,5.908723175183273,5.934752792695094,5.960782410206915,5.986812027718736,6.012841645230556,6.038871262742377,6.064900880254197,6.090930497766017,6.116960115277839,6.142989732789659,6.169019350301479,6.1950489678133,6.221078585325121,6.247108202836942,6.2731378203487616,6.299167437860582,6.325197055372403,6.351226672884223,6.377256290396044,6.403285907907865,6.429315525419685,6.455345142931505,6.481374760443327,6.507404377955147,6.533433995466967,6.559463612978788,6.585493230490609,6.61152284800243,6.63755246551425,6.66358208302607,6.689611700537892,6.715641318049712,6.741670935561532,6.767700553073353,6.7937301705851745,6.819759788096994,6.845789405608815,6.8718190231206355,6.897848640632455,6.923878258144276,6.9499078756560975,6.975937493167918,7.001967110679738,7.0279967281915585,7.05402634570338,7.0800559632152,7.1060855807270205,7.132115198238841,7.158144815750663,7.184174433262482,7.210204050774303,7.236233668286124,7.2622632857979434,7.288292903309765,7.314322520821586,7.340352138333405,7.366381755845226,7.392411373357048,7.418440990868868,7.444470608380688,7.470500225892509,7.49652984340433,7.522559460916149,7.5485890784279706,7.574618695939791,7.600648313451613,7.626677930963432,7.652707548475253,7.678737165987074,7.7047667834988935,7.730796401010714,7.756826018522536,7.782855636034356,7.808885253546176,7.834914871057997,7.860944488569818,7.886974106081638,7.913003723593459,7.939033341105279,7.965062958617101,7.991092576128921,8.017122193640741,8.043151811152562,8.069181428664383,8.095211046176203,8.121240663688024,8.147270281199845,8.173299898711665,8.199329516223486,8.225359133735306,8.251388751247125,8.277418368758948,8.303447986270768,8.329477603782589,8.355507221294408,8.38153683880623,8.407566456318051,8.43359607382987,8.45962569134169,8.485655308853513,8.511684926365332,8.537714543877152,8.563744161388973,8.589773778900794,8.615803396412614,8.641833013924435,8.667862631436256,8.693892248948076,8.719921866459897,8.745951483971718,8.771981101483538,8.798010718995359,8.82404033650718,8.850069954019,8.87609957153082,8.902129189042641,8.928158806554462,8.954188424066283,8.980218041578103,9.006247659089924,9.032277276601745,9.058306894113564,9.084336511625386,9.110366129137207,9.136395746649027,9.162425364160846,9.188454981672669,9.21448459918449,9.240514216696308,9.266543834208129,9.29257345171995,9.318603069231772,9.34463268674359,9.370662304255411,9.396691921767232,9.422721539279053,9.448751156790873,9.474780774302694,9.500810391814515,9.526840009326335,9.552869626838156,9.578899244349977,9.604928861861797,9.630958479373618,9.656988096885438,9.68301771439726,9.70904733190908,9.7350769494209,9.761106566932721,9.787136184444542,9.813165801956362,9.839195419468183,9.865225036980002,9.891254654491823,9.917284272003645,9.943313889515466,9.969343507027284,9.995373124539105,10.021402742050928,10.047432359562746,10.073461977074567,10.099491594586388,10.12552121209821,10.151550829610029,10.17758044712185,10.20361006463367,10.229639682145491,10.255669299657312,10.281698917169132,10.307728534680953,10.333758152192773,10.359787769704594,10.385817387216415,10.411847004728235,10.437876622240056,10.463906239751877,10.489935857263697,10.515965474775518,10.541995092287339,10.56802470979916,10.594054327310978,10.6200839448228,10.646113562334621,10.67214317984644,10.69817279735826,10.724202414870083,10.750232032381904,10.776261649893723,10.802291267405543,10.828320884917366,10.854350502429185,10.880380119941005,10.906409737452826,10.932439354964648,10.958468972476467,10.984498589988288,11.010528207500109,11.03655782501193,11.06258744252375,11.08861706003557,11.114646677547391,11.140676295059212,11.166705912571032,11.192735530082853,11.218765147594674,11.244794765106496,11.270824382618313,11.296854000130134,11.322883617641956,11.348913235153777,11.374942852665598,11.400972470177418,11.427002087689239,11.453031705201058,11.479061322712878,11.505090940224699,11.531120557736521,11.557150175248342,11.583179792760163,11.609209410271983,11.635239027783802,11.661268645295623,11.687298262807444,11.713327880319264,11.739357497831087,11.765387115342907,11.791416732854728,11.817446350366547,11.843475967878367,11.869505585390188,11.895535202902009,11.92156482041383,11.94759443792565,11.973624055437472,11.99965367294929,12.025683290461112,12.051712907972933,12.077742525484753,12.103772142996574,12.129801760508395,12.155831378020215,12.181860995532034,12.207890613043855,12.233920230555677,12.259949848067498,12.285979465579318,12.312009083091139,12.338038700602958,12.364068318114779,12.3900979356266,12.41612755313842,12.442157170650242,12.468186788162063,12.494216405673884,12.520246023185702,12.546275640697523,12.572305258209344,12.598334875721164,12.624364493232985,12.650394110744806,12.676423728256628,12.702453345768445,12.728482963280268,12.754512580792088,12.780542198303909,12.80657181581573,12.83260143332755,12.85863105083937,12.88466066835119,12.91069028586301,12.936719903374833,12.962749520886653,12.988779138398474,13.014808755910295,13.040838373422115,13.066867990933934,13.092897608445755,13.118927225957576,13.144956843469396,13.170986460981219,13.19701607849304,13.22304569600486,13.249075313516679,13.2751049310285,13.30113454854032,13.32716416605214,13.353193783563961,13.379223401075784,13.405253018587604,13.431282636099423,13.457312253611244,13.483341871123065,13.509371488634885,13.535401106146706,13.561430723658527,13.587460341170349,13.613489958682166,13.639519576193988,13.66554919370581,13.69157881121763,13.71760842872945,13.743638046241271,13.769667663753092,13.79569728126491,13.821726898776731,13.847756516288552,13.873786133800374,13.899815751312195,13.925845368824016,13.951874986335836,13.977904603847655,14.003934221359476,14.029963838871296,14.055993456383117,14.08202307389494,14.10805269140676,14.13408230891858,14.1601119264304,14.18614154394222,14.212171161454041,14.238200778965862,14.264230396477682,14.290260013989505,14.316289631501325,14.342319249013144,14.368348866524965,14.394378484036785,14.420408101548606,14.446437719060427,14.472467336572247,14.49849695408407,14.524526571595887,14.550556189107708,14.57658580661953,14.60261542413135,14.628645041643171,14.654674659154992,14.68070427666681,14.706733894178631,14.732763511690452,14.758793129202273,14.784822746714095,14.810852364225916,14.836881981737736,14.862911599249555,14.888941216761376,14.914970834273197,14.941000451785017,14.967030069296838,14.99305968680866,15.019089304320481,15.045118921832298,15.07114853934412,15.097178156855941,15.123207774367762,15.149237391879582,15.175267009391403,15.201296626903225,15.227326244415043,15.253355861926863,15.279385479438686,15.305415096950506,15.331444714462327,15.357474331974148,15.383503949485968,15.409533566997787,15.435563184509608,15.461592802021428,15.48762241953325,15.513652037045071,15.539681654556892,15.565711272068713,15.591740889580532,15.617770507092352,15.643800124604173,15.669829742115994,15.695859359627816,15.721888977139637,15.747918594651457,15.773948212163276,15.799977829675097,15.826007447186917,15.852037064698738,15.878066682210559,15.90409629972238,15.930125917234202,15.956155534746019,15.982185152257841,16.008214769769662,16.034244387281483,16.060274004793303,16.086303622305124,16.112333239816945,16.138362857328765,16.164392474840586,16.190422092352406,16.216451709864227,16.242481327376048,16.26851094488787,16.29454056239969,16.32057017991151,16.34659979742333,16.37262941493515,16.39865903244697,16.424688649958792,16.450718267470613,16.476747884982434,16.50277750249425,16.52880712000607,16.554836737517896,16.580866355029716,16.606895972541537,16.632925590053357,16.658955207565178,16.684984825076995,16.711014442588816,16.737044060100637,16.76307367761246,16.78910329512428,16.815132912636102,16.84116253014792,16.86719214765974,16.89322176517156,16.91925138268338,16.9452810001952,16.971310617707026,16.997340235218847,17.023369852730664,17.049399470242484,17.075429087754305,17.101458705266126,17.127488322777946,17.153517940289767,17.179547557801587,17.205577175313408,17.23160679282523,17.25763641033705,17.28366602784887,17.30969564536069,17.33572526287251,17.361754880384332,17.387784497896153,17.413814115407973,17.439843732919794,17.465873350431615,17.491902967943435,17.517932585455256,17.543962202967077,17.569991820478897,17.596021437990718,17.62205105550254,17.64808067301436,17.67411029052618,17.700139908038,17.72616952554982,17.75219914306164,17.778228760573462,17.804258378085283,17.830287995597104,17.856317613108924,17.882347230620745,17.908376848132566,17.934406465644383,17.960436083156207,17.986465700668028,18.012495318179848,18.03852493569167,18.06455455320349,18.09058417071531,18.116613788227127,18.142643405738948,18.168673023250772,18.194702640762593,18.220732258274413,18.246761875786234,18.272791493298055,18.29882111080987,18.324850728321692,18.350880345833513,18.376909963345337,18.402939580857158,18.42896919836898,18.4549988158808,18.481028433392616,18.507058050904437,18.533087668416258,18.559117285928078,18.5851469034399,18.611176520951723,18.637206138463544,18.66323575597536,18.68926537348718,18.715294990999002,18.741324608510823,18.767354226022643,18.793383843534464,18.819413461046288,18.845443078558105,18.871472696069926,18.897502313581747,18.923531931093567,18.949561548605388,18.97559116611721,19.00162078362903,19.02765040114085,19.05368001865267,19.07970963616449,19.10573925367631,19.131768871188132,19.157798488699953,19.183828106211774,19.209857723723594,19.235887341235415,19.261916958747236,19.287946576259056,19.313976193770877,19.340005811282698,19.36603542879452,19.39206504630634,19.41809466381816,19.44412428132998,19.4701538988418,19.49618351635362,19.522213133865442,19.54824275137726,19.574272368889083,19.600301986400904,19.626331603912725,19.652361221424545,19.678390838936366,19.704420456448187,19.730450073960004,19.756479691471824,19.782509308983645,19.80853892649547,19.83456854400729,19.86059816151911,19.88662777903093,19.91265739654275,19.93868701405457,19.96471663156639,19.99074624907821,20.016775866590034,20.042805484101855,20.068835101613676,20.094864719125493,20.120894336637313,20.146923954149134,20.172953571660955,20.198983189172775,20.2250128066846,20.25104242419642,20.277072041708237,20.303101659220058,20.32913127673188,20.3551608942437,20.38119051175552,20.40722012926734,20.433249746779165,20.459279364290982,20.485308981802802,20.511338599314623,20.537368216826444,20.563397834338264,20.589427451850085,20.615457069361906,20.641486686873726,20.667516304385547,20.693545921897368,20.71957553940919,20.74560515692101,20.77163477443283,20.79766439194465,20.82369400945647,20.84972362696829,20.875753244480112,20.901782861991933,20.927812479503753,20.953842097015574,20.979871714527395,21.005901332039215,21.031930949551036,21.057960567062857,21.083990184574677,21.110019802086498,21.13604941959832,21.16207903711014,21.188108654621956,21.21413827213378,21.2401678896456,21.266197507157422,21.292227124669242,21.318256742181063,21.34428635969288,21.3703159772047,21.39634559471652,21.422375212228346,21.448404829740166,21.474434447251987,21.500464064763808,21.526493682275625,21.552523299787445,21.578552917299266,21.604582534811087,21.63061215232291,21.65664176983473,21.682671387346552,21.70870100485837,21.73473062237019,21.76076023988201,21.78678985739383,21.812819474905652,21.838849092417473,21.864878709929297,21.890908327441114,21.916937944952934,21.942967562464755,21.968997179976576,21.995026797488396,22.021056415000217,22.047086032512038,22.07311565002386,22.09914526753568,22.1251748850475,22.15120450255932,22.17723412007114,22.20326373758296,22.229293355094782,22.255322972606603,22.281352590118424,22.307382207630244,22.333411825142065,22.359441442653885,22.385471060165706,22.411500677677527,22.437530295189347,22.463559912701168,22.489589530212992,22.515619147724806,22.541648765236626,22.567678382748447,22.593708000260268,22.619737617772092,22.645767235283913,22.671796852795733,22.697826470307554,22.723856087819374,22.749885705331195,22.775915322843016,22.801944940354836,22.827974557866657,22.854004175378478,22.880033792890295,22.906063410402115,22.932093027913936,22.958122645425757,22.984152262937577,23.010181880449398,23.03621149796122,23.062241115473043,23.088270732984864,23.114300350496684,23.140329968008505,23.166359585520325,23.192389203032146,23.218418820543967,23.244448438055784,23.270478055567605,23.296507673079425,23.322537290591246,23.348566908103066,23.374596525614887,23.400626143126708,23.42665576063853,23.45268537815035,23.478714995662173,23.504744613173994,23.530774230685815,23.556803848197635,23.582833465709456,23.608863083221273,23.634892700733094,23.660922318244914,23.686951935756735,23.712981553268556,23.739011170780376,23.765040788292197,23.791070405804017,23.817100023315838,23.84312964082766,23.86915925833948,23.8951888758513,23.921218493363124,23.947248110874945,23.97327772838676,23.99930734589858,24.025336963410403,24.051366580922224,24.077396198434045,24.103425815945865,24.129455433457686,24.155485050969506,24.181514668481327,24.207544285993148,24.23357390350497,24.25960352101679,24.28563313852861,24.31166275604043,24.337692373552247,24.363721991064068,24.38975160857589,24.41578122608771,24.44181084359953,24.467840461111354,24.493870078623175,24.519899696134996,24.545929313646816,24.571958931158637,24.597988548670457,24.624018166182278,24.6500477836941,24.676077401205916,24.702107018717737,24.728136636229557,24.754166253741378,24.7801958712532,24.80622548876502,24.83225510627684,24.85828472378866,24.884314341300485,24.910343958812305,24.936373576324126,24.962403193835947,24.988432811347767,25.014462428859588,25.040492046371405,25.066521663883226,25.092551281395046,25.118580898906867,25.144610516418687,25.170640133930508,25.19666975144233,25.22269936895415,25.24872898646597,25.27475860397779,25.30078822148961,25.326817839001436,25.352847456513256,25.378877074025077,25.40490669153689,25.43093630904871,25.456965926560535,25.482995544072356,25.509025161584177,25.535054779095997,25.561084396607818,25.58711401411964,25.61314363163146,25.63917324914328,25.6652028666551,25.69123248416692,25.71726210167874,25.743291719190566,25.76932133670238,25.7953509542142,25.82138057172602,25.84741018923784,25.873439806749666,25.899469424261486,25.925499041773307,25.951528659285128,25.977558276796948,26.00358789430877,26.02961751182059,26.05564712933241,26.08167674684423,26.10770636435605,26.13373598186787,26.15976559937969,26.18579521689151,26.21182483440333,26.23785445191515,26.26388406942697,26.289913686938792,26.315943304450617,26.341972921962437,26.368002539474258,26.39403215698608,26.4200617744979,26.44609139200972,26.47212100952154,26.498150627033358,26.524180244545178,26.550209862057,26.57623947956882,26.60226909708064,26.62829871459246,26.65432833210428,26.680357949616102,26.706387567127923,26.732417184639747,26.758446802151568,26.784476419663388,26.81050603717521,26.836535654687022,26.862565272198847,26.888594889710667,26.914624507222488,26.94065412473431,26.96668374224613,26.99271335975795,27.01874297726977,27.04477259478159,27.07080221229341,27.096831829805232,27.122861447317053,27.148891064828874,27.174920682340698,27.20095029985251,27.226979917364332,27.253009534876153,27.279039152387977,27.305068769899798,27.33109838741162,27.35712800492344,27.38315762243526,27.40918723994708,27.4352168574589,27.46124647497072,27.487276092482542,27.513305709994363,27.539335327506183,27.565364945018,27.59139456252982,27.617424180041642,27.643453797553462,27.669483415065283,27.695513032577104,27.721542650088928,27.74757226760075,27.77360188511257,27.79963150262439,27.82566112013621,27.85169073764803,27.877720355159852,27.903749972671672,27.92977959018349,27.95580920769531,27.98183882520713,28.00786844271895,28.033898060230772,28.059927677742593,28.085957295254413,28.111986912766234,28.13801653027806,28.16404614778988,28.1900757653017,28.21610538281352,28.24213500032534,28.26816461783716,28.29419423534898,28.3202238528608,28.34625347037262,28.37228308788444,28.39831270539626,28.424342322908082,28.450371940419902,28.476401557931723,28.502431175443544,28.528460792955364,28.554490410467185,28.58052002797901,28.60654964549083,28.63257926300265,28.658608880514464,28.68463849802629,28.71066811553811,28.73669773304993,28.76272735056175,28.78875696807357,28.81478658558539,28.840816203097212,28.866845820609033,28.892875438120853,28.918905055632674,28.944934673144495,28.970964290656315,28.99699390816814,29.023023525679953,29.049053143191774,29.075082760703594,29.101112378215415,29.12714199572724,29.15317161323906,29.17920123075088,29.2052308482627,29.231260465774522,29.257290083286343,29.283319700798163,29.309349318309984,29.335378935821804,29.36140855333362,29.387438170845442,29.413467788357263,29.439497405869083,29.465527023380904,29.491556640892725,29.517586258404545,29.54361587591637,29.56964549342819,29.59567511094001,29.62170472845183,29.647734345963652,29.673763963475473,29.699793580987293,29.72582319849911,29.75185281601093,29.777882433522752,29.803912051034573,29.829941668546393,29.855971286058214,29.882000903570034,29.908030521081855,29.934060138593676,29.960089756105496,29.98611937361732,30.01214899112914,30.038178608640962,30.064208226152783,30.090237843664596,30.11626746117642,30.14229707868824,30.16832669620006,30.194356313711882,30.220385931223703,30.246415548735524,30.272445166247344,30.298474783759165,30.324504401270985,30.350534018782806,30.376563636294627,30.40259325380645,30.42862287131827,30.454652488830085,30.480682106341906,30.506711723853726,30.53274134136555,30.55877095887737,30.584800576389192,30.610830193901013,30.636859811412833,30.662889428924654,30.688919046436475,30.714948663948295,30.740978281460116,30.767007898971936,30.793037516483757,30.819067133995574,30.845096751507395,30.871126369019215,30.897155986531036,30.923185604042857,30.949215221554677,30.9752448390665,31.001274456578322,31.027304074090143,31.053333691601964,31.079363309113784,31.105392926625605,31.131422544137425,31.157452161649246,31.183481779161063,31.209511396672884,31.235541014184705,31.261570631696525,31.287600249208346,31.313629866720166,31.339659484231987,31.365689101743808,31.391718719255632,31.417748336767453,31.443777954279273,31.469807571791094,31.495837189302915,31.52186680681473,31.547896424326552,31.573926041838373,31.599955659350194,31.625985276862014,31.652014894373835,31.678044511885656,31.704074129397476,31.730103746909297,31.756133364421117,31.782162981932938,31.80819259944476,31.834222216956583,31.860251834468404,31.886281451980217,31.912311069492038,31.938340687003862,31.964370304515683,31.990399922027503,32.016429539539324,32.04245915705114,32.068488774562965,32.09451839207479,32.12054800958661,32.14657762709843,32.17260724461025,32.19863686212207,32.22466647963389,32.250696097145706,32.27672571465753,32.30275533216935,32.32878494968117,32.35481456719299,32.38084418470481,32.40687380221663,32.432903419728454,32.45893303724027,32.484962654752096,32.51099227226391,32.53702188977574,32.56305150728756,32.58908112479938,32.615110742311195,32.64114035982302,32.66716997733484,32.69319959484666,32.71922921235848,32.7452588298703,32.77128844738212,32.79731806489394,32.82334768240576,32.849377299917585,32.8754069174294,32.901436534941226,32.92746615245304,32.95349576996487,32.979525387476684,33.0055550049885,33.031584622500326,33.05761424001214,33.08364385752397,33.10967347503579,33.13570309254761,33.16173271005943,33.18776232757125,33.213791945083074,33.23982156259489,33.265851180106715,33.29188079761853,33.317910415130356,33.34394003264217,33.36996965015399,33.395999267665815,33.42202888517763,33.448058502689456,33.47408812020127,33.5001177377131,33.52614735522492,33.55217697273674,33.57820659024856,33.60423620776038,33.630265825272204,33.65629544278402,33.68232506029584,33.70835467780766,33.73438429531948,33.760413912831304,33.78644353034312,33.812473147854945,33.83850276536676,33.864532382878586,33.8905620003904,33.91659161790223,33.94262123541405,33.96865085292587,33.99468047043769,34.02071008794951,34.04673970546133,34.07276932297315,34.09879894048497,34.12482855799679,34.15085817550861,34.176887793020434,34.20291741053225,34.228947028044075,34.25497664555589,34.28100626306772,34.307035880579534,34.33306549809136,34.359095115603175,34.385124733115,34.411154350626816,34.43718396813863,34.46321358565046,34.48924320316228,34.5152728206741,34.54130243818592,34.56733205569774,34.593361673209564,34.61939129072138,34.645420908233206,34.67145052574502,34.69748014325685,34.723509760768664,34.74953937828049,34.775568995792305,34.80159861330412,34.82762823081595,34.853657848327764,34.87968746583959,34.905717083351405,34.93174670086323,34.95777631837505,34.98380593588687,35.009835553398695,35.03586517091051,35.061894788422336,35.08792440593415,35.11395402344598,35.139983640957794,35.16601325846961,35.192042875981436,35.21807249349325,35.24410211100508,35.270131728516894,35.29616134602872,35.322190963540535,35.34822058105236,35.374250198564184,35.400279816076,35.426309433587825,35.45233905109964,35.478368668611466,35.50439828612328,35.5304279036351,35.556457521146925,35.58248713865874,35.608516756170566,35.63454637368238,35.66057599119421,35.686605608706024,35.71263522621785,35.738664843729666,35.76469446124149,35.790724078753314,35.81675369626513,35.84278331377695,35.868812931288765,35.89484254880059,35.920872166312414,35.94690178382423,35.972931401336055,35.99896101884787,36.024990636359696,36.05102025387151,36.07704987138334,36.103079488895155,36.12910910640698,36.155138723918796,36.18116834143062,36.20719795894244,36.233227576454254,36.25925719396608,36.285286811477896,36.31131642898972,36.337346046501544,36.36337566401336,36.389405281525185,36.415434899037,36.44146451654883,36.467494134060644,36.49352375157247,36.519553369084285,36.54558298659611,36.571612604107926,36.59764222161974,36.62367183913157,36.649701456643385,36.67573107415521,36.701760691667026,36.72779030917885,36.753819926690674,36.77984954420249,36.805879161714316,36.83190877922613,36.85793839673796,36.883968014249774,36.9099976317616,36.936027249273415,36.96205686678523,36.98808648429706,37.014116101808874,37.0401457193207,37.066175336832515,37.09220495434434,37.118234571856156,37.14426418936798,37.1702938068798,37.19632342439162,37.222353041903446,37.24838265941526,37.27441227692709,37.3004418944389,37.32647151195072,37.352501129462546,37.37853074697436,37.40456036448619,37.430589981998004,37.45661959950983,37.482649217021645,37.50867883453347,37.53470845204529,37.56073806955711,37.58676768706893,37.61279730458075,37.638826922092576,37.664856539604386,37.69088615711621,37.71691577462803,37.74294539213985,37.768975009651676,37.79500462716349,37.82103424467532,37.847063862187134,37.87309347969896,37.899123097210776,37.9251527147226,37.95118233223442,37.97721194974624,38.00324156725806,38.029271184769875,38.0553008022817,38.08133041979352,38.10736003730534,38.13338965481716,38.15941927232898,38.185448889840806,38.21147850735262,38.23750812486445,38.263537742376265,38.28956735988809,38.315596977399906,38.34162659491173,38.36765621242355,38.393685829935364,38.41971544744719,38.445745064959006,38.47177468247083,38.49780429998265,38.52383391749447,38.54986353500629,38.57589315251811,38.60192277002994,38.627952387541754,38.65398200505358,38.680011622565395,38.70604124007722,38.73207085758904,38.75810047510085,38.78413009261268,38.810159710124495,38.83618932763632,38.862218945148136,38.88824856265996,38.91427818017178,38.9403077976836,38.96633741519542,38.99236703270724,39.01839665021906,39.044426267730884,39.07045588524271,39.09648550275452,39.12251512026634,39.14854473777817,39.174574355289984,39.20060397280181,39.226633590313625,39.25266320782545,39.27869282533727,39.30472244284909,39.33075206036091,39.35678167787273,39.38281129538455,39.40884091289637,39.43487053040819,39.46090014792001,39.48692976543183,39.51295938294365,39.53898900045547,39.56501861796729,39.591048235479114,39.61707785299094,39.643107470502756,39.66913708801458,39.6951667055264,39.72119632303822,39.74722594055004,39.77325555806186,39.79928517557368,39.8253147930855,39.85134441059732,39.87737402810914,39.90340364562096,39.92943326313278,39.9554628806446,39.98149249815642,40.007522115668245,40.03355173318007,40.059581350691886,40.08561096820371,40.11164058571553,40.13767020322735,40.16369982073917,40.189729438250986,40.21575905576281,40.24178867327463,40.26781829078645,40.29384790829827,40.31987752581009,40.34590714332191,40.371936760833734,40.39796637834555,40.423995995857375,40.4500256133692,40.476055230881016,40.50208484839284,40.52811446590465,40.554144083416475,40.5801737009283,40.606203318440116,40.63223293595194,40.65826255346376,40.68429217097558,40.7103217884874,40.73635140599922,40.76238102351104,40.788410641022864,40.81444025853468,40.840469876046505,40.86649949355833,40.89252911107014,40.918558728581964,40.94458834609378,40.970617963605605,40.99664758111743,41.022677198629246,41.04870681614107,41.07473643365289,41.10076605116471,41.12679566867653,41.15282528618835,41.17885490370017,41.204884521211994,41.23091413872381,41.25694375623563,41.28297337374745,41.30900299125927,41.335032608771094,41.36106222628291,41.387091843794735,41.41312146130655,41.43915107881838,41.4651806963302,41.49121031384202,41.51723993135384,41.54326954886566,41.56929916637748,41.5953287838893,41.62135840140112,41.64738801891294,41.67341763642476,41.69944725393658,41.7254768714484,41.751506488960224,41.77753610647204,41.803565723983866,41.82959534149568,41.85562495900751,41.88165457651933,41.90768419403115,41.93371381154297,41.95974342905479,41.98577304656661,42.01180266407843,42.03783228159025,42.06386189910207,42.08989151661389,42.11592113412571,42.14195075163753,42.167980369149355,42.19400998666117,42.220039604172996,42.24606922168481,42.27209883919664,42.29812845670846,42.32415807422028,42.350187691732096,42.37621730924391,42.40224692675574,42.42827654426756,42.45430616177938,42.4803357792912,42.50636539680302,42.532395014314844,42.55842463182666,42.584454249338485,42.6104838668503,42.636513484362126,42.66254310187394,42.68857271938576,42.714602336897585,42.7406319544094,42.766661571921226,42.79269118943304,42.81872080694487,42.84475042445669,42.87078004196851,42.89680965948033,42.92283927699215,42.948868894503974,42.97489851201579,43.000928129527615,43.02695774703943,43.05298736455125,43.079016982063074,43.10504659957489,43.131076217086715,43.15710583459853,43.183135452110356,43.20916506962217,43.235194687134,43.26122430464582,43.28725392215764,43.31328353966946,43.33931315718128,43.365342774693104,43.39137239220492,43.41740200971674,43.44343162722856,43.46946124474038,43.495490862252204,43.52152047976402,43.547550097275845,43.57357971478766,43.59960933229949,43.625638949811304,43.65166856732313,43.677698184834945,43.70372780234677,43.72975741985859,43.75578703737041,43.78181665488223,43.80784627239405,43.83387588990587,43.85990550741769,43.88593512492951,43.911964742441334,43.93799435995315,43.964023977464976,43.99005359497679,44.01608321248862,44.042112830000434,44.06814244751226,44.094172065024075,44.1202016825359,44.14623130004772,44.172260917559534,44.19829053507136,44.224320152583175,44.250349770095,44.27637938760682,44.30240900511864,44.328438622630465,44.35446824014228,44.380497857654106,44.40652747516592,44.43255709267775,44.458586710189564,44.48461632770139,44.510645945213206,44.53667556272502,44.56270518023685,44.588734797748664,44.61476441526049,44.640794032772305,44.66682365028413],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221],"y":[0,0.030247303078563832,0.060494606157127664,0.09074190923569149,0.12098921231425533,0.15123651539281915,0.18148381847138298,0.2117311215499468,0.24197842462851066,0.2722257277070745,0.3024730307856383,0.33272033386420213,0.36296763694276596,0.3932149400213298,0.4234622430998936,0.45370954617845743,0.4839568492570213,0.5142041523355851,0.544451455414149,0.5746987584927128,0.6049460615712766,0.6351933646498404,0.6654406677284043,0.6956879708069681,0.7259352738855319,0.7561825769640957,0.7864298800426596,0.8166771831212234,0.8469244861997872,0.877171789278351,0.9074190923569149,0.9376663954354787,0.9679136985140426,0.9981610015926065,1.0284083046711703,1.058655607749734,1.088902910828298,1.1191502139068616,1.1493975169854256,1.1796448200639893,1.2098921231425532,1.240139426221117,1.2703867292996809,1.3006340323782446,1.3308813354568085,1.3611286385353722,1.3913759416139362,1.4216232446925,1.4518705477710638,1.4821178508496275,1.5123651539281915,1.5426124570067552,1.5728597600853191,1.6031070631638829,1.6333543662424468,1.6636016693210105,1.6938489723995744,1.7240962754781382,1.754343578556702,1.7845908816352658,1.8148381847138297,1.8450854877923935,1.8753327908709574,1.905580093949521,1.9358273970280853,1.966074700106649,1.996322003185213,2.026569306263777,2.0568166093423406,2.0870639124209043,2.117311215499468,2.147558518578032,2.177805821656596,2.2080531247351596,2.2383004278137233,2.2685477308922875,2.298795033970851,2.329042337049415,2.3592896401279786,2.3895369432065423,2.4197842462851065,2.4500315493636697,2.480278852442234,2.5105261555207976,2.5407734585993618,2.571020761677926,2.601268064756489,2.6315153678350534,2.661762670913617,2.6920099739921812,2.7222572770707445,2.7525045801493087,2.7827518832278724,2.8129991863064365,2.843246489385,2.873493792463564,2.9037410955421277,2.933988398620692,2.964235701699255,2.9944830047778193,3.024730307856383,3.054977610934947,3.0852249140135104,3.1154722170920746,3.1457195201706383,3.1759668232492024,3.2062141263277657,3.23646142940633,3.2667087324848936,3.2969560355634577,3.327203338642021,3.357450641720585,3.387697944799149,3.417945247877713,3.4481925509562763,3.4784398540348405,3.508687157113404,3.5389344601919683,3.5691817632705316,3.5994290663490958,3.6296763694276595,3.659923672506223,3.690170975584787,3.720418278663351,3.750665581741915,3.7809128848204785,3.811160187899042,3.8414074909776064,3.8716547940561705,3.901902097134734,3.932149400213298,3.9623967032918617,3.992644006370426,4.0228913094489895,4.053138612527554,4.083385915606117,4.113633218684681,4.143880521763244,4.1741278248418086,4.204375127920372,4.234622430998936,4.2648697340775,4.295117037156064,4.325364340234628,4.355611643313192,4.385858946391755,4.416106249470319,4.446353552548882,4.476600855627447,4.506848158706011,4.537095461784575,4.567342764863138,4.597590067941702,4.6278373710202665,4.65808467409883,4.688331977177393,4.718579280255957,4.748826583334521,4.779073886413085,4.809321189491649,4.839568492570213,4.869815795648777,4.9000630987273395,4.930310401805904,4.960557704884468,4.990805007963032,5.021052311041595,5.051299614120159,5.0815469171987235,5.111794220277288,5.142041523355852,5.172288826434414,5.202536129512978,5.2327834325915425,5.263030735670107,5.29327803874867,5.323525341827234,5.353772644905798,5.3840199479843625,5.414267251062925,5.444514554141489,5.474761857220053,5.505009160298617,5.535256463377181,5.565503766455745,5.595751069534309,5.625998372612873,5.656245675691435,5.68649297877,5.716740281848564,5.746987584927128,5.777234888005691,5.807482191084255,5.8377294941628195,5.867976797241384,5.898224100319946,5.92847140339851,5.958718706477074,5.9889660095556385,6.019213312634202,6.049460615712766,6.07970791879133,6.109955221869894,6.140202524948457,6.170449828027021,6.200697131105585,6.230944434184149,6.261191737262712,6.2914390403412765,6.321686343419841,6.351933646498405,6.382180949576968,6.412428252655531,6.442675555734096,6.47292285881266,6.503170161891224,6.533417464969787,6.563664768048351,6.5939120711269155,6.624159374205479,6.654406677284042,6.684653980362606,6.71490128344117,6.7451485865197345,6.775395889598298,6.805643192676862,6.835890495755426,6.866137798833989,6.896385101912553,6.926632404991117,6.956879708069681,6.987127011148245,7.017374314226808,7.0476216173053725,7.077868920383937,7.1081162234625,7.138363526541063,7.168610829619627,7.1988581326981915,7.229105435776756,7.259352738855319,7.289600041933883,7.319847345012446,7.3500946480910105,7.380341951169574,7.410589254248138,7.440836557326702,7.471083860405266,7.50133116348383,7.531578466562394,7.561825769640957,7.592073072719521,7.622320375798084,7.652567678876649,7.682814981955213,7.713062285033777,7.743309588112341,7.773556891190904,7.803804194269468,7.834051497348032,7.864298800426596,7.894546103505159,7.924793406583723,7.9550407096622875,7.985288012740852,8.015535315819415,8.045782618897979,8.076029921976543,8.106277225055107,8.13652452813367,8.166771831212234,8.197019134290798,8.227266437369362,8.257513740447925,8.287761043526489,8.318008346605053,8.348255649683617,8.378502952762181,8.408750255840744,8.438997558919308,8.469244861997872,8.499492165076436,8.529739468155,8.559986771233564,8.590234074312129,8.620481377390691,8.650728680469255,8.68097598354782,8.711223286626383,8.741470589704946,8.77171789278351,8.801965195862074,8.832212498940638,8.8624598020192,8.892707105097765,8.922954408176329,8.953201711254893,8.983449014333457,9.013696317412021,9.043943620490586,9.07419092356915,9.104438226647714,9.134685529726276,9.16493283280484,9.195180135883405,9.225427438961969,9.255674742040533,9.285922045119097,9.31616934819766,9.346416651276222,9.376663954354786,9.40691125743335,9.437158560511914,9.467405863590479,9.497653166669043,9.527900469747607,9.55814777282617,9.588395075904733,9.618642378983298,9.648889682061862,9.679136985140426,9.70938428821899,9.739631591297554,9.769878894376118,9.800126197454679,9.830373500533243,9.860620803611807,9.890868106690371,9.921115409768936,9.9513627128475,9.981610015926064,10.011857319004628,10.04210462208319,10.072351925161755,10.102599228240319,10.132846531318883,10.163093834397447,10.193341137476011,10.223588440554575,10.25383574363314,10.284083046711704,10.314330349790264,10.344577652868828,10.374824955947393,10.405072259025957,10.435319562104521,10.465566865183085,10.49581416826165,10.526061471340213,10.556308774418776,10.58655607749734,10.616803380575904,10.647050683654468,10.677297986733032,10.707545289811597,10.73779259289016,10.768039895968725,10.798287199047286,10.82853450212585,10.858781805204414,10.889029108282978,10.919276411361542,10.949523714440106,10.97977101751867,11.010018320597235,11.040265623675797,11.070512926754361,11.100760229832925,11.13100753291149,11.161254835990054,11.191502139068618,11.221749442147182,11.251996745225746,11.282244048304307,11.31249135138287,11.342738654461435,11.37298595754,11.403233260618563,11.433480563697128,11.463727866775692,11.493975169854256,11.52422247293282,11.554469776011382,11.584717079089947,11.61496438216851,11.645211685247075,11.675458988325639,11.705706291404203,11.735953594482767,11.766200897561331,11.796448200639892,11.826695503718456,11.85694280679702,11.887190109875585,11.917437412954149,11.947684716032713,11.977932019111277,12.008179322189841,12.038426625268404,12.068673928346968,12.098921231425532,12.129168534504096,12.15941583758266,12.189663140661224,12.219910443739789,12.250157746818353,12.280405049896913,12.310652352975477,12.340899656054042,12.371146959132606,12.40139426221117,12.431641565289734,12.461888868368298,12.492136171446862,12.522383474525425,12.552630777603989,12.582878080682553,12.613125383761117,12.643372686839681,12.673619989918246,12.70386729299681,12.734114596075374,12.764361899153936,12.794609202232499,12.824856505311063,12.855103808389627,12.885351111468191,12.915598414546755,12.94584571762532,12.976093020703884,13.006340323782448,13.03658762686101,13.066834929939574,13.097082233018138,13.127329536096703,13.157576839175267,13.187824142253831,13.218071445332393,13.248318748410957,13.27856605148952,13.308813354568084,13.339060657646648,13.369307960725212,13.399555263803776,13.42980256688234,13.460049869960905,13.490297173039469,13.520544476118031,13.550791779196595,13.58103908227516,13.611286385353724,13.641533688432288,13.671780991510852,13.702028294589415,13.732275597667979,13.762522900746541,13.792770203825105,13.82301750690367,13.853264809982234,13.883512113060798,13.913759416139362,13.944006719217926,13.97425402229649,14.004501325375053,14.034748628453617,14.06499593153218,14.095243234610745,14.12549053768931,14.155737840767873,14.185985143846436,14.216232446925,14.246479750003564,14.276727053082126,14.30697435616069,14.337221659239255,14.367468962317819,14.397716265396383,14.427963568474947,14.458210871553511,14.488458174632076,14.518705477710638,14.548952780789202,14.579200083867766,14.60944738694633,14.639694690024893,14.669941993103457,14.700189296182021,14.730436599260585,14.760683902339148,14.790931205417712,14.821178508496276,14.85142581157484,14.881673114653404,14.911920417731968,14.942167720810533,14.972415023889097,15.00266232696766,15.032909630046223,15.063156933124787,15.093404236203352,15.123651539281914,15.153898842360478,15.184146145439042,15.214393448517606,15.244640751596169,15.274888054674733,15.305135357753297,15.335382660831861,15.365629963910425,15.39587726698899,15.426124570067554,15.456371873146118,15.486619176224682,15.516866479303244,15.547113782381809,15.577361085460371,15.607608388538935,15.6378556916175,15.668102994696063,15.698350297774628,15.728597600853192,15.758844903931754,15.789092207010318,15.819339510088883,15.849586813167447,15.87983411624601,15.910081419324575,15.94032872240314,15.970576025481703,16.000823328560266,16.03107063163883,16.061317934717394,16.091565237795958,16.121812540874522,16.152059843953086,16.18230714703165,16.212554450110215,16.242801753188775,16.27304905626734,16.303296359345904,16.333543662424468,16.363790965503032,16.394038268581596,16.42428557166016,16.454532874738725,16.484780177817285,16.51502748089585,16.545274783974413,16.575522087052978,16.60576939013154,16.636016693210106,16.66626399628867,16.696511299367234,16.7267586024458,16.757005905524363,16.787253208602923,16.817500511681487,16.84774781476005,16.877995117838616,16.90824242091718,16.938489723995744,16.968737027074308,16.998984330152872,17.029231633231436,17.05947893631,17.089726239388565,17.11997354246713,17.150220845545693,17.180468148624257,17.21071545170282,17.240962754781382,17.271210057859946,17.30145736093851,17.331704664017074,17.36195196709564,17.392199270174203,17.422446573252767,17.45269387633133,17.48294117940989,17.513188482488456,17.54343578556702,17.573683088645584,17.60393039172415,17.634177694802712,17.664424997881277,17.69467230095984,17.7249196040384,17.755166907116966,17.78541421019553,17.815661513274094,17.845908816352658,17.876156119431222,17.906403422509786,17.93665072558835,17.966898028666915,17.99714533174548,18.027392634824043,18.057639937902607,18.08788724098117,18.118134544059735,18.1483818471383,18.178629150216864,18.208876453295428,18.23912375637399,18.269371059452553,18.299618362531117,18.32986566560968,18.360112968688245,18.39036027176681,18.420607574845373,18.450854877923938,18.4811021810025,18.511349484081066,18.541596787159627,18.571844090238194,18.602091393316755,18.63233869639532,18.662585999473883,18.692833302552444,18.72308060563101,18.753327908709572,18.78357521178814,18.8138225148667,18.844069817945265,18.87431712102383,18.904564424102393,18.934811727180957,18.96505903025952,18.995306333338085,19.02555363641665,19.055800939495214,19.086048242573778,19.11629554565234,19.146542848730906,19.176790151809467,19.207037454888034,19.237284757966595,19.26753206104516,19.297779364123723,19.328026667202288,19.35827397028085,19.388521273359412,19.41876857643798,19.44901587951654,19.47926318259511,19.50951048567367,19.539757788752237,19.570005091830797,19.600252394909358,19.630499697987926,19.660747001066486,19.690994304145054,19.721241607223615,19.751488910302182,19.781736213380743,19.81198351645931,19.84223081953787,19.872478122616435,19.902725425695,19.932972728773564,19.963220031852128,19.993467334930692,20.023714638009256,20.05396194108782,20.08420924416638,20.11445654724495,20.14470385032351,20.174951153402073,20.205198456480638,20.2354457595592,20.265693062637766,20.29594036571633,20.326187668794894,20.356434971873455,20.386682274952022,20.416929578030583,20.44717688110915,20.47742418418771,20.50767148726628,20.53791879034484,20.568166093423407,20.598413396501968,20.62866069958053,20.658908002659096,20.689155305737657,20.719402608816225,20.749649911894785,20.779897214973353,20.810144518051914,20.840391821130478,20.870639124209042,20.900886427287606,20.93113373036617,20.961381033444734,20.9916283365233,21.021875639601863,21.052122942680427,21.08237024575899,21.11261754883755,21.142864851916116,21.17311215499468,21.203359458073244,21.23360676115181,21.263854064230372,21.294101367308937,21.324348670387497,21.354595973466065,21.384843276544625,21.415090579623193,21.445337882701754,21.47558518578032,21.505832488858882,21.53607979193745,21.56632709501601,21.59657439809457,21.62682170117314,21.6570690042517,21.687316307330267,21.717563610408828,21.747810913487395,21.778058216565956,21.808305519644524,21.838552822723084,21.86880012580165,21.899047428880213,21.929294731958777,21.95954203503734,21.989789338115905,22.02003664119447,22.050283944273033,22.080531247351594,22.110778550430158,22.141025853508722,22.171273156587286,22.20152045966585,22.231767762744415,22.26201506582298,22.292262368901543,22.322509671980107,22.352756975058668,22.383004278137236,22.413251581215796,22.443498884294364,22.473746187372925,22.503993490451492,22.534240793530053,22.564488096608613,22.59473539968718,22.62498270276574,22.65523000584431,22.68547730892287,22.715724612001438,22.74597191508,22.776219218158566,22.806466521237127,22.83671382431569,22.866961127394255,22.89720843047282,22.927455733551383,22.957703036629947,22.98795033970851,23.018197642787072,23.04844494586564,23.0786922489442,23.108939552022765,23.13918685510133,23.169434158179893,23.199681461258457,23.22992876433702,23.260176067415586,23.29042337049415,23.32067067357271,23.350917976651278,23.38116527972984,23.411412582808406,23.441659885886967,23.471907188965535,23.502154492044095,23.532401795122663,23.562649098201224,23.592896401279784,23.623143704358352,23.653391007436912,23.68363831051548,23.71388561359404,23.74413291667261,23.77438021975117,23.804627522829733,23.834874825908297,23.86512212898686,23.895369432065426,23.92561673514399,23.955864038222554,23.986111341301115,24.016358644379682,24.046605947458243,24.076853250536807,24.10710055361537,24.137347856693935,24.1675951597725,24.197842462851064,24.228089765929628,24.258337069008192,24.288584372086756,24.31883167516532,24.34907897824388,24.37932628132245,24.40957358440101,24.439820887479577,24.470068190558138,24.500315493636705,24.530562796715266,24.560810099793827,24.591057402872394,24.621304705950955,24.651552009029523,24.681799312108083,24.71204661518665,24.74229391826521,24.77254122134378,24.80278852442234,24.833035827500904,24.863283130579468,24.893530433658032,24.923777736736596,24.954025039815157,24.984272342893725,25.014519645972285,25.04476694905085,25.075014252129414,25.105261555207978,25.135508858286542,25.165756161365106,25.19600346444367,25.226250767522234,25.2564980706008,25.286745373679363,25.316992676757923,25.34723997983649,25.37748728291505,25.40773458599362,25.43798188907218,25.468229192150748,25.49847649522931,25.528723798307873,25.558971101386437,25.589218404464997,25.619465707543565,25.649713010622126,25.679960313700693,25.710207616779254,25.74045491985782,25.770702222936382,25.800949526014946,25.83119682909351,25.86144413217207,25.89169143525064,25.9219387383292,25.952186041407767,25.982433344486328,26.012680647564896,26.042927950643456,26.07317525372202,26.103422556800584,26.13366985987915,26.163917162957713,26.194164466036277,26.22441176911484,26.254659072193405,26.284906375271966,26.315153678350534,26.345400981429094,26.375648284507662,26.405895587586222,26.436142890664787,26.46639019374335,26.496637496821915,26.52688479990048,26.55713210297904,26.587379406057607,26.617626709136168,26.647874012214736,26.678121315293296,26.708368618371864,26.738615921450425,26.768863224528992,26.799110527607553,26.829357830686114,26.85960513376468,26.889852436843242,26.92009973992181,26.95034704300037,26.980594346078938,27.0108416491575,27.041088952236063,27.071336255314627,27.10158355839319,27.131830861471755,27.16207816455032,27.192325467628883,27.222572770707448,27.252820073786012,27.283067376864576,27.313314679943137,27.343561983021704,27.373809286100265,27.40405658917883,27.434303892257393,27.464551195335957,27.49479849841452,27.525045801493082,27.55529310457165,27.58554040765021,27.615787710728778,27.64603501380734,27.676282316885906,27.706529619964467,27.736776923043035,27.767024226121595,27.797271529200156,27.827518832278724,27.857766135357284,27.888013438435852,27.918260741514413,27.94850804459298,27.97875534767154,28.009002650750105,28.03924995382867,28.069497256907233,28.099744559985798,28.12999186306436,28.160239166142926,28.19048646922149,28.220733772300054,28.25098107537862,28.28122837845718,28.311475681535747,28.341722984614307,28.37197028769287,28.402217590771436,28.43246489385,28.462712196928564,28.492959500007128,28.523206803085692,28.553454106164253,28.58370140924282,28.61394871232138,28.64419601539995,28.67444331847851,28.704690621557077,28.734937924635638,28.7651852277142,28.795432530792766,28.825679833871327,28.855927136949894,28.886174440028455,28.916421743107023,28.946669046185583,28.97691634926415,29.00716365234271,29.037410955421276,29.06765825849984,29.097905561578404,29.12815286465697,29.158400167735532,29.188647470814097,29.21889477389266,29.24914207697122,29.279389380049786,29.30963668312835,29.339883986206914,29.370131289285478,29.400378592364042,29.430625895442606,29.46087319852117,29.491120501599735,29.521367804678295,29.551615107756863,29.581862410835424,29.61210971391399,29.642357016992552,29.67260432007112,29.70285162314968,29.733098926228248,29.76334622930681,29.79359353238537,29.823840835463937,29.854088138542497,29.884335441621065,29.914582744699626,29.944830047778193,29.975077350856754,30.00532465393532,30.035571957013882,30.065819260092447,30.09606656317101,30.126313866249575,30.15656116932814,30.186808472406703,30.217055775485267,30.247303078563828,30.277550381642392,30.307797684720956,30.33804498779952,30.368292290878085,30.39853959395665,30.428786897035213,30.459034200113777,30.489281503192338,30.519528806270905,30.549776109349466,30.580023412428034,30.610270715506594,30.640518018585162,30.670765321663723,30.70101262474229,30.73125992782085,30.76150723089941,30.79175453397798,30.82200183705654,30.852249140135108,30.882496443213668,30.912743746292236,30.942991049370796,30.973238352449364,31.003485655527925,31.03373295860649,31.063980261685053,31.094227564763617,31.12447486784218,31.154722170920742,31.18496947399931,31.21521677707787,31.245464080156435,31.275711383235,31.305958686313563,31.336205989392127,31.36645329247069,31.396700595549255,31.42694789862782,31.457195201706384,31.487442504784948,31.51768980786351,31.547937110942076,31.578184414020637,31.608431717099204,31.638679020177765,31.668926323256333,31.699173626334893,31.729420929413454,31.75966823249202,31.789915535570582,31.82016283864915,31.85041014172771,31.88065744480628,31.91090474788484,31.941152050963407,31.971399354041967,32.00164665712053,32.0318939601991,32.06214126327766,32.09238856635623,32.12263586943479,32.152883172513356,32.183130475591916,32.21337777867048,32.243625081749045,32.273872384827605,32.30411968790617,32.33436699098473,32.3646142940633,32.39486159714186,32.42510890022043,32.45535620329899,32.48560350637755,32.51585080945612,32.54609811253468,32.57634541561325,32.60659271869181,32.636840021770375,32.667087324848936,32.6973346279275,32.727581931006064,32.757829234084625,32.78807653716319,32.81832384024175,32.84857114332032,32.87881844639888,32.90906574947745,32.93931305255601,32.96956035563457,32.99980765871314,33.0300549617917,33.060302264870266,33.09054956794883,33.120796871027395,33.151044174105955,33.18129147718452,33.21153878026308,33.241786083341644,33.27203338642021,33.30228068949877,33.33252799257734,33.3627752956559,33.39302259873447,33.42326990181303,33.4535172048916,33.48376450797016,33.514011811048725,33.544259114127286,33.574506417205846,33.604753720284414,33.635001023362975,33.66524832644154,33.6954956295201,33.72574293259867,33.75599023567723,33.7862375387558,33.81648484183436,33.84673214491293,33.87697944799149,33.907226751070056,33.937474054148616,33.967721357227184,33.997968660305744,34.02821596338431,34.05846326646287,34.08871056954144,34.11895787262,34.14920517569856,34.17945247877713,34.20969978185569,34.23994708493426,34.27019438801282,34.300441691091386,34.33068899416995,34.360936297248514,34.391183600327075,34.42143090340564,34.4516782064842,34.481925509562764,34.51217281264133,34.54242011571989,34.57266741879846,34.60291472187702,34.63316202495559,34.66340932803415,34.69365663111272,34.72390393419128,34.75415123726984,34.784398540348406,34.814645843426966,34.844893146505534,34.875140449584094,34.90538775266266,34.93563505574122,34.96588235881978,34.99612966189835,35.02637696497691,35.05662426805548,35.08687157113404,35.11711887421261,35.14736617729117,35.177613480369736,35.2078607834483,35.23810808652686,35.268355389605425,35.298602692683986,35.32884999576255,35.359097298841114,35.38934460191968,35.41959190499824,35.4498392080768,35.48008651115537,35.51033381423393,35.5405811173125,35.57082842039106,35.60107572346963,35.63132302654819,35.661570329626755,35.691817632705316,35.722064935783884,35.752312238862444,35.78255954194101,35.81280684501957,35.84305414809814,35.8733014511767,35.90354875425527,35.93379605733383,35.9640433604124,35.99429066349096,36.02453796656952,36.054785269648086,36.08503257272665,36.115279875805214,36.145527178883775,36.17577448196234,36.2060217850409,36.23626908811947,36.26651639119803,36.2967636942766,36.32701099735516,36.35725830043373,36.38750560351229,36.417752906590856,36.44800020966942,36.47824751274798,36.508494815826545,36.538742118905105,36.56898942198367,36.599236725062234,36.6294840281408,36.65973133121936,36.68997863429792,36.72022593737649,36.75047324045505,36.78072054353362,36.81096784661218,36.84121514969075,36.87146245276931,36.901709755847875,36.931957058926436],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"AI Rule 3","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784],"y":[0,0.03548994172439679,0.07097988344879358,0.10646982517319037,0.14195976689758716,0.17744970862198395,0.21293965034638074,0.24842959207077753,0.2839195337951743,0.3194094755195711,0.3548994172439679,0.3903893589683647,0.4258793006927615,0.4613692424171583,0.49685918414155505,0.5323491258659518,0.5678390675903486,0.6033290093147454,0.6388189510391422,0.674308892763539,0.7097988344879358,0.7452887762123326,0.7807787179367294,0.8162686596611262,0.851758601385523,0.8872485431099197,0.9227384848343166,0.9582284265587132,0.9937183682831101,1.029208310007507,1.0646982517319037,1.1001881934563005,1.1356781351806973,1.171168076905094,1.2066580186294908,1.2421479603538876,1.2776379020782844,1.3131278438026812,1.348617785527078,1.3841077272514748,1.4195976689758716,1.4550876107002684,1.4905775524246652,1.526067494149062,1.5615574358734587,1.5970473775978555,1.6325373193222523,1.6680272610466491,1.703517202771046,1.7390071444954427,1.7744970862198395,1.8099870279442363,1.8454769696686333,1.88096691139303,1.9164568531174264,1.9519467948418232,1.9874367365662202,2.022926678290617,2.058416620015014,2.0939065617394106,2.1293965034638074,2.164886445188204,2.200376386912601,2.2358663286369977,2.2713562703613945,2.3068462120857913,2.342336153810188,2.377826095534585,2.4133160372589817,2.4488059789833785,2.4842959207077753,2.519785862432172,2.555275804156569,2.5907657458809656,2.6262556876053624,2.661745629329759,2.697235571054156,2.732725512778553,2.7682154545029496,2.8037053962273464,2.839195337951743,2.87468527967614,2.9101752214005367,2.9456651631249335,2.9811551048493303,3.016645046573727,3.052134988298124,3.0876249300225207,3.1231148717469175,3.1586048134713143,3.194094755195711,3.229584696920108,3.2650746386445046,3.3005645803689014,3.3360545220932982,3.371544463817695,3.407034405542092,3.4425243472664886,3.4780142889908854,3.5135042307152817,3.548994172439679,3.5844841141640753,3.6199740558884725,3.6554639976128693,3.6909539393372666,3.726443881061663,3.76193382278606,3.7974237645104565,3.832913706234853,3.86840364795925,3.9038935896836464,3.9393835314080436,3.9748734731324404,4.010363414856838,4.045853356581234,4.081343298305631,4.116833240030028,4.152323181754425,4.187813123478821,4.2233030652032175,4.258793006927615,4.294282948652011,4.329772890376408,4.365262832100805,4.400752773825202,4.436242715549598,4.4717326572739955,4.507222598998392,4.542712540722789,4.578202482447186,4.613692424171583,4.64918236589598,4.684672307620376,4.7201622493447735,4.75565219106917,4.791142132793567,4.826632074517963,4.862122016242361,4.897611957966757,4.933101899691153,4.9685918414155505,5.004081783139947,5.039571724864344,5.0750616665887405,5.110551608313138,5.146041550037534,5.181531491761931,5.2170214334863285,5.252511375210725,5.288001316935122,5.323491258659518,5.358981200383916,5.394471142108312,5.429961083832709,5.465451025557106,5.500940967281503,5.536430909005899,5.5719208507302955,5.607410792454693,5.642900734179089,5.678390675903486,5.713880617627883,5.74937055935228,5.784860501076676,5.8203504428010735,5.85584038452547,5.891330326249867,5.926820267974264,5.962310209698661,5.997800151423058,6.033290093147454,6.0687800348718515,6.104269976596248,6.139759918320645,6.175249860045041,6.210739801769439,6.246229743493835,6.281719685218231,6.3172096269426286,6.352699568667025,6.388189510391422,6.4236794521158185,6.459169393840216,6.494659335564612,6.530149277289009,6.565639219013406,6.601129160737803,6.6366191024622,6.6721090441865964,6.707598985910994,6.74308892763539,6.778578869359787,6.814068811084184,6.849558752808581,6.885048694532977,6.920538636257374,6.956028577981771,6.991518519706167,7.0270084614305635,7.062498403154962,7.097988344879358,7.133478286603754,7.168968228328151,7.204458170052549,7.239948111776945,7.275438053501341,7.310927995225739,7.346417936950135,7.381907878674533,7.4173978203989295,7.452887762123326,7.488377703847722,7.52386764557212,7.559357587296517,7.594847529020913,7.630337470745309,7.665827412469706,7.701317354194104,7.7368072959185,7.7722972376428965,7.807787179367293,7.843277121091691,7.878767062816087,7.914257004540484,7.949746946264881,7.985236887989278,8.020726829713675,8.056216771438072,8.091706713162468,8.127196654886864,8.162686596611263,8.198176538335659,8.233666480060055,8.269156421784452,8.30464636350885,8.340136305233246,8.375626246957642,8.411116188682039,8.446606130406435,8.482096072130833,8.51758601385523,8.553075955579626,8.588565897304022,8.62405583902842,8.659545780752817,8.695035722477213,8.73052566420161,8.766015605926006,8.801505547650404,8.8369954893748,8.872485431099197,8.907975372823593,8.943465314547991,8.978955256272387,9.014445197996784,9.049935139721182,9.085425081445578,9.120915023169976,9.156404964894373,9.191894906618769,9.227384848343165,9.262874790067563,9.29836473179196,9.333854673516356,9.369344615240752,9.404834556965149,9.440324498689547,9.475814440413943,9.51130438213834,9.546794323862736,9.582284265587134,9.61777420731153,9.653264149035927,9.688754090760323,9.724244032484721,9.759733974209118,9.795223915933514,9.83071385765791,9.866203799382307,9.901693741106705,9.937183682831101,9.972673624555497,10.008163566279894,10.043653508004292,10.079143449728688,10.114633391453085,10.150123333177481,10.185613274901877,10.221103216626275,10.256593158350672,10.292083100075068,10.327573041799464,10.363062983523863,10.398552925248259,10.434042866972657,10.469532808697053,10.50502275042145,10.540512692145848,10.576002633870244,10.61149257559464,10.646982517319037,10.682472459043435,10.717962400767831,10.753452342492228,10.788942284216624,10.82443222594102,10.859922167665419,10.895412109389815,10.930902051114211,10.966391992838608,11.001881934563006,11.037371876287402,11.072861818011798,11.108351759736195,11.143841701460591,11.17933164318499,11.214821584909386,11.250311526633782,11.285801468358178,11.321291410082576,11.356781351806973,11.392271293531369,11.427761235255765,11.463251176980163,11.49874111870456,11.534231060428956,11.569721002153353,11.605210943877749,11.640700885602147,11.676190827326543,11.71168076905094,11.747170710775336,11.782660652499734,11.81815059422413,11.853640535948529,11.889130477672925,11.924620419397321,11.96011036112172,11.995600302846116,12.031090244570512,12.066580186294908,12.102070128019307,12.137560069743703,12.1730500114681,12.208539953192496,12.244029894916892,12.27951983664129,12.315009778365686,12.350499720090083,12.38598966181448,12.421479603538877,12.456969545263274,12.49245948698767,12.527949428712066,12.563439370436463,12.59892931216086,12.634419253885257,12.669909195609653,12.70539913733405,12.740889079058448,12.776379020782844,12.81186896250724,12.847358904231637,12.882848845956033,12.918338787680431,12.953828729404828,12.989318671129224,13.02480861285362,13.060298554578019,13.095788496302415,13.131278438026811,13.16676837975121,13.202258321475606,13.237748263200002,13.2732382049244,13.308728146648797,13.344218088373193,13.379708030097591,13.415197971821987,13.450687913546384,13.48617785527078,13.521667796995178,13.557157738719575,13.59264768044397,13.628137622168367,13.663627563892764,13.699117505617162,13.734607447341558,13.770097389065954,13.80558733079035,13.841077272514749,13.876567214239145,13.912057155963542,13.94754709768794,13.983037039412334,14.018526981136732,14.054016922861127,14.089506864585525,14.124996806309923,14.160486748034318,14.195976689758716,14.231466631483114,14.266956573207509,14.302446514931907,14.337936456656301,14.3734263983807,14.408916340105097,14.444406281829492,14.47989622355389,14.515386165278287,14.550876107002683,14.586366048727081,14.621855990451477,14.657345932175874,14.69283587390027,14.728325815624668,14.763815757349066,14.79930569907346,14.834795640797859,14.870285582522257,14.905775524246652,14.94126546597105,14.976755407695444,15.012245349419842,15.04773529114424,15.083225232868635,15.118715174593033,15.154205116317428,15.189695058041826,15.225184999766224,15.260674941490619,15.296164883215017,15.331654824939411,15.36714476666381,15.402634708388208,15.438124650112602,15.473614591837,15.509104533561398,15.544594475285793,15.580084417010191,15.615574358734586,15.651064300458984,15.686554242183382,15.722044183907776,15.757534125632175,15.79302406735657,15.828514009080967,15.864003950805365,15.899493892529762,15.934983834254158,15.970473775978556,16.00596371770295,16.04145365942735,16.076943601151743,16.112433542876143,16.14792348460054,16.183413426324936,16.218903368049332,16.25439330977373,16.289883251498125,16.325373193222525,16.360863134946918,16.396353076671318,16.43184301839571,16.46733296012011,16.502822901844507,16.538312843568903,16.5738027852933,16.6092927270177,16.644782668742096,16.680272610466492,16.71576255219089,16.751252493915285,16.78674243563968,16.822232377364077,16.857722319088477,16.89321226081287,16.92870220253727,16.964192144261666,16.999682085986063,17.03517202771046,17.070661969434855,17.10615191115925,17.14164185288365,17.177131794608044,17.212621736332444,17.24811167805684,17.283601619781237,17.319091561505633,17.35458150323003,17.390071444954426,17.425561386678826,17.46105132840322,17.49654127012762,17.53203121185201,17.56752115357641,17.603011095300808,17.638501037025204,17.6739909787496,17.709480920474,17.744970862198393,17.780460803922793,17.815950745647186,17.851440687371586,17.886930629095982,17.92242057082038,17.957910512544775,17.99340045426917,18.028890395993567,18.064380337717967,18.099870279442364,18.13536022116676,18.170850162891156,18.206340104615553,18.241830046339953,18.277319988064345,18.312809929788745,18.34829987151314,18.383789813237538,18.419279754961934,18.45476969668633,18.490259638410727,18.525749580135127,18.56123952185952,18.59672946358392,18.632219405308312,18.667709347032712,18.70319928875711,18.738689230481505,18.7741791722059,18.809669113930298,18.845159055654694,18.880648997379094,18.916138939103487,18.951628880827887,18.987118822552283,19.02260876427668,19.058098706001076,19.093588647725472,19.12907858944987,19.164568531174268,19.20005847289866,19.23554841462306,19.271038356347454,19.306528298071854,19.34201823979625,19.377508181520646,19.412998123245043,19.448488064969442,19.48397800669384,19.519467948418235,19.55495789014263,19.590447831867028,19.625937773591424,19.66142771531582,19.69691765704022,19.732407598764613,19.767897540489013,19.80338748221341,19.838877423937806,19.874367365662202,19.9098573073866,19.945347249110995,19.980837190835395,20.016327132559788,20.051817074284187,20.087307016008584,20.12279695773298,20.158286899457377,20.193776841181773,20.22926678290617,20.26475672463057,20.300246666354962,20.335736608079362,20.371226549803755,20.406716491528154,20.44220643325255,20.477696374976947,20.513186316701344,20.54867625842574,20.584166200150136,20.619656141874536,20.65514608359893,20.69063602532333,20.726125967047725,20.76161590877212,20.797105850496518,20.832595792220914,20.868085733945314,20.90357567566971,20.939065617394107,20.974555559118503,21.0100455008429,21.045535442567296,21.081025384291696,21.11651532601609,21.15200526774049,21.187495209464885,21.22298515118928,21.258475092913677,21.293965034638074,21.32945497636247,21.36494491808687,21.400434859811263,21.435924801535663,21.471414743260056,21.506904684984455,21.54239462670885,21.577884568433248,21.613374510157644,21.64886445188204,21.684354393606437,21.719844335330837,21.75533427705523,21.79082421877963,21.826314160504026,21.861804102228422,21.89729404395282,21.932783985677215,21.96827392740161,22.00376386912601,22.039253810850404,22.074743752574804,22.1102336942992,22.145723636023597,22.181213577747993,22.21670351947239,22.252193461196786,22.287683402921182,22.323173344645582,22.35866328636998,22.394153228094375,22.42964316981877,22.46513311154317,22.500623053267564,22.536112994991964,22.571602936716356,22.607092878440756,22.642582820165153,22.67807276188955,22.713562703613945,22.74905264533834,22.784542587062738,22.820032528787138,22.85552247051153,22.89101241223593,22.926502353960327,22.961992295684723,22.99748223740912,23.032972179133516,23.068462120857912,23.103952062582312,23.139442004306705,23.174931946031105,23.210421887755498,23.245911829479898,23.281401771204294,23.31689171292869,23.352381654653087,23.387871596377483,23.42336153810188,23.45885147982628,23.494341421550672,23.529831363275072,23.56532130499947,23.600811246723865,23.63630118844826,23.671791130172657,23.707281071897057,23.742771013621454,23.77826095534585,23.813750897070246,23.849240838794643,23.88473078051904,23.92022072224344,23.95571066396783,23.99120060569223,24.026690547416624,24.062180489141024,24.09767043086542,24.133160372589817,24.168650314314213,24.204140256038613,24.239630197763006,24.275120139487406,24.3106100812118,24.3461000229362,24.381589964660595,24.41707990638499,24.452569848109388,24.488059789833784,24.52354973155818,24.55903967328258,24.594529615006973,24.630019556731373,24.66550949845577,24.700999440180166,24.736489381904562,24.77197932362896,24.807469265353355,24.842959207077755,24.878449148802147,24.913939090526547,24.949429032250944,24.98491897397534,25.020408915699736,25.055898857424133,25.09138879914853,25.126878740872925,25.162368682597325,25.19785862432172,25.233348566046118,25.268838507770514,25.304328449494914,25.339818391219307,25.375308332943707,25.4107982746681,25.4462882163925,25.481778158116896,25.517268099841292,25.55275804156569,25.588247983290085,25.62373792501448,25.65922786673888,25.694717808463274,25.730207750187674,25.765697691912067,25.801187633636467,25.836677575360863,25.87216751708526,25.907657458809656,25.943147400534055,25.97863734225845,26.014127283982848,26.04961722570724,26.08510716743164,26.120597109156037,26.156087050880434,26.19157699260483,26.227066934329226,26.262556876053623,26.298046817778022,26.33353675950242,26.369026701226815,26.40451664295121,26.440006584675608,26.475496526400004,26.5109864681244,26.5464764098488,26.581966351573197,26.617456293297593,26.65294623502199,26.688436176746386,26.723926118470782,26.759416060195182,26.794906001919575,26.830395943643975,26.865885885368368,26.901375827092767,26.936865768817164,26.97235571054156,27.007845652265956,27.043335593990356,27.07882553571475,27.11431547743915,27.149805419163542,27.18529536088794,27.220785302612338,27.256275244336734,27.29176518606113,27.327255127785527,27.362745069509923,27.398235011234323,27.433724952958716,27.469214894683116,27.50470483640751,27.54019477813191,27.575684719856305,27.6111746615807,27.646664603305098,27.682154545029498,27.71764448675389,27.75313442847829,27.788624370202687,27.824114311927083],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Ext Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199],"y":[0,0.021625372677520214,0.04325074535504043,0.06487611803256065,0.08650149071008086,0.10812686338760108,0.1297522360651213,0.1513776087426415,0.1730029814201617,0.19462835409768195,0.21625372677520216,0.2378790994527224,0.2595044721302426,0.2811298448077628,0.302755217485283,0.32438059016280324,0.3460059628403234,0.36763133551784366,0.3892567081953639,0.41088208087288414,0.4325074535504043,0.4541328262279245,0.4757581989054448,0.497383571582965,0.5190089442604852,0.5406343169380053,0.5622596896155256,0.5838850622930458,0.605510434970566,0.6271358076480863,0.6487611803256065,0.6703865530031267,0.6920119256806468,0.7136372983581671,0.7352626710356873,0.7568880437132075,0.7785134163907278,0.800138789068248,0.8217641617457683,0.8433895344232885,0.8650149071008086,0.8866402797783289,0.908265652455849,0.9298910251333693,0.9515163978108896,0.9731417704884097,0.99476714316593,1.0163925158434501,1.0380178885209703,1.0596432611984905,1.0812686338760107,1.102894006553531,1.1245193792310513,1.1461447519085715,1.1677701245860916,1.189395497263612,1.211020869941132,1.2326462426186524,1.2542716152961726,1.2758969879736928,1.297522360651213,1.3191477333287331,1.3407731060062533,1.3623984786837737,1.3840238513612937,1.405649224038814,1.4272745967163343,1.4488999693938545,1.4705253420713746,1.4921507147488948,1.513776087426415,1.5354014601039354,1.5570268327814556,1.5786522054589758,1.600277578136496,1.6219029508140161,1.6435283234915365,1.6651536961690567,1.686779068846577,1.7084044415240969,1.7300298142016173,1.7516551868791375,1.7732805595566579,1.794905932234178,1.816531304911698,1.8381566775892182,1.8597820502667386,1.8814074229442588,1.9030327956217792,1.9246581682992994,1.9462835409768193,1.9679089136543395,1.98953428633186,2.01115965900938,2.0327850316869003,2.0544104043644205,2.0760357770419406,2.097661149719461,2.119286522396981,2.1409118950745016,2.1625372677520214,2.1841626404295416,2.205788013107062,2.2274133857845824,2.2490387584621025,2.2706641311396227,2.292289503817143,2.313914876494663,2.3355402491721833,2.3571656218497035,2.378790994527224,2.400416367204744,2.422041739882264,2.443667112559784,2.465292485237305,2.486917857914825,2.508543230592345,2.5301686032698654,2.5517939759473856,2.5734193486249057,2.595044721302426,2.616670093979946,2.6382954666574663,2.6599208393349865,2.6815462120125066,2.703171584690027,2.7247969573675475,2.746422330045067,2.7680477027225874,2.789673075400108,2.811298448077628,2.8329238207551484,2.8545491934326686,2.8761745661101887,2.897799938787709,2.919425311465229,2.9410506841427493,2.96267605682027,2.9843014294977896,3.00592680217531,3.02755217485283,3.0491775475303506,3.070802920207871,3.092428292885391,3.114053665562911,3.1356790382404314,3.1573044109179516,3.1789297835954717,3.200555156272992,3.222180528950512,3.2438059016280323,3.2654312743055525,3.287056646983073,3.3086820196605933,3.3303073923381135,3.3519327650156336,3.373558137693154,3.3951835103706736,3.4168088830481937,3.438434255725714,3.4600596284032346,3.4816850010807547,3.503310373758275,3.524935746435795,3.5465611191133157,3.568186491790836,3.589811864468356,3.6114372371458763,3.633062609823396,3.654687982500916,3.6763133551784364,3.697938727855957,3.719564100533477,3.7411894732109974,3.7628148458885176,3.7844402185660377,3.8060655912435584,3.8276909639210785,3.8493163365985987,3.8709417092761185,3.8925670819536387,3.914192454631159,3.935817827308679,3.9574431999861996,3.97906857266372,4.00069394534124,4.02231931801876,4.04394469069628,4.065570063373801,4.087195436051321,4.108820808728841,4.130446181406361,4.152071554083881,4.1736969267614015,4.195322299438922,4.216947672116442,4.238573044793962,4.260198417471483,4.281823790149003,4.3034491628265235,4.325074535504043,4.346699908181563,4.368325280859083,4.389950653536603,4.411576026214124,4.4332013988916446,4.454826771569165,4.476452144246685,4.498077516924205,4.519702889601725,4.5413282622792455,4.562953634956766,4.584579007634286,4.606204380311806,4.627829752989326,4.649455125666846,4.671080498344367,4.692705871021887,4.714331243699407,4.735956616376927,4.757581989054448,4.779207361731968,4.800832734409488,4.822458107087008,4.844083479764528,4.865708852442048,4.887334225119568,4.9089595977970895,4.93058497047461,4.95221034315213,4.97383571582965,4.99546108850717,5.01708646118469,5.03871183386221,5.060337206539731,5.081962579217251,5.103587951894771,5.125213324572291,5.1468386972498115,5.168464069927332,5.190089442604852,5.211714815282372,5.233340187959892,5.254965560637413,5.276590933314933,5.298216305992453,5.319841678669973,5.341467051347493,5.363092424025013,5.3847177967025335,5.406343169380054,5.427968542057575,5.449593914735095,5.471219287412615,5.492844660090134,5.514470032767655,5.536095405445175,5.557720778122695,5.579346150800216,5.600971523477736,5.622596896155256,5.644222268832777,5.665847641510297,5.687473014187817,5.709098386865337,5.730723759542857,5.7523491322203775,5.773974504897898,5.795599877575418,5.817225250252938,5.838850622930458,5.860475995607978,5.882101368285499,5.903726740963019,5.92535211364054,5.94697748631806,5.968602858995579,5.9902282316730995,6.01185360435062,6.03347897702814,6.05510434970566,6.076729722383181,6.098355095060701,6.1199804677382215,6.141605840415742,6.163231213093262,6.184856585770782,6.206481958448301,6.228107331125822,6.249732703803343,6.271358076480863,6.292983449158383,6.314608821835903,6.336234194513423,6.3578595671909435,6.379484939868464,6.401110312545984,6.422735685223505,6.444361057901024,6.465986430578544,6.487611803256065,6.509237175933586,6.530862548611105,6.552487921288625,6.574113293966146,6.5957386666436655,6.6173640393211866,6.638989411998706,6.660614784676227,6.682240157353746,6.703865530031267,6.7254909027087875,6.747116275386308,6.768741648063828,6.790367020741347,6.811992393418868,6.8336177660963875,6.855243138773909,6.876868511451428,6.898493884128949,6.920119256806469,6.94174462948399,6.9633700021615095,6.9849953748390305,7.00662074751655,7.028246120194069,7.04987149287159,7.07149686554911,7.0931222382266315,7.114747610904151,7.136372983581672,7.157998356259191,7.179623728936712,7.2012491016142315,7.2228744742917526,7.244499846969273,7.266125219646792,7.287750592324313,7.309375965001832,7.3310013376793535,7.352626710356873,7.374252083034394,7.395877455711914,7.417502828389434,7.439128201066954,7.4607535737444755,7.482378946421995,7.504004319099514,7.525629691777035,7.547255064454555,7.5688804371320755,7.590505809809596,7.612131182487117,7.633756555164636,7.655381927842157,7.677007300519676,7.6986326731971975,7.720258045874717,7.741883418552237,7.763508791229758,7.785134163907277,7.806759536584798,7.828384909262318,7.850010281939839,7.871635654617358,7.893261027294879,7.914886399972399,7.93651177264992,7.95813714532744,7.979762518004959,8.00138789068248,8.02301326336,8.04463863603752,8.06626400871504,8.08788938139256,8.10951475407008,8.131140126747601,8.152765499425122,8.174390872102641,8.196016244780163,8.217641617457682,8.239266990135203,8.260892362812722,8.282517735490243,8.304143108167763,8.325768480845284,8.347393853522803,8.369019226200324,8.390644598877843,8.412269971555363,8.433895344232884,8.455520716910405,8.477146089587924,8.498771462265445,8.520396834942966,8.542022207620485,8.563647580298007,8.585272952975526,8.606898325653047,8.628523698330566,8.650149071008085,8.671774443685607,8.693399816363126,8.715025189040647,8.736650561718166,8.758275934395687,8.779901307073207,8.801526679750728,8.823152052428249,8.84477742510577,8.866402797783289,8.888028170460808,8.90965354313833,8.931278915815849,8.95290428849337,8.97452966117089,8.99615503384841,9.01778040652593,9.03940577920345,9.06103115188097,9.082656524558491,9.10428189723601,9.125907269913531,9.147532642591052,9.169158015268572,9.190783387946093,9.212408760623612,9.234034133301133,9.255659505978652,9.277284878656173,9.298910251333693,9.320535624011214,9.342160996688733,9.363786369366252,9.385411742043773,9.407037114721293,9.428662487398814,9.450287860076333,9.471913232753854,9.493538605431375,9.515163978108896,9.536789350786416,9.558414723463937,9.580040096141456,9.601665468818975,9.623290841496496,9.644916214174016,9.666541586851537,9.688166959529056,9.709792332206577,9.731417704884096,9.753043077561617,9.774668450239137,9.796293822916658,9.817919195594179,9.839544568271698,9.86116994094922,9.882795313626739,9.90442068630426,9.926046058981779,9.9476714316593,9.96929680433682,9.99092217701434,10.01254754969186,10.03417292236938,10.0557982950469,10.07742366772442,10.09904904040194,10.120674413079461,10.14229978575698,10.163925158434502,10.185550531112023,10.207175903789542,10.228801276467063,10.250426649144583,10.272052021822104,10.293677394499623,10.315302767177142,10.336928139854663,10.358553512532183,10.380178885209704,10.401804257887223,10.423429630564744,10.445055003242263,10.466680375919784,10.488305748597305,10.509931121274827,10.531556493952346,10.553181866629865,10.574807239307386,10.596432611984905,10.618057984662427,10.639683357339946,10.661308730017467,10.682934102694986,10.704559475372507,10.726184848050027,10.747810220727546,10.769435593405067,10.791060966082588,10.812686338760107,10.834311711437628,10.85593708411515,10.877562456792669,10.89918782947019,10.92081320214771,10.94243857482523,10.96406394750275,10.985689320180269,11.00731469285779,11.02894006553531,11.05056543821283,11.07219081089035,11.09381618356787,11.11544155624539,11.137066928922911,11.158692301600432,11.180317674277953,11.201943046955472,11.223568419632992,11.245193792310513,11.266819164988032,11.288444537665553,11.310069910343072,11.331695283020593,11.353320655698113,11.374946028375634,11.396571401053153,11.418196773730674,11.439822146408194,11.461447519085715,11.483072891763236,11.504698264440755,11.526323637118276,11.547949009795795,11.569574382473316,11.591199755150836,11.612825127828357,11.634450500505876,11.656075873183397,11.677701245860916,11.699326618538436,11.720951991215957,11.742577363893476,11.764202736570997,11.785828109248518,11.807453481926037,11.829078854603559,11.85070422728108,11.872329599958599,11.89395497263612,11.91558034531364,11.937205717991159,11.95883109066868,11.980456463346199,12.00208183602372,12.02370720870124,12.04533258137876,12.06695795405628,12.0885833267338,12.11020869941132,12.131834072088841,12.153459444766362,12.175084817443881,12.196710190121403,12.218335562798922,12.239960935476443,12.261586308153962,12.283211680831483,12.304837053509003,12.326462426186524,12.348087798864043,12.369713171541564,12.391338544219083,12.412963916896603,12.434589289574124,12.456214662251645,12.477840034929164,12.499465407606685,12.521090780284206,12.542716152961725,12.564341525639247,12.585966898316766,12.607592270994287,12.629217643671806,12.650843016349326,12.672468389026847,12.694093761704366,12.715719134381887,12.737344507059406,12.758969879736927,12.780595252414447,12.802220625091968,12.823845997769489,12.84547137044701,12.86709674312453,12.888722115802048,12.91034748847957,12.931972861157089,12.95359823383461,12.97522360651213,12.996848979189648,13.018474351867171,13.04009972454469,13.06172509722221,13.08335046989973,13.10497584257725,13.126601215254771,13.148226587932292,13.169851960609812,13.191477333287331,13.213102705964854,13.234728078642373,13.256353451319892,13.277978823997412,13.299604196674935,13.321229569352454,13.342854942029973,13.364480314707492,13.386105687385012,13.407731060062535,13.429356432740054,13.450981805417575,13.472607178095094,13.494232550772615,13.515857923450136,13.537483296127656,13.559108668805175,13.580734041482694,13.602359414160217,13.623984786837736,13.645610159515256,13.667235532192775,13.688860904870298,13.710486277547817,13.732111650225336,13.753737022902856,13.775362395580379,13.796987768257898,13.818613140935419,13.840238513612938,13.861863886290458,13.88348925896798,13.9051146316455,13.926740004323019,13.948365377000538,13.969990749678061,13.99161612235558,14.0132414950331,14.034866867710619,14.056492240388138,14.078117613065661,14.09974298574318,14.121368358420701,14.14299373109822,14.164619103775742,14.186244476453263,14.207869849130782,14.229495221808302,14.25112059448582,14.272745967163344,14.294371339840863,14.315996712518382,14.337622085195902,14.359247457873424,14.380872830550944,14.402498203228463,14.424123575905984,14.445748948583505,14.467374321261024,14.488999693938545,14.510625066616065,14.532250439293584,14.553875811971107,14.575501184648626,14.597126557326146,14.618751930003665,14.640377302681188,14.662002675358707,14.683628048036226,14.705253420713746,14.726878793391265,14.748504166068788,14.770129538746307,14.791754911423828,14.813380284101347,14.835005656778868,14.85663102945639,14.878256402133909,14.899881774811428,14.921507147488951,14.94313252016647,14.96475789284399,14.986383265521509,15.008008638199028,15.029634010876551,15.05125938355407,15.07288475623159,15.09451012890911,15.116135501586632,15.137760874264151,15.159386246941672,15.181011619619191,15.20263699229671,15.224262364974233,15.245887737651753,15.267513110329272,15.289138483006791,15.310763855684314,15.332389228361833,15.354014601039353,15.375639973716872,15.397265346394395,15.418890719071914,15.440516091749434,15.462141464426955,15.483766837104474,15.505392209781997,15.527017582459516,15.548642955137035,15.570268327814555,15.591893700492077,15.613519073169597,15.635144445847116,15.656769818524635,15.678395191202155,15.700020563879677,15.721645936557197,15.743271309234716,15.764896681912237,15.786522054589758,15.808147427267278,15.829772799944799,15.851398172622318,15.87302354529984,15.89464891797736,15.91627429065488,15.937899663332399,15.959525036009918,15.98115040868744,16.00277578136496,16.02440115404248,16.04602652672,16.06765189939752,16.08927727207504,16.11090264475256,16.13252801743008,16.1541533901076,16.17577876278512,16.19740413546264,16.21902950814016,16.24065488081768,16.262280253495202,16.28390562617272,16.305530998850244,16.327156371527764,16.348781744205283,16.370407116882806,16.392032489560325,16.413657862237844,16.435283234915364,16.456908607592887,16.478533980270406,16.500159352947925,16.521784725625444,16.543410098302967,16.565035470980487,16.586660843658006,16.608286216335525,16.629911589013044,16.651536961690567,16.673162334368087,16.694787707045606,16.716413079723125,16.738038452400648,16.759663825078167,16.781289197755687,16.802914570433206,16.824539943110725,16.846165315788248,16.867790688465767,16.889416061143287,16.91104143382081,16.93266680649833,16.954292179175848,16.97591755185337,16.99754292453089,17.019168297208413,17.040793669885932,17.06241904256345,17.08404441524097,17.10566978791849,17.127295160596013,17.148920533273532,17.17054590595105,17.19217127862857,17.213796651306094,17.235422023983613,17.257047396661132,17.27867276933865,17.30029814201617,17.321923514693694,17.343548887371213,17.365174260048732,17.38679963272625,17.408425005403775,17.430050378081294,17.451675750758813,17.473301123436332,17.494926496113855,17.516551868791375,17.538177241468894,17.559802614146413,17.581427986823936,17.603053359501455,17.624678732178975,17.646304104856497,17.667929477534017,17.68955485021154,17.71118022288906,17.732805595566578,17.754430968244097,17.776056340921617,17.79768171359914,17.81930708627666,17.84093245895418,17.862557831631698,17.88418320430922,17.90580857698674,17.92743394966426,17.94905932234178,17.9706846950193,17.99231006769682,18.01393544037434,18.03556081305186,18.05718618572938,18.0788115584069,18.10043693108442,18.12206230376194,18.14368767643946,18.165313049116982,18.1869384217945,18.20856379447202,18.23018916714954,18.251814539827063,18.273439912504582,18.295065285182105,18.316690657859624,18.338316030537143,18.359941403214666,18.381566775892185,18.403192148569705,18.424817521247224,18.446442893924747,18.468068266602266,18.489693639279785,18.511319011957305,18.532944384634824,18.554569757312347,18.576195129989866,18.597820502667386,18.619445875344905,18.641071248022428,18.662696620699947,18.684321993377466,18.705947366054986,18.727572738732505,18.749198111410028,18.770823484087547,18.792448856765066,18.814074229442586,18.83569960212011,18.857324974797628,18.878950347475147,18.900575720152666,18.92220109283019,18.94382646550771,18.96545183818523,18.98707721086275,19.00870258354027,19.030327956217793,19.051953328895312,19.07357870157283,19.09520407425035,19.116829446927873,19.138454819605393,19.160080192282912,19.18170556496043,19.20333093763795,19.224956310315473,19.246581682992993,19.268207055670512,19.28983242834803,19.311457801025554,19.333083173703073,19.354708546380593,19.376333919058112,19.39795929173563,19.419584664413154,19.441210037090674,19.462835409768193,19.484460782445712,19.506086155123235,19.527711527800754,19.549336900478274,19.570962273155793,19.592587645833316,19.614213018510835,19.635838391188358,19.657463763865877,19.679089136543396,19.70071450922092,19.72233988189844,19.743965254575958,19.765590627253477,19.787215999931,19.80884137260852,19.83046674528604,19.852092117963558,19.873717490641077,19.8953428633186,19.91696823599612,19.93859360867364,19.960218981351158,19.98184435402868,20.0034697267062,20.02509509938372,20.04672047206124,20.06834584473876,20.08997121741628,20.1115965900938,20.13322196277132,20.15484733544884,20.17647270812636,20.19809808080388,20.2197234534814,20.241348826158923,20.262974198836442,20.28459957151396,20.306224944191484,20.327850316869004,20.349475689546523,20.371101062224046,20.392726434901565,20.414351807579084,20.435977180256604,20.457602552934127,20.479227925611646,20.500853298289165,20.522478670966684,20.544104043644207,20.565729416321727,20.587354788999246,20.608980161676765,20.630605534354284,20.652230907031807,20.673856279709327,20.695481652386846,20.717107025064365,20.738732397741888,20.760357770419407,20.781983143096927,20.803608515774446,20.825233888451965,20.846859261129488,20.868484633807007,20.890110006484527,20.91173537916205,20.93336075183957,20.954986124517088,20.97661149719461,20.99823686987213,21.019862242549653,21.041487615227172,21.06311298790469,21.08473836058221,21.10636373325973,21.127989105937253,21.149614478614772,21.17123985129229,21.19286522396981,21.214490596647334,21.236115969324853,21.257741342002372,21.27936671467989,21.30099208735741,21.322617460034934,21.344242832712453,21.365868205389972,21.38749357806749,21.409118950745015,21.430744323422534,21.452369696100053,21.473995068777572,21.49562044145509,21.517245814132615,21.538871186810134,21.560496559487653,21.582121932165176,21.603747304842695,21.625372677520215,21.646998050197737,21.668623422875257,21.69024879555278,21.7118741682303,21.733499540907818,21.755124913585338,21.776750286262857,21.79837565894038,21.8200010316179,21.84162640429542,21.863251776972938,21.88487714965046,21.90650252232798,21.9281278950055,21.94975326768302,21.971378640360538,21.99300401303806,22.01462938571558,22.0362547583931,22.05788013107062,22.07950550374814,22.10113087642566,22.12275624910318,22.1443816217807,22.166006994458222,22.18763236713574,22.20925773981326,22.23088311249078,22.252508485168303,22.274133857845822,22.295759230523345,22.317384603200864,22.339009975878383,22.360635348555906,22.382260721233425,22.403886093910945,22.425511466588464,22.447136839265983,22.468762211943506,22.490387584621025,22.512012957298545,22.533638329976064,22.555263702653587,22.576889075331106,22.598514448008626,22.620139820686145,22.641765193363668,22.663390566041187,22.685015938718706,22.706641311396226,22.728266684073745,22.749892056751268,22.771517429428787,22.793142802106306,22.814768174783826,22.83639354746135,22.858018920138868,22.879644292816387,22.901269665493906,22.92289503817143,22.94452041084895,22.96614578352647,22.98777115620399,23.00939652888151,23.031021901559033,23.052647274236552,23.07427264691407,23.09589801959159,23.117523392269113,23.139148764946633,23.160774137624152,23.18239951030167,23.20402488297919,23.225650255656713,23.247275628334233,23.268901001011752,23.29052637368927,23.312151746366794,23.333777119044314,23.355402491721833,23.377027864399352,23.39865323707687,23.420278609754394,23.441903982431914,23.463529355109433,23.485154727786952,23.506780100464475,23.528405473141994,23.550030845819514,23.571656218497036,23.593281591174556,23.614906963852075,23.636532336529598,23.658157709207117,23.679783081884636,23.70140845456216,23.72303382723968,23.744659199917198,23.766284572594717,23.78790994527224,23.80953531794976,23.83116069062728,23.852786063304798,23.874411435982317,23.89603680865984,23.91766218133736,23.93928755401488,23.960912926692398,23.98253829936992,24.00416367204744,24.02578904472496,24.04741441740248,24.069039790079998,24.09066516275752,24.11229053543504,24.13391590811256,24.15554128079008,24.1771666534676,24.19879202614512,24.22041739882264,24.242042771500163,24.263668144177682,24.2852935168552,24.306918889532724,24.328544262210244,24.350169634887763,24.371795007565286,24.393420380242805,24.415045752920324,24.436671125597844,24.458296498275367,24.479921870952886,24.501547243630405,24.523172616307924,24.544797988985444,24.566423361662967,24.588048734340486,24.609674107018005,24.631299479695524,24.652924852373047,24.674550225050567,24.696175597728086,24.717800970405605,24.739426343083128,24.761051715760647,24.782677088438167,24.804302461115686,24.825927833793205,24.847553206470728,24.869178579148247,24.890803951825767,24.91242932450329,24.93405469718081,24.955680069858328,24.97730544253585,24.99893081521337,25.02055618789089,25.042181560568412,25.06380693324593,25.08543230592345,25.10705767860097,25.128683051278493,25.150308423956012,25.17193379663353,25.19355916931105,25.215184541988574,25.236809914666093,25.258435287343612,25.28006066002113,25.30168603269865,25.323311405376174,25.344936778053693,25.366562150731212,25.38818752340873,25.409812896086255,25.431438268763774,25.453063641441293,25.474689014118812,25.49631438679633,25.517939759473855,25.539565132151374,25.561190504828893,25.582815877506416,25.604441250183935,25.626066622861458,25.647691995538977,25.669317368216497,25.69094274089402,25.71256811357154,25.73419348624906,25.755818858926578,25.777444231604097,25.79906960428162,25.82069497695914,25.84232034963666,25.863945722314178,25.8855710949917,25.90719646766922,25.92882184034674],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432],"y":[0,0.04983319680449775,0.0996663936089955,0.14949959041349326,0.199332787217991,0.24916598402248874,0.2989991808269865,0.3488323776314842,0.398665574435982,0.44849877124047977,0.4983319680449775,0.5481651648494752,0.597998361653973,0.6478315584584707,0.6976647552629685,0.7474979520674663,0.797331148871964,0.8471643456764618,0.8969975424809595,0.9468307392854574,0.996663936089955,1.0464971328944528,1.0963303296989504,1.1461635265034482,1.195996723307946,1.2458299201124439,1.2956631169169415,1.3454963137214393,1.395329510525937,1.445162707330435,1.4949959041349326,1.5448291009394304,1.594662297743928,1.644495494548426,1.6943286913529236,1.7441618881574212,1.793995084961919,1.8438282817664169,1.8936614785709147,1.9434946753754123,1.99332787217991,2.043161068984408,2.0929942657889056,2.142827462593403,2.192660659397901,2.242493856202399,2.2923270530068964,2.3421602498113945,2.391993446615892,2.44182664342039,2.4916598402248877,2.5414930370293853,2.591326233833883,2.641159430638381,2.6909926274428786,2.7408258242473766,2.790659021051874,2.840492217856372,2.89032541466087,2.940158611465367,2.989991808269865,3.0398250050743627,3.0896582018788608,3.1394913986833584,3.189324595487856,3.239157792292354,3.288990989096852,3.338824185901349,3.3886573827058473,3.4384905795103444,3.4883237763148425,3.5381569731193405,3.587990169923838,3.6378233667283357,3.6876565635328338,3.7374897603373314,3.7873229571418294,3.8371561539463266,3.8869893507508246,3.9368225475553227,3.98665574435982,4.036488941164318,4.086322137968816,4.1361553347733135,4.185988531577811,4.235821728382309,4.285654925186806,4.335488121991305,4.385321318795802,4.4351545156003,4.484987712404798,4.534820909209295,4.584654106013793,4.634487302818291,4.684320499622789,4.734153696427287,4.783986893231784,4.833820090036282,4.88365328684078,4.933486483645277,4.9833196804497755,5.033152877254273,5.082986074058771,5.132819270863268,5.182652467667766,5.2324856644722635,5.282318861276762,5.33215205808126,5.381985254885757,5.431818451690255,5.481651648494753,5.53148484529925,5.581318042103748,5.631151238908246,5.680984435712744,5.730817632517242,5.78065082932174,5.8304840261262365,5.880317222930734,5.930150419735233,5.97998361653973,6.029816813344229,6.079650010148725,6.129483206953223,6.1793164037577215,6.229149600562219,6.278982797366717,6.328815994171214,6.378649190975712,6.4284823877802095,6.478315584584708,6.528148781389206,6.577981978193704,6.627815174998201,6.677648371802698,6.727481568607196,6.7773147654116945,6.827147962216192,6.876981159020689,6.926814355825187,6.976647552629685,7.026480749434183,7.076313946238681,7.126147143043179,7.175980339847676,7.225813536652174,7.2756467334566715,7.32547993026117,7.3753131270656676,7.425146323870164,7.474979520674663,7.52481271747916,7.574645914283659,7.6244791110881565,7.674312307892653,7.724145504697151,7.773978701501649,7.823811898306147,7.873645095110645,7.923478291915143,7.97331148871964,8.023144685524137,8.072977882328637,8.122811079133134,8.172644275937632,8.222477472742128,8.272310669546627,8.322143866351125,8.371977063155622,8.42181025996012,8.471643456764617,8.521476653569115,8.571309850373613,8.62114304717811,8.67097624398261,8.720809440787107,8.770642637591603,8.820475834396102,8.8703090312006,8.920142228005098,8.969975424809595,9.019808621614093,9.06964181841859,9.119475015223088,9.169308212027586,9.219141408832085,9.268974605636583,9.318807802441079,9.368640999245578,9.418474196050076,9.468307392854573,9.51814058965907,9.567973786463568,9.617806983268066,9.667640180072564,9.717473376877061,9.76730657368156,9.817139770486058,9.866972967290554,9.916806164095052,9.966639360899551,10.016472557704049,10.066305754508546,10.116138951313044,10.165972148117541,10.215805344922039,10.265638541726537,10.315471738531034,10.365304935335532,10.41513813214003,10.464971328944527,10.514804525749026,10.564637722553524,10.614470919358022,10.66430411616252,10.714137312967017,10.763970509771514,10.813803706576012,10.86363690338051,10.913470100185007,10.963303296989507,11.013136493794002,11.0629696905985,11.112802887403,11.162636084207495,11.212469281011995,11.262302477816492,11.312135674620988,11.361968871425487,11.411802068229985,11.461635265034484,11.51146846183898,11.56130165864348,11.611134855447977,11.660968052252473,11.710801249056972,11.760634445861468,11.810467642665968,11.860300839470465,11.910134036274961,11.95996723307946,12.009800429883958,12.059633626688457,12.109466823492953,12.15930002029745,12.20913321710195,12.258966413906446,12.308799610710945,12.358632807515443,12.408466004319939,12.458299201124438,12.508132397928936,12.557965594733433,12.607798791537931,12.657631988342429,12.707465185146926,12.757298381951424,12.807131578755923,12.856964775560419,12.906797972364918,12.956631169169416,13.006464365973912,13.056297562778411,13.106130759582909,13.155963956387408,13.205797153191904,13.255630349996402,13.305463546800901,13.355296743605397,13.405129940409896,13.454963137214392,13.50479633401889,13.554629530823389,13.604462727627885,13.654295924432384,13.704129121236882,13.753962318041378,13.803795514845877,13.853628711650375,13.903461908454874,13.95329510525937,14.003128302063867,14.052961498868367,14.102794695672863,14.152627892477362,14.20246108928186,14.252294286086357,14.302127482890855,14.351960679695352,14.40179387649985,14.451627073304348,14.501460270108847,14.551293466913343,14.60112666371784,14.65095986052234,14.700793057326836,14.750626254131335,14.800459450935833,14.850292647740329,14.900125844544828,14.949959041349326,14.999792238153825,15.04962543495832,15.099458631762818,15.149291828567318,15.199125025371814,15.248958222176313,15.298791418980809,15.348624615785306,15.398457812589806,15.448291009394302,15.498124206198801,15.547957403003299,15.597790599807798,15.647623796612294,15.697456993416791,15.74729019022129,15.797123387025787,15.846956583830286,15.896789780634784,15.94662297743928,15.996456174243779,16.046289371048275,16.096122567852774,16.145955764657273,16.19578896146177,16.24562215826627,16.295455355070764,16.345288551875264,16.39512174867976,16.444954945484255,16.494788142288755,16.544621339093254,16.594454535897754,16.64428773270225,16.694120929506745,16.743954126311245,16.79378732311574,16.84362051992024,16.89345371672474,16.943286913529235,16.993120110333734,17.04295330713823,17.09278650394273,17.142619700747225,17.192452897551725,17.24228609435622,17.29211929116072,17.34195248796522,17.391785684769715,17.441618881574215,17.49145207837871,17.541285275183206,17.591118471987706,17.640951668792205,17.690784865596704,17.7406180624012,17.790451259205696,17.840284456010195,17.89011765281469,17.93995084961919,17.989784046423686,18.039617243228186,18.089450440032685,18.13928363683718,18.18911683364168,18.238950030446176,18.288783227250676,18.33861642405517,18.38844962085967,18.43828281766417,18.488116014468666,18.537949211273165,18.58778240807766,18.637615604882157,18.687448801686656,18.737281998491156,18.78711519529565,18.83694839210015,18.886781588904647,18.936614785709146,18.986447982513642,19.03628117931814,19.086114376122637,19.135947572927137,19.185780769731636,19.235613966536132,19.28544716334063,19.335280360145127,19.385113556949623,19.434946753754122,19.48477995055862,19.53461314736312,19.584446344167617,19.634279540972116,19.684112737776612,19.733945934581108,19.783779131385607,19.833612328190103,19.883445524994602,19.933278721799102,19.983111918603598,20.032945115408097,20.082778312212593,20.132611509017092,20.182444705821588,20.232277902626087,20.282111099430587,20.331944296235083,20.381777493039582,20.431610689844078,20.481443886648574,20.531277083453073,20.581110280257572,20.63094347706207,20.680776673866568,20.730609870671064,20.780443067475563,20.83027626428006,20.880109461084558,20.929942657889054,20.979775854693553,21.029609051498053,21.07944224830255,21.129275445107048,21.179108641911544,21.228941838716043,21.27877503552054,21.32860823232504,21.378441429129538,21.428274625934034,21.478107822738533,21.52794101954303],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">44.67%</td>
                <td>64.9%</td>
                <td>1716</td>
                <td>1.10</td>
                <td>0.00</td>
                <td>23.75%</td>
                <td>67.3</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">36.93%</td>
                <td>65.2%</td>
                <td>1221</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>10.30%</td>
                <td>64.3</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>AI Rule 3: Smart Money Flow Divergence</td>
                <td>AI_GENERATED</td>
                <td class="positive">27.82%</td>
                <td>64.9%</td>
                <td>784</td>
                <td>1.13</td>
                <td>0.00</td>
                <td>11.36%</td>
                <td>60.6</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Ext Rule 6: Fibonacci Support Confluence</td>
                <td>UNKNOWN</td>
                <td class="positive">25.93%</td>
                <td>64.6%</td>
                <td>1199</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>15.57%</td>
                <td>59.8</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">21.53%</td>
                <td>66.7%</td>
                <td>432</td>
                <td>1.19</td>
                <td>0.00</td>
                <td>5.37%</td>
                <td>58.6</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">7.06%</td>
                <td>64.5%</td>
                <td>217</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>7.33%</td>
                <td>52.2</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Rule 28: Volume Breakout</td>
                <td>ORIGINAL</td>
                <td class="positive">9.10%</td>
                <td>60.9%</td>
                <td>281</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>6.01%</td>
                <td>51.9</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">5.07%</td>
                <td>63.8%</td>
                <td>185</td>
                <td>1.10</td>
                <td>0.00</td>
                <td>4.61%</td>
                <td>51.2</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">7.00%</td>
                <td>67.4%</td>
                <td>92</td>
                <td>1.33</td>
                <td>0.00</td>
                <td>3.17%</td>
                <td>50.6</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Volatility Rule 2: ATR Expansion Signal</td>
                <td>UNKNOWN</td>
                <td class="positive">4.85%</td>
                <td>59.9%</td>
                <td>312</td>
                <td>1.06</td>
                <td>0.00</td>
                <td>5.76%</td>
                <td>49.9</td>
            </tr>
            
            <tr>
                <td>11</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">2.09%</td>
                <td>61.6%</td>
                <td>573</td>
                <td>1.01</td>
                <td>0.00</td>
                <td>11.32%</td>
                <td>49.3</td>
            </tr>
            
            <tr>
                <td>12</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">1.18%</td>
                <td>61.6%</td>
                <td>151</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>5.44%</td>
                <td>48.9</td>
            </tr>
            
            <tr>
                <td>13</td>
                <td>Volume Rule 4: Volume Breakout Confirmation</td>
                <td>UNKNOWN</td>
                <td class="positive">2.23%</td>
                <td>59.4%</td>
                <td>212</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>7.04%</td>
                <td>48.7</td>
            </tr>
            
            <tr>
                <td>14</td>
                <td>Rule 27: Structure Break Up</td>
                <td>ORIGINAL</td>
                <td class="positive">1.27%</td>
                <td>60.3%</td>
                <td>506</td>
                <td>1.01</td>
                <td>0.00</td>
                <td>10.55%</td>
                <td>48.6</td>
            </tr>
            
            <tr>
                <td>15</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">6.01%</td>
                <td>67.6%</td>
                <td>74</td>
                <td>1.33</td>
                <td>0.00</td>
                <td>2.22%</td>
                <td>44.9</td>
            </tr>
            
            <tr>
                <td>16</td>
                <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                <td>UNKNOWN</td>
                <td class="positive">1.99%</td>
                <td>63.0%</td>
                <td>54</td>
                <td>1.13</td>
                <td>0.00</td>
                <td>4.50%</td>
                <td>35.9</td>
            </tr>
            
            <tr>
                <td>17</td>
                <td>Advanced Rule 7: DMI ADX Filter</td>
                <td>UNKNOWN</td>
                <td class="positive">1.10%</td>
                <td>60.7%</td>
                <td>28</td>
                <td>1.14</td>
                <td>0.00</td>
                <td>2.64%</td>
                <td>27.1</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
