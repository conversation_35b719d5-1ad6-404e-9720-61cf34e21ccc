
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 15:05:07 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">0.34%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">29</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.1%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1.09</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>2.64%</strong></td>
                    <td>72.7%</td>
                    <td>11</td>
                    <td>1.66</td>
                    <td class="neutral">2.52%</td>
                    <td class="positive"><strong>0.4391</strong></td>
                    <td class="negative">+0.82% / -1.33%</td>
                    <td>2h25m<br><small>(2.0m - 8h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>0.89%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.89% / 0.00%</td>
                    <td>38.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>0.76%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.76% / 0.00%</td>
                    <td>6h48m</td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-0.48%</strong></td>
                    <td>50.0%</td>
                    <td>2</td>
                    <td>0.64</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>0.0321</strong></td>
                    <td class="negative">+0.85% / -1.32%</td>
                    <td>4h32m<br><small>(3h53m - 5h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="negative"><strong>-0.85%</strong></td>
                    <td>58.3%</td>
                    <td>12</td>
                    <td>0.87</td>
                    <td class="neutral">3.96%</td>
                    <td class="positive"><strong>0.2803</strong></td>
                    <td class="negative">+0.83% / -1.32%</td>
                    <td>2h22m<br><small>(2.0m - 8h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-1.28%</strong></td>
                    <td>0.0%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0505</strong></td>
                    <td class="negative">+0.00% / -1.26%</td>
                    <td>4h58m</td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-1.34%</strong></td>
                    <td>0.0%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0564</strong></td>
                    <td class="negative">+0.00% / -1.32%</td>
                    <td>3h51m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">0.89%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.89% / 0.00%</td>
                    <td>38.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">0.76%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.76% / 0.00%</td>
                    <td>6h48m</td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>72.7%</strong></td>
                    <td class="positive">2.64%</td>
                    <td>11</td>
                    <td>1.66</td>
                    <td class="neutral">2.52%</td>
                    <td class="positive"><strong>0.4391</strong></td>
                    <td class="negative">+0.82% / -1.33%</td>
                    <td>2h25m<br><small>(2.0m - 8h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>58.3%</strong></td>
                    <td class="negative">-0.85%</td>
                    <td>12</td>
                    <td>0.87</td>
                    <td class="neutral">3.96%</td>
                    <td class="positive"><strong>0.2803</strong></td>
                    <td class="negative">+0.83% / -1.32%</td>
                    <td>2h22m<br><small>(2.0m - 8h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-0.48%</td>
                    <td>2</td>
                    <td>0.64</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>0.0321</strong></td>
                    <td class="negative">+0.85% / -1.32%</td>
                    <td>4h32m<br><small>(3h53m - 5h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">-1.28%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0505</strong></td>
                    <td class="negative">+0.00% / -1.26%</td>
                    <td>4h58m</td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">-1.34%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0564</strong></td>
                    <td class="negative">+0.00% / -1.32%</td>
                    <td>3h51m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Volume Rule 5: Smart Money Vol...', 'Momentum Rule 2: Momentum Dive...', 'Ext Rule 6: Fibonacci Support ...', 'AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'Advanced Rule 7: DMI ADX Filte...'],
                y: [100.0, 100.0, 72.72727272727273, 58.333333333333336, 50.0, 0.0, 0.0],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Volume Rule 5: Smart Money Vol...', 'Momentum Rule 2: Momentum Dive...', 'Ext Rule 6: Fibonacci Support ...', 'AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'Advanced Rule 7: DMI ADX Filte...'],
                y: [np.float64(0.8937543608650302), np.float64(0.7587474745193177), np.float64(2.6420142938775824), np.float64(-0.851743208798769), np.float64(-0.4804624140360975), np.float64(-1.2822860112312482), np.float64(-1.342809897692656)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
