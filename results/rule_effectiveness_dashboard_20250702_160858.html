
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 16:08:58 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">4.99%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">4,463</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.0%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1.07</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>20.58%</strong></td>
                    <td>69.0%</td>
                    <td>171</td>
                    <td>1.27</td>
                    <td class="negative">12.13%</td>
                    <td class="positive"><strong>0.6707</strong></td>
                    <td class="negative">+0.89% / -1.52%</td>
                    <td>1h50m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>19.06%</strong></td>
                    <td>62.5%</td>
                    <td>1359</td>
                    <td>1.03</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.7974</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h31m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>8.42%</strong></td>
                    <td>82.4%</td>
                    <td>17</td>
                    <td>3.24</td>
                    <td class="positive">1.63%</td>
                    <td class="positive"><strong>0.6564</strong></td>
                    <td class="negative">+0.88% / -1.34%</td>
                    <td>3h43m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>8.13%</strong></td>
                    <td>68.7%</td>
                    <td>67</td>
                    <td>1.27</td>
                    <td class="negative">5.61%</td>
                    <td class="positive"><strong>0.5562</strong></td>
                    <td class="negative">+0.90% / -1.51%</td>
                    <td>2h53m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>5.59%</strong></td>
                    <td>66.7%</td>
                    <td>36</td>
                    <td>1.37</td>
                    <td class="negative">5.39%</td>
                    <td class="positive"><strong>0.5254</strong></td>
                    <td class="negative">+0.99% / -1.33%</td>
                    <td>2h6m<br><small>(2.0m - 11h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>3.52%</strong></td>
                    <td>62.7%</td>
                    <td>59</td>
                    <td>1.12</td>
                    <td class="negative">5.55%</td>
                    <td class="positive"><strong>0.5109</strong></td>
                    <td class="negative">+0.90% / -1.36%</td>
                    <td>3h32m<br><small>(7.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>2.99%</strong></td>
                    <td>63.8%</td>
                    <td>149</td>
                    <td>1.04</td>
                    <td class="negative">16.45%</td>
                    <td class="positive"><strong>0.5629</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>2h6m<br><small>(2.0m - 12h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>2.18%</strong></td>
                    <td>100.0%</td>
                    <td>3</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.82% / 0.00%</td>
                    <td>52.3m<br><small>(13.0m - 1h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>2.08%</strong></td>
                    <td>66.7%</td>
                    <td>9</td>
                    <td>1.52</td>
                    <td class="positive">1.36%</td>
                    <td class="positive"><strong>0.3971</strong></td>
                    <td class="negative">+1.07% / -1.50%</td>
                    <td>3.1m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>0.73%</strong></td>
                    <td>61.7%</td>
                    <td>1078</td>
                    <td>1.00</td>
                    <td class="negative">40.62%</td>
                    <td class="positive"><strong>0.7088</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h21m<br><small>(1.0m - 45h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>0.22%</strong></td>
                    <td>62.5%</td>
                    <td>40</td>
                    <td>1.01</td>
                    <td class="neutral">4.22%</td>
                    <td class="positive"><strong>0.4349</strong></td>
                    <td class="negative">+0.86% / -1.41%</td>
                    <td>2h49m<br><small>(4.0m - 24h50m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-0.01%</strong></td>
                    <td>63.6%</td>
                    <td>11</td>
                    <td>1.00</td>
                    <td class="neutral">3.08%</td>
                    <td class="positive"><strong>0.3052</strong></td>
                    <td class="negative">+0.81% / -1.38%</td>
                    <td>1h39m<br><small>(6.0m - 4h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-1.47%</strong></td>
                    <td>61.5%</td>
                    <td>13</td>
                    <td>0.80</td>
                    <td class="neutral">3.17%</td>
                    <td class="positive"><strong>0.2587</strong></td>
                    <td class="negative">+0.82% / -1.51%</td>
                    <td>2h38m<br><small>(19.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-2.89%</strong></td>
                    <td>45.5%</td>
                    <td>11</td>
                    <td>0.62</td>
                    <td class="neutral">4.39%</td>
                    <td class="positive"><strong>0.1761</strong></td>
                    <td class="negative">+0.90% / -1.35%</td>
                    <td>1h33m<br><small>(13.0m - 4h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-5.71%</strong></td>
                    <td>59.8%</td>
                    <td>107</td>
                    <td>0.90</td>
                    <td class="negative">13.79%</td>
                    <td class="positive"><strong>0.4714</strong></td>
                    <td class="negative">+0.88% / -1.47%</td>
                    <td>1h40m<br><small>(1.0m - 15h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-58.44%</strong></td>
                    <td>59.9%</td>
                    <td>1333</td>
                    <td>0.92</td>
                    <td class="negative">62.29%</td>
                    <td class="positive"><strong>0.5291</strong></td>
                    <td class="negative">+0.86% / -1.39%</td>
                    <td>2h58m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">2.18%</td>
                    <td>3</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.82% / 0.00%</td>
                    <td>52.3m<br><small>(13.0m - 1h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>82.4%</strong></td>
                    <td class="positive">8.42%</td>
                    <td>17</td>
                    <td>3.24</td>
                    <td class="positive">1.63%</td>
                    <td class="positive"><strong>0.6564</strong></td>
                    <td class="negative">+0.88% / -1.34%</td>
                    <td>3h43m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>69.0%</strong></td>
                    <td class="positive">20.58%</td>
                    <td>171</td>
                    <td>1.27</td>
                    <td class="negative">12.13%</td>
                    <td class="positive"><strong>0.6707</strong></td>
                    <td class="negative">+0.89% / -1.52%</td>
                    <td>1h50m<br><small>(1.0m - 28h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>68.7%</strong></td>
                    <td class="positive">8.13%</td>
                    <td>67</td>
                    <td>1.27</td>
                    <td class="negative">5.61%</td>
                    <td class="positive"><strong>0.5562</strong></td>
                    <td class="negative">+0.90% / -1.51%</td>
                    <td>2h53m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">5.59%</td>
                    <td>36</td>
                    <td>1.37</td>
                    <td class="negative">5.39%</td>
                    <td class="positive"><strong>0.5254</strong></td>
                    <td class="negative">+0.99% / -1.33%</td>
                    <td>2h6m<br><small>(2.0m - 11h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">2.08%</td>
                    <td>9</td>
                    <td>1.52</td>
                    <td class="positive">1.36%</td>
                    <td class="positive"><strong>0.3971</strong></td>
                    <td class="negative">+1.07% / -1.50%</td>
                    <td>3.1m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>63.8%</strong></td>
                    <td class="positive">2.99%</td>
                    <td>149</td>
                    <td>1.04</td>
                    <td class="negative">16.45%</td>
                    <td class="positive"><strong>0.5629</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>2h6m<br><small>(2.0m - 12h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>63.6%</strong></td>
                    <td class="negative">-0.01%</td>
                    <td>11</td>
                    <td>1.00</td>
                    <td class="neutral">3.08%</td>
                    <td class="positive"><strong>0.3052</strong></td>
                    <td class="negative">+0.81% / -1.38%</td>
                    <td>1h39m<br><small>(6.0m - 4h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>62.7%</strong></td>
                    <td class="positive">3.52%</td>
                    <td>59</td>
                    <td>1.12</td>
                    <td class="negative">5.55%</td>
                    <td class="positive"><strong>0.5109</strong></td>
                    <td class="negative">+0.90% / -1.36%</td>
                    <td>3h32m<br><small>(7.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>62.5%</strong></td>
                    <td class="positive">19.06%</td>
                    <td>1359</td>
                    <td>1.03</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.7974</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h31m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>62.5%</strong></td>
                    <td class="positive">0.22%</td>
                    <td>40</td>
                    <td>1.01</td>
                    <td class="neutral">4.22%</td>
                    <td class="positive"><strong>0.4349</strong></td>
                    <td class="negative">+0.86% / -1.41%</td>
                    <td>2h49m<br><small>(4.0m - 24h50m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>61.7%</strong></td>
                    <td class="positive">0.73%</td>
                    <td>1078</td>
                    <td>1.00</td>
                    <td class="negative">40.62%</td>
                    <td class="positive"><strong>0.7088</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h21m<br><small>(1.0m - 45h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>61.5%</strong></td>
                    <td class="negative">-1.47%</td>
                    <td>13</td>
                    <td>0.80</td>
                    <td class="neutral">3.17%</td>
                    <td class="positive"><strong>0.2587</strong></td>
                    <td class="negative">+0.82% / -1.51%</td>
                    <td>2h38m<br><small>(19.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>59.9%</strong></td>
                    <td class="negative">-58.44%</td>
                    <td>1333</td>
                    <td>0.92</td>
                    <td class="negative">62.29%</td>
                    <td class="positive"><strong>0.5291</strong></td>
                    <td class="negative">+0.86% / -1.39%</td>
                    <td>2h58m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>59.8%</strong></td>
                    <td class="negative">-5.71%</td>
                    <td>107</td>
                    <td>0.90</td>
                    <td class="negative">13.79%</td>
                    <td class="positive"><strong>0.4714</strong></td>
                    <td class="negative">+0.88% / -1.47%</td>
                    <td>1h40m<br><small>(1.0m - 15h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="neutral"><strong>45.5%</strong></td>
                    <td class="negative">-2.89%</td>
                    <td>11</td>
                    <td>0.62</td>
                    <td class="neutral">4.39%</td>
                    <td class="positive"><strong>0.1761</strong></td>
                    <td class="negative">+0.90% / -1.35%</td>
                    <td>1h33m<br><small>(13.0m - 4h11m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'Rule 6: Stochastic Oversold Cr...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 8: Momentum Divergence...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'Price Action Rule 3: Engulfing...', 'Professional Rule 7: Chaikin M...', 'Rule 27: Structure Break Up', 'Momentum Rule 2: Momentum Dive...', 'Ext Rule 6: Fibonacci Support ...', 'Acad Rule 2: Mean Reversion Fa...', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike'],
                y: [69.00584795321637, 62.54598969830758, 63.758389261744966, 61.68831168831169, 59.813084112149525, 68.65671641791045, 62.71186440677966, 82.35294117647058, 66.66666666666666, 100.0, 62.5, 59.86496624156039, 66.66666666666666, 63.63636363636363, 61.53846153846154],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'Rule 6: Stochastic Oversold Cr...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 8: Momentum Divergence...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'Price Action Rule 3: Engulfing...', 'Professional Rule 7: Chaikin M...', 'Rule 27: Structure Break Up', 'Momentum Rule 2: Momentum Dive...', 'Ext Rule 6: Fibonacci Support ...', 'Acad Rule 2: Mean Reversion Fa...', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike'],
                y: [np.float64(20.58151536879575), np.float64(19.058810492686067), np.float64(2.9938026302837244), np.float64(0.734721938011091), np.float64(-5.711179106181532), np.float64(8.133433005205545), np.float64(3.5156451872715406), np.float64(8.420381604790645), np.float64(5.5902201037222845), np.float64(2.183789633605906), np.float64(0.21513645453345087), np.float64(-58.4403522623532), np.float64(2.078210741632283), np.float64(-0.008747032713959926), np.float64(-1.4669047751967301)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
