
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 10 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 16:17:16</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">10</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">55.6%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">38.7%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">139.2%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">69.3%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">4,128</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["139.2%","96.4%","49.8%","19.6%","25.5%","15.6%","23.9%","1.1%","12.6%","3.7%"],"textposition":"auto","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[139.15907420801233,96.36221832403827,49.829512193053496,19.622594216750613,25.52468557034625,15.613675702882043,23.932170925797777,1.05459263090085,12.64154634988784,3.705853668156837],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[833,830,112,96,62,659,23,31,13,14],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[454,453,53,46,27,388,6,17,3,8],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[139.15907420801233,96.36221832403827,49.829512193053496,19.622594216750613,25.52468557034625,15.613675702882043,23.932170925797777,1.05459263090085,12.64154634988784,3.705853668156837],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Ext Rule 6","AI Rule 10","Prof Rule 7","Rule 6","AI Rule 8","Rule 7","Momentum Rule 2","Professional Rule 10","Price Action Rule 3","Rule 10"],"textposition":"top center","x":[22.30179593251712,21.510148813184337,16.404199174493996,12.945122476484466,7.895460462478751,45.55013874661983,3.6738976656817774,14.890052885284808,3.410632552237279,5.598690664613469],"y":[139.15907420801233,96.36221832403827,49.829512193053496,19.622594216750613,25.52468557034625,15.613675702882043,23.932170925797777,1.05459263090085,12.64154634988784,3.705853668156837],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["44.2%","60.9%","49.8%","13.0%"],"textposition":"auto","x":["UNKNOWN","AI_GENERATED","PROFESSIONAL","ORIGINAL"],"y":[44.1968460286497,60.94345194719226,49.829512193053496,12.980707862596496],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["1287","1283","165","142","89","1047","29","48","16","22"],"textposition":"auto","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[1287,1283,165,142,89,1047,29,48,16,22],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Ext Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287],"y":[0,0.10812670878633436,0.21625341757266872,0.3243801263590031,0.43250683514533744,0.5406335439316718,0.6487602527180062,0.7568869615043405,0.8650136702906749,0.9731403790770092,1.0812670878633437,1.1893937966496781,1.2975205054360124,1.4056472142223468,1.513773923008681,1.6219006317950155,1.7300273405813498,1.8381540493676842,1.9462807581540185,2.054407466940353,2.1625341757266874,2.2706608845130214,2.3787875932993563,2.4869143020856903,2.5950410108720248,2.703167719658359,2.8112944284446937,2.919421137231028,3.027547846017362,3.1356745548036966,3.243801263590031,3.3519279723763655,3.4600546811626995,3.568181389949034,3.6763080987353685,3.784434807521703,3.892561516308037,4.000688225094371,4.108814933880706,4.216941642667041,4.325068351453375,4.433195060239709,4.541321769026043,4.649448477812378,4.757575186598713,4.865701895385047,4.973828604171381,5.0819553129577155,5.1900820217440495,5.2982087305303835,5.406335439316718,5.5144621481030525,5.622588856889387,5.730715565675721,5.838842274462056,5.94696898324839,6.055095692034724,6.163222400821058,6.271349109607393,6.379475818393727,6.487602527180062,6.595729235966397,6.703855944752731,6.811982653539065,6.920109362325399,7.028236071111734,7.136362779898068,7.244489488684402,7.352616197470737,7.460742906257072,7.568869615043406,7.67699632382974,7.785123032616074,7.893249741402409,8.001376450188742,8.109503158975077,8.217629867761412,8.325756576547747,8.433883285334081,8.542009994120415,8.65013670290675,8.758263411693084,8.866390120479418,8.974516829265752,9.082643538052086,9.190770246838422,9.298896955624755,9.40702366441109,9.515150373197425,9.623277081983758,9.731403790770093,9.839530499556426,9.947657208342761,10.055783917129096,10.163910625915431,10.272037334701764,10.380164043488099,10.488290752274434,10.596417461060767,10.704544169847102,10.812670878633435,10.920797587419772,11.028924296206105,11.13705100499244,11.245177713778775,11.353304422565108,11.461431131351443,11.569557840137776,11.677684548924113,11.785811257710446,11.89393796649678,12.002064675283114,12.110191384069449,12.218318092855784,12.326444801642117,12.434571510428453,12.542698219214786,12.650824928001121,12.758951636787454,12.86707834557379,12.975205054360124,13.083331763146457,13.191458471932794,13.299585180719127,13.407711889505462,13.515838598291795,13.62396530707813,13.732092015864463,13.840218724650798,13.948345433437133,14.056472142223468,14.164598851009803,14.272725559796136,14.380852268582471,14.488978977368804,14.597105686155139,14.705232394941474,14.813359103727809,14.921485812514144,15.029612521300477,15.137739230086812,15.245865938873145,15.35399264765948,15.462119356445813,15.570246065232148,15.678372774018484,15.786499482804818,15.894626191591152,16.002752900377484,16.11087960916382,16.219006317950154,16.32713302673649,16.435259735522823,16.543386444309157,16.651513153095493,16.759639861881826,16.867766570668163,16.975893279454496,17.08401998824083,17.192146697027162,17.3002734058135,17.408400114599832,17.51652682338617,17.624653532172502,17.732780240958835,17.840906949745168,17.949033658531505,18.057160367317838,18.16528707610417,18.273413784890508,18.381540493676845,18.489667202463178,18.59779391124951,18.705920620035847,18.81404732882218,18.922174037608514,19.03030074639485,19.138427455181183,19.246554163967517,19.35468087275385,19.462807581540186,19.57093429032652,19.679060999112853,19.787187707899186,19.895314416685522,20.00344112547186,20.111567834258192,20.21969454304453,20.327821251830862,20.435947960617195,20.54407466940353,20.652201378189865,20.760328086976198,20.86845479576253,20.976581504548868,21.0847082133352,21.192834922121534,21.300961630907867,21.409088339694204,21.517215048480537,21.62534175726687,21.73346846605321,21.841595174839544,21.949721883625877,22.05784859241221,22.165975301198547,22.27410200998488,22.382228718771213,22.49035542755755,22.598482136343883,22.706608845130216,22.81473555391655,22.922862262702886,23.03098897148922,23.13911568027555,23.247242389061885,23.355369097848225,23.46349580663456,23.57162251542089,23.679749224207228,23.78787593299356,23.896002641779894,24.004129350566227,24.112256059352564,24.220382768138897,24.32850947692523,24.436636185711567,24.5447628944979,24.652889603284233,24.761016312070566,24.869143020856907,24.97726972964324,25.085396438429573,25.19352314721591,25.301649856002243,25.409776564788576,25.51790327357491,25.626029982361246,25.73415669114758,25.842283399933912,25.95041010872025,26.05853681750658,26.166663526292915,26.274790235079248,26.382916943865588,26.49104365265192,26.599170361438254,26.707297070224588,26.815423779010924,26.923550487797257,27.03167719658359,27.139803905369927,27.24793061415626,27.356057322942593,27.464184031728927,27.572310740515263,27.680437449301596,27.78856415808793,27.896690866874266,28.004817575660603,28.112944284446936,28.22107099323327,28.329197702019606,28.43732441080594,28.545451119592272,28.65357782837861,28.761704537164942,28.869831245951275,28.977957954737608,29.086084663523945,29.194211372310278,29.30233808109661,29.410464789882948,29.51859149866928,29.626718207455617,29.73484491624195,29.842971625028287,29.95109833381462,30.059225042600954,30.167351751387287,30.275478460173623,30.383605168959956,30.49173187774629,30.599858586532626,30.70798529531896,30.816112004105292,30.924238712891626,31.032365421677962,31.140492130464295,31.248618839250632,31.35674554803697,31.464872256823302,31.572998965609635,31.681125674395968,31.789252383182305,31.897379091968638,32.00550580075497,32.11363250954131,32.22175921832764,32.32988592711398,32.43801263590031,32.546139344686644,32.65426605347298,32.76239276225931,32.87051947104565,32.97864617983198,33.08677288861831,33.19489959740465,33.303026306190986,33.411153014977316,33.51927972376365,33.62740643254998,33.735533141336326,33.843659850122656,33.95178655890899,34.05991326769533,34.16803997648166,34.276166685267995,34.384293394054325,34.49242010284066,34.600546811627,34.70867352041333,34.816800229199664,34.924926937985994,35.03305364677234,35.141180355558674,35.249307064345004,35.35743377313134,35.46556048191767,35.57368719070401,35.681813899490336,35.78994060827668,35.89806731706301,36.00619402584935,36.114320734635676,36.22244744342201,36.33057415220834,36.43870086099468,36.546827569781016,36.65495427856735,36.76308098735369,36.87120769614002,36.979334404926355,37.087461113712685,37.19558782249902,37.30371453128536,37.411841240071695,37.519967948858024,37.62809465764436,37.73622136643069,37.84434807521703,37.95247478400336,38.0606014927897,38.16872820157603,38.27685491036237,38.384981619148704,38.49310832793503,38.60123503672137,38.7093617455077,38.817488454294036,38.92561516308037,39.03374187186671,39.14186858065304,39.249995289439376,39.358121998225705,39.46624870701204,39.57437541579837,39.682502124584715,39.790628833371045,39.89875554215738,40.00688225094372,40.11500895973005,40.223135668516385,40.331262377302714,40.43938908608906,40.54751579487539,40.655642503661724,40.763769212448054,40.87189592123439,40.98002263002072,41.08814933880706,41.19627604759339,41.30440275637973,41.41252946516606,41.520656173952396,41.62878288273873,41.73690959152506,41.8450363003114,41.953163009097736,42.06128971788407,42.1694164266704,42.27754313545674,42.38566984424307,42.493796553029405,42.601923261815735,42.71004997060208,42.81817667938841,42.926303388174745,43.034430096961074,43.14255680574741,43.25068351453374,43.35881022332008,43.46693693210642,43.57506364089275,43.68319034967909,43.79131705846542,43.89944376725175,44.00757047603808,44.11569718482442,44.223823893610756,44.33195060239709,44.44007731118342,44.54820401996976,44.65633072875609,44.764457437542426,44.872584146328755,44.9807108551151,45.088837563901436,45.196964272687765,45.3050909814741,45.41321769026043,45.52134439904677,45.6294711078331,45.737597816619434,45.84572452540577,45.95385123419211,46.06197794297844,46.170104651764774,46.2782313605511,46.38635806933744,46.49448477812377,46.602611486910114,46.71073819569645,46.81886490448278,46.92699161326912,47.035118322055446,47.14324503084178,47.25137173962811,47.359498448414456,47.467625157200786,47.57575186598712,47.68387857477345,47.79200528355979,47.90013199234612,48.008258701132455,48.1163854099188,48.22451211870513,48.332638827491465,48.440765536277794,48.54889224506413,48.65701895385046,48.7651456626368,48.873272371423134,48.98139908020947,49.0895257889958,49.19765249778214,49.30577920656847,49.4139059153548,49.52203262414113,49.63015933292748,49.73828604171381,49.84641275050014,49.95453945928648,50.06266616807281,50.170792876859146,50.278919585645475,50.38704629443182,50.49517300321815,50.603299712004485,50.711426420790815,50.81955312957715,50.92767983836348,51.03580654714982,51.14393325593616,51.25205996472249,51.36018667350883,51.46831338229516,51.576440091081494,51.684566799867824,51.79269350865416,51.9008202174405,52.008946926226834,52.11707363501316,52.2252003437995,52.33332705258583,52.441453761372166,52.549580470158496,52.65770717894483,52.765833887731176,52.873960596517506,52.98208730530384,53.09021401409017,53.19834072287651,53.30646743166284,53.414594140449175,53.52272084923551,53.63084755802185,53.73897426680818,53.847100975594515,53.955227684380844,54.06335439316718,54.17148110195351,54.279607810739854,54.38773451952619,54.49586122831252,54.60398793709886,54.71211464588519,54.82024135467152,54.92836806345785,55.0364947722442,55.144621481030526,55.25274818981686,55.36087489860319,55.46900160738953,55.57712831617586,55.685255024962196,55.79338173374853,55.90150844253487,56.009635151321206,56.117761860107535,56.22588856889387,56.3340152776802,56.44214198646654,56.550268695252875,56.65839540403921,56.76652211282554,56.87464882161188,56.98277553039821,57.090902239184544,57.199028947970874,57.30715565675722,57.41528236554355,57.523409074329884,57.63153578311622,57.73966249190255,57.84778920068889,57.955915909475216,58.06404261826156,58.17216932704789,58.280296035834226,58.388422744620556,58.49654945340689,58.60467616219322,58.71280287097956,58.820929579765895,58.92905628855223,59.03718299733856,59.1453097061249,59.253436414911235,59.361563123697564,59.4696898324839,59.57781654127023,59.685943250056575,59.794069958842904,59.90219666762924,60.01032337641557,60.11845008520191,60.22657679398824,60.33470350277457,60.44283021156091,60.55095692034725,60.659083629133576,60.76721033791991,60.87533704670625,60.98346375549258,61.091590464278916,61.19971717306525,61.30784388185159,61.41597059063792,61.524097299424255,61.632224008210585,61.74035071699692,61.84847742578325,61.956604134569595,62.064730843355925,62.17285755214226,62.28098426092859,62.38911096971493,62.497237678501264,62.605364387287594,62.71349109607394,62.82161780486027,62.929744513646604,63.03787122243293,63.14599793121927,63.2541246400056,63.362251348791936,63.47037805757827,63.57850476636461,63.68663147515094,63.794758183937276,63.902884892723606,64.01101160150994,64.11913831029628,64.22726501908262,64.33539172786895,64.44351843665528,64.55164514544161,64.65977185422796,64.76789856301428,64.87602527180061,64.98415198058696,65.09227868937329,65.20040539815962,65.30853210694596,65.41665881573229,65.52478552451862,65.63291223330495,65.7410389420913,65.84916565087764,65.95729235966397,66.0654190684503,66.17354577723663,66.28167248602297,66.3897991948093,66.49792590359563,66.60605261238197,66.7141793211683,66.82230602995463,66.93043273874098,67.0385594475273,67.14668615631363,67.25481286509996,67.36293957388631,67.47106628267265,67.57919299145898,67.68731970024531,67.79544640903164,67.90357311781798,68.01169982660431,68.11982653539066,68.22795324417699,68.33607995296332,68.44420666174965,68.55233337053599,68.66046007932232,68.76858678810865,68.876713496895,68.98484020568132,69.09296691446767,69.201093623254,69.30922033204033,69.41734704082666,69.525473749613,69.63360045839933,69.74172716718567,69.84985387597199,69.95798058475833,70.06610729354468,70.174234002331,70.28236071111735,70.39048741990366,70.49861412869001,70.60674083747634,70.71486754626268,70.82299425504901,70.93112096383534,71.03924767262168,71.14737438140801,71.25550109019434,71.36362779898067,71.47175450776702,71.57988121655336,71.68800792533968,71.79613463412602,71.90426134291235,72.0123880516987,72.12051476048501,72.22864146927135,72.3367681780577,72.44489488684403,72.55302159563037,72.66114830441668,72.76927501320303,72.87740172198936,72.9855284307757,73.09365513956203,73.20178184834836,73.3099085571347,73.41803526592103,73.52616197470738,73.6342886834937,73.74241539228004,73.85054210106638,73.95866880985271,74.06679551863904,74.17492222742537,74.28304893621171,74.39117564499804,74.49930235378437,74.60742906257072,74.71555577135705,74.82368248014339,74.9318091889297,75.03993589771605,75.14806260650238,75.25618931528872,75.36431602407507,75.47244273286138,75.58056944164773,75.68869615043405,75.7968228592204,75.90494956800671,76.01307627679306,76.1212029855794,76.22932969436573,76.33745640315206,76.44558311193839,76.55370982072473,76.66183652951106,76.76996323829741,76.87808994708372,76.98621665587007,77.09434336465641,77.20247007344274,77.31059678222907,77.4187234910154,77.52685019980174,77.63497690858807,77.7431036173744,77.85123032616075,77.95935703494708,78.06748374373342,78.17561045251973,78.28373716130608,78.39186387009241,78.49999057887875,78.6081172876651,78.71624399645141,78.82437070523775,78.93249741402408,79.04062412281043,79.14875083159674,79.25687754038309,79.36500424916943,79.47313095795576,79.58125766674209,79.68938437552842,79.79751108431476,79.90563779310109,80.01376450188744,80.12189121067377,80.2300179194601,80.33814462824644,80.44627133703277,80.5543980458191,80.66252475460543,80.77065146339177,80.87877817217812,80.98690488096443,81.09503158975077,81.2031582985371,81.31128500732345,81.41941171610976,81.52753842489611,81.63566513368245,81.74379184246878,81.85191855125512,81.96004526004144,82.06817196882778,82.17629867761411,82.28442538640046,82.39255209518679,82.50067880397312,82.60880551275946,82.71693222154579,82.82505893033212,82.93318563911845,83.04131234790479,83.14943905669114,83.25756576547747,83.3656924742638,83.47381918305013,83.58194589183647,83.6900726006228,83.79819930940913,83.90632601819547,84.0144527269818,84.12257943576815,84.23070614455446,84.3388328533408,84.44695956212713,84.55508627091348,84.66321297969982,84.77133968848614,84.87946639727248,84.98759310605881,85.09571981484515,85.20384652363147,85.31197323241781,85.42009994120416,85.52822664999049,85.63635335877682,85.74448006756315,85.85260677634949,85.96073348513582,86.06886019392215,86.17698690270849,86.28511361149482,86.39324032028117,86.50136702906748,86.60949373785382,86.71762044664015,86.8257471554265,86.93387386421284,87.04200057299916,87.1501272817855,87.25825399057183,87.36638069935817,87.47450740814449,87.58263411693083,87.69076082571718,87.7988875345035,87.90701424328985,88.01514095207617,88.12326766086251,88.23139436964884,88.33952107843518,88.44764778722151,88.55577449600784,88.66390120479419,88.77202791358052,88.88015462236685,88.98828133115317,89.09640803993952,89.20453474872586,89.31266145751218,89.42078816629852,89.52891487508485,89.6370415838712,89.74516829265751,89.85329500144385,89.9614217102302,90.06954841901653,90.17767512780287,90.28580183658919,90.39392854537553,90.50205525416186,90.6101819629482,90.71830867173452,90.82643538052086,90.9345620893072,91.04268879809354,91.15081550687988,91.2589422156662,91.36706892445254,91.47519563323887,91.58332234202521,91.69144905081154,91.79957575959787,91.90770246838422,92.01582917717054,92.12395588595687,92.2320825947432,92.34020930352955,92.44833601231589,92.5564627211022,92.66458942988855,92.77271613867488,92.88084284746122,92.98896955624754,93.09709626503388,93.20522297382023,93.31334968260656,93.4214763913929,93.52960310017922,93.63772980896556,93.74585651775189,93.85398322653823,93.96210993532456,94.07023664411089,94.17836335289724,94.28649006168357,94.39461677046991,94.50274347925622,94.61087018804257,94.71899689682891,94.82712360561524,94.93525031440157,95.0433770231879,95.15150373197424,95.25963044076057,95.3677571495469,95.47588385833325,95.58401056711958,95.69213727590592,95.80026398469224,95.90839069347858,96.01651740226491,96.12464411105125,96.2327708198376,96.34089752862391,96.44902423741026,96.55715094619659,96.66527765498293,96.77340436376925,96.88153107255559,96.98965778134193,97.09778449012826,97.20591119891459,97.31403790770092,97.42216461648727,97.5302913252736,97.63841803405994,97.74654474284627,97.8546714516326,97.96279816041894,98.07092486920527,98.1790515779916,98.28717828677793,98.39530499556427,98.50343170435062,98.61155841313693,98.71968512192328,98.8278118307096,98.93593853949595,99.04406524828227,99.15219195706861,99.26031866585495,99.36844537464128,99.47657208342763,99.58469879221394,99.69282550100029,99.80095220978662,99.90907891857296,100.01720562735929,100.12533233614562,100.23345904493196,100.34158575371829,100.44971246250462,100.55783917129095,100.6659658800773,100.77409258886364,100.88221929764997,100.9903460064363,101.09847271522263,101.20659942400897,101.3147261327953,101.42285284158163,101.53097955036797,101.6391062591543,101.74723296794065,101.85535967672696,101.9634863855133,102.07161309429964,102.17973980308598,102.28786651187232,102.39599322065864,102.50411992944498,102.61224663823131,102.72037334701766,102.82850005580397,102.93662676459032,103.04475347337666,103.15288018216299,103.26100689094932,103.36913359973565,103.47726030852199,103.58538701730832,103.69351372609465,103.801640434881,103.90976714366732,104.01789385245367,104.12602056124,104.23414727002633,104.34227397881266,104.450400687599,104.55852739638533,104.66665410517166,104.774780813958,104.88290752274433,104.99103423153068,105.09916094031699,105.20728764910334,105.31541435788967,105.42354106667601,105.53166777546235,105.63979448424867,105.74792119303501,105.85604790182134,105.96417461060769,106.072301319394,106.18042802818034,106.28855473696669,106.39668144575302,106.50480815453935,106.61293486332568,106.72106157211202,106.82918828089835,106.93731498968468,107.04544169847102,107.15356840725735,107.2616951160437,107.36982182483003,107.47794853361636,107.58607524240269,107.69420195118903,107.80232865997537,107.91045536876169,108.01858207754803,108.12670878633436,108.2348354951207,108.34296220390702,108.45108891269336,108.55921562147971,108.66734233026604,108.77546903905238,108.8835957478387,108.99172245662504,109.09984916541137,109.20797587419771,109.31610258298404,109.42422929177037,109.53235600055672,109.64048270934305,109.74860941812938,109.8567361269157,109.96486283570205,110.0729895444884,110.18111625327471,110.28924296206105,110.39736967084738,110.50549637963373,110.61362308842006,110.72174979720639,110.82987650599273,110.93800321477906,111.0461299235654,111.15425663235172,111.26238334113806,111.37051004992439,111.47863675871073,111.58676346749706,111.6948901762834,111.80301688506974,111.91114359385607,112.01927030264241,112.12739701142873,112.23552372021507,112.34365042900141,112.45177713778774,112.55990384657407,112.6680305553604,112.77615726414675,112.88428397293308,112.9924106817194,113.10053739050575,113.20866409929208,113.31679080807842,113.42491751686474,113.53304422565108,113.64117093443741,113.74929764322376,113.8574243520101,113.96555106079641,114.07367776958276,114.18180447836909,114.28993118715543,114.39805789594175,114.50618460472809,114.61431131351443,114.72243802230076,114.8305647310871,114.93869143987342,115.04681814865977,115.1549448574461,115.26307156623244,115.37119827501877,115.4793249838051,115.58745169259144,115.69557840137777,115.8037051101641,115.91183181895043,116.01995852773678,116.12808523652312,116.23621194530944,116.34433865409578,116.45246536288211,116.56059207166845,116.66871878045477,116.77684548924111,116.88497219802746,116.99309890681378,117.10122561560013,117.20935232438644,117.31747903317279,117.42560574195912,117.53373245074546,117.64185915953179,117.74998586831812,117.85811257710446,117.9662392858908,118.07436599467712,118.18249270346345,118.2906194122498,118.39874612103613,118.50687282982247,118.6149995386088,118.72312624739513,118.83125295618147,118.9393796649678,119.04750637375413,119.15563308254046,119.2637597913268,119.37188650011315,119.48001320889946,119.58813991768581,119.69626662647214,119.80439333525848,119.9125200440448,120.02064675283114,120.12877346161748,120.23690017040381,120.34502687919016,120.45315358797647,120.56128029676282,120.66940700554915,120.77753371433549,120.88566042312182,120.99378713190815,121.1019138406945,121.21004054948082,121.31816725826715,121.42629396705348,121.53442067583983,121.64254738462617,121.7506740934125,121.85880080219883,121.96692751098516,122.0750542197715,122.18318092855783,122.29130763734416,122.3994343461305,122.50756105491683,122.61568776370318,122.7238144724895,122.83194118127584,122.94006789006217,123.04819459884851,123.15632130763485,123.26444801642117,123.37257472520751,123.48070143399384,123.58882814278019,123.6969548515665,123.80508156035285,123.91320826913919,124.02133497792552,124.12946168671185,124.23758839549818,124.34571510428452,124.45384181307085,124.56196852185718,124.67009523064353,124.77822193942986,124.8863486482162,124.99447535700253,125.10260206578886,125.21072877457519,125.31885548336153,125.42698219214788,125.53510890093419,125.64323560972053,125.75136231850686,125.85948902729321,125.96761573607952,126.07574244486587,126.18386915365221,126.29199586243854,126.40012257122488,126.5082492800112,126.61637598879754,126.72450269758387,126.83262940637022,126.94075611515655,127.04888282394288,127.15700953272922,127.26513624151555,127.37326295030188,127.48138965908821,127.58951636787455,127.6976430766609,127.80576978544721,127.91389649423355,128.02202320301987,128.13014991180623,128.23827662059256,128.3464033293789,128.45453003816525,128.56265674695155,128.6707834557379,128.77891016452423,128.88703687331056,128.9951635820969,129.10329029088322,129.21141699966958,129.3195437084559,129.42767041724224,129.53579712602857,129.6439238348149,129.75205054360123,129.8601772523876,129.96830396117392,130.07643066996025,130.18455737874658,130.2926840875329,130.40081079631923,130.50893750510556,130.61706421389192,130.72519092267825,130.83331763146458,130.9414443402509,131.04957104903724,131.1576977578236,131.2658244666099,131.37395117539626,131.4820778841826,131.59020459296892,131.69833130175527,131.80645801054158,131.91458471932793,132.02271142811426,132.1308381369006,132.23896484568692,132.34709155447325,132.4552182632596,132.56334497204594,132.67147168083227,132.7795983896186,132.88772509840493,132.99585180719126,133.10397851597762,133.21210522476395,133.32023193355028,133.4283586423366,133.53648535112293,133.64461205990926,133.7527387686956,133.86086547748195,133.96899218626828,134.0771188950546,134.18524560384094,134.29337231262727,134.40149902141363,134.50962573019993,134.6177524389863,134.72587914777262,134.83400585655895,134.9421325653453,135.0502592741316,135.15838598291796,135.2665126917043,135.37463940049062,135.48276610927695,135.59089281806328,135.69901952684964,135.80714623563597,135.9152729444223,136.02339965320863,136.13152636199496,136.23965307078132,136.34777977956765,136.45590648835397,136.5640331971403,136.67215990592663,136.78028661471296,136.8884133234993,136.99654003228565,137.10466674107198,137.2127934498583,137.32092015864464,137.42904686743097,137.5371735762173,137.64530028500366,137.75342699379,137.86155370257632,137.96968041136265,138.07780712014898,138.18593382893533,138.29406053772163,138.402187246508,138.51031395529432,138.61844066408065,138.72656737286698,138.8346940816533,138.94282079043967,139.050947499226,139.15907420801233],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283],"y":[0,0.07510695114889966,0.15021390229779932,0.225320853446699,0.30042780459559865,0.3755347557444983,0.450641706893398,0.5257486580422976,0.6008556091911973,0.6759625603400969,0.7510695114889966,0.8261764626378963,0.901283413786796,0.9763903649356956,1.0514973160845953,1.126604267233495,1.2017112183823946,1.2768181695312943,1.3519251206801939,1.4270320718290936,1.5021390229779932,1.577245974126893,1.6523529252757927,1.7274598764246922,1.802566827573592,1.8776737787224915,1.9527807298713913,2.0278876810202906,2.1029946321691906,2.17810158331809,2.25320853446699,2.3283154856158896,2.403422436764789,2.4785293879136887,2.5536363390625887,2.6287432902114882,2.7038502413603878,2.7789571925092877,2.8540641436581873,2.929171094807087,3.0042780459559864,3.079384997104886,3.154491948253786,3.2295988994026854,3.3047058505515854,3.3798128017004845,3.4549197528493845,3.530026703998284,3.605133655147184,3.680240606296083,3.755347557444983,3.8304545085938826,3.9055614597427826,3.9806684108916817,4.055775362040581,4.130882313189481,4.205989264338381,4.281096215487281,4.35620316663618,4.43131011778508,4.50641706893398,4.581524020082879,4.656630971231779,4.731737922380678,4.806844873529578,4.881951824678478,4.957058775827377,5.032165726976277,5.107272678125177,5.1823796292740765,5.2574865804229765,5.3325935315718755,5.4077004827207755,5.4828074338696755,5.5579143850185755,5.633021336167475,5.708128287316375,5.7832352384652745,5.858342189614174,5.933449140763073,6.008556091911973,6.083663043060873,6.158769994209772,6.233876945358673,6.308983896507572,6.384090847656471,6.459197798805371,6.53430474995427,6.609411701103171,6.68451865225207,6.759625603400969,6.83473255454987,6.909839505698769,6.984946456847669,7.060053407996568,7.135160359145468,7.210267310294368,7.285374261443267,7.360481212592166,7.435588163741067,7.510695114889966,7.585802066038866,7.660909017187765,7.736015968336665,7.811122919485565,7.886229870634464,7.961336821783363,8.036443772932264,8.111550724081162,8.186657675230064,8.261764626378962,8.336871577527862,8.411978528676762,8.487085479825662,8.562192430974562,8.63729938212346,8.71240633327236,8.78751328442126,8.86262023557016,8.937727186719059,9.01283413786796,9.087941089016859,9.163048040165759,9.238154991314659,9.313261942463559,9.388368893612459,9.463475844761357,9.538582795910258,9.613689747059157,9.688796698208057,9.763903649356957,9.839010600505855,9.914117551654755,9.989224502803655,10.064331453952555,10.139438405101455,10.214545356250355,10.289652307399253,10.364759258548153,10.439866209697053,10.514973160845953,10.590080111994853,10.665187063143751,10.740294014292653,10.815400965441551,10.890507916590451,10.965614867739351,11.040721818888251,11.115828770037151,11.19093572118605,11.26604267233495,11.34114962348385,11.41625657463275,11.491363525781647,11.566470476930549,11.641577428079447,11.716684379228347,11.791791330377247,11.866898281526145,11.942005232675047,12.017112183823945,12.092219134972847,12.167326086121745,12.242433037270645,12.317539988419544,12.392646939568445,12.467753890717345,12.542860841866244,12.617967793015143,12.693074744164042,12.768181695312942,12.843288646461843,12.918395597610742,12.993502548759642,13.06860949990854,13.14371645105744,13.218823402206342,13.293930353355242,13.36903730450414,13.44414425565304,13.519251206801938,13.59435815795084,13.66946510909974,13.744572060248638,13.819679011397538,13.894785962546436,13.969892913695338,14.044999864844238,14.120106815993136,14.195213767142036,14.270320718290936,14.345427669439834,14.420534620588736,14.495641571737636,14.570748522886534,14.645855474035434,14.720962425184332,14.796069376333234,14.871176327482134,14.946283278631032,15.021390229779932,15.09649718092883,15.171604132077732,15.246711083226632,15.32181803437553,15.39692498552443,15.47203193667333,15.547138887822232,15.62224583897113,15.69735279012003,15.772459741268928,15.847566692417828,15.922673643566727,15.997780594715628,16.07288754586453,16.14799449701343,16.223101448162325,16.298208399311225,16.37331535046013,16.44842230160903,16.523529252757925,16.598636203906825,16.673743155055725,16.748850106204625,16.823957057353525,16.899064008502425,16.974170959651325,17.04927791080022,17.124384861949125,17.199491813098025,17.27459876424692,17.34970571539582,17.42481266654472,17.49991961769362,17.57502656884252,17.65013351999142,17.72524047114032,17.80034742228922,17.875454373438117,17.95056132458702,18.02566827573592,18.100775226884817,18.175882178033717,18.250989129182617,18.326096080331517,18.401203031480417,18.476309982629317,18.551416933778217,18.626523884927117,18.701630836076017,18.776737787224917,18.851844738373817,18.926951689522713,19.002058640671613,19.077165591820517,19.152272542969413,19.227379494118313,19.302486445267213,19.377593396416113,19.45270034756501,19.527807298713913,19.602914249862813,19.67802120101171,19.75312815216061,19.82823510330951,19.903342054458413,19.97844900560731,20.05355595675621,20.12866290790511,20.20376985905401,20.27887681020291,20.35398376135181,20.42909071250071,20.504197663649606,20.579304614798506,20.65441156594741,20.729518517096306,20.804625468245206,20.879732419394106,20.954839370543006,21.029946321691906,21.105053272840806,21.180160223989706,21.255267175138606,21.330374126287502,21.405481077436402,21.480588028585306,21.555694979734202,21.630801930883102,21.705908882032002,21.781015833180902,21.856122784329802,21.931229735478702,22.006336686627602,22.081443637776502,22.1565505889254,22.231657540074302,22.306764491223202,22.3818714423721,22.456978393521,22.5320853446699,22.6071922958188,22.6822992469677,22.7574061981166,22.8325131492655,22.907620100414395,22.982727051563295,23.057834002712198,23.132940953861098,23.208047905009995,23.283154856158895,23.358261807307795,23.433368758456695,23.508475709605595,23.583582660754494,23.658689611903394,23.73379656305229,23.808903514201194,23.884010465350094,23.95911741649899,24.03422436764789,24.10933131879679,24.184438269945694,24.25954522109459,24.33465217224349,24.409759123392394,24.48486607454129,24.55997302569019,24.635079976839087,24.71018692798799,24.78529387913689,24.860400830285787,24.93550778143469,25.010614732583587,25.085721683732487,25.160828634881383,25.235935586030287,25.311042537179187,25.386149488328083,25.461256439476987,25.536363390625883,25.611470341774783,25.686577292923687,25.761684244072583,25.836791195221483,25.91189814637038,25.987005097519283,26.062112048668183,26.13721899981708,26.212325950965983,26.28743290211488,26.362539853263783,26.437646804412683,26.51275375556158,26.587860706710483,26.66296765785938,26.73807460900828,26.813181560157183,26.88828851130608,26.96339546245498,27.038502413603876,27.11360936475278,27.18871631590168,27.263823267050576,27.33893021819948,27.414037169348376,27.489144120497276,27.56425107164618,27.639358022795076,27.714464973943976,27.789571925092872,27.864678876241776,27.939785827390676,28.014892778539572,28.089999729688476,28.165106680837372,28.240213631986272,28.31532058313517,28.390427534284072,28.465534485432975,28.540641436581872,28.615748387730772,28.69085533887967,28.765962290028572,28.841069241177472,28.91617619232637,28.991283143475272,29.06639009462417,29.141497045773068,29.21660399692197,29.291710948070868,29.366817899219768,29.441924850368665,29.517031801517568,29.592138752666468,29.667245703815365,29.742352654964268,29.817459606113164,29.892566557262064,29.967673508410968,30.042780459559864,30.117887410708764,30.19299436185766,30.268101313006564,30.343208264155464,30.41831521530436,30.493422166453264,30.56852911760216,30.64363606875106,30.718743019899964,30.79384997104886,30.868956922197764,30.94406387334666,31.01917082449556,31.094277775644464,31.16938472679336,31.24449167794226,31.319598629091157,31.39470558024006,31.46981253138896,31.544919482537857,31.62002643368676,31.695133384835657,31.770240335984557,31.845347287133453,31.920454238282357,31.995561189431257,32.07066814058015,32.14577509172906,32.22088204287795,32.29598899402686,32.37109594517575,32.44620289632465,32.52130984747355,32.59641679862245,32.67152374977135,32.74663070092026,32.82173765206915,32.89684460321806,32.97195155436695,33.04705850551585,33.12216545666475,33.19727240781365,33.27237935896255,33.34748631011145,33.42259326126035,33.49770021240925,33.572807163558146,33.64791411470705,33.723021065855946,33.79812801700485,33.87323496815375,33.94834191930265,34.023448870451546,34.09855582160044,34.173662772749346,34.24876972389825,34.323876675047146,34.39898362619605,34.474090577344946,34.54919752849384,34.624304479642745,34.69941143079164,34.774518381940545,34.84962533308944,34.924732284238345,34.99983923538724,35.074946186536145,35.15005313768504,35.22516008883394,35.30026703998284,35.37537399113174,35.45048094228064,35.525587893429545,35.60069484457844,35.67580179572734,35.750908746876235,35.82601569802514,35.90112264917404,35.97622960032294,36.05133655147184,36.12644350262074,36.201550453769634,36.27665740491854,36.351764356067434,36.42687130721634,36.501978258365234,36.57708520951414,36.652192160663034,36.72729911181193,36.802406062960834,36.87751301410973,36.952619965258634,37.02772691640754,37.102833867556434,37.17794081870533,37.253047769854234,37.32815472100313,37.403261672152034,37.47836862330093,37.553475574449834,37.62858252559873,37.703689476747634,37.77879642789653,37.85390337904543,37.92901033019433,38.00411728134323,38.07922423249213,38.154331183641034,38.22943813478993,38.30454508593883,38.37965203708772,38.45475898823663,38.52986593938552,38.60497289053443,38.68007984168333,38.75518679283223,38.83029374398112,38.90540069513002,38.98050764627892,39.05561459742783,39.13072154857672,39.20582849972563,39.28093545087452,39.35604240202342,39.43114935317232,39.50625630432122,39.58136325547012,39.65647020661902,39.73157715776792,39.806684108916826,39.88179106006572,39.95689801121462,40.032004962363516,40.10711191351242,40.18221886466132,40.25732581581022,40.33243276695912,40.40753971810802,40.482646669256916,40.55775362040582,40.632860571554716,40.70796752270362,40.783074473852515,40.85818142500142,40.933288376150315,41.00839532729921,41.083502278448115,41.15860922959701,41.233716180745915,41.30882313189482,41.383930083043715,41.45903703419261,41.53414398534151,41.60925093649041,41.68435788763931,41.75946483878821,41.834571789937115,41.90967874108601,41.984785692234915,42.05989264338381,42.13499959453271,42.21010654568161,42.28521349683051,42.36032044797941,42.43542739912831,42.51053435027721,42.58564130142611,42.660748252575004,42.73585520372391,42.810962154872804,42.88606910602171,42.96117605717061,43.03628300831951,43.111389959468404,43.1864969106173,43.261603861766204,43.33671081291511,43.411817764064004,43.48692471521291,43.562031666361804,43.6371386175107,43.712245568659604,43.7873525198085,43.862459470957404,43.9375664221063,44.012673373255204,44.08778032440411,44.162887275553004,44.2379942267019,44.3131011778508,44.3882081289997,44.463315080148604,44.5384220312975,44.613528982446404,44.6886359335953,44.7637428847442,44.8388498358931,44.913956787042,44.9890637381909,45.0641706893398,45.1392776404887,45.2143845916376,45.28949154278649,45.3645984939354,45.43970544508429,45.5148123962332,45.58991934738209,45.665026298531,45.74013324967989,45.81524020082879,45.89034715197769,45.96545410312659,46.04056105427549,46.115668005424396,46.19077495657329,46.265881907722196,46.34098885887109,46.41609581001999,46.49120276116889,46.56630971231779,46.64141666346669,46.71652361461559,46.79163056576449,46.86673751691339,46.941844468062286,47.01695141921119,47.092058370360085,47.16716532150899,47.24227227265789,47.31737922380679,47.392486174955685,47.46759312610458,47.542700077253485,47.61780702840239,47.692913979551285,47.76802093070019,47.843127881849085,47.91823483299798,47.993341784146885,48.06844873529578,48.143555686444685,48.21866263759358,48.29376958874248,48.36887653989139,48.443983491040285,48.51909044218918,48.594197393338085,48.66930434448698,48.74441129563588,48.81951824678479,48.894625197933685,48.96973214908258,49.04483910023148,49.11994605138038,49.19505300252928,49.270159953678174,49.345266904827085,49.42037385597598,49.49548080712488,49.57058775827378,49.64569470942268,49.720801660571574,49.79590861172047,49.87101556286938,49.94612251401828,50.021229465167174,50.09633641631608,50.171443367464974,50.24655031861387,50.32165726976277,50.39676422091168,50.471871172060574,50.54697812320947,50.622085074358374,50.69719202550727,50.77229897665617,50.84740592780508,50.922512878953974,50.99761983010287,51.07272678125177,51.14783373240067,51.22294068354957,51.29804763469846,51.373154585847374,51.44826153699627,51.52336848814517,51.59847543929407,51.67358239044297,51.74868934159186,51.82379629274076,51.89890324388967,51.97401019503857,52.04911714618746,52.124224097336366,52.19933104848526,52.27443799963416,52.34954495078307,52.424651901931966,52.49975885308086,52.57486580422976,52.64997275537867,52.725079706527566,52.80018665767646,52.875293608825366,52.95040055997426,53.02550751112316,53.10061446227207,53.175721413420966,53.25082836456986,53.32593531571876,53.40104226686766,53.47614921801656,53.551256169165455,53.626363120314366,53.70147007146326,53.77657702261216,53.851683973761055,53.92679092490996,54.001897876058855,54.07700482720775,54.15211177835666,54.22721872950556,54.302325680654455,54.37743263180336,54.452539582952255,54.52764653410115,54.60275348525005,54.67786043639896,54.752967387547855,54.82807433869675,54.903181289845655,54.97828824099455,55.05339519214345,55.12850214329236,55.203609094441255,55.27871604559015,55.35382299673905,55.42892994788795,55.50403689903685,55.579143850185744,55.654250801334655,55.72935775248355,55.80446470363245,55.87957165478135,55.95467860593025,56.029785557079144,56.10489250822804,56.17999945937695,56.25510641052585,56.330213361674744,56.40532031282365,56.480427263972544,56.55553421512144,56.63064116627034,56.70574811741925,56.780855068568144,56.85596201971704,56.93106897086595,57.00617592201485,57.081282873163744,57.15638982431265,57.231496775461544,57.30660372661044,57.38171067775934,57.45681762890825,57.531924580057144,57.60703153120604,57.682138482354944,57.75724543350384,57.83235238465274,57.90745933580165,57.982566286950544,58.05767323809944,58.13278018924834,58.20788714039724,58.282994091546136,58.35810104269503,58.43320799384394,58.50831494499284,58.583421896141736,58.65852884729064,58.733635798439536,58.80874274958843,58.88384970073733,58.95895665188624,59.034063603035136,59.10917055418403,59.184277505332936,59.25938445648183,59.33449140763073,59.40959835877964,59.484705309928536,59.55981226107743,59.63491921222633,59.71002616337523,59.78513311452413,59.860240065673025,59.935347016821936,60.01045396797083,60.08556091911973,60.160667870268625,60.23577482141753,60.310881772566425,60.38598872371532,60.46109567486423,60.53620262601313,60.611309577162025,60.68641652831093,60.761523479459825,60.83663043060872,60.91173738175762,60.98684433290653,61.061951284055425,61.13705823520432,61.212165186353225,61.28727213750212,61.362379088651025,61.43748603979993,61.512592990948825,61.58769994209772,61.66280689324662,61.73791384439553,61.813020795544425,61.88812774669332,61.963234697842225,62.03834164899112,62.11344860014002,62.18855555128893,62.263662502437825,62.33876945358672,62.41387640473562,62.48898335588452,62.56409030703342,62.639197258182314,62.714304209331225,62.78941116048012,62.86451811162902,62.93962506277792,63.01473201392682,63.089838965075714,63.16494591622461,63.24005286737352,63.31515981852242,63.390266769671314,63.46537372082022,63.540480671969114,63.61558762311801,63.69069457426691,63.76580152541582,63.840908476564714,63.91601542771361,63.991122378862514,64.06622933001141,64.1413362811603,64.21644323230922,64.29155018345811,64.36665713460701,64.4417640857559,64.51687103690482,64.59197798805371,64.66708493920261,64.7421918903515,64.8172988415004,64.8924057926493,64.96751274379821,65.0426196949471,65.117726646096,65.1928335972449,65.26794054839381,65.3430474995427,65.4181544506916,65.49326140184051,65.56836835298941,65.6434753041383,65.71858225528722,65.79368920643611,65.86879615758501,65.9439031087339,66.0190100598828,66.0941170110317,66.1692239621806,66.2443309133295,66.3194378644784,66.3945448156273,66.4696517667762,66.5447587179251,66.619865669074,66.6949726202229,66.77007957137181,66.8451865225207,66.9202934736696,66.9954004248185,67.0705073759674,67.14561432711629,67.22072127826519,67.2958282294141,67.370935180563,67.44604213171189,67.5211490828608,67.5962560340097,67.6713629851586,67.7464699363075,67.8215768874564,67.8966838386053,67.9717907897542,68.04689774090309,68.12200469205199,68.19711164320088,68.2722185943498,68.34732554549869,68.42243249664759,68.4975394477965,68.5726463989454,68.64775335009429,68.72286030124319,68.7979672523921,68.873074203541,68.94818115468989,69.02328810583879,69.09839505698768,69.17350200813658,69.24860895928549,69.32371591043439,69.39882286158328,69.47392981273218,69.54903676388109,69.62414371502999,69.69925066617888,69.7743576173278,69.84946456847669,69.92457151962559,69.99967847077448,70.0747854219234,70.14989237307229,70.22499932422119,70.30010627537008,70.37521322651898,70.45032017766788,70.52542712881679,70.60053407996568,70.67564103111458,70.75074798226348,70.82585493341239,70.90096188456128,70.97606883571018,71.05117578685909,71.12628273800799,71.20138968915688,71.27649664030578,71.35160359145468,71.42671054260357,71.50181749375247,71.57692444490138,71.65203139605028,71.72713834719917,71.80224529834808,71.87735224949698,71.95245920064588,72.02756615179479,72.10267310294368,72.17778005409258,72.25288700524148,72.32799395639037,72.40310090753927,72.47820785868817,72.55331480983708,72.62842176098597,72.70352871213487,72.77863566328378,72.85374261443268,72.92884956558157,73.00395651673047,73.07906346787938,73.15417041902828,73.22927737017717,73.30438432132607,73.37949127247497,73.45459822362386,73.52970517477276,73.60481212592167,73.67991907707056,73.75502602821946,73.83013297936837,73.90523993051727,73.98034688166616,74.05545383281508,74.13056078396397,74.20566773511287,74.28077468626176,74.35588163741066,74.43098858855956,74.50609553970847,74.58120249085736,74.65630944200626,74.73141639315516,74.80652334430407,74.88163029545296,74.95673724660186,75.03184419775076,75.10695114889967,75.18205810004856,75.25716505119746,75.33227200234637,75.40737895349527,75.48248590464416,75.55759285579306,75.63269980694196,75.70780675809085,75.78291370923975,75.85802066038866,75.93312761153756,76.00823456268645,76.08334151383536,76.15844846498426,76.23355541613316,76.30866236728207,76.38376931843096,76.45887626957986,76.53398322072876,76.60909017187765,76.68419712302655,76.75930407417545,76.83441102532436,76.90951797647325,76.98462492762215,77.05973187877105,77.13483882991996,77.20994578106885,77.28505273221775,77.36015968336666,77.43526663451556,77.51037358566445,77.58548053681335,77.66058748796225,77.73569443911114,77.81080139026004,77.88590834140895,77.96101529255785,78.03612224370674,78.11122919485565,78.18633614600455,78.26144309715345,78.33655004830236,78.41165699945125,78.48676395060015,78.56187090174905,78.63697785289794,78.71208480404684,78.78719175519574,78.86229870634465,78.93740565749354,79.01251260864244,79.08761955979135,79.16272651094025,79.23783346208914,79.31294041323804,79.38804736438695,79.46315431553585,79.53826126668474,79.61336821783365,79.68847516898255,79.76358212013145,79.83868907128034,79.91379602242924,79.98890297357813,80.06400992472703,80.13911687587594,80.21422382702484,80.28933077817373,80.36443772932265,80.43954468047154,80.51465163162044,80.58975858276933,80.66486553391825,80.73997248506714,80.81507943621604,80.89018638736493,80.96529333851383,81.04040028966273,81.11550724081164,81.19061419196053,81.26572114310943,81.34082809425833,81.41593504540724,81.49104199655613,81.56614894770503,81.64125589885394,81.71636285000284,81.79146980115173,81.86657675230063,81.94168370344953,82.01679065459842,82.09189760574732,82.16700455689623,82.24211150804513,82.31721845919402,82.39232541034293,82.46743236149183,82.54253931264073,82.61764626378964,82.69275321493853,82.76786016608743,82.84296711723633,82.91807406838522,82.99318101953412,83.06828797068302,83.14339492183193,83.21850187298082,83.29360882412972,83.36871577527862,83.44382272642753,83.51892967757642,83.59403662872532,83.66914357987423,83.74425053102313,83.81935748217202,83.89446443332093,83.96957138446983,84.04467833561873,84.11978528676762,84.19489223791652,84.26999918906542,84.34510614021431,84.42021309136322,84.49532004251212,84.57042699366102,84.64553394480993,84.72064089595882,84.79574784710772,84.87085479825662,84.94596174940553,85.02106870055442,85.09617565170332,85.17128260285222,85.24638955400111,85.32149650515001,85.39660345629892,85.47171040744782,85.54681735859671,85.62192430974561,85.69703126089452,85.77213821204342,85.84724516319231,85.92235211434122,85.99745906549012,86.07256601663902,86.14767296778791,86.22277991893681,86.2978868700857,86.3729938212346,86.44810077238351,86.52320772353241,86.5983146746813,86.67342162583022,86.74852857697911,86.82363552812801,86.8987424792769,86.97384943042582,87.04895638157471,87.12406333272361,87.1991702838725,87.2742772350214,87.3493841861703,87.42449113731921,87.4995980884681,87.574705039617,87.6498119907659,87.72491894191481,87.8000258930637,87.8751328442126,87.95023979536151,88.02534674651041,88.1004536976593,88.17556064880822,88.25066759995711,88.32577455110601,88.4008815022549,88.4759884534038,88.5510954045527,88.6262023557016,88.7013093068505,88.7764162579994,88.8515232091483,88.92663016029721,89.0017371114461,89.076844062595,89.1519510137439,89.22705796489281,89.3021649160417,89.3772718671906,89.4523788183395,89.5274857694884,89.60259272063729,89.6776996717862,89.7528066229351,89.827913574084,89.90302052523289,89.9781274763818,90.0532344275307,90.1283413786796,90.2034483298285,90.2785552809774,90.3536622321263,90.4287691832752,90.50387613442409,90.57898308557299,90.65409003672188,90.7291969878708,90.80430393901969,90.87941089016859,90.9545178413175,91.0296247924664,91.10473174361529,91.17983869476419,91.2549456459131,91.330052597062,91.40515954821089,91.48026649935979,91.55537345050868,91.63048040165758,91.70558735280649,91.78069430395539,91.85580125510428,91.93090820625318,92.00601515740209,92.08112210855099,92.15622905969988,92.23133601084879,92.30644296199769,92.38154991314659,92.4566568642955,92.53176381544439,92.60687076659329,92.68197771774219,92.75708466889108,92.83219162003998,92.90729857118887,92.98240552233779,93.05751247348668,93.13261942463558,93.20772637578447,93.28283332693339,93.35794027808228,93.43304722923118,93.50815418038009,93.58326113152899,93.65836808267788,93.73347503382678,93.80858198497567,93.88368893612457,93.95879588727347,94.03390283842238,94.10900978957127,94.18411674072017,94.25922369186908,94.33433064301798,94.40943759416687,94.48454454531579,94.55965149646468,94.63475844761358,94.70986539876247,94.78497234991137,94.86007930106027,94.93518625220916,95.01029320335807,95.08540015450697,95.16050710565587,95.23561405680478,95.31072100795367,95.38582795910257,95.46093491025147,95.53604186140038,95.61114881254927,95.68625576369817,95.76136271484707,95.83646966599596,95.91157661714486,95.98668356829377,96.06179051944267,96.13689747059156,96.21200442174046,96.28711137288937,96.36221832403827],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165],"y":[0,0.3019970435942636,0.6039940871885272,0.9059911307827908,1.2079881743770544,1.509985217971318,1.8119822615655816,2.1139793051598454,2.415976348754109,2.717973392348372,3.019970435942636,3.3219674795369,3.6239645231311632,3.9259615667254266,4.227958610319691,4.529955653913954,4.831952697508218,5.133949741102481,5.435946784696744,5.737943828291009,6.039940871885272,6.3419379154795354,6.6439349590738,6.945932002668064,7.2479290462623265,7.549926089856591,7.851923133450853,8.153920177045118,8.455917220639382,8.757914264233644,9.059911307827909,9.361908351422171,9.663905395016435,9.9659024386107,10.267899482204962,10.569896525799226,10.871893569393489,11.173890612987753,11.475887656582017,11.77788470017628,12.079881743770544,12.381878787364808,12.683875830959071,12.985872874553335,13.2878699181476,13.589866961741862,13.891864005336128,14.19386104893039,14.495858092524653,14.797855136118919,15.099852179713181,15.401849223307444,15.703846266901706,16.005843310495973,16.307840354090235,16.609837397684498,16.911834441278764,17.213831484873026,17.51582852846729,17.817825572061555,18.119822615655817,18.42181965925008,18.723816702844342,19.025813746438608,19.32781079003287,19.629807833627133,19.9318048772214,20.23380192081566,20.535798964409924,20.837796008004187,21.139793051598453,21.441790095192715,21.743787138786978,22.045784182381244,22.347781225975506,22.64977826956977,22.951775313164035,23.253772356758297,23.55576940035256,23.857766443946826,24.15976348754109,24.46176053113535,24.763757574729617,25.06575461832388,25.367751661918142,25.669748705512408,25.97174574910667,26.273742792700933,26.5757398362952,26.87773687988946,27.179733923483724,27.48173096707799,27.783728010672256,28.085725054266515,28.38772209786078,28.689719141455047,28.991716185049306,29.293713228643572,29.595710272237838,29.897707315832097,30.199704359426363,30.501701403020622,30.803698446614888,31.105695490209154,31.407692533803413,31.70968957739768,32.011686620991945,32.31368366458621,32.61568070818047,32.91767775177473,33.219674795368995,33.521671838963265,33.82366888255753,34.12566592615179,34.42766296974605,34.729660013340315,35.03165705693458,35.33365410052885,35.63565114412311,35.93764818771737,36.239645231311634,36.5416422749059,36.84363931850016,37.14563636209443,37.447633405688684,37.749630449282954,38.051627492877216,38.35362453647148,38.65562158006574,38.95761862366001,39.259615667254266,39.561612710848536,39.8636097544428,40.16560679803706,40.46760384163132,40.76960088522559,41.07159792881985,41.37359497241412,41.67559201600837,41.97758905960264,42.279586103196905,42.58158314679117,42.88358019038543,43.1855772339797,43.487574277573955,43.789571321168225,44.09156836476249,44.39356540835675,44.69556245195101,44.99755949554528,45.29955653913954,45.60155358273381,45.90355062632807,46.20554766992233,46.507544713516594,46.809541757110864,47.11153880070512,47.41353584429939,47.71553288789365,48.017529931487914,48.31952697508218,48.62152401867644,48.9235210622707,49.22551810586497,49.527515149459234,49.829512193053496],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142],"y":[0,0.13818728321655363,0.27637456643310726,0.41456184964966086,0.5527491328662145,0.6909364160827681,0.8291236992993217,0.9673109825158752,1.105498265732429,1.2436855489489824,1.3818728321655362,1.5200601153820896,1.6582473985986435,1.796434681815197,1.9346219650317504,2.072809248248304,2.210996531464858,2.3491838146814112,2.487371097897965,2.6255583811145184,2.7637456643310725,2.9019329475476257,3.0401202307641793,3.1783075139807333,3.316494797197287,3.45468208041384,3.592869363630394,3.7310566468469477,3.869243930063501,4.007431213280055,4.145618496496608,4.283805779713162,4.421993062929716,4.560180346146269,4.6983676293628225,4.836554912579377,4.97474219579593,5.112929479012483,5.251116762229037,5.389304045445591,5.527491328662145,5.665678611878699,5.803865895095251,5.942053178311805,6.0802404615283585,6.218427744744913,6.356615027961467,6.49480231117802,6.632989594394574,6.7711768776111265,6.90936416082768,7.047551444044235,7.185738727260788,7.323926010477342,7.462113293693895,7.600300576910448,7.738487860127002,7.876675143343556,8.01486242656011,8.153049709776663,8.291236992993216,8.42942427620977,8.567611559426323,8.705798842642878,8.843986125859432,8.982173409075985,9.120360692292538,9.258547975509092,9.396735258725645,9.5349225419422,9.673109825158754,9.811297108375307,9.94948439159186,10.087671674808414,10.225858958024967,10.364046241241521,10.502233524458074,10.64042080767463,10.778608090891183,10.916795374107735,11.05498265732429,11.193169940540843,11.331357223757397,11.46954450697395,11.607731790190503,11.745919073407057,11.88410635662361,12.022293639840164,12.160480923056717,12.298668206273273,12.436855489489826,12.57504277270638,12.713230055922933,12.851417339139486,12.98960462235604,13.127791905572593,13.265979188789148,13.4041664720057,13.542353755222253,13.680541038438808,13.81872832165536,13.956915604871917,14.09510288808847,14.233290171305024,14.371477454521576,14.50966473773813,14.647852020954684,14.786039304171236,14.92422658738779,15.062413870604344,15.200601153820896,15.33878843703745,15.476975720254003,15.61516300347056,15.753350286687112,15.891537569903667,16.02972485312022,16.167912136336774,16.306099419553327,16.44428670276988,16.582473985986432,16.72066126920299,16.85884855241954,16.997035835636094,17.135223118852647,17.273410402069203,17.411597685285756,17.54978496850231,17.687972251718865,17.826159534935417,17.96434681815197,18.102534101368523,18.240721384585076,18.378908667801632,18.517095951018185,18.655283234234737,18.79347051745129,18.931657800667846,19.0698450838844,19.20803236710095,19.346219650317508,19.48440693353406,19.622594216750613],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"AI Rule 8","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89],"y":[0,0.28679421989153087,0.5735884397830617,0.8603826596745927,1.1471768795661235,1.4339710994576544,1.7207653193491854,2.007559539240716,2.294353759132247,2.581147979023778,2.867942198915309,3.1547364188068396,3.441530638698371,3.7283248585899016,4.015119078481432,4.3019132983729635,4.588707518264494,4.875501738156025,5.162295958047556,5.449090177939087,5.735884397830618,6.022678617722149,6.309472837613679,6.596267057505211,6.883061277396742,7.169855497288273,7.456649717179803,7.743443937071334,8.030238156962865,8.317032376854396,8.603826596745927,8.890620816637458,9.177415036528988,9.46420925642052,9.75100347631205,10.037797696203581,10.324591916095113,10.611386135986644,10.898180355878173,11.184974575769706,11.471768795661236,11.758563015552767,12.045357235444298,12.33215145533583,12.618945675227359,12.905739895118892,13.192534115010423,13.47932833490195,13.766122554793483,14.052916774685015,14.339710994576546,14.626505214468075,14.913299434359606,15.200093654251138,15.486887874142669,15.773682094034198,16.06047631392573,16.347270533817262,16.634064753708792,16.92085897360032,17.207653193491854,17.494447413383384,17.781241633274917,18.068035853166446,18.354830073057975,18.64162429294951,18.92841851284104,19.21521273273257,19.5020069526241,19.788801172515633,20.075595392407163,20.362389612298692,20.649183832190225,20.935978052081754,21.222772271973287,21.509566491864817,21.796360711756346,22.08315493164788,22.369949151539412,22.656743371430938,22.94353759132247,23.230331811214004,23.517126031105533,23.803920250997063,24.090714470888596,24.377508690780125,24.66430291067166,24.951097130563188,25.237891350454717,25.52468557034625],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Ext Rule 6: Fibonacci Support Confluence</td>
                <td>UNKNOWN</td>
                <td class="positive">139.16%</td>
                <td>64.7%</td>
                <td>1287</td>
                <td>1.11</td>
                <td>0.00</td>
                <td>22.30%</td>
                <td>105.1</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">96.36%</td>
                <td>64.7%</td>
                <td>1283</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>21.51%</td>
                <td>88.0</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">49.83%</td>
                <td>67.9%</td>
                <td>165</td>
                <td>1.36</td>
                <td>0.00</td>
                <td>16.40%</td>
                <td>70.3</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">19.62%</td>
                <td>67.6%</td>
                <td>142</td>
                <td>1.15</td>
                <td>0.00</td>
                <td>12.95%</td>
                <td>58.1</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">25.52%</td>
                <td>69.7%</td>
                <td>89</td>
                <td>1.32</td>
                <td>0.00</td>
                <td>7.90%</td>
                <td>57.8</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">15.61%</td>
                <td>62.9%</td>
                <td>1047</td>
                <td>1.01</td>
                <td>0.00</td>
                <td>45.55%</td>
                <td>55.1</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                <td>UNKNOWN</td>
                <td class="positive">23.93%</td>
                <td>79.3%</td>
                <td>29</td>
                <td>2.44</td>
                <td>0.00</td>
                <td>3.67%</td>
                <td>42.1</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">1.05%</td>
                <td>66.7%</td>
                <td>48</td>
                <td>1.02</td>
                <td>0.00</td>
                <td>14.89%</td>
                <td>34.8</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">12.64%</td>
                <td>81.2%</td>
                <td>16</td>
                <td>2.57</td>
                <td>0.00</td>
                <td>3.41%</td>
                <td>34.2</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">3.71%</td>
                <td>68.2%</td>
                <td>22</td>
                <td>1.18</td>
                <td>0.00</td>
                <td>5.60%</td>
                <td>28.5</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 812,700<br>
            • Backtest Range: 300 to 813,000<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
