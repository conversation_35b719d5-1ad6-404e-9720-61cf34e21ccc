
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 10 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 13:53:22</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">10</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">55.6%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">30.5%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">94.0%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">69.5%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">3,779</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["94.0%","88.6%","45.8%","15.1%","17.8%","8.1%","17.5%","11.7%","4.1%","1.8%"],"textposition":"auto","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[94.00089590856908,88.64836494254284,45.81117961435876,15.057120438142563,17.832418585445296,8.059959952512683,17.5446439221921,11.674975889427136,4.1341305647352415,1.7785699352802185],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[773,765,100,85,56,606,18,13,14,14],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[427,414,45,41,26,359,6,3,8,6],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[94.00089590856908,88.64836494254284,45.81117961435876,15.057120438142563,17.832418585445296,8.059959952512683,17.5446439221921,11.674975889427136,4.1341305647352415,1.7785699352802185],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Ext Rule 6","AI Rule 10","Prof Rule 7","Rule 6","AI Rule 8","Rule 7","Momentum Rule 2","Price Action Rule 3","Rule 10","Professional Rule 7"],"textposition":"top center","x":[26.526302890343906,15.616105808231776,10.493847043829215,11.635838871221688,7.817320987373552,45.55013874661983,3.6738976656817774,2.7571637771318596,4.633222123734829,11.872682869888095],"y":[94.00089590856908,88.64836494254284,45.81117961435876,15.057120438142563,17.832418585445296,8.059959952512683,17.5446439221921,11.674975889427136,4.1341305647352415,1.7785699352802185],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["31.2%","53.2%","45.8%","9.1%"],"textposition":"auto","x":["UNKNOWN","AI_GENERATED","PROFESSIONAL","ORIGINAL"],"y":[31.249771413867133,53.24039176399407,45.81117961435876,9.083736985130162],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["1200","1179","145","126","82","965","24","16","22","20"],"textposition":"auto","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[1200,1179,145,126,82,965,24,16,22,20],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Ext Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200],"y":[0,0.07833407992380757,0.15666815984761515,0.2350022397714227,0.3133363196952303,0.3916703996190378,0.4700044795428454,0.548338559466653,0.6266726393904606,0.7050067193142681,0.7833407992380756,0.8616748791618832,0.9400089590856908,1.0183430390094983,1.096677118933306,1.1750111988571135,1.2533452787809212,1.3316793587047284,1.4100134386285361,1.4883475185523438,1.5666815984761513,1.645015678399959,1.7233497583237665,1.801683838247574,1.8800179181713816,1.9583519980951891,2.0366860780189966,2.115020157942804,2.193354237866612,2.2716883177904195,2.350022397714227,2.4283564776380344,2.5066905575618423,2.58502463748565,2.663358717409457,2.7416927973332648,2.8200268772570722,2.89836095718088,2.9766950371046876,3.055029117028495,3.1333631969523026,3.21169727687611,3.290031356799918,3.3683654367237255,3.446699516647533,3.5250335965713404,3.603367676495148,3.681701756418956,3.7600358363427633,3.8383699162665708,3.9167039961903782,3.995038076114186,4.073372156037993,4.151706235961801,4.230040315885608,4.308374395809416,4.386708475733224,4.465042555657031,4.543376635580839,4.621710715504646,4.700044795428454,4.778378875352262,4.856712955276069,4.935047035199877,5.013381115123685,5.091715195047492,5.1700492749713,5.248383354895107,5.326717434818914,5.4050515147427225,5.4833855946665295,5.5617196745903374,5.6400537545141445,5.718387834437952,5.79672191436176,5.875055994285567,5.953390074209375,6.031724154133182,6.11005823405699,6.188392313980797,6.266726393904605,6.345060473828413,6.42339455375222,6.501728633676028,6.580062713599836,6.658396793523643,6.736730873447451,6.815064953371258,6.893399033295066,6.971733113218874,7.050067193142681,7.128401273066489,7.206735352990296,7.285069432914104,7.363403512837912,7.441737592761719,7.520071672685527,7.598405752609333,7.6767398325331415,7.755073912456949,7.8334079923807565,7.911742072304564,7.990076152228372,8.068410232152178,8.146744312075986,8.225078391999794,8.303412471923602,8.38174655184741,8.460080631771216,8.538414711695024,8.616748791618832,8.69508287154264,8.773416951466448,8.851751031390254,8.930085111314062,9.00841919123787,9.086753271161678,9.165087351085486,9.243421431009292,9.3217555109331,9.400089590856908,9.478423670780716,9.556757750704524,9.63509183062833,9.713425910552138,9.791759990475946,9.870094070399754,9.948428150323561,10.02676223024737,10.105096310171175,10.183430390094983,10.261764470018791,10.3400985499426,10.418432629866407,10.496766709790213,10.575100789714021,10.653434869637827,10.731768949561637,10.810103029485445,10.888437109409251,10.966771189333059,11.045105269256865,11.123439349180675,11.201773429104483,11.280107509028289,11.358441588952097,11.436775668875905,11.515109748799711,11.59344382872352,11.671777908647327,11.750111988571135,11.82844606849494,11.90678014841875,11.985114228342558,12.063448308266365,12.141782388190174,12.22011646811398,12.298450548037787,12.376784627961595,12.455118707885404,12.53345278780921,12.611786867733016,12.690120947656826,12.768455027580632,12.84678910750444,12.92512318742825,13.003457267352056,13.081791347275862,13.160125427199672,13.238459507123478,13.316793587047286,13.395127666971092,13.473461746894902,13.551795826818708,13.630129906742516,13.708463986666324,13.786798066590132,13.865132146513938,13.943466226437748,14.021800306361554,14.100134386285362,14.178468466209171,14.256802546132977,14.335136626056784,14.413470705980592,14.4918047859044,14.570138865828207,14.648472945752014,14.726807025675823,14.80514110559963,14.883475185523437,14.961809265447245,15.040143345371053,15.11847742529486,15.196811505218665,15.275145585142475,15.353479665066283,15.43181374499009,15.510147824913899,15.588481904837705,15.666815984761513,15.74515006468532,15.823484144609129,15.901818224532935,15.980152304456745,16.05848638438055,16.136820464304357,16.215154544228163,16.293488624151973,16.371822704075782,16.45015678399959,16.5284908639234,16.606824943847204,16.68515902377101,16.76349310369482,16.841827183618626,16.920161263542433,16.998495343466242,17.07682942339005,17.155163503313855,17.233497583237664,17.311831663161474,17.39016574308528,17.468499823009086,17.546833902932896,17.625167982856702,17.703502062780508,17.781836142704318,17.860170222628124,17.93850430255193,18.01683838247574,18.09517246239955,18.173506542323356,18.251840622247162,18.33017470217097,18.408508782094778,18.486842862018584,18.565176941942394,18.6435110218662,18.721845101790006,18.800179181713816,18.87851326163762,18.95684734156143,19.03518142148524,19.113515501409047,19.191849581332853,19.27018366125666,19.34851774118047,19.426851821104275,19.50518590102808,19.58351998095189,19.661854060875697,19.740188140799507,19.818522220723317,19.896856300647123,19.97519038057093,20.05352446049474,20.131858540418545,20.21019262034235,20.288526700266157,20.366860780189967,20.445194860113773,20.523528940037583,20.60186301996139,20.6801970998852,20.758531179809005,20.836865259732814,20.91519933965662,20.993533419580427,21.071867499504233,21.150201579428042,21.22853565935185,21.306869739275655,21.385203819199464,21.463537899123274,21.54187197904708,21.62020605897089,21.698540138894696,21.776874218818502,21.855208298742312,21.933542378666118,22.011876458589924,22.09021053851373,22.16854461843754,22.24687869836135,22.325212778285156,22.403546858208966,22.48188093813277,22.560215018056578,22.638549097980388,22.716883177904194,22.795217257828,22.87355133775181,22.951885417675616,23.030219497599422,23.10855357752323,23.18688765744704,23.265221737370847,23.343555817294654,23.421889897218463,23.50022397714227,23.57855805706608,23.65689213698988,23.73522621691369,23.8135602968375,23.891894376761307,23.970228456685117,24.048562536608923,24.12689661653273,24.20523069645654,24.28356477638035,24.36189885630415,24.44023293622796,24.518567016151763,24.596901096075573,24.675235175999383,24.75356925592319,24.831903335847,24.91023741577081,24.98857149569461,25.06690557561842,25.14523965554223,25.223573735466033,25.301907815389843,25.380241895313652,25.45857597523746,25.536910055161265,25.615244135085074,25.69357821500888,25.77191229493269,25.8502463748565,25.928580454780302,26.006914534704112,26.085248614627922,26.163582694551724,26.241916774475534,26.320250854399344,26.39858493432315,26.476919014246956,26.555253094170762,26.633587174094572,26.71192125401838,26.790255333942184,26.868589413865994,26.946923493789804,27.025257573713606,27.103591653637416,27.181925733561226,27.260259813485032,27.33859389340884,27.416927973332648,27.495262053256454,27.573596133180263,27.651930213104073,27.730264293027876,27.808598372951685,27.886932452875495,27.965266532799298,28.043600612723107,28.121934692646917,28.200268772570723,28.278602852494533,28.356936932418343,28.435271012342145,28.513605092265955,28.591939172189758,28.670273252113567,28.748607332037377,28.826941411961183,28.90527549188499,28.9836095718088,29.061943651732605,29.140277731656415,29.218611811580224,29.296945891504027,29.375279971427837,29.453614051351646,29.53194813127545,29.61028221119926,29.68861629112307,29.766950371046875,29.845284450970684,29.92361853089449,30.001952610818297,30.080286690742106,30.158620770665916,30.23695485058972,30.31528893051353,30.39362301043733,30.47195709036114,30.55029117028495,30.628625250208756,30.706959330132566,30.785293410056376,30.86362748998018,30.941961569903988,31.020295649827798,31.0986297297516,31.17696380967541,31.25529788959922,31.333631969523026,31.411966049446832,31.49030012937064,31.568634209294448,31.646968289218258,31.725302369142067,31.80363644906587,31.88197052898968,31.96030460891349,32.038638688837295,32.1169727687611,32.195306848684915,32.273640928608714,32.35197500853253,32.430309088456326,32.50864316838014,32.586977248303945,32.66531132822775,32.743645408151565,32.82197948807537,32.90031356799918,32.97864764792298,33.0569817278468,33.135315807770596,33.21364988769441,33.291983967618215,33.37031804754202,33.44865212746583,33.52698620738964,33.60532028731345,33.68365436723725,33.761988447161066,33.840322527084865,33.91865660700868,33.996990686932484,34.07532476685629,34.1536588467801,34.23199292670391,34.31032700662771,34.38866108655152,34.46699516647533,34.545329246399135,34.62366332632295,34.70199740624675,34.78033148617056,34.858665566094366,34.93699964601817,35.01533372594198,35.09366780586579,35.1720018857896,35.250335965713404,35.32867004563721,35.407004125561016,35.48533820548483,35.563672285408636,35.64200636533244,35.72034044525625,35.79867452518006,35.87700860510386,35.955342685027674,36.03367676495148,36.112010844875286,36.1903449247991,36.2686790047229,36.34701308464671,36.42534716457052,36.503681244494324,36.58201532441813,36.66034940434194,36.73868348426574,36.817017564189555,36.89535164411336,36.97368572403717,37.05201980396098,37.13035388388479,37.20868796380859,37.2870220437324,37.36535612365621,37.44369020358001,37.522024283503825,37.60035836342763,37.67869244335144,37.75702652327524,37.83536060319906,37.91369468312286,37.99202876304667,38.07036284297048,38.14869692289428,38.227031002818094,38.30536508274189,38.38369916266571,38.46203324258951,38.54036732251332,38.61870140243713,38.69703548236094,38.775369562284745,38.85370364220855,38.932037722132364,39.01037180205616,39.088705881979976,39.16703996190378,39.24537404182759,39.323708121751395,39.40204220167521,39.480376281599014,39.55871036152282,39.63704444144663,39.71537852137043,39.793712601294246,39.87204668121805,39.95038076114186,40.028714841065664,40.10704892098948,40.185383000913276,40.26371708083709,40.342051160760896,40.4203852406847,40.498719320608515,40.577053400532314,40.65538748045613,40.733721560379934,40.81205564030374,40.890389720227546,40.96872380015136,41.047057880075165,41.12539195999897,41.20372603992278,41.282060119846584,41.3603941997704,41.4387282796942,41.51706235961801,41.595396439541815,41.67373051946563,41.75206459938943,41.83039867931324,41.90873275923705,41.98706683916085,42.06540091908467,42.143734999008466,42.22206907893228,42.300403158856085,42.37873723877989,42.4570713187037,42.53540539862751,42.61373947855131,42.69207355847512,42.77040763839893,42.848741718322735,42.92707579824655,43.005409878170354,43.08374395809416,43.16207803801797,43.24041211794178,43.31874619786558,43.39708027778939,43.4754143577132,43.553748437637005,43.63208251756081,43.710416597484624,43.78875067740843,43.867084757332236,43.94541883725605,44.02375291717985,44.10208699710366,44.18042107702746,44.258755156951274,44.33708923687508,44.415423316798886,44.4937573967227,44.572091476646506,44.65042555657031,44.72875963649412,44.80709371641793,44.88542779634173,44.96376187626554,45.04209595618935,45.120430036113156,45.19876411603696,45.277098195960775,45.35543227588458,45.43376635580839,45.5121004357322,45.590434515656,45.66876859557981,45.74710267550362,45.825436755427425,45.90377083535123,45.982104915275045,46.060438995198844,46.13877307512266,46.21710715504646,46.29544123497027,46.37377531489408,46.45210939481788,46.530443474741695,46.6087775546655,46.68711163458931,46.76544571451311,46.84377979443693,46.92211387436073,47.00044795428454,47.078782034208345,47.15711611413216,47.23545019405596,47.31378427397976,47.39211835390358,47.47045243382738,47.548786513751196,47.627120593675,47.7054546735988,47.783788753522614,47.86212283344642,47.940456913370234,48.01879099329404,48.097125073217846,48.175459153141645,48.25379323306546,48.332127312989265,48.41046139291308,48.488795472836884,48.5671295527607,48.645463632684496,48.7237977126083,48.802131792532116,48.88046587245592,48.95879995237973,49.03713403230353,49.11546811222734,49.193802192151146,49.27213627207496,49.350470351998766,49.42880443192258,49.50713851184638,49.585472591770184,49.663806671694,49.742140751617804,49.82047483154162,49.89880891146542,49.97714299138922,50.05547707131303,50.13381115123684,50.21214523116065,50.29047931108446,50.36881339100827,50.447147470932066,50.52548155085588,50.603815630779685,50.6821497107035,50.760483790627305,50.83881787055112,50.91715195047492,50.99548603039872,51.07382011032253,51.15215419024634,51.23048827017015,51.30882235009395,51.38715643001776,51.46549050994157,51.54382458986538,51.62215866978919,51.700492749713,51.7788268296368,51.857160909560605,51.93549498948441,52.013829069408224,52.09216314933203,52.170497229255844,52.24883130917964,52.32716538910345,52.40549946902726,52.48383354895107,52.56216762887488,52.64050170879869,52.71883578872249,52.7971698686463,52.875503948570106,52.95383802849391,53.032172108417726,53.110506188341525,53.18884026826533,53.267174348189144,53.34550842811295,53.42384250803676,53.50217658796057,53.58051066788437,53.65884474780818,53.73717882773199,53.8155129076558,53.89384698757961,53.97218106750341,54.05051514742721,54.128849227351026,54.20718330727483,54.285517387198645,54.36385146712245,54.442185547046265,54.520519626970064,54.59885370689387,54.67718778681768,54.75552186674149,54.833855946665295,54.912190026589094,54.99052410651291,55.068858186436714,55.14719226636053,55.22552634628433,55.303860426208146,55.382194506131945,55.46052858605575,55.538862665979565,55.61719674590337,55.695530825827184,55.77386490575099,55.85219898567479,55.930533065598595,56.00886714552241,56.087201225446215,56.16553530537003,56.243869385293834,56.32220346521763,56.40053754514145,56.47887162506525,56.557205704989066,56.63553978491287,56.713873864836685,56.792207944760484,56.87054202468429,56.9488761046081,57.02721018453191,57.105544264455716,57.183878344379515,57.26221242430333,57.340546504227135,57.41888058415095,57.497214664074754,57.57554874399857,57.653882823922366,57.73221690384617,57.81055098376998,57.88888506369379,57.9672191436176,58.04555322354141,58.12388730346521,58.202221383389016,58.28055546331283,58.358889543236636,58.43722362316045,58.515557703084255,58.593891783008054,58.67222586293187,58.75055994285567,58.82889402277948,58.90722810270329,58.98556218262709,59.0638962625509,59.14223034247471,59.22056442239852,59.29889850232233,59.37723258224614,59.455566662169936,59.53390074209375,59.612234822017555,59.69056890194137,59.768902981865175,59.84723706178898,59.92557114171278,60.00390522163659,60.0822393015604,60.16057338148421,60.23890746140802,60.31724154133183,60.39557562125563,60.47390970117944,60.55224378110325,60.63057786102706,60.70891194095086,60.78724602087466,60.865580100798475,60.94391418072228,61.022248260646094,61.1005823405699,61.178916420493714,61.25725050041751,61.33558458034132,61.41391866026513,61.49225274018894,61.57058682011275,61.64892090003656,61.72725497996036,61.80558905988416,61.883923139807976,61.96225721973178,62.040591299655595,62.1189253795794,62.1972594595032,62.275593539427014,62.35392761935082,62.43226169927463,62.51059577919844,62.58892985912225,62.66726393904605,62.74559801896986,62.823932098893664,62.90226617881748,62.98060025874128,63.05893433866508,63.137268418588896,63.2156024985127,63.293936578436515,63.37227065836032,63.450604738284134,63.52893881820793,63.60727289813174,63.685606978055546,63.76394105797936,63.842275137903165,63.92060921782698,63.99894329775078,64.07727737767459,64.1556114575984,64.2339455375222,64.31227961744601,64.39061369736983,64.46894777729362,64.54728185721743,64.62561593714125,64.70395001706505,64.78228409698886,64.86061817691265,64.93895225683647,65.01728633676028,65.09562041668408,65.17395449660789,65.25228857653171,65.3306226564555,65.40895673637931,65.48729081630313,65.56562489622694,65.64395897615074,65.72229305607455,65.80062713599835,65.87896121592216,65.95729529584597,66.03562937576977,66.1139634556936,66.1922975356174,66.27063161554119,66.34896569546501,66.42729977538882,66.50563385531262,66.58396793523643,66.66230201516024,66.74063609508404,66.81897017500785,66.89730425493165,66.97563833485547,67.05397241477928,67.13230649470307,67.2106405746269,67.2889746545507,67.3673087344745,67.44564281439831,67.52397689432213,67.60231097424592,67.68064505416973,67.75897913409354,67.83731321401736,67.91564729394116,67.99398137386497,68.07231545378878,68.15064953371258,68.22898361363639,68.3073176935602,68.38565177348401,68.46398585340782,68.54231993333161,68.62065401325542,68.69898809317924,68.77732217310304,68.85565625302685,68.93399033295066,69.01232441287446,69.09065849279827,69.16899257272208,69.2473266526459,69.3256607325697,69.4039948124935,69.48232889241731,69.56066297234112,69.63899705226493,69.71733113218873,69.79566521211254,69.87399929203634,69.95233337196015,70.03066745188396,70.10900153180778,70.18733561173158,70.26566969165539,70.3440037715792,70.422337851503,70.50067193142681,70.57900601135061,70.65734009127442,70.73567417119823,70.81400825112203,70.89234233104584,70.97067641096966,71.04901049089347,71.12734457081727,71.20567865074108,71.28401273066488,71.36234681058869,71.4406808905125,71.5190149704363,71.59734905036012,71.67568313028391,71.75401721020772,71.83235129013154,71.91068537005535,71.98901944997915,72.06735352990296,72.14568760982677,72.22402168975057,72.30235576967438,72.3806898495982,72.459023929522,72.5373580094458,72.6156920893696,72.69402616929342,72.77236024921723,72.85069432914104,72.92902840906484,73.00736248898865,73.08569656891245,73.16403064883626,73.24236472876008,73.32069880868389,73.39903288860769,73.47736696853148,73.5557010484553,73.63403512837911,73.71236920830292,73.79070328822672,73.86903736815054,73.94737144807434,74.02570552799814,74.10403960792196,74.18237368784577,74.26070776776957,74.33904184769338,74.41737592761719,74.49571000754099,74.5740440874648,74.6523781673886,74.73071224731243,74.80904632723622,74.88738040716002,74.96571448708384,75.04404856700765,75.12238264693146,75.20071672685526,75.27905080677907,75.35738488670287,75.43571896662668,75.51405304655049,75.5923871264743,75.67072120639811,75.7490552863219,75.82738936624573,75.90572344616953,75.98405752609334,76.06239160601714,76.14072568594096,76.21905976586476,76.29739384578856,76.37572792571237,76.45406200563619,76.53239608556,76.61073016548379,76.68906424540761,76.76739832533141,76.84573240525522,76.92406648517903,77.00240056510285,77.08073464502664,77.15906872495044,77.23740280487426,77.31573688479807,77.39407096472188,77.47240504464568,77.55073912456949,77.6290732044933,77.7074072844171,77.78574136434091,77.86407544426473,77.94240952418853,78.02074360411233,78.09907768403615,78.17741176395995,78.25574584388376,78.33407992380756,78.41241400373137,78.49074808365518,78.56908216357898,78.64741624350279,78.72575032342661,78.80408440335042,78.88241848327421,78.96075256319803,79.03908664312183,79.11742072304564,79.19575480296945,79.27408888289327,79.35242296281706,79.43075704274086,79.50909112266467,79.58742520258849,79.6657592825123,79.7440933624361,79.82242744235991,79.90076152228372,79.97909560220752,80.05742968213133,80.13576376205515,80.21409784197895,80.29243192190275,80.37076600182655,80.44910008175037,80.52743416167418,80.60576824159799,80.68410232152179,80.7624364014456,80.8407704813694,80.91910456129321,80.99743864121703,81.07577272114084,81.15410680106463,81.23244088098845,81.31077496091225,81.38910904083606,81.46744312075987,81.54577720068367,81.62411128060748,81.70244536053129,81.78077944045509,81.85911352037891,81.93744760030272,82.01578168022652,82.09411576015033,82.17244984007414,82.25078391999794,82.32911799992175,82.40745207984556,82.48578615976936,82.56412023969317,82.64245431961697,82.7207883995408,82.7991224794646,82.8774565593884,82.95579063931221,83.03412471923602,83.11245879915982,83.19079287908363,83.26912695900744,83.34746103893126,83.42579511885505,83.50412919877886,83.58246327870268,83.66079735862648,83.73913143855029,83.8174655184741,83.8957995983979,83.9741336783217,84.05246775824551,84.13080183816933,84.20913591809314,84.28746999801693,84.36580407794074,84.44413815786456,84.52247223778836,84.60080631771217,84.67914039763598,84.75747447755978,84.83580855748359,84.9141426374074,84.99247671733121,85.07081079725502,85.14914487717883,85.22747895710262,85.30581303702644,85.38414711695025,85.46248119687405,85.54081527679786,85.61914935672168,85.69748343664547,85.77581751656928,85.8541515964931,85.9324856764169,86.01081975634071,86.08915383626452,86.16748791618832,86.24582199611213,86.32415607603593,86.40249015595974,86.48082423588356,86.55915831580735,86.63749239573116,86.71582647565498,86.79416055557878,86.87249463550259,86.9508287154264,87.0291627953502,87.10749687527401,87.18583095519782,87.26416503512162,87.34249911504544,87.42083319496925,87.49916727489304,87.57750135481686,87.65583543474067,87.73416951466447,87.81250359458828,87.8908376745121,87.96917175443589,88.0475058343597,88.12583991428352,88.20417399420732,88.28250807413113,88.36084215405492,88.43917623397874,88.51751031390255,88.59584439382635,88.67417847375016,88.75251255367398,88.83084663359777,88.90918071352158,88.9875147934454,89.0658488733692,89.14418295329301,89.22251703321682,89.30085111314062,89.37918519306443,89.45751927298824,89.53585335291204,89.61418743283586,89.69252151275967,89.77085559268346,89.84918967260728,89.92752375253109,90.0058578324549,90.0841919123787,90.1625259923025,90.24086007222631,90.31919415215012,90.39752823207392,90.47586231199774,90.55419639192155,90.63253047184534,90.71086455176916,90.78919863169297,90.86753271161678,90.94586679154058,91.0242008714644,91.1025349513882,91.180869031312,91.2592031112358,91.33753719115963,91.41587127108343,91.49420535100724,91.57253943093104,91.65087351085485,91.72920759077866,91.80754167070246,91.88587575062628,91.96420983055009,92.04254391047388,92.12087799039769,92.19921207032151,92.27754615024531,92.35588023016912,92.43421431009293,92.51254839001673,92.59088246994054,92.66921654986434,92.74755062978817,92.82588470971197,92.90421878963576,92.98255286955958,93.06088694948339,93.1392210294072,93.217555109331,93.29588918925481,93.37422326917861,93.45255734910242,93.53089142902623,93.60922550895005,93.68755958887385,93.76589366879766,93.84422774872147,93.92256182864527,94.00089590856908],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179],"y":[0,0.07518945287747485,0.1503789057549497,0.22556835863242455,0.3007578115098994,0.3759472643873742,0.4511367172648491,0.526326170142324,0.6015156230197988,0.6767050758972736,0.7518945287747484,0.8270839816522233,0.9022734345296982,0.9774628874071729,1.052652340284648,1.1278417931621225,1.2030312460395975,1.2782206989170724,1.3534101517945472,1.428599604672022,1.5037890575494968,1.5789785104269718,1.6541679633044466,1.7293574161819214,1.8045468690593964,1.879736321936871,1.9549257748143458,2.030115227691821,2.105304680569296,2.1804941334467705,2.255683586324245,2.33087303920172,2.406062492079195,2.4812519449566697,2.5564413978341447,2.6316308507116197,2.7068203035890943,2.7820097564665693,2.857199209344044,2.9323886622215185,3.0075781150989935,3.0827675679764686,3.1579570208539436,3.233146473731418,3.308335926608893,3.3835253794863682,3.458714832363843,3.533904285241318,3.609093738118793,3.684283190996267,3.759472643873742,3.834662096751217,3.9098515496286916,3.9850410025061667,4.060230455383642,4.135419908261117,4.210609361138592,4.285798814016066,4.360988266893541,4.436177719771016,4.51136717264849,4.586556625525965,4.66174607840344,4.736935531280915,4.81212498415839,4.887314437035865,4.962503889913339,5.037693342790814,5.112882795668289,5.188072248545764,5.2632617014232395,5.338451154300714,5.413640607178189,5.488830060055664,5.564019512933139,5.639208965810613,5.714398418688088,5.789587871565563,5.864777324443037,5.939966777320513,6.015156230197987,6.090345683075463,6.165535135952937,6.240724588830412,6.315914041707887,6.391103494585362,6.466292947462836,6.5414824003403105,6.616671853217786,6.6918613060952605,6.7670507589727364,6.842240211850211,6.917429664727686,6.992619117605161,7.067808570482636,7.14299802336011,7.218187476237586,7.29337692911506,7.368566381992534,7.44375583487001,7.518945287747484,7.594134740624959,7.669324193502434,7.744513646379909,7.819703099257383,7.894892552134859,7.970082005012333,8.04527145788981,8.120460910767283,8.195650363644758,8.270839816522233,8.346029269399708,8.421218722277183,8.496408175154658,8.571597628032132,8.646787080909608,8.721976533787082,8.797165986664556,8.872355439542032,8.947544892419506,9.02273434529698,9.097923798174456,9.17311325105193,9.248302703929406,9.32349215680688,9.398681609684356,9.47387106256183,9.549060515439306,9.62424996831678,9.699439421194256,9.77462887407173,9.849818326949205,9.925007779826679,10.000197232704155,10.075386685581629,10.150576138459103,10.225765591336579,10.300955044214053,10.376144497091529,10.451333949969003,10.526523402846479,10.601712855723953,10.676902308601427,10.752091761478903,10.827281214356377,10.902470667233853,10.977660120111327,11.052849572988803,11.128039025866277,11.203228478743751,11.278417931621226,11.353607384498702,11.428796837376176,11.50398629025365,11.579175743131126,11.654365196008602,11.729554648886074,11.80474410176355,11.879933554641026,11.955123007518502,12.030312460395974,12.10550191327345,12.180691366150926,12.255880819028398,12.331070271905874,12.40625972478335,12.481449177660824,12.556638630538298,12.631828083415774,12.707017536293248,12.782206989170724,12.857396442048197,12.932585894925673,13.007775347803149,13.082964800680621,13.158154253558097,13.233343706435573,13.308533159313049,13.383722612190521,13.458912065067997,13.534101517945473,13.609290970822947,13.684480423700421,13.759669876577897,13.834859329455371,13.910048782332845,13.985238235210321,14.060427688087795,14.135617140965271,14.210806593842744,14.28599604672022,14.361185499597696,14.436374952475171,14.511564405352644,14.58675385823012,14.661943311107596,14.737132763985068,14.812322216862544,14.88751166974002,14.962701122617494,15.037890575494968,15.113080028372444,15.188269481249918,15.263458934127394,15.338648387004868,15.413837839882342,15.489027292759818,15.564216745637292,15.639406198514767,15.714595651392242,15.789785104269718,15.86497455714719,15.940164010024667,16.01535346290214,16.09054291577962,16.16573236865709,16.240921821534567,16.31611127441204,16.391300727289515,16.46649018016699,16.541679633044467,16.61686908592194,16.692058538799415,16.76724799167689,16.842437444554367,16.91762689743184,16.992816350309315,17.06800580318679,17.143195256064264,17.218384708941738,17.293574161819215,17.36876361469669,17.443953067574164,17.519142520451638,17.594331973329112,17.66952142620659,17.744710879084064,17.819900331961538,17.895089784839012,17.97027923771649,18.04546869059396,18.120658143471438,18.195847596348912,18.271037049226386,18.34622650210386,18.421415954981338,18.496605407858812,18.571794860736286,18.64698431361376,18.722173766491235,18.797363219368712,18.872552672246186,18.94774212512366,19.022931578001135,19.098121030878612,19.173310483756083,19.24849993663356,19.323689389511035,19.398878842388513,19.474068295265983,19.54925774814346,19.624447201020935,19.69963665389841,19.774826106775883,19.850015559653357,19.925205012530835,20.00039446540831,20.075583918285783,20.150773371163258,20.225962824040735,20.301152276918206,20.376341729795683,20.451531182673158,20.52672063555063,20.601910088428106,20.677099541305584,20.752288994183058,20.827478447060532,20.902667899938006,20.977857352815484,21.053046805692958,21.128236258570432,21.203425711447906,21.27861516432538,21.353804617202854,21.42899407008033,21.504183522957806,21.57937297583528,21.654562428712754,21.72975188159023,21.804941334467706,21.88013078734518,21.955320240222655,22.03050969310013,22.105699145977606,22.18088859885508,22.256078051732555,22.331267504610025,22.406456957487503,22.481646410364977,22.55683586324245,22.63202531611993,22.707214768997403,22.78240422187488,22.85759367475235,22.932783127629826,23.0079725805073,23.083162033384777,23.15835148626225,23.23354093913973,23.308730392017203,23.383919844894677,23.459109297772148,23.534298750649626,23.6094882035271,23.684677656404578,23.75986710928205,23.835056562159526,23.910246015037004,23.985435467914474,24.06062492079195,24.135814373669422,24.2110038265469,24.286193279424374,24.361382732301852,24.436572185179326,24.511761638056797,24.58695109093427,24.66214054381175,24.737329996689223,24.8125194495667,24.887708902444174,24.96289835532165,25.038087808199126,25.113277261076597,25.18846671395407,25.26365616683155,25.338845619709023,25.414035072586497,25.489224525463975,25.56441397834145,25.63960343121892,25.714792884096394,25.78998233697387,25.865171789851345,25.940361242728823,26.015550695606297,26.09074014848377,26.165929601361242,26.24111905423872,26.316308507116194,26.39149795999367,26.466687412871146,26.54187686574862,26.617066318626097,26.69225577150357,26.767445224381042,26.84263467725852,26.917824130135994,26.993013583013468,27.068203035890946,27.14339248876842,27.218581941645894,27.293771394523365,27.368960847400842,27.444150300278316,27.519339753155794,27.59452920603327,27.669718658910742,27.74490811178822,27.82009756466569,27.895287017543165,27.970476470420643,28.045665923298117,28.12085537617559,28.19604482905307,28.271234281930543,28.346423734808017,28.421613187685487,28.496802640562965,28.57199209344044,28.647181546317917,28.72237099919539,28.797560452072865,28.872749904950343,28.947939357827813,29.023128810705288,29.098318263582765,29.17350771646024,29.248697169337714,29.32388662221519,29.399076075092665,29.474265527970136,29.549454980847614,29.624644433725088,29.699833886602562,29.77502333948004,29.850212792357514,29.925402245234988,30.000591698112466,30.075781150989936,30.15097060386741,30.226160056744888,30.301349509622362,30.376538962499836,30.451728415377314,30.526917868254788,30.60210732113226,30.677296774009736,30.75248622688721,30.827675679764685,30.902865132642162,30.978054585519637,31.053244038397114,31.128433491274585,31.20362294415206,31.278812397029533,31.35400184990701,31.429191302784485,31.50438075566196,31.579570208539437,31.65475966141691,31.72994911429438,31.80513856717186,31.880328020049333,31.955517472926807,32.03070692580428,32.10589637868176,32.18108583155924,32.256275284436704,32.33146473731418,32.40665419019166,32.48184364306913,32.55703309594661,32.63222254882408,32.707412001701556,32.78260145457903,32.857790907456504,32.93298036033398,33.00816981321146,33.083359266088934,33.15854871896641,33.23373817184388,33.308927624721356,33.38411707759883,33.459306530476304,33.53449598335378,33.60968543623125,33.684874889108734,33.76006434198621,33.83525379486368,33.91044324774115,33.98563270061863,34.060822153496105,34.13601160637358,34.21120105925105,34.28639051212853,34.36157996500601,34.436769417883475,34.51195887076095,34.58714832363843,34.662337776515905,34.73752722939338,34.81271668227085,34.88790613514833,34.9630955880258,35.038285040903276,35.11347449378075,35.188663946658224,35.263853399535705,35.33904285241318,35.41423230529065,35.48942175816813,35.5646112110456,35.639800663923076,35.71499011680055,35.790179569678024,35.8653690225555,35.94055847543298,36.015747928310454,36.09093738118792,36.1661268340654,36.241316286942876,36.31650573982035,36.391695192697824,36.4668846455753,36.54207409845277,36.61726355133025,36.69245300420772,36.767642457085195,36.842831909962676,36.91802136284015,36.993210815717624,37.0684002685951,37.14358972147257,37.21877917435005,37.29396862722752,37.369158080104995,37.44434753298247,37.51953698585995,37.594726438737425,37.6699158916149,37.74510534449237,37.82029479736985,37.89548425024732,37.970673703124795,38.04586315600227,38.121052608879744,38.196242061757225,38.27143151463469,38.346620967512166,38.42181042038965,38.49699987326712,38.572189326144596,38.64737877902207,38.722568231899544,38.797757684777025,38.87294713765449,38.948136590531966,39.02332604340944,39.09851549628692,39.173704949164396,39.24889440204187,39.324083854919344,39.39927330779682,39.47446276067429,39.54965221355177,39.62484166642924,39.700031119306715,39.775220572184196,39.85041002506167,39.92559947793914,40.00078893081662,40.07597838369409,40.15116783657157,40.22635728944904,40.301546742326515,40.376736195203996,40.45192564808147,40.52711510095894,40.60230455383641,40.67749400671389,40.75268345959137,40.82787291246884,40.903062365346315,40.97825181822379,41.05344127110126,41.12863072397874,41.20382017685621,41.279009629733686,41.35419908261117,41.42938853548864,41.504577988366115,41.57976744124359,41.654956894121064,41.73014634699854,41.80533579987601,41.880525252753486,41.95571470563097,42.03090415850844,42.106093611385916,42.18128306426338,42.256472517140864,42.33166197001834,42.40685142289581,42.482040875773286,42.55723032865076,42.63241978152824,42.70760923440571,42.78279868728318,42.85798814016066,42.93317759303814,43.00836704591561,43.08355649879309,43.15874595167056,43.233935404548035,43.30912485742551,43.38431431030298,43.45950376318046,43.53469321605794,43.60988266893541,43.68507212181289,43.76026157469036,43.835451027567835,43.91064048044531,43.98582993332278,44.06101938620026,44.13620883907773,44.21139829195521,44.28658774483269,44.36177719771016,44.43696665058763,44.51215610346511,44.587345556342584,44.66253500922005,44.73772446209753,44.812913914975006,44.88810336785249,44.963292820729954,45.038482273607436,45.1136717264849,45.18886117936238,45.26405063223986,45.339240085117325,45.414429537994806,45.48961899087228,45.56480844374976,45.63999789662723,45.7151873495047,45.790376802382184,45.86556625525965,45.94075570813713,46.0159451610146,46.09113461389208,46.166324066769555,46.241513519647036,46.3167029725245,46.39189242540198,46.46708187827946,46.542271331156925,46.61746078403441,46.692650236911874,46.767839689789355,46.84302914266683,46.918218595544296,46.99340804842178,47.06859750129925,47.14378695417673,47.2189764070542,47.29416585993168,47.369355312809155,47.44454476568662,47.5197342185641,47.59492367144157,47.67011312431905,47.745302577196526,47.82049203007401,47.895681482951474,47.97087093582895,48.04606038870643,48.1212498415839,48.19643929446138,48.271628747338845,48.346818200216326,48.4220076530938,48.49719710597127,48.57238655884875,48.64757601172622,48.722765464603704,48.79795491748117,48.87314437035865,48.948333823236126,49.02352327611359,49.098712728991075,49.17390218186854,49.24909163474602,49.3242810876235,49.39947054050098,49.474659993378445,49.54984944625593,49.6250388991334,49.70022835201087,49.77541780488835,49.850607257765816,49.9257967106433,50.00098616352077,50.07617561639825,50.15136506927572,50.226554522153194,50.301743975030675,50.37693342790814,50.45212288078562,50.5273123336631,50.60250178654057,50.677691239418046,50.75288069229551,50.828070145172994,50.90325959805047,50.97844905092795,51.053638503805416,51.1288279566829,51.20401740956037,51.27920686243784,51.35439631531532,51.42958576819279,51.50477522107027,51.57996467394774,51.655154126825224,51.73034357970269,51.805533032580165,51.880722485457646,51.95591193833511,52.031101391212594,52.10629084409007,52.18148029696754,52.25666974984502,52.331859202722484,52.407048655599965,52.48223810847744,52.55742756135492,52.63261701423239,52.70780646710987,52.78299591998734,52.85818537286482,52.93337482574229,53.00856427861976,53.08375373149724,53.158943184374714,53.234132637252195,53.30932209012966,53.38451154300714,53.45970099588462,53.534890448762084,53.610079901639565,53.68526935451704,53.760458807394514,53.83564826027199,53.91083771314947,53.986027166026936,54.06121661890441,54.13640607178189,54.21159552465936,54.28678497753684,54.361974430414314,54.43716388329179,54.51235333616926,54.58754278904673,54.66273224192421,54.737921694801685,54.813111147679166,54.88830060055663,54.963490053434114,55.03867950631159,55.113868959189055,55.18905841206654,55.26424786494401,55.339437317821485,55.41462677069896,55.48981622357644,55.56500567645391,55.64019512933138,55.71538458220886,55.79057403508633,55.86576348796381,55.940952940841285,56.01614239371876,56.09133184659623,56.1665212994737,56.24171075235118,56.316900205228656,56.39208965810614,56.467279110983604,56.542468563861085,56.61765801673856,56.692847469616034,56.76803692249351,56.843226375370975,56.918415828248456,56.99360528112593,57.06879473400341,57.14398418688088,57.21917363975836,57.294363092635834,57.3695525455133,57.44474199839078,57.519931451268256,57.59512090414573,57.670310357023205,57.745499809900686,57.82068926277815,57.89587871565563,57.97106816853311,58.046257621410575,58.12144707428806,58.19663652716553,58.271825980043005,58.34701543292048,58.422204885797946,58.49739433867543,58.5725837915529,58.64777324443038,58.72296269730785,58.79815215018533,58.873341603062805,58.94853105594027,59.02372050881775,59.09890996169523,59.1740994145727,59.249288867450176,59.32447832032766,59.399667773205124,59.4748572260826,59.55004667896008,59.625236131837546,59.70042558471503,59.7756150375925,59.850804490469976,59.92599394334745,60.00118339622493,60.0763728491024,60.15156230197987,60.226751754857354,60.30194120773482,60.3771306606123,60.452320113489776,60.52750956636726,60.602699019244724,60.6778884721222,60.75307792499967,60.82826737787715,60.90345683075463,60.978646283632095,61.053835736509576,61.12902518938705,61.20421464226452,61.279404095142,61.35459354801947,61.42978300089695,61.50497245377442,61.5801619066519,61.65535135952937,61.73054081240684,61.805730265284325,61.88091971816179,61.95610917103927,62.03129862391675,62.10648807679423,62.181677529671695,62.25686698254917,62.332056435426644,62.40724588830412,62.4824353411816,62.557624794059066,62.63281424693655,62.70800369981402,62.78319315269149,62.85838260556897,62.933572058446444,63.00876151132392,63.08395096420139,63.15914041707887,63.23432986995634,63.30951932283382,63.384708775711296,63.45989822858876,63.535087681466244,63.61027713434372,63.6854665872212,63.76065604009867,63.83584549297615,63.911034945853615,63.98622439873109,64.06141385160856,64.13660330448604,64.21179275736353,64.28698221024099,64.36217166311847,64.43736111599594,64.51255056887341,64.58774002175089,64.66292947462836,64.73811892750584,64.81330838038332,64.8884978332608,64.96368728613827,65.03887673901573,65.11406619189322,65.18925564477068,65.26444509764816,65.33963455052564,65.41482400340311,65.4900134562806,65.56520290915806,65.64039236203554,65.71558181491301,65.79077126779049,65.86596072066796,65.94115017354544,66.01633962642292,66.09152907930039,66.16671853217787,66.24190798505533,66.31709743793282,66.39228689081028,66.46747634368776,66.54266579656523,66.61785524944271,66.6930447023202,66.76823415519766,66.84342360807514,66.91861306095261,66.99380251383009,67.06899196670756,67.14418141958504,67.2193708724625,67.29456032533999,67.36974977821747,67.44493923109493,67.52012868397242,67.59531813684988,67.67050758972736,67.74569704260483,67.8208864954823,67.89607594835978,67.97126540123726,68.04645485411474,68.12164430699221,68.19683375986969,68.27202321274716,68.34721266562462,68.4224021185021,68.49759157137957,68.57278102425705,68.64797047713454,68.72315993001202,68.79834938288948,68.87353883576695,68.94872828864443,69.0239177415219,69.09910719439938,69.17429664727686,69.24948610015433,69.32467555303181,69.39986500590928,69.47505445878676,69.55024391166423,69.6254333645417,69.70062281741917,69.77581227029665,69.85100172317414,69.9261911760516,70.00138062892908,70.07657008180655,70.15175953468403,70.2269489875615,70.30213844043898,70.37732789331645,70.45251734619393,70.52770679907141,70.60289625194888,70.67808570482636,70.75327515770383,70.8284646105813,70.90365406345877,70.97884351633625,71.05403296921372,71.1292224220912,71.20441187496868,71.27960132784615,71.35479078072363,71.4299802336011,71.50516968647858,71.58035913935605,71.65554859223352,71.730738045111,71.80592749798848,71.88111695086596,71.95630640374343,72.03149585662091,72.10668530949837,72.18187476237584,72.25706421525332,72.3322536681308,72.40744312100827,72.48263257388575,72.55782202676323,72.6330114796407,72.70820093251817,72.78339038539565,72.85857983827312,72.9337692911506,73.00895874402808,73.08414819690555,73.15933764978303,73.2345271026605,73.30971655553797,73.38490600841544,73.46009546129292,73.53528491417039,73.61047436704787,73.68566381992535,73.76085327280283,73.8360427256803,73.91123217855777,73.98642163143525,74.06161108431272,74.1368005371902,74.21198999006766,74.28717944294515,74.36236889582263,74.4375583487001,74.51274780157758,74.58793725445504,74.66312670733252,74.73831616020999,74.81350561308747,74.88869506596494,74.96388451884242,75.0390739717199,75.11426342459737,75.18945287747485,75.26464233035232,75.3398317832298,75.41502123610726,75.49021068898475,75.56540014186221,75.6405895947397,75.71577904761718,75.79096850049464,75.86615795337212,75.94134740624959,76.01653685912706,76.09172631200454,76.16691576488202,76.24210521775949,76.31729467063697,76.39248412351445,76.46767357639192,76.54286302926938,76.61805248214687,76.69324193502433,76.76843138790181,76.8436208407793,76.91881029365678,76.99399974653424,77.06918919941172,77.14437865228919,77.21956810516666,77.29475755804414,77.3699470109216,77.44513646379909,77.52032591667657,77.59551536955405,77.67070482243152,77.74589427530898,77.82108372818647,77.89627318106393,77.97146263394141,78.04665208681888,78.12184153969636,78.19703099257384,78.27222044545131,78.34740989832879,78.42259935120626,78.49778880408374,78.5729782569612,78.64816770983869,78.72335716271616,78.79854661559364,78.87373606847112,78.94892552134858,79.02411497422607,79.09930442710353,79.17449387998101,79.24968333285848,79.32487278573596,79.40006223861343,79.47525169149091,79.55044114436839,79.62563059724586,79.70082005012334,79.77600950300081,79.85119895587827,79.92638840875576,80.00157786163324,80.0767673145107,80.15195676738819,80.22714622026567,80.30233567314313,80.37752512602061,80.45271457889808,80.52790403177555,80.60309348465303,80.67828293753051,80.75347239040799,80.82866184328546,80.90385129616294,80.97904074904041,81.05423020191787,81.12941965479536,81.20460910767282,81.2797985605503,81.35498801342779,81.43017746630527,81.50536691918273,81.5805563720602,81.65574582493768,81.73093527781515,81.80612473069263,81.8813141835701,81.95650363644758,82.03169308932506,82.10688254220253,82.18207199508001,82.25726144795748,82.33245090083496,82.40764035371242,82.4828298065899,82.55801925946737,82.63320871234485,82.70839816522233,82.7835876180998,82.85877707097728,82.93396652385475,83.00915597673223,83.0843454296097,83.15953488248718,83.23472433536465,83.30991378824213,83.38510324111961,83.46029269399708,83.53548214687456,83.61067159975202,83.6858610526295,83.76105050550697,83.83623995838445,83.91142941126193,83.9866188641394,84.06180831701688,84.13699776989435,84.21218722277183,84.2873766756493,84.36256612852677,84.43775558140425,84.51294503428173,84.58813448715921,84.66332394003668,84.73851339291416,84.81370284579162,84.88889229866909,84.96408175154657,85.03927120442404,85.11446065730152,85.189650110179,85.26483956305648,85.34002901593395,85.41521846881142,85.4904079216889,85.56559737456637,85.64078682744385,85.71597628032131,85.7911657331988,85.86635518607628,85.94154463895374,86.01673409183122,86.09192354470869,86.16711299758617,86.24230245046364,86.31749190334112,86.39268135621859,86.46787080909607,86.54306026197355,86.61824971485102,86.6934391677285,86.76862862060597,86.84381807348345,86.91900752636091,86.9941969792384,87.06938643211588,87.14457588499334,87.21976533787083,87.29495479074829,87.37014424362577,87.44533369650324,87.52052314938072,87.59571260225819,87.67090205513567,87.74609150801315,87.82128096089062,87.8964704137681,87.97165986664557,88.04684931952305,88.12203877240051,88.19722822527798,88.27241767815546,88.34760713103294,88.42279658391043,88.49798603678789,88.57317548966537,88.64836494254284],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145],"y":[0,0.31593916975419833,0.6318783395083967,0.947817509262595,1.2637566790167933,1.5796958487709916,1.89563501852519,2.2115741882793882,2.5275133580335867,2.8434525277877847,3.159391697541983,3.4753308672961816,3.79127003705038,4.107209206804578,4.4231483765587765,4.739087546312975,5.055026716067173,5.370965885821372,5.686905055575569,6.002844225329769,6.318783395083966,6.634722564838166,6.950661734592363,7.266600904346562,7.58254007410076,7.8984792438549585,8.214418413609156,8.530357583363354,8.846296753117553,9.162235922871751,9.47817509262595,9.794114262380148,10.110053432134347,10.425992601888545,10.741931771642744,11.057870941396942,11.373810111151139,11.689749280905339,12.005688450659537,12.321627620413734,12.637566790167932,12.953505959922131,13.269445129676331,13.585384299430528,13.901323469184726,14.217262638938925,14.533201808693123,14.84914097844732,15.16508014820152,15.481019317955719,15.796958487709917,16.112897657464114,16.428836827218312,16.74477599697251,17.06071516672671,17.376654336480907,17.692593506235106,18.008532675989304,18.324471845743503,18.6404110154977,18.9563501852519,19.272289355006098,19.588228524760297,19.904167694514495,20.220106864268693,20.536046034022892,20.85198520377709,21.167924373531285,21.483863543285487,21.799802713039686,22.115741882793884,22.43168105254808,22.747620222302277,23.06355939205648,23.379498561810678,23.695437731564876,24.011376901319075,24.32731607107327,24.643255240827468,24.959194410581667,25.275133580335865,25.591072750090063,25.907011919844262,26.222951089598464,26.538890259352662,26.854829429106854,27.170768598861056,27.486707768615254,27.802646938369453,28.11858610812365,28.43452527787785,28.750464447632048,29.066403617386246,29.38234278714044,29.69828195689464,30.014221126648838,30.33016029640304,30.64609946615724,30.962038635911437,31.277977805665635,31.593916975419834,31.90985614517403,32.22579531492823,32.54173448468243,32.857673654436624,33.173612824190826,33.48955199394502,33.80549116369922,34.12143033345342,34.43736950320761,34.753308672961815,35.06924784271601,35.38518701247021,35.701126182224414,36.01706535197861,36.33300452173281,36.648943691487005,36.9648828612412,37.2808220309954,37.5967612007496,37.9127003705038,38.228639540257994,38.544578710012196,38.8605178797664,39.17645704952059,39.49239621927479,39.80833538902899,40.124274558783185,40.44021372853739,40.75615289829158,41.072092068045784,41.38803123779998,41.70397040755418,42.019909577308376,42.33584874706257,42.65178791681677,42.967727086570974,43.28366625632517,43.59960542607937,43.915544595833566,44.23148376558777,44.54742293534196,44.86336210509616,45.17930127485036,45.495240444604555,45.81117961435876],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126],"y":[0,0.1195009558582743,0.2390019117165486,0.3585028675748229,0.4780038234330972,0.5975047792913716,0.7170057351496458,0.8365066910079202,0.9560076468661944,1.0755086027244687,1.1950095585827432,1.3145105144410174,1.4340114702992917,1.553512426157566,1.6730133820158404,1.7925143378741146,1.9120152937323889,2.031516249590663,2.1510172054489374,2.2705181613072116,2.3900191171654863,2.5095200730237606,2.629021028882035,2.748521984740309,2.8680229405985833,2.9875238964568576,3.107024852315132,3.226525808173406,3.3460267640316808,3.465527719889955,3.5850286757482293,3.7045296316065035,3.8240305874647778,3.9435315433230524,4.063032499181326,4.182533455039601,4.302034410897875,4.421535366756149,4.541036322614423,4.660537278472698,4.780038234330973,4.899539190189247,5.019040146047521,5.138541101905796,5.25804205776407,5.377543013622344,5.497043969480618,5.616544925338893,5.736045881197167,5.855546837055441,5.975047792913715,6.09454874877199,6.214049704630264,6.333550660488538,6.453051616346812,6.572552572205087,6.6920535280633615,6.811554483921636,6.93105543977991,7.050556395638185,7.1700573514964585,7.289558307354733,7.409059263213007,7.528560219071282,7.6480611749295555,7.767562130787831,7.887063086646105,8.00656404250438,8.126064998362653,8.245565954220929,8.365066910079202,8.484567865937477,8.60406882179575,8.723569777654026,8.843070733512299,8.962571689370574,9.082072645228846,9.201573601087123,9.321074556945396,9.44057551280367,9.560076468661945,9.67957742452022,9.799078380378495,9.918579336236768,10.038080292095042,10.157581247953317,10.277082203811592,10.396583159669865,10.51608411552814,10.635585071386414,10.755086027244689,10.874586983102962,10.994087938961236,11.113588894819511,11.233089850677786,11.35259080653606,11.472091762394333,11.59159271825261,11.711093674110883,11.830594629969157,11.95009558582743,12.069596541685707,12.18909749754398,12.308598453402254,12.428099409260527,12.547600365118804,12.667101320977077,12.786602276835351,12.906103232693624,13.0256041885519,13.145105144410174,13.264606100268448,13.384107056126723,13.503608011984998,13.623108967843272,13.742609923701545,13.86211087955982,13.981611835418095,14.10111279127637,14.220613747134642,14.340114702992917,14.459615658851192,14.579116614709466,14.69861757056774,14.818118526426014,14.937619482284289,15.057120438142563],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"AI Rule 8","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82],"y":[0,0.21746851933469874,0.4349370386693975,0.6524055580040962,0.869874077338795,1.0873425966734938,1.3048111160081923,1.5222796353428913,1.73974815467759,1.9572166740122887,2.1746851933469875,2.392153712681686,2.6096222320163847,2.8270907513510837,3.0445592706857827,3.262027790020481,3.47949630935518,3.6969648286898784,3.9144333480245774,4.131901867359276,4.349370386693975,4.566838906028673,4.784307425363372,5.001775944698071,5.219244464032769,5.436712983367468,5.654181502702167,5.871650022036866,6.089118541371565,6.306587060706263,6.524055580040962,6.741524099375661,6.95899261871036,7.176461138045059,7.393929657379757,7.611398176714456,7.828866696049155,8.046335215383854,8.263803734718552,8.48127225405325,8.69874077338795,8.916209292722648,9.133677812057346,9.351146331392046,9.568614850726744,9.786083370061444,10.003551889396142,10.22102040873084,10.438488928065539,10.655957447400237,10.873425966734937,11.090894486069635,11.308363005404335,11.525831524739033,11.743300044073733,11.96076856340843,12.17823708274313,12.395705602077827,12.613174121412525,12.830642640747225,13.048111160081923,13.265579679416623,13.483048198751321,13.700516718086021,13.91798523742072,14.13545375675542,14.352922276090117,14.570390795424814,14.787859314759514,15.005327834094212,15.222796353428912,15.44026487276361,15.65773339209831,15.875201911433008,16.092670430767708,16.310138950102406,16.527607469437104,16.745075988771802,16.9625445081065,17.1800130274412,17.3974815467759,17.614950066110598,17.832418585445296],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Ext Rule 6: Fibonacci Support Confluence</td>
                <td>UNKNOWN</td>
                <td class="positive">94.00%</td>
                <td>64.4%</td>
                <td>1200</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>26.53%</td>
                <td>86.9</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">88.65%</td>
                <td>64.9%</td>
                <td>1179</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>15.62%</td>
                <td>84.9</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">45.81%</td>
                <td>69.0%</td>
                <td>145</td>
                <td>1.44</td>
                <td>0.00</td>
                <td>10.49%</td>
                <td>69.0</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">15.06%</td>
                <td>67.5%</td>
                <td>126</td>
                <td>1.15</td>
                <td>0.00</td>
                <td>11.64%</td>
                <td>56.3</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">17.83%</td>
                <td>68.3%</td>
                <td>82</td>
                <td>1.27</td>
                <td>0.00</td>
                <td>7.82%</td>
                <td>52.2</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">8.06%</td>
                <td>62.8%</td>
                <td>965</td>
                <td>1.01</td>
                <td>0.00</td>
                <td>45.55%</td>
                <td>52.1</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                <td>UNKNOWN</td>
                <td class="positive">17.54%</td>
                <td>79.2%</td>
                <td>24</td>
                <td>2.40</td>
                <td>0.00</td>
                <td>3.67%</td>
                <td>38.0</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">11.67%</td>
                <td>81.2%</td>
                <td>16</td>
                <td>2.61</td>
                <td>0.00</td>
                <td>2.76%</td>
                <td>33.8</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">4.13%</td>
                <td>68.2%</td>
                <td>22</td>
                <td>1.23</td>
                <td>0.00</td>
                <td>4.63%</td>
                <td>28.7</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">1.78%</td>
                <td>70.0%</td>
                <td>20</td>
                <td>1.11</td>
                <td>0.00</td>
                <td>11.87%</td>
                <td>27.7</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 812,700<br>
            • Backtest Range: 300 to 813,000<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
