
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 16 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-01 23:29:24</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">16</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">36.4%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">8.9%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">28.8%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">64.0%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">4,543</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["28.8%","27.1%","22.1%","14.0%","6.3%","7.2%","5.2%","5.3%","5.3%","5.3%"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","AI Rule 3: Smart Money Flow Divergence","Prof Rule 7: Mean Reversion Volatility Filter","Rule 10: Volume Spike","Rule 22: Higher High Pattern","Volume Rule 4: Volume Breakout Confirmation","AI Rule 8: Momentum Divergence Reversal","Rule 28: Volume Breakout","Ext Rule 6: Fibonacci Support Confluence"],"y":[28.810043675599445,27.060049888983407,22.05367599965613,14.03716411243267,6.283046889535624,7.160221089116726,5.193660046021658,5.264249739371757,5.3396881381349015,5.318697847093063],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","AI Rule 3: Smart Money Flow Divergence","Prof Rule 7: Mean Reversion Volatility Filter","Rule 10: Volume Spike","Rule 22: Higher High Pattern","Volume Rule 4: Volume Breakout Confirmation","AI Rule 8: Momentum Divergence Reversal","Rule 28: Volume Breakout","Ext Rule 6: Fibonacci Support Confluence"],"y":[584,405,312,154,212,212,74,73,92,386],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","AI Rule 3: Smart Money Flow Divergence","Prof Rule 7: Mean Reversion Volatility Filter","Rule 10: Volume Spike","Rule 22: Higher High Pattern","Volume Rule 4: Volume Breakout Confirmation","AI Rule 8: Momentum Divergence Reversal","Rule 28: Volume Breakout","Ext Rule 6: Fibonacci Support Confluence"],"y":[312,212,161,78,117,124,42,44,54,229],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[28.810043675599445,27.060049888983407,22.05367599965613,14.03716411243267,6.283046889535624,7.160221089116726,5.193660046021658,5.264249739371757,5.3396881381349015,5.318697847093063,4.7539528520763925,1.5874949282262023,2.02337174410226,1.7106206528251642,4.508886937175557,1.5219410734041083],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["AI Rule 10","Rule 7","AI Rule 3","Prof Rule 7","Rule 10","Rule 22","Volume Rule 4","AI Rule 8","Rule 28","Ext Rule 6","Volatility Rule 2","Professional Rule 7","Rule 3","Rule 11","Volume Rule 5","Professional Rule 10"],"textposition":"top center","x":[23.754105629922954,10.302293779448538,11.363149666495534,5.37180864851428,11.31649082644239,8.336539587776294,3.9798404551255335,7.333885741907897,7.375396858240284,15.566786241210167,5.759582459247839,4.6095150819708195,9.022726073022605,10.719516546005734,2.2195712724271766,3.1713154415171556],"y":[28.810043675599445,27.060049888983407,22.05367599965613,14.03716411243267,6.283046889535624,7.160221089116726,5.193660046021658,5.264249739371757,5.3396881381349015,5.318697847093063,4.7539528520763925,1.5874949282262023,2.02337174410226,1.7106206528251642,4.508886937175557,1.5219410734041083],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["18.7%","8.3%","14.0%","3.8%"],"textposition":"auto","x":["AI_GENERATED","ORIGINAL","PROFESSIONAL","UNKNOWN"],"y":[18.70932313820911,8.262833067116349,14.03716411243267,3.8141056139994967],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["896","617","473","232","329","336","116","117","146","615"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","AI Rule 3: Smart Money Flow Divergence","Prof Rule 7: Mean Reversion Volatility Filter","Rule 10: Volume Spike","Rule 22: Higher High Pattern","Volume Rule 4: Volume Breakout Confirmation","AI Rule 8: Momentum Divergence Reversal","Rule 28: Volume Breakout","Ext Rule 6: Fibonacci Support Confluence"],"y":[896,617,473,232,329,336,116,117,146,615],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896],"y":[0,0.032154066602231524,0.06430813320446305,0.09646219980669457,0.1286162664089261,0.16077033301115762,0.19292439961338914,0.22507846621562067,0.2572325328178522,0.2893865994200837,0.32154066602231524,0.35369473262454676,0.3858487992267783,0.4180028658290098,0.45015693243124133,0.48231099903347285,0.5144650656357044,0.5466191322379359,0.5787731988401674,0.610927265442399,0.6430813320446305,0.675235398646862,0.7073894652490935,0.739543531851325,0.7716975984535566,0.8038516650557881,0.8360057316580196,0.8681597982602511,0.9003138648624827,0.9324679314647143,0.9646219980669457,0.9967760646691773,1.0289301312714088,1.0610841978736403,1.0932382644758718,1.1253923310781033,1.1575463976803348,1.1897004642825664,1.221854530884798,1.2540085974870294,1.286162664089261,1.3183167306914925,1.350470797293724,1.3826248638959555,1.414778930498187,1.4469329971004186,1.47908706370265,1.5112411303048816,1.5433951969071131,1.5755492635093447,1.6077033301115762,1.6398573967138077,1.6720114633160392,1.7041655299182707,1.7363195965205023,1.7684736631227338,1.8006277297249653,1.8327817963271966,1.8649358629294286,1.89708992953166,1.9292439961338914,1.9613980627361227,1.9935521293383547,2.025706195940586,2.0578602625428175,2.090014329145049,2.1221683957472806,2.154322462349512,2.1864765289517436,2.218630595553975,2.2507846621562067,2.282938728758438,2.3150927953606697,2.347246861962901,2.3794009285651327,2.4115549951673643,2.443709061769596,2.4758631283718273,2.508017194974059,2.5401712615762904,2.572325328178522,2.6044793947807534,2.636633461382985,2.6687875279852165,2.700941594587448,2.7330956611896795,2.765249727791911,2.7974037943941426,2.829557860996374,2.8617119275986056,2.893865994200837,2.9260200608030686,2.9581741274053,2.9903281940075317,3.022482260609763,3.0546363272119947,3.0867903938142263,3.118944460416458,3.1510985270186893,3.183252593620921,3.2154066602231524,3.247560726825384,3.2797147934276154,3.311868860029847,3.3440229266320785,3.37617699323431,3.4083310598365415,3.440485126438773,3.4726391930410045,3.504793259643236,3.5369473262454676,3.569101392847699,3.6012554594499306,3.633409526052162,3.6655635926543932,3.6977176592566248,3.729871725858857,3.7620257924610887,3.79417985906332,3.8263339256655513,3.858487992267783,3.890642058870014,3.9227961254722454,3.954950192074478,3.9871042586767094,4.0192583252789404,4.051412391881172,4.0835664584834035,4.115720525085635,4.1478745916878665,4.180028658290098,4.21218272489233,4.244336791494561,4.276490858096793,4.308644924699024,4.340798991301256,4.372953057903487,4.405107124505719,4.43726119110795,4.469415257710182,4.501569324312413,4.533723390914645,4.565877457516876,4.598031524119108,4.630185590721339,4.662339657323571,4.694493723925802,4.726647790528034,4.7588018571302655,4.790955923732497,4.8231099903347285,4.85526405693696,4.887418123539192,4.919572190141423,4.951726256743655,4.983880323345886,5.016034389948118,5.048188456550349,5.080342523152581,5.112496589754812,5.144650656357044,5.176804722959275,5.208958789561507,5.241112856163738,5.27326692276597,5.305420989368201,5.337575055970433,5.369729122572664,5.401883189174896,5.4340372557771275,5.466191322379359,5.4983453889815905,5.530499455583822,5.562653522186054,5.594807588788285,5.626961655390517,5.659115721992748,5.69126978859498,5.723423855197211,5.755577921799443,5.787731988401674,5.819886055003906,5.852040121606137,5.884194188208369,5.9163482548106,5.948502321412832,5.980656388015063,6.012810454617295,6.044964521219526,6.077118587821758,6.1092726544239895,6.141426721026221,6.1735807876284525,6.205734854230684,6.237888920832916,6.270042987435147,6.302197054037379,6.33435112063961,6.366505187241842,6.398659253844073,6.430813320446305,6.462967387048536,6.495121453650768,6.527275520252999,6.559429586855231,6.591583653457462,6.623737720059694,6.655891786661925,6.688045853264157,6.720199919866388,6.75235398646862,6.7845080530708515,6.816662119673083,6.8488161862753145,6.880970252877546,6.913124319479778,6.945278386082009,6.977432452684241,7.009586519286472,7.041740585888704,7.073894652490935,7.106048719093167,7.138202785695398,7.17035685229763,7.202510918899861,7.234664985502094,7.266819052104324,7.298973118706557,7.3311271853087865,7.363281251911019,7.3954353185132495,7.427589385115482,7.459743451717714,7.491897518319945,7.524051584922177,7.556205651524407,7.58835971812664,7.62051378472887,7.652667851331103,7.684821917933335,7.716975984535566,7.749130051137798,7.781284117740028,7.81343818434226,7.845592250944491,7.877746317546723,7.909900384148956,7.942054450751186,7.974208517353419,8.00636258395565,8.038516650557881,8.070670717160112,8.102824783762344,8.134978850364575,8.167132916966807,8.199286983569039,8.23144105017127,8.263595116773502,8.295749183375733,8.327903249977965,8.360057316580196,8.392211383182428,8.42436544978466,8.45651951638689,8.488673582989122,8.520827649591354,8.552981716193585,8.585135782795817,8.617289849398048,8.64944391600028,8.681597982602511,8.713752049204743,8.745906115806974,8.778060182409206,8.810214249011437,8.842368315613669,8.8745223822159,8.906676448818132,8.938830515420364,8.970984582022595,9.003138648624827,9.035292715227058,9.06744678182929,9.099600848431521,9.131754915033753,9.163908981635984,9.196063048238216,9.228217114840447,9.260371181442679,9.29252524804491,9.324679314647142,9.356833381249373,9.388987447851605,9.421141514453836,9.453295581056068,9.4854496476583,9.517603714260531,9.549757780862763,9.581911847464994,9.614065914067226,9.646219980669457,9.678374047271689,9.71052811387392,9.742682180476152,9.774836247078383,9.806990313680615,9.839144380282846,9.871298446885078,9.90345251348731,9.93560658008954,9.967760646691772,9.999914713294004,10.032068779896235,10.064222846498467,10.096376913100698,10.12853097970293,10.160685046305161,10.192839112907393,10.224993179509624,10.257147246111856,10.289301312714088,10.321455379316319,10.35360944591855,10.385763512520782,10.417917579123014,10.450071645725245,10.482225712327477,10.514379778929708,10.54653384553194,10.578687912134171,10.610841978736403,10.642996045338634,10.675150111940866,10.707304178543097,10.739458245145329,10.77161231174756,10.803766378349792,10.835920444952023,10.868074511554255,10.900228578156486,10.932382644758718,10.96453671136095,10.996690777963181,11.028844844565413,11.060998911167644,11.093152977769876,11.125307044372107,11.157461110974339,11.18961517757657,11.221769244178802,11.253923310781033,11.286077377383265,11.318231443985496,11.350385510587728,11.38253957718996,11.41469364379219,11.446847710394422,11.479001776996654,11.511155843598885,11.543309910201117,11.575463976803348,11.60761804340558,11.639772110007812,11.671926176610043,11.704080243212275,11.736234309814506,11.768388376416738,11.80054244301897,11.8326965096212,11.864850576223432,11.897004642825664,11.929158709427895,11.961312776030127,11.993466842632358,12.02562090923459,12.057774975836821,12.089929042439053,12.122083109041284,12.154237175643516,12.186391242245747,12.218545308847979,12.25069937545021,12.282853442052442,12.315007508654674,12.347161575256905,12.379315641859137,12.411469708461368,12.4436237750636,12.475777841665831,12.507931908268063,12.540085974870294,12.572240041472526,12.604394108074757,12.636548174676989,12.66870224127922,12.700856307881452,12.733010374483683,12.765164441085915,12.797318507688146,12.829472574290378,12.86162664089261,12.893780707494841,12.925934774097072,12.958088840699304,12.990242907301536,13.022396973903767,13.054551040505999,13.08670510710823,13.118859173710462,13.151013240312693,13.183167306914925,13.215321373517156,13.247475440119388,13.27962950672162,13.31178357332385,13.343937639926082,13.376091706528314,13.408245773130545,13.440399839732777,13.472553906335008,13.50470797293724,13.536862039539471,13.569016106141703,13.601170172743934,13.633324239346166,13.665478305948398,13.697632372550629,13.72978643915286,13.761940505755092,13.794094572357324,13.826248638959555,13.858402705561787,13.890556772164018,13.92271083876625,13.954864905368481,13.987018971970713,14.019173038572944,14.051327105175176,14.083481171777407,14.115635238379639,14.14778930498187,14.179943371584102,14.212097438186333,14.244251504788565,14.276405571390796,14.308559637993028,14.34071370459526,14.372867771197491,14.405021837799723,14.437175904401952,14.469329971004187,14.501484037606417,14.533638104208649,14.565792170810878,14.597946237413113,14.630100304015343,14.662254370617573,14.694408437219808,14.726562503822038,14.75871657042427,14.790870637026499,14.823024703628734,14.855178770230964,14.887332836833194,14.919486903435429,14.951640970037658,14.98379503663989,15.01594910324212,15.048103169844355,15.080257236446585,15.112411303048814,15.14456536965105,15.17671943625328,15.20887350285551,15.24102756945774,15.273181636059975,15.305335702662205,15.337489769264435,15.36964383586667,15.4017979024689,15.433951969071131,15.466106035673361,15.498260102275596,15.530414168877826,15.562568235480056,15.59472230208229,15.62687636868452,15.659030435286752,15.691184501888982,15.723338568491217,15.755492635093447,15.787646701695676,15.819800768297911,15.851954834900141,15.884108901502373,15.916262968104602,15.948417034706837,15.980571101309067,16.0127251679113,16.04487923451353,16.077033301115762,16.109187367717993,16.141341434320225,16.173495500922456,16.205649567524688,16.23780363412692,16.26995770072915,16.302111767331382,16.334265833933614,16.366419900535845,16.398573967138077,16.43072803374031,16.46288210034254,16.49503616694477,16.527190233547003,16.559344300149235,16.591498366751466,16.623652433353698,16.65580649995593,16.68796056655816,16.720114633160392,16.752268699762624,16.784422766364855,16.816576832967087,16.84873089956932,16.88088496617155,16.91303903277378,16.945193099376013,16.977347165978244,17.009501232580476,17.041655299182707,17.07380936578494,17.10596343238717,17.138117498989402,17.170271565591634,17.202425632193865,17.234579698796097,17.266733765398328,17.29888783200056,17.33104189860279,17.363195965205023,17.395350031807254,17.427504098409486,17.459658165011717,17.49181223161395,17.52396629821618,17.556120364818412,17.588274431420643,17.620428498022875,17.652582564625106,17.684736631227338,17.71689069782957,17.7490447644318,17.781198831034033,17.813352897636264,17.845506964238496,17.877661030840727,17.90981509744296,17.94196916404519,17.97412323064742,18.006277297249653,18.038431363851885,18.070585430454116,18.102739497056348,18.13489356365858,18.16704763026081,18.199201696863042,18.231355763465274,18.263509830067505,18.295663896669737,18.32781796327197,18.3599720298742,18.39212609647643,18.424280163078663,18.456434229680895,18.488588296283126,18.520742362885358,18.55289642948759,18.58505049608982,18.617204562692052,18.649358629294284,18.681512695896515,18.713666762498747,18.74582082910098,18.77797489570321,18.81012896230544,18.842283028907673,18.874437095509904,18.906591162112136,18.938745228714367,18.9708992953166,19.00305336191883,19.035207428521062,19.067361495123293,19.099515561725525,19.131669628327757,19.163823694929988,19.19597776153222,19.22813182813445,19.260285894736683,19.292439961338914,19.324594027941146,19.356748094543377,19.38890216114561,19.42105622774784,19.45321029435007,19.485364360952303,19.517518427554535,19.549672494156766,19.581826560758998,19.61398062736123,19.64613469396346,19.678288760565692,19.710442827167924,19.742596893770155,19.774750960372387,19.80690502697462,19.83905909357685,19.87121316017908,19.903367226781313,19.935521293383545,19.967675359985776,19.999829426588008,20.03198349319024,20.06413755979247,20.096291626394702,20.128445692996934,20.160599759599165,20.192753826201397,20.22490789280363,20.25706195940586,20.28921602600809,20.321370092610323,20.353524159212554,20.385678225814786,20.417832292417017,20.44998635901925,20.48214042562148,20.514294492223712,20.546448558825944,20.578602625428175,20.610756692030407,20.642910758632638,20.67506482523487,20.7072188918371,20.739372958439333,20.771527025041564,20.803681091643796,20.835835158246027,20.86798922484826,20.90014329145049,20.932297358052722,20.964451424654953,20.996605491257185,21.028759557859416,21.060913624461648,21.09306769106388,21.12522175766611,21.157375824268343,21.189529890870574,21.221683957472806,21.253838024075037,21.28599209067727,21.3181461572795,21.35030022388173,21.382454290483963,21.414608357086195,21.446762423688426,21.478916490290658,21.51107055689289,21.54322462349512,21.575378690097352,21.607532756699584,21.639686823301815,21.671840889904047,21.70399495650628,21.73614902310851,21.76830308971074,21.800457156312973,21.832611222915205,21.864765289517436,21.896919356119668,21.9290734227219,21.96122748932413,21.993381555926362,22.025535622528594,22.057689689130825,22.089843755733057,22.121997822335288,22.15415188893752,22.18630595553975,22.218460022141983,22.250614088744214,22.282768155346446,22.314922221948677,22.34707628855091,22.37923035515314,22.411384421755372,22.443538488357603,22.475692554959835,22.507846621562067,22.540000688164298,22.57215475476653,22.60430882136876,22.636462887970993,22.668616954573224,22.700771021175456,22.732925087777687,22.76507915437992,22.79723322098215,22.82938728758438,22.861541354186613,22.893695420788845,22.925849487391076,22.958003553993308,22.99015762059554,23.02231168719777,23.054465753800002,23.086619820402234,23.118773887004465,23.150927953606697,23.18308202020893,23.21523608681116,23.24739015341339,23.279544220015623,23.311698286617855,23.343852353220086,23.376006419822318,23.40816048642455,23.44031455302678,23.472468619629012,23.504622686231244,23.536776752833475,23.568930819435707,23.60108488603794,23.63323895264017,23.6653930192424,23.697547085844633,23.729701152446864,23.761855219049096,23.794009285651327,23.82616335225356,23.85831741885579,23.890471485458022,23.922625552060254,23.954779618662485,23.986933685264717,24.019087751866948,24.05124181846918,24.08339588507141,24.115549951673643,24.147704018275874,24.179858084878106,24.212012151480337,24.24416621808257,24.2763202846848,24.308474351287032,24.340628417889263,24.372782484491495,24.404936551093726,24.437090617695958,24.46924468429819,24.50139875090042,24.533552817502652,24.565706884104884,24.597860950707116,24.630015017309347,24.66216908391158,24.69432315051381,24.72647721711604,24.758631283718273,24.790785350320505,24.822939416922736,24.855093483524968,24.8872475501272,24.91940161672943,24.951555683331662,24.983709749933894,25.015863816536125,25.048017883138357,25.08017194974059,25.11232601634282,25.14448008294505,25.176634149547283,25.208788216149514,25.240942282751746,25.273096349353978,25.30525041595621,25.33740448255844,25.369558549160672,25.401712615762904,25.433866682365135,25.466020748967367,25.498174815569598,25.53032888217183,25.56248294877406,25.594637015376293,25.626791081978524,25.658945148580756,25.691099215182987,25.72325328178522,25.75540734838745,25.787561414989682,25.819715481591913,25.851869548194145,25.884023614796376,25.916177681398608,25.94833174800084,25.98048581460307,26.012639881205303,26.044793947807534,26.076948014409766,26.109102081011997,26.14125614761423,26.17341021421646,26.20556428081869,26.237718347420923,26.269872414023155,26.302026480625386,26.334180547227618,26.36633461382985,26.39848868043208,26.430642747034312,26.462796813636544,26.494950880238775,26.527104946841007,26.55925901344324,26.59141308004547,26.6235671466477,26.655721213249933,26.687875279852165,26.720029346454396,26.752183413056628,26.78433747965886,26.81649154626109,26.848645612863322,26.880799679465554,26.912953746067785,26.945107812670017,26.97726187927225,27.00941594587448,27.04157001247671,27.073724079078943,27.105878145681174,27.138032212283406,27.170186278885637,27.20234034548787,27.2344944120901,27.266648478692332,27.298802545294564,27.330956611896795,27.363110678499027,27.395264745101258,27.42741881170349,27.45957287830572,27.491726944907953,27.523881011510184,27.556035078112416,27.588189144714647,27.62034321131688,27.65249727791911,27.684651344521342,27.716805411123573,27.748959477725805,27.781113544328036,27.813267610930268,27.8454216775325,27.87757574413473,27.909729810736962,27.941883877339194,27.974037943941426,28.006192010543657,28.03834607714589,28.07050014374812,28.10265421035035,28.134808276952583,28.166962343554815,28.199116410157046,28.231270476759278,28.26342454336151,28.29557860996374,28.327732676565972,28.359886743168204,28.392040809770435,28.424194876372667,28.4563489429749,28.48850300957713,28.52065707617936,28.552811142781593,28.584965209383824,28.617119275986056,28.649273342588288,28.68142740919052,28.71358147579275,28.745735542394982,28.777889608997214,28.810043675599445],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617],"y":[0,0.04385745524956792,0.08771491049913584,0.13157236574870376,0.17542982099827167,0.2192872762478396,0.2631447314974075,0.30700218674697544,0.35085964199654335,0.3947170972461113,0.4385745524956792,0.48243200774524714,0.526289462994815,0.5701469182443829,0.6140043734939509,0.6578618287435188,0.7017192839930867,0.7455767392426547,0.7894341944922226,0.8332916497417905,0.8771491049913585,0.9210065602409262,0.9648640154904943,1.0087214707400622,1.05257892598963,1.096436381239198,1.1402938364887658,1.1841512917383339,1.2280087469879017,1.2718662022374696,1.3157236574870377,1.3595811127366055,1.4034385679861734,1.4472960232357415,1.4911534784853093,1.5350109337348772,1.5788683889844453,1.6227258442340131,1.666583299483581,1.7104407547331488,1.754298209982717,1.7981556652322848,1.8420131204818524,1.8858705757314205,1.9297280309809886,1.9735854862305562,2.0174429414801245,2.0613003967296923,2.10515785197926,2.149015307228828,2.192872762478396,2.236730217727964,2.2805876729775316,2.3244451282271,2.3683025834766678,2.4121600387262356,2.4560174939758035,2.499874949225372,2.543732404474939,2.5875898597245075,2.6314473149740754,2.675304770223643,2.719162225473211,2.763019680722779,2.806877135972347,2.8507345912219146,2.894592046471483,2.9384495017210503,2.9823069569706186,3.0261644122201865,3.0700218674697544,3.113879322719322,3.1577367779688905,3.201594233218458,3.2454516884680262,3.289309143717594,3.333166598967162,3.37702405421673,3.4208815094662977,3.464738964715866,3.508596419965434,3.5524538752150012,3.5963113304645695,3.6401687857141374,3.684026240963705,3.7278836962132735,3.771741151462841,3.815598606712409,3.859456061961977,3.903313517211545,3.9471709724611124,3.991028427710681,4.034885882960249,4.078743338209817,4.122600793459385,4.1664582487089525,4.21031570395852,4.254173159208088,4.298030614457656,4.341888069707225,4.385745524956792,4.42960298020636,4.473460435455928,4.517317890705496,4.561175345955063,4.605032801204632,4.6488902564542,4.692747711703768,4.7366051669533356,4.780462622202903,4.824320077452471,4.868177532702039,4.912034987951607,4.955892443201175,4.999749898450744,5.0436073537003105,5.087464808949878,5.131322264199447,5.175179719449015,5.219037174698582,5.262894629948151,5.306752085197719,5.350609540447286,5.394466995696854,5.438324450946422,5.48218190619599,5.526039361445558,5.569896816695126,5.613754271944694,5.657611727194262,5.701469182443829,5.745326637693397,5.789184092942966,5.833041548192534,5.876899003442101,5.920756458691669,5.964613913941237,6.008471369190805,6.052328824440373,6.096186279689941,6.140043734939509,6.183901190189077,6.227758645438644,6.271616100688212,6.315473555937781,6.359331011187348,6.403188466436916,6.447045921686485,6.4909033769360525,6.534760832185619,6.578618287435188,6.622475742684756,6.666333197934324,6.710190653183892,6.75404810843346,6.797905563683028,6.841763018932595,6.885620474182163,6.929477929431732,6.973335384681299,7.017192839930868,7.0610502951804355,7.1049077504300024,7.148765205679571,7.192622660929139,7.236480116178706,7.280337571428275,7.3241950266778435,7.36805248192741,7.411909937176978,7.455767392426547,7.499624847676114,7.543482302925682,7.587339758175251,7.631197213424818,7.675054668674386,7.718912123923954,7.762769579173521,7.80662703442309,7.850484489672658,7.894341944922225,7.9381994001717935,7.982056855421362,8.02591431067093,8.069771765920498,8.113629221170065,8.157486676419634,8.2013441316692,8.24520158691877,8.289059042168338,8.332916497417905,8.376773952667474,8.42063140791704,8.464488863166608,8.508346318416177,8.552203773665745,8.596061228915312,8.639918684164881,8.68377613941445,8.727633594664017,8.771491049913584,8.815348505163152,8.85920596041272,8.903063415662288,8.946920870911857,8.990778326161424,9.034635781410993,9.07849323666056,9.122350691910126,9.166208147159695,9.210065602409264,9.253923057658831,9.2977805129084,9.341637968157968,9.385495423407535,9.429352878657102,9.473210333906671,9.517067789156238,9.560925244405807,9.604782699655376,9.648640154904943,9.692497610154511,9.736355065404078,9.780212520653645,9.824069975903214,9.867927431152783,9.91178488640235,9.955642341651918,9.999499796901487,10.043357252151054,10.087214707400621,10.13107216265019,10.174929617899757,10.218787073149326,10.262644528398894,10.306501983648461,10.35035943889803,10.394216894147597,10.438074349397164,10.481931804646733,10.525789259896301,10.569646715145868,10.613504170395437,10.657361625645006,10.701219080894573,10.74507653614414,10.788933991393709,10.832791446643276,10.876648901892844,10.920506357142413,10.96436381239198,11.008221267641549,11.052078722891116,11.095936178140683,11.139793633390251,11.18365108863982,11.227508543889387,11.271365999138956,11.315223454388525,11.359080909638092,11.402938364887659,11.446795820137227,11.490653275386794,11.534510730636363,11.578368185885932,11.622225641135499,11.666083096385067,11.709940551634634,11.753798006884201,11.79765546213377,11.841512917383339,11.885370372632906,11.929227827882475,11.973085283132043,12.01694273838161,12.060800193631177,12.104657648880746,12.148515104130313,12.192372559379882,12.23623001462945,12.280087469879017,12.323944925128586,12.367802380378153,12.41165983562772,12.455517290877289,12.499374746126858,12.543232201376425,12.587089656625993,12.630947111875562,12.674804567125129,12.718662022374696,12.762519477624265,12.806376932873832,12.8502343881234,12.89409184337297,12.937949298622536,12.981806753872105,13.025664209121672,13.069521664371239,13.113379119620808,13.157236574870376,13.201094030119943,13.244951485369512,13.28880894061908,13.332666395868648,13.376523851118215,13.420381306367783,13.46423876161735,13.50809621686692,13.551953672116488,13.595811127366057,13.639668582615625,13.68352603786519,13.727383493114758,13.771240948364326,13.815098403613895,13.858955858863464,13.902813314113033,13.946670769362598,13.990528224612166,14.034385679861735,14.078243135111302,14.122100590360871,14.16595804561044,14.209815500860005,14.253672956109574,14.297530411359142,14.341387866608711,14.385245321858278,14.429102777107847,14.472960232357412,14.51681768760698,14.56067514285655,14.604532598106118,14.648390053355687,14.692247508605254,14.73610496385482,14.779962419104388,14.823819874353957,14.867677329603525,14.911534784853094,14.955392240102663,14.999249695352228,15.043107150601797,15.086964605851364,15.130822061100933,15.174679516350501,15.21853697160007,15.262394426849635,15.306251882099204,15.350109337348773,15.39396679259834,15.437824247847908,15.481681703097477,15.525539158347042,15.569396613596611,15.61325406884618,15.657111524095749,15.700968979345316,15.744826434594884,15.78868388984445,15.832541345094018,15.876398800343587,15.920256255593156,15.964113710842724,16.007971166092293,16.05182862134186,16.095686076591427,16.139543531840996,16.18340098709056,16.22725844234013,16.2711158975897,16.314973352839267,16.358830808088832,16.4026882633384,16.44654571858797,16.49040317383754,16.534260629087107,16.578118084336676,16.62197553958624,16.66583299483581,16.70969045008538,16.753547905334948,16.797405360584516,16.84126281583408,16.885120271083647,16.928977726333216,16.972835181582784,17.016692636832353,17.06055009208192,17.10440754733149,17.148265002581056,17.192122457830624,17.235979913080193,17.279837368329762,17.32369482357933,17.3675522788289,17.411409734078465,17.455267189328033,17.4991246445776,17.542982099827167,17.586839555076736,17.630697010326305,17.67455446557587,17.71841192082544,17.762269376075007,17.806126831324576,17.849984286574145,17.893841741823714,17.93769919707328,17.981556652322848,18.025414107572416,18.069271562821985,18.113129018071554,18.15698647332112,18.200843928570684,18.244701383820253,18.28855883906982,18.33241629431939,18.37627374956896,18.420131204818528,18.463988660068093,18.507846115317662,18.55170357056723,18.5955610258168,18.639418481066368,18.683275936315937,18.727133391565502,18.77099084681507,18.81484830206464,18.858705757314205,18.902563212563773,18.946420667813342,18.990278123062907,19.034135578312476,19.077993033562045,19.121850488811614,19.165707944061182,19.20956539931075,19.253422854560316,19.297280309809885,19.341137765059454,19.384995220309023,19.42885267555859,19.472710130808156,19.51656758605772,19.56042504130729,19.60428249655686,19.648139951806428,19.691997407055997,19.735854862305565,19.77971231755513,19.8235697728047,19.867427228054268,19.911284683303837,19.955142138553406,19.998999593802974,20.04285704905254,20.08671450430211,20.130571959551677,20.174429414801242,20.21828687005081,20.26214432530038,20.306001780549945,20.349859235799514,20.393716691049082,20.43757414629865,20.48143160154822,20.52528905679779,20.569146512047354,20.613003967296923,20.65686142254649,20.70071887779606,20.74457633304563,20.788433788295194,20.832291243544763,20.876148698794328,20.920006154043897,20.963863609293465,21.007721064543034,21.051578519792603,21.095435975042168,21.139293430291737,21.183150885541306,21.227008340790874,21.270865796040443,21.31472325129001,21.358580706539577,21.402438161789146,21.446295617038714,21.49015307228828,21.53401052753785,21.577867982787417,21.621725438036982,21.66558289328655,21.70944034853612,21.75329780378569,21.797155259035257,21.841012714284826,21.884870169534395,21.92872762478396,21.97258508003353,22.016442535283097,22.060299990532666,22.10415744578223,22.1480149010318,22.191872356281365,22.235729811530934,22.279587266780503,22.32344472203007,22.36730217727964,22.41115963252921,22.455017087778774,22.498874543028343,22.54273199827791,22.58658945352748,22.63044690877705,22.674304364026618,22.718161819276183,22.762019274525752,22.805876729775317,22.849734185024886,22.893591640274455,22.937449095524023,22.98130655077359,23.025164006023157,23.069021461272726,23.112878916522295,23.156736371771864,23.200593827021432,23.244451282270997,23.288308737520566,23.332166192770135,23.376023648019704,23.41988110326927,23.463738558518838,23.507596013768403,23.55145346901797,23.59531092426754,23.63916837951711,23.683025834766678,23.726883290016247,23.77074074526581,23.81459820051538,23.85845565576495,23.902313111014518,23.946170566264087,23.990028021513655,24.03388547676322,24.07774293201279,24.121600387262355,24.165457842511923,24.209315297761492,24.25317275301106,24.297030208260626,24.340887663510195,24.384745118759763,24.428602574009332,24.4724600292589,24.51631748450847,24.560174939758035,24.604032395007604,24.647889850257172,24.69174730550674,24.735604760756306,24.779462216005875,24.82331967125544,24.86717712650501,24.911034581754578,24.954892037004146,24.998749492253715,25.042606947503284,25.08646440275285,25.130321858002418,25.174179313251987,25.218036768501555,25.261894223751124,25.305751679000693,25.349609134250258,25.393466589499827,25.437324044749392,25.48118149999896,25.52503895524853,25.5688964104981,25.612753865747663,25.656611320997232,25.7004687762468,25.74432623149637,25.78818368674594,25.832041141995507,25.875898597245072,25.91975605249464,25.96361350774421,26.00747096299378,26.051328418243344,26.095185873492913,26.139043328742478,26.182900783992046,26.226758239241615,26.270615694491184,26.314473149740753,26.35833060499032,26.402188060239887,26.446045515489455,26.489902970739024,26.533760425988593,26.57761788123816,26.62147533648773,26.665332791737296,26.709190246986864,26.75304770223643,26.796905157485998,26.840762612735567,26.884620067985136,26.9284775232347,26.97233497848427,27.01619243373384,27.060049888983407],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"AI Rule 3","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473],"y":[0,0.04662510782168315,0.0932502156433663,0.13987532346504944,0.1865004312867326,0.23312553910841574,0.2797506469300989,0.32637575475178204,0.3730008625734652,0.4196259703951483,0.4662510782168315,0.5128761860385146,0.5595012938601978,0.6061264016818809,0.6527515095035641,0.6993766173252471,0.7460017251469304,0.7926268329686135,0.8392519407902966,0.8858770486119797,0.932502156433663,0.9791272642553461,1.0257523720770292,1.0723774798987125,1.1190025877203955,1.1656276955420788,1.2122528033637618,1.258877911185445,1.3055030190071282,1.3521281268288112,1.3987532346504943,1.4453783424721778,1.4920034502938608,1.5386285581155439,1.585253665937227,1.6318787737589102,1.6785038815805933,1.7251289894022763,1.7717540972239594,1.8183792050456429,1.865004312867326,1.911629420689009,1.9582545285106923,2.0048796363323755,2.0515047441540584,2.0981298519757416,2.144754959797425,2.1913800676191078,2.238005175440791,2.284630283262474,2.3312553910841576,2.3778804989058404,2.4245056067275237,2.471130714549207,2.51775582237089,2.564380930192573,2.6110060380142563,2.6576311458359396,2.7042562536576225,2.7508813614793057,2.7975064693009886,2.844131577122672,2.8907566849443556,2.9373817927660384,2.9840069005877217,3.0306320084094045,3.0772571162310878,3.1238822240527706,3.170507331874454,3.2171324396961367,3.2637575475178204,3.3103826553395037,3.3570077631611865,3.40363287098287,3.4502579788045526,3.496883086626236,3.5435081944479188,3.5901333022696025,3.6367584100912858,3.6833835179129686,3.730008625734652,3.7766337335563347,3.823258841378018,3.869883949199701,3.9165090570213845,3.963134164843068,4.009759272664751,4.056384380486434,4.103009488308117,4.1496345961298,4.196259703951483,4.242884811773167,4.28950991959485,4.336135027416533,4.3827601352382155,4.429385243059899,4.476010350881582,4.522635458703265,4.569260566524948,4.615885674346631,4.662510782168315,4.709135889989998,4.755760997811681,4.802386105633364,4.849011213455047,4.89563632127673,4.942261429098414,4.988886536920097,5.03551164474178,5.082136752563463,5.128761860385146,5.175386968206829,5.222012076028513,5.2686371838501955,5.315262291671879,5.361887399493562,5.408512507315245,5.455137615136929,5.5017627229586115,5.548387830780295,5.595012938601977,5.641638046423661,5.688263154245344,5.734888262067027,5.781513369888711,5.828138477710393,5.874763585532077,5.92138869335376,5.968013801175443,6.014638908997125,6.061264016818809,6.107889124640493,6.1545142324621755,6.201139340283859,6.247764448105541,6.294389555927225,6.341014663748908,6.3876397715705915,6.434264879392273,6.480889987213957,6.527515095035641,6.574140202857324,6.620765310679007,6.667390418500689,6.714015526322373,6.760640634144056,6.80726574196574,6.853890849787423,6.900515957609105,6.947141065430789,6.993766173252472,7.040391281074156,7.0870163888958375,7.133641496717521,7.180266604539205,7.226891712360888,7.2735168201825715,7.3201419280042535,7.366767035825937,7.41339214364762,7.460017251469304,7.506642359290987,7.553267467112669,7.599892574934353,7.646517682756036,7.69314279057772,7.739767898399402,7.786393006221085,7.833018114042769,7.879643221864452,7.926268329686136,7.9728934375078175,8.019518545329502,8.066143653151183,8.112768760972868,8.15939386879455,8.206018976616233,8.252644084437918,8.2992691922596,8.345894300081284,8.392519407902967,8.43914451572465,8.485769623546334,8.532394731368015,8.5790198391897,8.625644947011383,8.672270054833065,8.718895162654748,8.765520270476431,8.812145378298114,8.858770486119798,8.905395593941481,8.952020701763164,8.998645809584847,9.04527091740653,9.091896025228214,9.138521133049895,9.18514624087158,9.231771348693263,9.278396456514946,9.32502156433663,9.371646672158311,9.418271779979996,9.464896887801679,9.511521995623362,9.558147103445046,9.604772211266727,9.651397319088412,9.698022426910095,9.744647534731778,9.79127264255346,9.837897750375143,9.884522858196828,9.93114796601851,9.977773073840194,10.024398181661876,10.07102328948356,10.117648397305242,10.164273505126927,10.21089861294861,10.257523720770292,10.304148828591975,10.350773936413658,10.397399044235343,10.444024152057025,10.490649259878708,10.537274367700391,10.583899475522074,10.630524583343758,10.677149691165441,10.723774798987124,10.770399906808807,10.81702501463049,10.863650122452174,10.910275230273857,10.95690033809554,11.003525445917223,11.050150553738906,11.09677566156059,11.143400769382271,11.190025877203954,11.236650985025639,11.283276092847322,11.329901200669006,11.376526308490687,11.42315141631237,11.469776524134055,11.516401631955738,11.563026739777422,11.609651847599103,11.656276955420786,11.70290206324247,11.749527171064154,11.796152278885835,11.84277738670752,11.889402494529202,11.936027602350887,11.98265271017257,12.02927781799425,12.075902925815935,12.122528033637618,12.169153141459303,12.215778249280985,12.262403357102666,12.309028464924351,12.355653572746034,12.402278680567719,12.4489037883894,12.495528896211082,12.542154004032767,12.58877911185445,12.635404219676134,12.682029327497816,12.728654435319498,12.775279543141183,12.821904650962866,12.868529758784547,12.915154866606231,12.961779974427914,13.008405082249599,13.055030190071282,13.101655297892963,13.148280405714647,13.19490551353633,13.241530621358015,13.288155729179698,13.334780837001379,13.381405944823063,13.428031052644746,13.47465616046643,13.521281268288112,13.567906376109795,13.61453148393148,13.661156591753162,13.707781699574847,13.754406807396528,13.80103191521821,13.847657023039895,13.894282130861578,13.940907238683263,13.987532346504944,14.034157454326627,14.080782562148311,14.127407669969994,14.174032777791675,14.22065788561336,14.267282993435042,14.313908101256727,14.36053320907841,14.407158316900091,14.453783424721776,14.500408532543458,14.547033640365143,14.593658748186826,14.640283856008507,14.686908963830192,14.733534071651874,14.780159179473559,14.82678428729524,14.873409395116923,14.920034502938607,14.96665961076029,15.013284718581975,15.059909826403656,15.106534934225339,15.153160042047023,15.199785149868706,15.246410257690387,15.293035365512072,15.339660473333755,15.38628558115544,15.432910688977122,15.479535796798803,15.526160904620488,15.57278601244217,15.619411120263855,15.666036228085538,15.71266133590722,15.759286443728904,15.805911551550587,15.852536659372271,15.899161767193952,15.945786875015635,15.99241198283732,16.039037090659004,16.085662198480687,16.132287306302366,16.178912414124053,16.225537521945736,16.27216262976742,16.3187877375891,16.365412845410784,16.412037953232467,16.45866306105415,16.505288168875836,16.551913276697515,16.5985383845192,16.645163492340885,16.691788600162567,16.73841370798425,16.785038815805933,16.831663923627616,16.8782890314493,16.92491413927098,16.971539247092668,17.018164354914347,17.06478946273603,17.111414570557717,17.1580396783794,17.20466478620108,17.251289894022765,17.297915001844448,17.34454010966613,17.391165217487814,17.437790325309496,17.48441543313118,17.531040540952862,17.57766564877455,17.624290756596228,17.67091586441791,17.717540972239597,17.76416608006128,17.810791187882963,17.857416295704645,17.90404140352633,17.95066651134801,17.997291619169694,18.04391672699138,18.09054183481306,18.137166942634742,18.18379205045643,18.23041715827811,18.27704226609979,18.323667373921477,18.37029248174316,18.416917589564843,18.463542697386526,18.51016780520821,18.55679291302989,18.603418020851574,18.65004312867326,18.696668236494943,18.743293344316623,18.78991845213831,18.836543559959992,18.883168667781675,18.929793775603358,18.97641888342504,19.023043991246723,19.069669099068406,19.116294206890093,19.162919314711772,19.209544422533455,19.25616953035514,19.302794638176824,19.349419745998507,19.39604485382019,19.442669961641872,19.489295069463555,19.535920177285238,19.58254528510692,19.629170392928604,19.675795500750286,19.722420608571973,19.769045716393656,19.815670824215335,19.86229593203702,19.908921039858704,19.955546147680387,20.00217125550207,20.048796363323753,20.095421471145436,20.14204657896712,20.188671686788805,20.235296794610484,20.281921902432167,20.328547010253853,20.375172118075536,20.42179722589722,20.4684223337189,20.515047441540585,20.561672549362267,20.60829765718395,20.654922765005633,20.701547872827316,20.748172980649,20.794798088470685,20.841423196292368,20.88804830411405,20.934673411935734,20.981298519757416,21.0279236275791,21.074548735400782,21.121173843222465,21.167798951044148,21.21442405886583,21.261049166687517,21.307674274509196,21.354299382330883,21.400924490152565,21.44754959797425,21.49417470579593,21.540799813617614,21.587424921439297,21.63405002926098,21.680675137082662,21.72730024490435,21.773925352726028,21.820550460547715,21.867175568369397,21.91380067619108,21.960425784012763,22.007050891834446,22.05367599965613],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232],"y":[0,0.060505017726002884,0.12101003545200577,0.18151505317800867,0.24202007090401154,0.3025250886300145,0.36303010635601735,0.4235351240820202,0.48404014180802307,0.544545159534026,0.605050177260029,0.6655551949860318,0.7260602127120347,0.7865652304380376,0.8470702481640404,0.9075752658900433,0.9680802836160461,1.028585301342049,1.089090319068052,1.1495953367940548,1.210100354520058,1.2706053722460606,1.3311103899720635,1.3916154076980665,1.4521204254240694,1.512625443150072,1.5731304608760752,1.633635478602078,1.6941404963280808,1.7546455140540838,1.8151505317800867,1.8756555495060894,1.9361605672320923,1.9966655849580954,2.057170602684098,2.117675620410101,2.178180638136104,2.238685655862107,2.2991906735881096,2.3596956913141125,2.420200709040116,2.4807057267661183,2.5412107444921213,2.601715762218124,2.662220779944127,2.72272579767013,2.783230815396133,2.8437358331221354,2.9042408508481388,2.9647458685741417,3.025250886300144,3.085755904026147,3.1462609217521504,3.2067659394781534,3.267270957204156,3.3277759749301588,3.3882809926561617,3.4487860103821646,3.5092910281081675,3.56979604583417,3.6303010635601733,3.6908060812861763,3.7513110990121787,3.811816116738182,3.8723211344641846,3.9328261521901875,3.993331169916191,4.053836187642194,4.114341205368196,4.1748462230942,4.235351240820202,4.295856258546205,4.356361276272208,4.41686629399821,4.477371311724214,4.537876329450217,4.598381347176219,4.658886364902223,4.719391382628225,4.779896400354228,4.840401418080232,4.900906435806234,4.961411453532237,5.02191647125824,5.0824214889842425,5.142926506710245,5.203431524436248,5.263936542162251,5.324441559888254,5.384946577614257,5.44545159534026,5.505956613066263,5.566461630792266,5.626966648518269,5.687471666244271,5.747976683970275,5.8084817016962775,5.86898671942228,5.929491737148283,5.989996754874285,6.050501772600288,6.111006790326292,6.171511808052294,6.232016825778298,6.292521843504301,6.353026861230303,6.413531878956307,6.474036896682309,6.534541914408312,6.5950469321343155,6.6555519498603175,6.71605696758632,6.776561985312323,6.837067003038326,6.897572020764329,6.958077038490332,7.018582056216335,7.079087073942338,7.13959209166834,7.200097109394345,7.260602127120347,7.32110714484635,7.3816121625723525,7.442117180298355,7.5026221980243575,7.563127215750361,7.623632233476364,7.684137251202367,7.744642268928369,7.805147286654372,7.865652304380375,7.926157322106379,7.986662339832382,8.047167357558385,8.107672375284388,8.168177393010389,8.228682410736392,8.289187428462396,8.3496924461884,8.410197463914402,8.470702481640403,8.531207499366406,8.59171251709241,8.652217534818414,8.712722552544417,8.773227570270418,8.83373258799642,8.894237605722424,8.954742623448428,9.015247641174431,9.075752658900434,9.136257676626435,9.196762694352438,9.257267712078441,9.317772729804446,9.378277747530449,9.43878276525645,9.499287782982453,9.559792800708456,9.620297818434459,9.680802836160463,9.741307853886465,9.801812871612468,9.86231788933847,9.922822907064473,9.983327924790476,10.04383294251648,10.104337960242482,10.164842977968485,10.225347995694488,10.28585301342049,10.346358031146494,10.406863048872497,10.4673680665985,10.527873084324503,10.588378102050505,10.648883119776508,10.709388137502511,10.769893155228514,10.830398172954517,10.89090319068052,10.951408208406523,11.011913226132526,11.072418243858529,11.132923261584532,11.193428279310535,11.253933297036538,11.31443831476254,11.374943332488542,11.435448350214546,11.49595336794055,11.556458385666552,11.616963403392555,11.677468421118556,11.73797343884456,11.798478456570564,11.858983474296567,11.91948849202257,11.97999350974857,12.040498527474574,12.101003545200577,12.161508562926581,12.222013580652584,12.282518598378587,12.343023616104588,12.403528633830591,12.464033651556596,12.524538669282599,12.585043687008602,12.645548704734603,12.706053722460606,12.766558740186609,12.827063757912613,12.887568775638616,12.948073793364618,13.00857881109062,13.069083828816623,13.129588846542626,13.190093864268631,13.250598881994632,13.311103899720635,13.371608917446638,13.43211393517264,13.492618952898644,13.553123970624647,13.61362898835065,13.674134006076653,13.734639023802655,13.795144041528658,13.855649059254661,13.916154076980664,13.976659094706667,14.03716411243267],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329],"y":[0,0.019097406959074845,0.03819481391814969,0.057292220877224534,0.07638962783629938,0.09548703479537424,0.11458444175444907,0.13368184871352393,0.15277925567259876,0.17187666263167362,0.19097406959074847,0.2100714765498233,0.22916888350889814,0.248266290467973,0.26736369742704785,0.2864611043861227,0.3055585113451975,0.3246559183042724,0.34375332526334723,0.3628507322224221,0.38194813918149695,0.40104554614057175,0.4201429530996466,0.4392403600587214,0.45833776701779627,0.47743517397687113,0.496532580935946,0.5156299878950208,0.5347273948540957,0.5538248018131705,0.5729222087722454,0.5920196157313202,0.611117022690395,0.6302144296494699,0.6493118366085447,0.6684092435676197,0.6875066505266945,0.7066040574857693,0.7257014644448442,0.744798871403919,0.7638962783629939,0.7829936853220687,0.8020910922811435,0.8211884992402183,0.8402859061992932,0.859383313158368,0.8784807201174428,0.8975781270765177,0.9166755340355925,0.9357729409946675,0.9548703479537423,0.9739677549128171,0.993065161871892,1.0121625688309668,1.0312599757900416,1.0503573827491166,1.0694547897081914,1.0885521966672662,1.107649603626341,1.1267470105854158,1.1458444175444908,1.1649418245035656,1.1840392314626405,1.2031366384217153,1.22223404538079,1.241331452339865,1.2604288592989399,1.2795262662580147,1.2986236732170895,1.3177210801761643,1.3368184871352393,1.3559158940943141,1.375013301053389,1.3941107080124637,1.4132081149715385,1.4323055219306136,1.4514029288896884,1.4705003358487632,1.489597742807838,1.508695149766913,1.5277925567259878,1.5468899636850626,1.5659873706441374,1.5850847776032122,1.604182184562287,1.623279591521362,1.6423769984804366,1.6614744054395116,1.6805718123985864,1.6996692193576615,1.718766626316736,1.737864033275811,1.7569614402348857,1.7760588471939607,1.7951562541530355,1.8142536611121105,1.833351068071185,1.8524484750302601,1.871545881989335,1.89064328894841,1.9097406959074845,1.9288381028665595,1.9479355098256341,1.9670329167847092,1.986130323743784,2.0052277307028588,2.0243251376619336,2.043422544621009,2.062519951580083,2.0816173585391584,2.100714765498233,2.119812172457308,2.138909579416383,2.1580069863754576,2.1771043933345324,2.1962018002936072,2.215299207252682,2.2343966142117573,2.2534940211708316,2.272591428129907,2.2916888350889817,2.3107862420480565,2.3298836490071313,2.348981055966206,2.368078462925281,2.387175869884356,2.4062732768434305,2.4253706838025058,2.44446809076158,2.4635654977206554,2.48266290467973,2.501760311638805,2.5208577185978798,2.5399551255569546,2.5590525325160294,2.578149939475104,2.597247346434179,2.616344753393254,2.6354421603523286,2.6545395673114034,2.6736369742704786,2.692734381229553,2.7118317881886282,2.730929195147703,2.750026602106778,2.7691240090658527,2.7882214160249275,2.8073188229840023,2.826416229943077,2.845513636902152,2.864611043861227,2.8837084508203015,2.9028058577793767,2.9219032647384515,2.9410006716975263,2.960098078656601,2.979195485615676,2.9982928925747507,3.017390299533826,3.0364877064929003,3.0555851134519756,3.07468252041105,3.093779927370125,3.1128773343292,3.131974741288275,3.15107214824735,3.1701695552064244,3.189266962165499,3.208364369124574,3.2274617760836493,3.246559183042724,3.2656565900017984,3.2847539969608732,3.3038514039199485,3.3229488108790233,3.342046217838098,3.361143624797173,3.380241031756248,3.399338438715323,3.4184358456743973,3.437533252633472,3.4566306595925473,3.475728066551622,3.494825473510697,3.5139228804697713,3.533020287428847,3.5521176943879214,3.571215101346996,3.590312508306071,3.609409915265146,3.628507322224221,3.647604729183296,3.66670213614237,3.685799543101446,3.7048969500605202,3.723994357019595,3.74309176397867,3.762189170937745,3.78128657789682,3.8003839848558942,3.819481391814969,3.838578798774044,3.857676205733119,3.876773612692194,3.8958710196512683,3.914968426610343,3.9340658335694183,3.953163240528493,3.972260647487568,3.9913580544466427,4.0104554614057175,4.029552868364792,4.048650275323867,4.067747682282942,4.086845089242018,4.105942496201092,4.125039903160166,4.144137310119241,4.163234717078317,4.182332124037392,4.201429530996466,4.22052693795554,4.239624344914616,4.258721751873691,4.277819158832766,4.2969165657918404,4.316013972750915,4.33511137970999,4.354208786669065,4.37330619362814,4.3924036005872145,4.411501007546289,4.430598414505364,4.449695821464439,4.468793228423515,4.487890635382589,4.506988042341663,4.526085449300738,4.545182856259814,4.564280263218889,4.583377670177963,4.602475077137037,4.621572484096113,4.640669891055188,4.659767298014263,4.678864704973337,4.697962111932412,4.717059518891487,4.736156925850562,4.755254332809637,4.774351739768712,4.793449146727786,4.812546553686861,4.831643960645936,4.8507413676050115,4.869838774564086,4.88893618152316,4.908033588482235,4.927130995441311,4.9462284024003855,4.96532580935946,4.984423216318534,5.00352062327761,5.022618030236685,5.0417154371957595,5.060812844154834,5.079910251113909,5.099007658072984,5.118105065032059,5.137202471991134,5.156299878950208,5.175397285909283,5.194494692868358,5.213592099827433,5.232689506786508,5.251786913745583,5.270884320704657,5.289981727663732,5.309079134622807,5.3281765415818825,5.347273948540957,5.366371355500032,5.385468762459106,5.404566169418182,5.4236635763772565,5.442760983336331,5.461858390295406,5.480955797254481,5.500053204213556,5.5191506111726305,5.538248018131705,5.55734542509078,5.576442832049855,5.59554023900893,5.6146376459680045,5.63373505292708,5.652832459886154,5.671929866845229,5.691027273804304,5.710124680763379,5.729222087722454,5.748319494681529,5.767416901640603,5.786514308599679,5.805611715558753,5.824709122517828,5.843806529476903,5.862903936435978,5.882001343395053,5.9010987503541275,5.920196157313202,5.939293564272277,5.958390971231352,5.977488378190427,5.9965857851495015,6.015683192108577,6.034780599067652,6.053878006026726,6.072975412985801,6.092072819944876,6.111170226903951,6.130267633863026,6.1493650408221,6.168462447781176,6.18755985474025,6.206657261699325,6.2257546686584,6.244852075617475,6.26394948257655,6.283046889535624],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">28.81%</td>
                <td>65.2%</td>
                <td>896</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>23.75%</td>
                <td>61.1</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">27.06%</td>
                <td>65.6%</td>
                <td>617</td>
                <td>1.13</td>
                <td>0.00</td>
                <td>10.30%</td>
                <td>60.5</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>AI Rule 3: Smart Money Flow Divergence</td>
                <td>AI_GENERATED</td>
                <td class="positive">22.05%</td>
                <td>66.0%</td>
                <td>473</td>
                <td>1.14</td>
                <td>0.00</td>
                <td>11.36%</td>
                <td>58.6</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">14.04%</td>
                <td>66.4%</td>
                <td>232</td>
                <td>1.18</td>
                <td>0.00</td>
                <td>5.37%</td>
                <td>55.5</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">6.28%</td>
                <td>64.4%</td>
                <td>329</td>
                <td>1.05</td>
                <td>0.00</td>
                <td>11.32%</td>
                <td>51.8</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Rule 22: Higher High Pattern</td>
                <td>ORIGINAL</td>
                <td class="positive">7.16%</td>
                <td>63.1%</td>
                <td>336</td>
                <td>1.06</td>
                <td>0.00</td>
                <td>8.34%</td>
                <td>51.8</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Volume Rule 4: Volume Breakout Confirmation</td>
                <td>UNKNOWN</td>
                <td class="positive">5.19%</td>
                <td>63.8%</td>
                <td>116</td>
                <td>1.13</td>
                <td>0.00</td>
                <td>3.98%</td>
                <td>51.2</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">5.26%</td>
                <td>63.2%</td>
                <td>117</td>
                <td>1.13</td>
                <td>0.00</td>
                <td>7.33%</td>
                <td>51.1</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Rule 28: Volume Breakout</td>
                <td>ORIGINAL</td>
                <td class="positive">5.34%</td>
                <td>63.0%</td>
                <td>146</td>
                <td>1.11</td>
                <td>0.00</td>
                <td>7.38%</td>
                <td>51.0</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Ext Rule 6: Fibonacci Support Confluence</td>
                <td>UNKNOWN</td>
                <td class="positive">5.32%</td>
                <td>62.8%</td>
                <td>615</td>
                <td>1.02</td>
                <td>0.00</td>
                <td>15.57%</td>
                <td>51.0</td>
            </tr>
            
            <tr>
                <td>11</td>
                <td>Volatility Rule 2: ATR Expansion Signal</td>
                <td>UNKNOWN</td>
                <td class="positive">4.75%</td>
                <td>62.7%</td>
                <td>158</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>5.76%</td>
                <td>50.7</td>
            </tr>
            
            <tr>
                <td>12</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">1.59%</td>
                <td>64.0%</td>
                <td>100</td>
                <td>1.05</td>
                <td>0.00</td>
                <td>4.61%</td>
                <td>49.8</td>
            </tr>
            
            <tr>
                <td>13</td>
                <td>Rule 3: RSI Oversold</td>
                <td>ORIGINAL</td>
                <td class="positive">2.02%</td>
                <td>62.8%</td>
                <td>191</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>9.02%</td>
                <td>49.7</td>
            </tr>
            
            <tr>
                <td>14</td>
                <td>Rule 11: RSI Divergence</td>
                <td>ORIGINAL</td>
                <td class="positive">1.71%</td>
                <td>62.3%</td>
                <td>130</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>10.72%</td>
                <td>49.4</td>
            </tr>
            
            <tr>
                <td>15</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">4.51%</td>
                <td>68.2%</td>
                <td>44</td>
                <td>1.34</td>
                <td>0.00</td>
                <td>2.22%</td>
                <td>35.5</td>
            </tr>
            
            <tr>
                <td>16</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">1.52%</td>
                <td>60.5%</td>
                <td>43</td>
                <td>1.10</td>
                <td>0.00</td>
                <td>3.17%</td>
                <td>31.6</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
