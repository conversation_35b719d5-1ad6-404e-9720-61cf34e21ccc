
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 01:09:20 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">301.19%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">7,870</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">64.1%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">3.04</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>76.83%</strong></td>
                    <td>65.4%</td>
                    <td>587</td>
                    <td>1.14</td>
                    <td class="negative">24.06%</td>
                    <td class="positive"><strong>0.8998</strong></td>
                    <td class="negative">+0.85% / -1.37%</td>
                    <td>4h34m<br><small>(1.0m - 55h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>71.94%</strong></td>
                    <td>66.1%</td>
                    <td>307</td>
                    <td>1.28</td>
                    <td class="negative">9.31%</td>
                    <td class="positive"><strong>0.8741</strong></td>
                    <td class="negative">+0.89% / -1.34%</td>
                    <td>6h16m<br><small>(1.0m - 78h15m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>46.05%</strong></td>
                    <td>64.0%</td>
                    <td>702</td>
                    <td>1.07</td>
                    <td class="negative">27.48%</td>
                    <td class="positive"><strong>0.8156</strong></td>
                    <td class="negative">+0.86% / -1.42%</td>
                    <td>4h27m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>41.71%</strong></td>
                    <td>64.4%</td>
                    <td>542</td>
                    <td>1.08</td>
                    <td class="negative">23.69%</td>
                    <td class="positive"><strong>0.7909</strong></td>
                    <td class="negative">+0.87% / -1.38%</td>
                    <td>3h18m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>38.67%</strong></td>
                    <td>83.8%</td>
                    <td>37</td>
                    <td>3.09</td>
                    <td class="neutral">4.93%</td>
                    <td class="positive"><strong>0.8015</strong></td>
                    <td class="negative">+0.88% / -1.31%</td>
                    <td>3h50m<br><small>(4.0m - 17h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>32.76%</strong></td>
                    <td>64.7%</td>
                    <td>1046</td>
                    <td>1.03</td>
                    <td class="negative">41.48%</td>
                    <td class="positive"><strong>0.7908</strong></td>
                    <td class="negative">+0.83% / -1.43%</td>
                    <td>3h55m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>31.76%</strong></td>
                    <td>68.2%</td>
                    <td>107</td>
                    <td>1.36</td>
                    <td class="negative">26.88%</td>
                    <td class="positive"><strong>0.6546</strong></td>
                    <td class="negative">+0.86% / -1.36%</td>
                    <td>4h33m<br><small>(8.0m - 26h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>31.27%</strong></td>
                    <td>66.8%</td>
                    <td>196</td>
                    <td>1.18</td>
                    <td class="negative">10.25%</td>
                    <td class="positive"><strong>0.7062</strong></td>
                    <td class="negative">+0.86% / -1.38%</td>
                    <td>5h14m<br><small>(1.0m - 73h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>29.36%</strong></td>
                    <td>64.9%</td>
                    <td>775</td>
                    <td>1.04</td>
                    <td class="negative">37.35%</td>
                    <td class="positive"><strong>0.7582</strong></td>
                    <td class="negative">+0.83% / -1.45%</td>
                    <td>3h51m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>13.53%</strong></td>
                    <td>63.6%</td>
                    <td>165</td>
                    <td>1.08</td>
                    <td class="negative">20.69%</td>
                    <td class="positive"><strong>0.6078</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>3h21m<br><small>(1.0m - 26h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>12.56%</strong></td>
                    <td>67.9%</td>
                    <td>28</td>
                    <td>1.60</td>
                    <td class="negative">5.21%</td>
                    <td class="positive"><strong>0.5375</strong></td>
                    <td class="negative">+0.86% / -1.36%</td>
                    <td>8h7m<br><small>(5.0m - 46h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>7.73%</strong></td>
                    <td>65.8%</td>
                    <td>219</td>
                    <td>1.03</td>
                    <td class="negative">35.74%</td>
                    <td class="positive"><strong>0.5837</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>3h13m<br><small>(1.0m - 49h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>5.39%</strong></td>
                    <td>63.1%</td>
                    <td>122</td>
                    <td>1.04</td>
                    <td class="negative">15.75%</td>
                    <td class="positive"><strong>0.5530</strong></td>
                    <td class="negative">+0.86% / -1.38%</td>
                    <td>3h41m<br><small>(1.0m - 26h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>4.41%</strong></td>
                    <td>62.7%</td>
                    <td>276</td>
                    <td>1.02</td>
                    <td class="negative">24.34%</td>
                    <td class="positive"><strong>0.6161</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>4h55m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>3.02%</strong></td>
                    <td>75.0%</td>
                    <td>4</td>
                    <td>1.88</td>
                    <td class="neutral">3.29%</td>
                    <td class="positive"><strong>0.3707</strong></td>
                    <td class="negative">+0.81% / -1.28%</td>
                    <td>3h19m<br><small>(16.0m - 8h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>1.27%</strong></td>
                    <td>67.9%</td>
                    <td>28</td>
                    <td>1.04</td>
                    <td class="negative">7.39%</td>
                    <td class="positive"><strong>0.4127</strong></td>
                    <td class="negative">+0.82% / -1.39%</td>
                    <td>3h44m<br><small>(4.0m - 16h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>0.51%</strong></td>
                    <td>62.0%</td>
                    <td>50</td>
                    <td>1.01</td>
                    <td class="negative">14.63%</td>
                    <td class="positive"><strong>0.4471</strong></td>
                    <td class="negative">+0.87% / -1.37%</td>
                    <td>3h43m<br><small>(3.0m - 27h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="negative"><strong>-0.67%</strong></td>
                    <td>50.0%</td>
                    <td>2</td>
                    <td>0.73</td>
                    <td class="neutral">2.45%</td>
                    <td class="positive"><strong>0.1483</strong></td>
                    <td class="positive">+1.90% / -1.43%</td>
                    <td>5.5m<br><small>(1.0m - 10.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-1.22%</strong></td>
                    <td>62.7%</td>
                    <td>153</td>
                    <td>0.99</td>
                    <td class="negative">22.89%</td>
                    <td class="positive"><strong>0.5324</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>3h32m<br><small>(1.0m - 48h23m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="negative"><strong>-4.55%</strong></td>
                    <td>56.2%</td>
                    <td>32</td>
                    <td>0.87</td>
                    <td class="negative">16.47%</td>
                    <td class="positive"><strong>0.3454</strong></td>
                    <td class="negative">+0.86% / -1.36%</td>
                    <td>3h59m<br><small>(2.0m - 53h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-8.75%</strong></td>
                    <td>63.7%</td>
                    <td>673</td>
                    <td>0.99</td>
                    <td class="negative">39.32%</td>
                    <td class="positive"><strong>0.6342</strong></td>
                    <td class="negative">+0.83% / -1.44%</td>
                    <td>4h6m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-8.84%</strong></td>
                    <td>60.7%</td>
                    <td>61</td>
                    <td>0.87</td>
                    <td class="negative">24.22%</td>
                    <td class="positive"><strong>0.3920</strong></td>
                    <td class="negative">+0.87% / -1.40%</td>
                    <td>4h32m<br><small>(3.0m - 21h52m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-15.07%</strong></td>
                    <td>62.3%</td>
                    <td>154</td>
                    <td>0.91</td>
                    <td class="negative">30.71%</td>
                    <td class="positive"><strong>0.4637</strong></td>
                    <td class="negative">+0.83% / -1.45%</td>
                    <td>3h52m<br><small>(3.0m - 119h34m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="negative"><strong>-16.70%</strong></td>
                    <td>59.4%</td>
                    <td>138</td>
                    <td>0.89</td>
                    <td class="negative">24.40%</td>
                    <td class="positive"><strong>0.4571</strong></td>
                    <td class="negative">+0.84% / -1.37%</td>
                    <td>4h51m<br><small>(1.0m - 53h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="negative"><strong>-35.46%</strong></td>
                    <td>62.3%</td>
                    <td>475</td>
                    <td>0.93</td>
                    <td class="negative">56.12%</td>
                    <td class="positive"><strong>0.4943</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>3h33m<br><small>(1.0m - 49h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-56.33%</strong></td>
                    <td>62.4%</td>
                    <td>991</td>
                    <td>0.94</td>
                    <td class="negative">82.68%</td>
                    <td class="positive"><strong>0.4825</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>4h40m<br><small>(1.0m - 119h34m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>83.8%</strong></td>
                    <td class="positive">38.67%</td>
                    <td>37</td>
                    <td>3.09</td>
                    <td class="neutral">4.93%</td>
                    <td class="positive"><strong>0.8015</strong></td>
                    <td class="negative">+0.88% / -1.31%</td>
                    <td>3h50m<br><small>(4.0m - 17h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">3.02%</td>
                    <td>4</td>
                    <td>1.88</td>
                    <td class="neutral">3.29%</td>
                    <td class="positive"><strong>0.3707</strong></td>
                    <td class="negative">+0.81% / -1.28%</td>
                    <td>3h19m<br><small>(16.0m - 8h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>68.2%</strong></td>
                    <td class="positive">31.76%</td>
                    <td>107</td>
                    <td>1.36</td>
                    <td class="negative">26.88%</td>
                    <td class="positive"><strong>0.6546</strong></td>
                    <td class="negative">+0.86% / -1.36%</td>
                    <td>4h33m<br><small>(8.0m - 26h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>67.9%</strong></td>
                    <td class="positive">12.56%</td>
                    <td>28</td>
                    <td>1.60</td>
                    <td class="negative">5.21%</td>
                    <td class="positive"><strong>0.5375</strong></td>
                    <td class="negative">+0.86% / -1.36%</td>
                    <td>8h7m<br><small>(5.0m - 46h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>67.9%</strong></td>
                    <td class="positive">1.27%</td>
                    <td>28</td>
                    <td>1.04</td>
                    <td class="negative">7.39%</td>
                    <td class="positive"><strong>0.4127</strong></td>
                    <td class="negative">+0.82% / -1.39%</td>
                    <td>3h44m<br><small>(4.0m - 16h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>66.8%</strong></td>
                    <td class="positive">31.27%</td>
                    <td>196</td>
                    <td>1.18</td>
                    <td class="negative">10.25%</td>
                    <td class="positive"><strong>0.7062</strong></td>
                    <td class="negative">+0.86% / -1.38%</td>
                    <td>5h14m<br><small>(1.0m - 73h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>66.1%</strong></td>
                    <td class="positive">71.94%</td>
                    <td>307</td>
                    <td>1.28</td>
                    <td class="negative">9.31%</td>
                    <td class="positive"><strong>0.8741</strong></td>
                    <td class="negative">+0.89% / -1.34%</td>
                    <td>6h16m<br><small>(1.0m - 78h15m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>65.8%</strong></td>
                    <td class="positive">7.73%</td>
                    <td>219</td>
                    <td>1.03</td>
                    <td class="negative">35.74%</td>
                    <td class="positive"><strong>0.5837</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>3h13m<br><small>(1.0m - 49h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>65.4%</strong></td>
                    <td class="positive">76.83%</td>
                    <td>587</td>
                    <td>1.14</td>
                    <td class="negative">24.06%</td>
                    <td class="positive"><strong>0.8998</strong></td>
                    <td class="negative">+0.85% / -1.37%</td>
                    <td>4h34m<br><small>(1.0m - 55h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>64.9%</strong></td>
                    <td class="positive">29.36%</td>
                    <td>775</td>
                    <td>1.04</td>
                    <td class="negative">37.35%</td>
                    <td class="positive"><strong>0.7582</strong></td>
                    <td class="negative">+0.83% / -1.45%</td>
                    <td>3h51m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>64.7%</strong></td>
                    <td class="positive">32.76%</td>
                    <td>1046</td>
                    <td>1.03</td>
                    <td class="negative">41.48%</td>
                    <td class="positive"><strong>0.7908</strong></td>
                    <td class="negative">+0.83% / -1.43%</td>
                    <td>3h55m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>64.4%</strong></td>
                    <td class="positive">41.71%</td>
                    <td>542</td>
                    <td>1.08</td>
                    <td class="negative">23.69%</td>
                    <td class="positive"><strong>0.7909</strong></td>
                    <td class="negative">+0.87% / -1.38%</td>
                    <td>3h18m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>64.0%</strong></td>
                    <td class="positive">46.05%</td>
                    <td>702</td>
                    <td>1.07</td>
                    <td class="negative">27.48%</td>
                    <td class="positive"><strong>0.8156</strong></td>
                    <td class="negative">+0.86% / -1.42%</td>
                    <td>4h27m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>63.7%</strong></td>
                    <td class="negative">-8.75%</td>
                    <td>673</td>
                    <td>0.99</td>
                    <td class="negative">39.32%</td>
                    <td class="positive"><strong>0.6342</strong></td>
                    <td class="negative">+0.83% / -1.44%</td>
                    <td>4h6m<br><small>(1.0m - 105h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>63.6%</strong></td>
                    <td class="positive">13.53%</td>
                    <td>165</td>
                    <td>1.08</td>
                    <td class="negative">20.69%</td>
                    <td class="positive"><strong>0.6078</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>3h21m<br><small>(1.0m - 26h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>63.1%</strong></td>
                    <td class="positive">5.39%</td>
                    <td>122</td>
                    <td>1.04</td>
                    <td class="negative">15.75%</td>
                    <td class="positive"><strong>0.5530</strong></td>
                    <td class="negative">+0.86% / -1.38%</td>
                    <td>3h41m<br><small>(1.0m - 26h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>62.7%</strong></td>
                    <td class="negative">-1.22%</td>
                    <td>153</td>
                    <td>0.99</td>
                    <td class="negative">22.89%</td>
                    <td class="positive"><strong>0.5324</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>3h32m<br><small>(1.0m - 48h23m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>62.7%</strong></td>
                    <td class="positive">4.41%</td>
                    <td>276</td>
                    <td>1.02</td>
                    <td class="negative">24.34%</td>
                    <td class="positive"><strong>0.6161</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>4h55m<br><small>(1.0m - 60h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>62.4%</strong></td>
                    <td class="negative">-56.33%</td>
                    <td>991</td>
                    <td>0.94</td>
                    <td class="negative">82.68%</td>
                    <td class="positive"><strong>0.4825</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>4h40m<br><small>(1.0m - 119h34m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>62.3%</strong></td>
                    <td class="negative">-15.07%</td>
                    <td>154</td>
                    <td>0.91</td>
                    <td class="negative">30.71%</td>
                    <td class="positive"><strong>0.4637</strong></td>
                    <td class="negative">+0.83% / -1.45%</td>
                    <td>3h52m<br><small>(3.0m - 119h34m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>62.3%</strong></td>
                    <td class="negative">-35.46%</td>
                    <td>475</td>
                    <td>0.93</td>
                    <td class="negative">56.12%</td>
                    <td class="positive"><strong>0.4943</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>3h33m<br><small>(1.0m - 49h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>62.0%</strong></td>
                    <td class="positive">0.51%</td>
                    <td>50</td>
                    <td>1.01</td>
                    <td class="negative">14.63%</td>
                    <td class="positive"><strong>0.4471</strong></td>
                    <td class="negative">+0.87% / -1.37%</td>
                    <td>3h43m<br><small>(3.0m - 27h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>60.7%</strong></td>
                    <td class="negative">-8.84%</td>
                    <td>61</td>
                    <td>0.87</td>
                    <td class="negative">24.22%</td>
                    <td class="positive"><strong>0.3920</strong></td>
                    <td class="negative">+0.87% / -1.40%</td>
                    <td>4h32m<br><small>(3.0m - 21h52m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>59.4%</strong></td>
                    <td class="negative">-16.70%</td>
                    <td>138</td>
                    <td>0.89</td>
                    <td class="negative">24.40%</td>
                    <td class="positive"><strong>0.4571</strong></td>
                    <td class="negative">+0.84% / -1.37%</td>
                    <td>4h51m<br><small>(1.0m - 53h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>56.2%</strong></td>
                    <td class="negative">-4.55%</td>
                    <td>32</td>
                    <td>0.87</td>
                    <td class="negative">16.47%</td>
                    <td class="positive"><strong>0.3454</strong></td>
                    <td class="negative">+0.86% / -1.36%</td>
                    <td>3h59m<br><small>(2.0m - 53h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-0.67%</td>
                    <td>2</td>
                    <td>0.73</td>
                    <td class="neutral">2.45%</td>
                    <td class="positive"><strong>0.1483</strong></td>
                    <td class="positive">+1.90% / -1.43%</td>
                    <td>5.5m<br><small>(1.0m - 10.0m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Rule 10: Volume Spike', 'Ext Rule 5: ATR Volatility Exp...', 'Acad Rule 3: Volatility Breako...', 'Rule 27: Structure Break Up', 'Ext Rule 3: Bollinger Squeeze ...', 'Professional Rule 7: Chaikin M...', 'AI Rule 10: Composite Sentimen...', 'Acad Rule 2: Mean Reversion Fa...', 'Volume Rule 4: Volume Breakout...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 2: Golden Cross', 'Rule 28: Volume Breakout', 'Volatility Rule 2: ATR Expansi...', 'Rule 6: Stochastic Oversold Cr...', 'Rule 7: Bollinger Band Bounce'],
                y: [65.41737649063032, 66.12377850162866, 63.960113960113965, 64.39114391143912, 68.22429906542055, 66.83673469387756, 64.72275334608031, 64.90322580645162, 63.63636363636363, 65.75342465753424, 83.78378378378379, 63.114754098360656, 62.68115942028986, 62.745098039215684, 63.74442793462109],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Rule 10: Volume Spike', 'Ext Rule 5: ATR Volatility Exp...', 'Acad Rule 3: Volatility Breako...', 'Rule 27: Structure Break Up', 'Ext Rule 3: Bollinger Squeeze ...', 'Professional Rule 7: Chaikin M...', 'AI Rule 10: Composite Sentimen...', 'Acad Rule 2: Mean Reversion Fa...', 'Volume Rule 4: Volume Breakout...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 2: Golden Cross', 'Rule 28: Volume Breakout', 'Volatility Rule 2: ATR Expansi...', 'Rule 6: Stochastic Oversold Cr...', 'Rule 7: Bollinger Band Bounce'],
                y: [np.float64(76.82959728738268), np.float64(71.940273251672), np.float64(46.05195943249707), np.float64(41.714684526271725), np.float64(31.755023264410948), np.float64(31.27464483948937), np.float64(32.76125080498738), np.float64(29.357521424543652), np.float64(13.53109003577492), np.float64(7.726944959600209), np.float64(38.66795929506372), np.float64(5.390060215865466), np.float64(4.41061194190933), np.float64(-1.2219784042689426), np.float64(-8.7457773564097)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
