
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 01:15:35 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">-45.40%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">8,010</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">61.6%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.64</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>15.15%</strong></td>
                    <td>63.3%</td>
                    <td>785</td>
                    <td>1.04</td>
                    <td class="negative">28.45%</td>
                    <td class="positive"><strong>0.7370</strong></td>
                    <td class="negative">+0.91% / -1.50%</td>
                    <td>1h32m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>13.67%</strong></td>
                    <td>64.3%</td>
                    <td>558</td>
                    <td>1.05</td>
                    <td class="negative">14.98%</td>
                    <td class="positive"><strong>0.7241</strong></td>
                    <td class="negative">+0.87% / -1.45%</td>
                    <td>1h37m<br><small>(1.0m - 17h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>12.11%</strong></td>
                    <td>64.8%</td>
                    <td>145</td>
                    <td>1.17</td>
                    <td class="negative">8.02%</td>
                    <td class="positive"><strong>0.6238</strong></td>
                    <td class="negative">+0.91% / -1.45%</td>
                    <td>1h48m<br><small>(1.0m - 11h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>11.85%</strong></td>
                    <td>62.9%</td>
                    <td>1106</td>
                    <td>1.02</td>
                    <td class="negative">33.34%</td>
                    <td class="positive"><strong>0.7535</strong></td>
                    <td class="negative">+0.90% / -1.47%</td>
                    <td>1h33m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>10.05%</strong></td>
                    <td>68.4%</td>
                    <td>79</td>
                    <td>1.29</td>
                    <td class="neutral">4.80%</td>
                    <td class="positive"><strong>0.5855</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>2h0m<br><small>(1.0m - 13h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>3.80%</strong></td>
                    <td>66.7%</td>
                    <td>24</td>
                    <td>1.39</td>
                    <td class="neutral">3.71%</td>
                    <td class="positive"><strong>0.4825</strong></td>
                    <td class="negative">+1.02% / -1.41%</td>
                    <td>37.1m<br><small>(2.0m - 4h38m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>3.18%</strong></td>
                    <td>64.7%</td>
                    <td>34</td>
                    <td>1.20</td>
                    <td class="neutral">4.39%</td>
                    <td class="positive"><strong>0.4724</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>2h41m<br><small>(2.0m - 12h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>2.82%</strong></td>
                    <td>62.4%</td>
                    <td>229</td>
                    <td>1.02</td>
                    <td class="negative">13.17%</td>
                    <td class="positive"><strong>0.6090</strong></td>
                    <td class="negative">+0.88% / -1.40%</td>
                    <td>2h0m<br><small>(1.0m - 16h52m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>2.63%</strong></td>
                    <td>62.1%</td>
                    <td>729</td>
                    <td>1.01</td>
                    <td class="negative">25.54%</td>
                    <td class="positive"><strong>0.6973</strong></td>
                    <td class="negative">+0.91% / -1.47%</td>
                    <td>1h42m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>1.91%</strong></td>
                    <td>64.7%</td>
                    <td>17</td>
                    <td>1.24</td>
                    <td class="neutral">2.75%</td>
                    <td class="positive"><strong>0.4007</strong></td>
                    <td class="negative">+0.92% / -1.48%</td>
                    <td>3h6m<br><small>(9.0m - 17h5m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="positive"><strong>1.76%</strong></td>
                    <td>100.0%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.33% / 0.00%</td>
                    <td>3.0m<br><small>(1.0m - 5.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>1.53%</strong></td>
                    <td>100.0%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.81% / 0.00%</td>
                    <td>1h37m<br><small>(22.0m - 2h52m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>1.21%</strong></td>
                    <td>61.4%</td>
                    <td>233</td>
                    <td>1.01</td>
                    <td class="negative">14.75%</td>
                    <td class="positive"><strong>0.6027</strong></td>
                    <td class="negative">+0.89% / -1.37%</td>
                    <td>2h10m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-0.47%</strong></td>
                    <td>62.2%</td>
                    <td>944</td>
                    <td>1.00</td>
                    <td class="negative">30.67%</td>
                    <td class="positive"><strong>0.7082</strong></td>
                    <td class="negative">+0.86% / -1.40%</td>
                    <td>2h9m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="negative"><strong>-1.03%</strong></td>
                    <td>61.8%</td>
                    <td>89</td>
                    <td>0.98</td>
                    <td class="negative">10.95%</td>
                    <td class="positive"><strong>0.5002</strong></td>
                    <td class="negative">+0.92% / -1.40%</td>
                    <td>2h40m<br><small>(1.0m - 19h29m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-3.82%</strong></td>
                    <td>56.5%</td>
                    <td>69</td>
                    <td>0.90</td>
                    <td class="negative">12.73%</td>
                    <td class="positive"><strong>0.4390</strong></td>
                    <td class="negative">+0.89% / -1.37%</td>
                    <td>1h43m<br><small>(1.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="negative"><strong>-3.98%</strong></td>
                    <td>58.1%</td>
                    <td>43</td>
                    <td>0.84</td>
                    <td class="negative">7.45%</td>
                    <td class="positive"><strong>0.3812</strong></td>
                    <td class="negative">+0.93% / -1.48%</td>
                    <td>1h4m<br><small>(1.0m - 6h22m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-4.21%</strong></td>
                    <td>59.9%</td>
                    <td>157</td>
                    <td>0.95</td>
                    <td class="negative">17.54%</td>
                    <td class="positive"><strong>0.5206</strong></td>
                    <td class="negative">+0.91% / -1.49%</td>
                    <td>1h33m<br><small>(1.0m - 13h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="negative"><strong>-4.35%</strong></td>
                    <td>60.7%</td>
                    <td>163</td>
                    <td>0.95</td>
                    <td class="negative">12.22%</td>
                    <td class="positive"><strong>0.5384</strong></td>
                    <td class="negative">+0.87% / -1.40%</td>
                    <td>2h12m<br><small>(1.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="negative"><strong>-4.39%</strong></td>
                    <td>61.6%</td>
                    <td>232</td>
                    <td>0.96</td>
                    <td class="negative">12.82%</td>
                    <td class="positive"><strong>0.5759</strong></td>
                    <td class="negative">+0.90% / -1.44%</td>
                    <td>1h45m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="negative"><strong>-4.42%</strong></td>
                    <td>60.0%</td>
                    <td>160</td>
                    <td>0.95</td>
                    <td class="negative">12.65%</td>
                    <td class="positive"><strong>0.5361</strong></td>
                    <td class="negative">+0.90% / -1.41%</td>
                    <td>2h5m<br><small>(1.0m - 15h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="negative"><strong>-4.52%</strong></td>
                    <td>58.9%</td>
                    <td>141</td>
                    <td>0.94</td>
                    <td class="negative">15.39%</td>
                    <td class="positive"><strong>0.5210</strong></td>
                    <td class="negative">+0.90% / -1.36%</td>
                    <td>2h31m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-5.22%</strong></td>
                    <td>58.3%</td>
                    <td>36</td>
                    <td>0.77</td>
                    <td class="negative">12.79%</td>
                    <td class="positive"><strong>0.3456</strong></td>
                    <td class="negative">+0.93% / -1.38%</td>
                    <td>2h31m<br><small>(2.0m - 24h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="negative"><strong>-5.81%</strong></td>
                    <td>61.4%</td>
                    <td>596</td>
                    <td>0.98</td>
                    <td class="negative">27.32%</td>
                    <td class="positive"><strong>0.6500</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h5m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="negative"><strong>-7.45%</strong></td>
                    <td>59.1%</td>
                    <td>225</td>
                    <td>0.94</td>
                    <td class="negative">16.72%</td>
                    <td class="positive"><strong>0.5568</strong></td>
                    <td class="negative">+0.93% / -1.39%</td>
                    <td>1h46m<br><small>(1.0m - 15h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="negative"><strong>-19.87%</strong></td>
                    <td>58.9%</td>
                    <td>616</td>
                    <td>0.94</td>
                    <td class="negative">27.10%</td>
                    <td class="positive"><strong>0.6094</strong></td>
                    <td class="negative">+0.91% / -1.39%</td>
                    <td>1h44m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-57.54%</strong></td>
                    <td>57.4%</td>
                    <td>596</td>
                    <td>0.83</td>
                    <td class="negative">60.71%</td>
                    <td class="positive"><strong>0.4280</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>1h53m<br><small>(1.0m - 18h23m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.76%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.33% / 0.00%</td>
                    <td>3.0m<br><small>(1.0m - 5.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.53%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.81% / 0.00%</td>
                    <td>1h37m<br><small>(22.0m - 2h52m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>68.4%</strong></td>
                    <td class="positive">10.05%</td>
                    <td>79</td>
                    <td>1.29</td>
                    <td class="neutral">4.80%</td>
                    <td class="positive"><strong>0.5855</strong></td>
                    <td class="negative">+0.86% / -1.44%</td>
                    <td>2h0m<br><small>(1.0m - 13h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">3.80%</td>
                    <td>24</td>
                    <td>1.39</td>
                    <td class="neutral">3.71%</td>
                    <td class="positive"><strong>0.4825</strong></td>
                    <td class="negative">+1.02% / -1.41%</td>
                    <td>37.1m<br><small>(2.0m - 4h38m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>64.8%</strong></td>
                    <td class="positive">12.11%</td>
                    <td>145</td>
                    <td>1.17</td>
                    <td class="negative">8.02%</td>
                    <td class="positive"><strong>0.6238</strong></td>
                    <td class="negative">+0.91% / -1.45%</td>
                    <td>1h48m<br><small>(1.0m - 11h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>64.7%</strong></td>
                    <td class="positive">3.18%</td>
                    <td>34</td>
                    <td>1.20</td>
                    <td class="neutral">4.39%</td>
                    <td class="positive"><strong>0.4724</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>2h41m<br><small>(2.0m - 12h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>64.7%</strong></td>
                    <td class="positive">1.91%</td>
                    <td>17</td>
                    <td>1.24</td>
                    <td class="neutral">2.75%</td>
                    <td class="positive"><strong>0.4007</strong></td>
                    <td class="negative">+0.92% / -1.48%</td>
                    <td>3h6m<br><small>(9.0m - 17h5m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>64.3%</strong></td>
                    <td class="positive">13.67%</td>
                    <td>558</td>
                    <td>1.05</td>
                    <td class="negative">14.98%</td>
                    <td class="positive"><strong>0.7241</strong></td>
                    <td class="negative">+0.87% / -1.45%</td>
                    <td>1h37m<br><small>(1.0m - 17h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>63.3%</strong></td>
                    <td class="positive">15.15%</td>
                    <td>785</td>
                    <td>1.04</td>
                    <td class="negative">28.45%</td>
                    <td class="positive"><strong>0.7370</strong></td>
                    <td class="negative">+0.91% / -1.50%</td>
                    <td>1h32m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>62.9%</strong></td>
                    <td class="positive">11.85%</td>
                    <td>1106</td>
                    <td>1.02</td>
                    <td class="negative">33.34%</td>
                    <td class="positive"><strong>0.7535</strong></td>
                    <td class="negative">+0.90% / -1.47%</td>
                    <td>1h33m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>62.4%</strong></td>
                    <td class="positive">2.82%</td>
                    <td>229</td>
                    <td>1.02</td>
                    <td class="negative">13.17%</td>
                    <td class="positive"><strong>0.6090</strong></td>
                    <td class="negative">+0.88% / -1.40%</td>
                    <td>2h0m<br><small>(1.0m - 16h52m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>62.2%</strong></td>
                    <td class="negative">-0.47%</td>
                    <td>944</td>
                    <td>1.00</td>
                    <td class="negative">30.67%</td>
                    <td class="positive"><strong>0.7082</strong></td>
                    <td class="negative">+0.86% / -1.40%</td>
                    <td>2h9m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>62.1%</strong></td>
                    <td class="positive">2.63%</td>
                    <td>729</td>
                    <td>1.01</td>
                    <td class="negative">25.54%</td>
                    <td class="positive"><strong>0.6973</strong></td>
                    <td class="negative">+0.91% / -1.47%</td>
                    <td>1h42m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>61.8%</strong></td>
                    <td class="negative">-1.03%</td>
                    <td>89</td>
                    <td>0.98</td>
                    <td class="negative">10.95%</td>
                    <td class="positive"><strong>0.5002</strong></td>
                    <td class="negative">+0.92% / -1.40%</td>
                    <td>2h40m<br><small>(1.0m - 19h29m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>61.6%</strong></td>
                    <td class="negative">-4.39%</td>
                    <td>232</td>
                    <td>0.96</td>
                    <td class="negative">12.82%</td>
                    <td class="positive"><strong>0.5759</strong></td>
                    <td class="negative">+0.90% / -1.44%</td>
                    <td>1h45m<br><small>(1.0m - 28h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>61.4%</strong></td>
                    <td class="negative">-5.81%</td>
                    <td>596</td>
                    <td>0.98</td>
                    <td class="negative">27.32%</td>
                    <td class="positive"><strong>0.6500</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h5m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>61.4%</strong></td>
                    <td class="positive">1.21%</td>
                    <td>233</td>
                    <td>1.01</td>
                    <td class="negative">14.75%</td>
                    <td class="positive"><strong>0.6027</strong></td>
                    <td class="negative">+0.89% / -1.37%</td>
                    <td>2h10m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>60.7%</strong></td>
                    <td class="negative">-4.35%</td>
                    <td>163</td>
                    <td>0.95</td>
                    <td class="negative">12.22%</td>
                    <td class="positive"><strong>0.5384</strong></td>
                    <td class="negative">+0.87% / -1.40%</td>
                    <td>2h12m<br><small>(1.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="negative">-4.42%</td>
                    <td>160</td>
                    <td>0.95</td>
                    <td class="negative">12.65%</td>
                    <td class="positive"><strong>0.5361</strong></td>
                    <td class="negative">+0.90% / -1.41%</td>
                    <td>2h5m<br><small>(1.0m - 15h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>59.9%</strong></td>
                    <td class="negative">-4.21%</td>
                    <td>157</td>
                    <td>0.95</td>
                    <td class="negative">17.54%</td>
                    <td class="positive"><strong>0.5206</strong></td>
                    <td class="negative">+0.91% / -1.49%</td>
                    <td>1h33m<br><small>(1.0m - 13h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>59.1%</strong></td>
                    <td class="negative">-7.45%</td>
                    <td>225</td>
                    <td>0.94</td>
                    <td class="negative">16.72%</td>
                    <td class="positive"><strong>0.5568</strong></td>
                    <td class="negative">+0.93% / -1.39%</td>
                    <td>1h46m<br><small>(1.0m - 15h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>58.9%</strong></td>
                    <td class="negative">-19.87%</td>
                    <td>616</td>
                    <td>0.94</td>
                    <td class="negative">27.10%</td>
                    <td class="positive"><strong>0.6094</strong></td>
                    <td class="negative">+0.91% / -1.39%</td>
                    <td>1h44m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>58.9%</strong></td>
                    <td class="negative">-4.52%</td>
                    <td>141</td>
                    <td>0.94</td>
                    <td class="negative">15.39%</td>
                    <td class="positive"><strong>0.5210</strong></td>
                    <td class="negative">+0.90% / -1.36%</td>
                    <td>2h31m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>58.3%</strong></td>
                    <td class="negative">-5.22%</td>
                    <td>36</td>
                    <td>0.77</td>
                    <td class="negative">12.79%</td>
                    <td class="positive"><strong>0.3456</strong></td>
                    <td class="negative">+0.93% / -1.38%</td>
                    <td>2h31m<br><small>(2.0m - 24h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>58.1%</strong></td>
                    <td class="negative">-3.98%</td>
                    <td>43</td>
                    <td>0.84</td>
                    <td class="negative">7.45%</td>
                    <td class="positive"><strong>0.3812</strong></td>
                    <td class="negative">+0.93% / -1.48%</td>
                    <td>1h4m<br><small>(1.0m - 6h22m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>57.4%</strong></td>
                    <td class="negative">-57.54%</td>
                    <td>596</td>
                    <td>0.83</td>
                    <td class="negative">60.71%</td>
                    <td class="positive"><strong>0.4280</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>1h53m<br><small>(1.0m - 18h23m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>56.5%</strong></td>
                    <td class="negative">-3.82%</td>
                    <td>69</td>
                    <td>0.90</td>
                    <td class="negative">12.73%</td>
                    <td class="positive"><strong>0.4390</strong></td>
                    <td class="negative">+0.89% / -1.37%</td>
                    <td>1h43m<br><small>(1.0m - 13h49m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Acad Rule 2: Mean Reversion Fa...', 'AI Rule 3: Smart Money Flow Di...', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 10: Composite Sentimen...', 'Professional Rule 7: Chaikin M...', 'Rule 7: Bollinger Band Bounce', 'Volatility Rule 2: ATR Expansi...', 'Ext Rule 6: Fibonacci Support ...', 'Volume Rule 5: Smart Money Vol...', 'Prof Rule 7: Mean Reversion Vo...', 'Price Action Rule 3: Engulfing...', 'AI Rule 8: Momentum Divergence...', 'Rule 28: Volume Breakout', 'Acad Rule 3: Volatility Breako...', 'Ext Rule 5: ATR Volatility Exp...'],
                y: [63.31210191082802, 64.33691756272401, 64.82758620689654, 62.92947558770343, 62.44541484716157, 62.139917695473244, 61.37339055793991, 62.182203389830505, 68.35443037974683, 61.63793103448276, 60.73619631901841, 59.87261146496815, 60.0, 61.40939597315436, 58.86524822695035],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Acad Rule 2: Mean Reversion Fa...', 'AI Rule 3: Smart Money Flow Di...', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 10: Composite Sentimen...', 'Professional Rule 7: Chaikin M...', 'Rule 7: Bollinger Band Bounce', 'Volatility Rule 2: ATR Expansi...', 'Ext Rule 6: Fibonacci Support ...', 'Volume Rule 5: Smart Money Vol...', 'Prof Rule 7: Mean Reversion Vo...', 'Price Action Rule 3: Engulfing...', 'AI Rule 8: Momentum Divergence...', 'Rule 28: Volume Breakout', 'Acad Rule 3: Volatility Breako...', 'Ext Rule 5: ATR Volatility Exp...'],
                y: [np.float64(15.145075345860626), np.float64(13.667620190409629), np.float64(12.111143298087294), np.float64(11.849334788563008), np.float64(2.817098491696146), np.float64(2.6319336923255032), np.float64(1.2109347761705576), np.float64(-0.4737334549724183), np.float64(10.04988755953715), np.float64(-4.386947562654211), np.float64(-4.3536924944935524), np.float64(-4.211560354747424), np.float64(-4.418276489016207), np.float64(-5.809759458629509), np.float64(-4.524013659574077)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
