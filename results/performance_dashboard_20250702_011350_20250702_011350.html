
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 14 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 01:13:50</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">14</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">53.8%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">27.7%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">66.8%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">66.3%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">4,751</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["66.8%","43.3%","42.5%","42.0%","34.7%","30.6%","22.4%","16.9%","15.0%","17.4%"],"textposition":"auto","x":["Ext Rule 5: ATR Volatility Expansion","Rule 10: Volume Spike","AI Rule 10: Composite Sentiment Reversal","Acad Rule 2: Mean Reversion Factor","Prof Rule 7: Mean Reversion Volatility Filter","Rule 27: Structure Break Up","Acad Rule 3: Volatility Breakout","Volume Rule 4: Volume Breakout Confirmation","Professional Rule 7: Chaikin Money Flow Reversal","Ext Rule 3: Bollinger Squeeze Breakout"],"y":[66.77601392348883,43.319791250666704,42.537075903287786,42.02822251730629,34.6686369422493,30.638940092930206,22.370324785890407,16.87193218875531,15.043124600192154,17.41429890000836],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Ext Rule 5: ATR Volatility Expansion","Rule 10: Volume Spike","AI Rule 10: Composite Sentiment Reversal","Acad Rule 2: Mean Reversion Factor","Prof Rule 7: Mean Reversion Volatility Filter","Rule 27: Structure Break Up","Acad Rule 3: Volatility Breakout","Volume Rule 4: Volume Breakout Confirmation","Professional Rule 7: Chaikin Money Flow Reversal","Ext Rule 3: Bollinger Squeeze Breakout"],"y":[197,357,607,455,135,334,415,103,124,63],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Ext Rule 5: ATR Volatility Expansion","Rule 10: Volume Spike","AI Rule 10: Composite Sentiment Reversal","Acad Rule 2: Mean Reversion Factor","Prof Rule 7: Mean Reversion Volatility Filter","Rule 27: Structure Break Up","Acad Rule 3: Volatility Breakout","Volume Rule 4: Volume Breakout Confirmation","Professional Rule 7: Chaikin Money Flow Reversal","Ext Rule 3: Bollinger Squeeze Breakout"],"y":[102,196,330,241,64,186,241,57,67,31],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[66.77601392348883,43.319791250666704,42.537075903287786,42.02822251730629,34.6686369422493,30.638940092930206,22.370324785890407,16.87193218875531,15.043124600192154,17.41429890000836,14.001188774160022,10.804137431649579,24.279605778318743,6.868684430789522],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Ext Rule 5","Rule 10","AI Rule 10","Acad Rule 2","Prof Rule 7","Rule 27","Acad Rule 3","Volume Rule 4","Professional Rule 7","Ext Rule 3","Volatility Rule 2","Rule 28","Rule 2","Volume Rule 3"],"textposition":"top center","x":[8.028371954621564,30.68617570264312,33.76036577758132,26.219840389706846,18.99274551323732,25.217288282506672,27.482509626665586,17.6974777681712,16.79025442231299,31.370160855256522,18.694753023976734,12.88492496256763,4.287317044141918,5.211907920675015],"y":[66.77601392348883,43.319791250666704,42.537075903287786,42.02822251730629,34.6686369422493,30.638940092930206,22.370324785890407,16.87193218875531,15.043124600192154,17.41429890000836,14.001188774160022,10.804137431649579,24.279605778318743,6.868684430789522],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["22.8%","27.3%","42.5%","32.2%","34.7%"],"textposition":"auto","x":["UNKNOWN","ORIGINAL","AI_GENERATED","ACADEMIC","PROFESSIONAL"],"y":[22.82920713623237,27.260618638391307,42.537075903287786,32.19927365159835,34.6686369422493],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["299","553","937","696","199","520","656","160","191","94"],"textposition":"auto","x":["Ext Rule 5: ATR Volatility Expansion","Rule 10: Volume Spike","AI Rule 10: Composite Sentiment Reversal","Acad Rule 2: Mean Reversion Factor","Prof Rule 7: Mean Reversion Volatility Filter","Rule 27: Structure Break Up","Acad Rule 3: Volatility Breakout","Volume Rule 4: Volume Breakout Confirmation","Professional Rule 7: Chaikin Money Flow Reversal","Ext Rule 3: Bollinger Squeeze Breakout"],"y":[299,553,937,696,199,520,656,160,191,94],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Ext Rule 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299],"y":[0,0.22333115024578204,0.4466623004915641,0.6699934507373462,0.8933246009831282,1.1166557512289101,1.3399869014746923,1.5633180517204743,1.7866492019662563,2.0099803522120383,2.2333115024578203,2.4566426527036027,2.6799738029493847,2.9033049531951667,3.1266361034409487,3.3499672536867307,3.5732984039325126,3.7966295541782946,4.019960704424077,4.243291854669859,4.466623004915641,4.6899541551614226,4.913285305407205,5.136616455652987,5.359947605898769,5.583278756144551,5.806609906390333,6.029941056636115,6.253272206881897,6.476603357127679,6.699934507373461,6.923265657619243,7.146596807865025,7.369927958110807,7.593259108356589,7.816590258602372,8.039921408848153,8.263252559093935,8.486583709339717,8.7099148595855,8.933246009831281,9.156577160077063,9.379908310322845,9.603239460568627,9.82657061081441,10.049901761060193,10.273232911305975,10.496564061551757,10.719895211797539,10.94322636204332,11.166557512289103,11.389888662534885,11.613219812780667,11.836550963026449,12.05988211327223,12.283213263518013,12.506544413763795,12.729875564009578,12.953206714255359,13.17653786450114,13.399869014746923,13.623200164992705,13.846531315238487,14.069862465484269,14.29319361573005,14.516524765975833,14.739855916221615,14.963187066467396,15.186518216713178,15.409849366958962,15.633180517204744,15.856511667450526,16.079842817696306,16.30317396794209,16.52650511818787,16.749836268433654,16.973167418679434,17.196498568925218,17.419829719171,17.643160869416782,17.866492019662562,18.089823169908346,18.313154320154126,18.53648547039991,18.75981662064569,18.983147770891474,19.206478921137254,19.429810071383038,19.65314122162882,19.876472371874605,20.099803522120386,20.32313467236617,20.54646582261195,20.76979697285773,20.993128123103514,21.216459273349294,21.439790423595078,21.663121573840858,21.88645272408664,22.10978387433242,22.333115024578206,22.556446174823986,22.77977732506977,23.00310847531555,23.226439625561333,23.449770775807114,23.673101926052897,23.896433076298678,24.11976422654446,24.34309537679024,24.566426527036025,24.789757677281806,25.01308882752759,25.236419977773373,25.459751128019157,25.683082278264937,25.906413428510717,26.1297445787565,26.35307572900228,26.576406879248065,26.799738029493845,27.02306917973963,27.24640032998541,27.469731480231193,27.693062630476973,27.916393780722757,28.139724930968537,28.36305608121432,28.5863872314601,28.809718381705885,29.033049531951665,29.25638068219745,29.47971183244323,29.703042982689013,29.926374132934793,30.149705283180577,30.373036433426357,30.59636758367214,30.819698733917924,31.043029884163705,31.26636103440949,31.48969218465527,31.713023334901052,31.936354485146833,32.15968563539261,32.3830167856384,32.60634793588418,32.829679086129964,33.05301023637574,33.276341386621525,33.49967253686731,33.72300368711309,33.94633483735887,34.16966598760465,34.392997137850436,34.61632828809622,34.839659438342,35.06299058858778,35.286321738833564,35.50965288907935,35.732984039325125,35.95631518957091,36.17964633981669,36.40297749006247,36.62630864030825,36.849639790554036,37.07297094079982,37.2963020910456,37.51963324129138,37.742964391537164,37.96629554178295,38.189626692028725,38.41295784227451,38.63628899252029,38.859620142766076,39.08295129301186,39.30628244325764,39.52961359350343,39.75294474374921,39.97627589399499,40.19960704424077,40.422938194486555,40.64626934473234,40.869600494978116,41.0929316452239,41.31626279546968,41.53959394571546,41.76292509596124,41.98625624620703,42.20958739645281,42.43291854669859,42.65624969694437,42.879580847190155,43.10291199743594,43.326243147681716,43.5495742979275,43.77290544817328,43.99623659841907,44.21956774866484,44.44289889891063,44.66623004915641,44.889561199402195,45.11289234964797,45.336223499893755,45.55955465013954,45.78288580038532,46.0062169506311,46.22954810087688,46.45287925112267,46.676210401368444,46.89954155161423,47.12287270186001,47.346203852105795,47.56953500235157,47.792866152597355,48.01619730284314,48.23952845308892,48.4628596033347,48.68619075358048,48.90952190382627,49.13285305407205,49.35618420431783,49.57951535456361,49.802846504809395,50.02617765505518,50.24950880530096,50.472839955546746,50.69617110579253,50.919502256038314,51.14283340628409,51.366164556529874,51.58949570677566,51.812826857021435,52.03615800726722,52.259489157513,52.482820307758786,52.70615145800456,52.929482608250346,53.15281375849613,53.376144908741914,53.59947605898769,53.822807209233474,54.04613835947926,54.26946950972504,54.49280065997082,54.7161318102166,54.939462960462386,55.16279411070817,55.386125260953946,55.60945641119973,55.832787561445514,56.0561187116913,56.279449861937074,56.50278101218286,56.72611216242864,56.94944331267442,57.1727744629202,57.396105613165986,57.61943676341177,57.842767913657546,58.06609906390333,58.289430214149114,58.5127613643949,58.736092514640674,58.95942366488646,59.18275481513224,59.406085965378026,59.6294171156238,59.852748265869586,60.07607941611537,60.29941056636115,60.52274171660693,60.746072866852714,60.9694040170985,61.19273516734428,61.416066317590065,61.63939746783585,61.86272861808163,62.08605976832741,62.30939091857319,62.53272206881898,62.75605321906476,62.97938436931054,63.20271551955632,63.426046669802105,63.64937782004789,63.872708970293665,64.09604012053946,64.31937127078523,64.54270242103101,64.7660335712768,64.98936472152258,65.21269587176836,65.43602702201414,65.65935817225993,65.88268932250571,66.10602047275148,66.32935162299727,66.55268277324305,66.77601392348883],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553],"y":[0,0.07833596971187469,0.15667193942374938,0.23500790913562408,0.31334387884749876,0.3916798485593735,0.47001581827124816,0.5483517879831228,0.6266877576949975,0.7050237274068722,0.783359697118747,0.8616956668306216,0.9400316365424963,1.018367606254371,1.0967035759662456,1.1750395456781204,1.253375515389995,1.3317114851018697,1.4100474548137445,1.4883834245256191,1.566719394237494,1.6450553639493686,1.7233913336612432,1.8017273033731178,1.8800632730849927,1.9583992427968673,2.036735212508742,2.1150711822206167,2.193407151932491,2.271743121644366,2.350079091356241,2.428415061068115,2.50675103077999,2.585087000491865,2.6634229702037393,2.741758939915614,2.820094909627489,2.898430879339364,2.9767668490512382,3.055102818763113,3.133438788474988,3.2117747581868623,3.290110727898737,3.3684466976106116,3.4467826673224864,3.5251186370343612,3.6034546067462356,3.6817905764581105,3.7601265461699853,3.8384625158818597,3.9167984855937346,3.9951344553056094,4.073470425017484,4.151806394729358,4.2301423644412335,4.308478334153108,4.386814303864982,4.465150273576858,4.543486243288732,4.621822213000606,4.700158182712482,4.778494152424356,4.85683012213623,4.935166091848106,5.01350206155998,5.0918380312718545,5.17017400098373,5.248509970695604,5.326845940407479,5.405181910119353,5.483517879831228,5.561853849543103,5.640189819254978,5.718525788966852,5.796861758678728,5.875197728390601,5.9535336981024765,6.031869667814351,6.110205637526226,6.1885416072381005,6.266877576949976,6.345213546661849,6.423549516373725,6.501885486085599,6.580221455797474,6.658557425509349,6.736893395221223,6.8152293649330975,6.893565334644973,6.971901304356847,7.0502372740687225,7.128573243780596,7.206909213492471,7.285245183204346,7.363581152916221,7.441917122628095,7.520253092339971,7.598589062051844,7.676925031763719,7.755261001475594,7.833596971187469,7.911932940899344,7.990268910611219,8.068604880323093,8.146940850034968,8.225276819746844,8.303612789458716,8.381948759170593,8.460284728882467,8.538620698594341,8.616956668306216,8.695292638018092,8.773628607729965,8.85196457744184,8.930300547153715,9.00863651686559,9.086972486577464,9.165308456289338,9.243644426001213,9.321980395713089,9.400316365424963,9.478652335136838,9.556988304848712,9.635324274560586,9.71366024427246,9.791996213984337,9.870332183696211,9.948668153408086,10.02700412311996,10.105340092831835,10.183676062543709,10.262012032255585,10.34034800196746,10.418683971679334,10.497019941391208,10.575355911103083,10.653691880814957,10.732027850526833,10.810363820238706,10.88869978995058,10.967035759662457,11.045371729374331,11.123707699086205,11.20204366879808,11.280379638509956,11.35871560822183,11.437051577933705,11.51538754764558,11.593723517357455,11.67205948706933,11.750395456781202,11.82873142649308,11.907067396204953,11.985403365916827,12.063739335628702,12.142075305340578,12.220411275052452,12.298747244764327,12.377083214476201,12.455419184188077,12.533755153899952,12.612091123611824,12.690427093323699,12.768763063035575,12.84709903274745,12.925435002459324,13.003770972171198,13.082106941883074,13.160442911594949,13.238778881306823,13.317114851018697,13.395450820730574,13.473786790442446,13.55212276015432,13.630458729866195,13.708794699578071,13.787130669289946,13.86546663900182,13.943802608713694,14.02213857842557,14.100474548137445,14.17881051784932,14.257146487561192,14.33548245727307,14.413818426984943,14.492154396696817,14.570490366408691,14.648826336120568,14.727162305832442,14.805498275544316,14.88383424525619,14.962170214968067,15.040506184679941,15.118842154391814,15.197178124103688,15.275514093815564,15.353850063527439,15.432186033239313,15.510522002951188,15.588857972663064,15.667193942374938,15.745529912086813,15.823865881798689,15.902201851510563,15.980537821222438,16.05887379093431,16.137209760646186,16.215545730358063,16.293881700069935,16.37221766978181,16.450553639493688,16.52888960920556,16.607225578917433,16.68556154862931,16.763897518341185,16.842233488053058,16.920569457764934,16.998905427476807,17.077241397188683,17.15557736690056,17.23391333661243,17.312249306324304,17.390585276036184,17.468921245748056,17.54725721545993,17.625593185171805,17.70392915488368,17.782265124595554,17.86060109430743,17.938937064019303,18.01727303373118,18.095609003443055,18.173944973154928,18.2522809428668,18.330616912578677,18.408952882290553,18.487288852002425,18.5656248217143,18.643960791426178,18.72229676113805,18.800632730849927,18.8789687005618,18.957304670273675,19.03564063998555,19.113976609697424,19.192312579409297,19.270648549121173,19.34898451883305,19.42732048854492,19.505656458256798,19.583992427968674,19.662328397680547,19.740664367392423,19.8190003371043,19.89733630681617,19.975672276528044,20.05400824623992,20.132344215951797,20.21068018566367,20.289016155375545,20.367352125087418,20.445688094799294,20.52402406451117,20.602360034223043,20.68069600393492,20.759031973646795,20.837367943358668,20.91570391307054,20.994039882782417,21.072375852494293,21.150711822206166,21.229047791918042,21.307383761629914,21.38571973134179,21.464055701053667,21.54239167076554,21.620727640477412,21.69906361018929,21.77739957990116,21.855735549613037,21.934071519324913,22.012407489036786,22.090743458748662,22.169079428460538,22.24741539817241,22.325751367884287,22.40408733759616,22.482423307308036,22.560759277019912,22.639095246731785,22.71743121644366,22.795767186155537,22.87410315586741,22.952439125579286,23.03077509529116,23.109111065003034,23.18744703471491,23.26578300442678,23.34411897413866,23.422454943850536,23.500790913562405,23.57912688327428,23.65746285298616,23.73579882269803,23.814134792409906,23.89247076212178,23.970806731833655,24.04914270154553,24.127478671257403,24.20581464096928,24.284150610681156,24.36248658039303,24.440822550104905,24.519158519816777,24.597494489528653,24.67583045924053,24.754166428952402,24.83250239866428,24.910838368376155,24.989174338088027,25.067510307799903,25.145846277511772,25.22418224722365,25.30251821693553,25.380854186647397,25.459190156359274,25.53752612607115,25.615862095783022,25.6941980654949,25.77253403520677,25.850870004918647,25.929205974630523,26.007541944342396,26.085877914054272,26.16421388376615,26.24254985347802,26.320885823189897,26.39922179290177,26.477557762613646,26.555893732325522,26.634229702037395,26.71256567174927,26.790901641461147,26.869237611173016,26.947573580884892,27.025909550596765,27.10424552030864,27.182581490020517,27.26091745973239,27.339253429444266,27.417589399156142,27.495925368868015,27.57426133857989,27.652597308291767,27.73093327800364,27.809269247715516,27.88760521742739,27.965941187139265,28.04427715685114,28.122613126563014,28.20094909627489,28.279285065986766,28.35762103569864,28.435957005410515,28.514292975122384,28.59262894483426,28.67096491454614,28.74930088425801,28.827636853969885,28.90597282368176,28.984308793393634,29.06264476310551,29.140980732817383,29.21931670252926,29.297652672241135,29.375988641953008,29.454324611664884,29.53266058137676,29.610996551088633,29.68933252080051,29.76766849051238,29.846004460224258,29.924340429936134,30.002676399648006,30.081012369359883,30.15934833907176,30.237684308783628,30.316020278495508,30.394356248207377,30.472692217919253,30.55102818763113,30.629364157343,30.707700127054878,30.786036096766754,30.864372066478627,30.942708036190503,31.021044005902375,31.09937997561425,31.177715945326128,31.256051915038,31.334387884749876,31.412723854461753,31.491059824173625,31.5693957938855,31.647731763597378,31.72606773330925,31.804403703021126,31.882739672732995,31.961075642444875,32.03941161215675,32.11774758186862,32.1960835515805,32.27441952129237,32.352755491004245,32.431091460716125,32.509427430428,32.58776340013987,32.66609936985175,32.74443533956362,32.822771309275495,32.901107278987375,32.97944324869924,33.05777921841112,33.13611518812299,33.214451157834866,33.292787127546745,33.37112309725862,33.44945906697049,33.52779503668237,33.60613100639424,33.684466976106116,33.76280294581799,33.84113891552987,33.91947488524174,33.99781085495361,34.07614682466549,34.154482794377365,34.23281876408924,34.31115473380112,34.38949070351298,34.46782667322486,34.54616264293674,34.62449861264861,34.70283458236049,34.78117055207237,34.85950652178423,34.93784249149611,35.016178461207986,35.09451443091986,35.17285040063174,35.25118637034361,35.32952234005548,35.40785830976736,35.486194279479236,35.56453024919111,35.64286621890299,35.72120218861486,35.79953815832673,35.877874128038606,35.956210097750485,36.03454606746236,36.11288203717423,36.19121800688611,36.26955397659798,36.347889946309856,36.426225916021735,36.5045618857336,36.58289785544548,36.66123382515735,36.739569794869226,36.817905764581106,36.89624173429298,36.97457770400485,37.05291367371673,37.1312496434286,37.209585613140476,37.287921582852356,37.36625755256423,37.4445935222761,37.52292949198798,37.60126546169985,37.679601431411726,37.7579374011236,37.83627337083548,37.91460934054735,37.99294531025922,38.0712812799711,38.149617249682976,38.22795321939485,38.30628918910672,38.384625158818594,38.46296112853047,38.541297098242346,38.61963306795422,38.6979690376661,38.77630500737797,38.85464097708984,38.93297694680172,39.011312916513596,39.08964888622547,39.16798485593735,39.24632082564922,39.32465679536109,39.40299276507297,39.481328734784846,39.55966470449672,39.6380006742086,39.71633664392047,39.79467261363234,39.873008583344216,39.95134455305609,40.02968052276797,40.10801649247984,40.186352462191714,40.26468843190359,40.343024401615466,40.42136037132734,40.49969634103921,40.57803231075109,40.656368280462964,40.734704250174836,40.813040219886716,40.89137618959859,40.96971215931046,41.04804812902234,41.12638409873421,41.204720068446086,41.283056038157966,41.36139200786984,41.43972797758171,41.51806394729359,41.596399917005456,41.674735886717336,41.75307185642921,41.83140782614108,41.90974379585296,41.988079765564834,42.066415735276706,42.144751704988586,42.22308767470046,42.30142364441233,42.379759614124204,42.458095583836084,42.536431553547956,42.61476752325983,42.69310349297171,42.77143946268358,42.849775432395454,42.928111402107334,43.006447371819206,43.08478334153108,43.16311931124296,43.241455280954824,43.319791250666704],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937],"y":[0,0.045397092746305004,0.09079418549261001,0.136191278238915,0.18158837098522002,0.226985463731525,0.27238255647783,0.317779649224135,0.36317674197044003,0.408573834716745,0.45397092746305,0.499368020209355,0.54476511295566,0.590162205701965,0.63555929844827,0.680956391194575,0.7263534839408801,0.7717505766871849,0.81714766943349,0.8625447621797949,0.9079418549261,0.9533389476724049,0.99873604041871,1.044133133165015,1.08953022591132,1.134927318657625,1.18032441140393,1.2257215041502352,1.27111859689654,1.3165156896428452,1.36191278238915,1.4073098751354551,1.4527069678817601,1.498104060628065,1.5435011533743699,1.588898246120675,1.63429533886698,1.679692431613285,1.7250895243595898,1.770486617105895,1.8158837098522,1.861280802598505,1.9066778953448098,1.952074988091115,1.99747208083742,2.042869173583725,2.08826626633003,2.1336633590763348,2.17906045182264,2.224457544568945,2.26985463731525,2.3152517300615547,2.36064882280786,2.406045915554165,2.4514430083004703,2.496840101046775,2.54223719379308,2.587634286539385,2.6330313792856903,2.678428472031995,2.7238255647783,2.769222657524605,2.8146197502709103,2.860016843017215,2.9054139357635203,2.9508110285098255,2.99620812125613,3.041605214002435,3.0870023067487398,3.132399399495045,3.17779649224135,3.223193584987655,3.26859067773396,3.3139877704802654,3.35938486322657,3.404781955972875,3.4501790487191797,3.495576141465485,3.54097323421179,3.586370326958095,3.6317674197044,3.6771645124507053,3.72256160519701,3.7679586979433153,3.8133557906896196,3.858752883435925,3.90414997618223,3.9495470689285352,3.99494416167484,4.040341254421145,4.08573834716745,4.131135439913756,4.17653253266006,4.221929625406365,4.2673267181526695,4.312723810898975,4.35812090364528,4.403517996391585,4.44891508913789,4.494312181884196,4.5397092746305,4.585106367376805,4.630503460123109,4.675900552869415,4.72129764561572,4.766694738362025,4.81209183110833,4.8574889238546355,4.902886016600941,4.948283109347245,4.99368020209355,5.039077294839855,5.08447438758616,5.129871480332465,5.17526857307877,5.220665665825075,5.266062758571381,5.311459851317685,5.35685694406399,5.402254036810295,5.4476511295566,5.493048222302906,5.53844531504921,5.583842407795514,5.6292395005418205,5.674636593288125,5.72003368603443,5.765430778780734,5.8108278715270405,5.856224964273345,5.901622057019651,5.947019149765955,5.99241624251226,6.037813335258565,6.08321042800487,6.128607520751175,6.1740046134974795,6.219401706243786,6.26479879899009,6.310195891736395,6.3555929844827,6.400990077229005,6.44638716997531,6.491784262721615,6.53718135546792,6.582578448214225,6.627975540960531,6.673372633706835,6.71876972645314,6.7641668191994455,6.80956391194575,6.854961004692055,6.900358097438359,6.9457551901846655,6.99115228293097,7.036549375677276,7.08194646842358,7.127343561169885,7.17274065391619,7.218137746662495,7.2635348394088,7.3089319321551045,7.354329024901411,7.399726117647715,7.44512321039402,7.490520303140325,7.535917395886631,7.581314488632935,7.626711581379239,7.672108674125545,7.71750576687185,7.762902859618156,7.80829995236446,7.853697045110765,7.8990941378570705,7.944491230603376,7.98988832334968,8.035285416095984,8.08068250884229,8.126079601588595,8.1714766943349,8.216873787081205,8.262270879827511,8.307667972573816,8.35306506532012,8.398462158066426,8.44385925081273,8.489256343559035,8.534653436305339,8.580050529051645,8.62544762179795,8.670844714544256,8.71624180729056,8.761638900036864,8.80703599278317,8.852433085529475,8.89783017827578,8.943227271022085,8.988624363768391,9.034021456514695,9.079418549261,9.124815642007306,9.17021273475361,9.215609827499915,9.261006920246219,9.306404012992525,9.35180110573883,9.397198198485135,9.44259529123144,9.487992383977746,9.53338947672405,9.578786569470354,9.62418366221666,9.669580754962965,9.714977847709271,9.760374940455575,9.805772033201881,9.851169125948186,9.89656621869449,9.941963311440794,9.9873604041871,10.032757496933405,10.07815458967971,10.123551682426015,10.16894877517232,10.214345867918626,10.25974296066493,10.305140053411236,10.35053714615754,10.395934238903845,10.44133133165015,10.486728424396455,10.532125517142761,10.577522609889066,10.62291970263537,10.668316795381676,10.71371388812798,10.759110980874285,10.80450807362059,10.849905166366893,10.8953022591132,10.940699351859505,10.986096444605812,11.031493537352114,11.07689063009842,11.122287722844726,11.167684815591029,11.213081908337335,11.258479001083641,11.303876093829945,11.34927318657625,11.394670279322556,11.44006737206886,11.485464464815166,11.530861557561469,11.576258650307775,11.621655743054081,11.667052835800384,11.71244992854669,11.757847021292996,11.803244114039302,11.848641206785604,11.89403829953191,11.939435392278217,11.98483248502452,12.030229577770825,12.07562667051713,12.121023763263436,12.16642085600974,12.211817948756044,12.25721504150235,12.302612134248657,12.348009226994959,12.393406319741265,12.438803412487571,12.484200505233874,12.52959759798018,12.574994690726486,12.62039178347279,12.665788876219095,12.7111859689654,12.756583061711705,12.80198015445801,12.847377247204316,12.89277433995062,12.938171432696926,12.98356852544323,13.028965618189535,13.07436271093584,13.119759803682143,13.16515689642845,13.210553989174755,13.255951081921062,13.301348174667364,13.34674526741367,13.392142360159976,13.43753945290628,13.482936545652585,13.528333638398891,13.573730731145195,13.6191278238915,13.664524916637804,13.70992200938411,13.755319102130416,13.800716194876719,13.846113287623025,13.891510380369331,13.936907473115633,13.98230456586194,14.027701658608246,14.073098751354552,14.118495844100854,14.16389293684716,14.209290029593467,14.25468712233977,14.300084215086075,14.34548130783238,14.390878400578686,14.43627549332499,14.481672586071294,14.5270696788176,14.572466771563906,14.617863864310209,14.663260957056515,14.708658049802821,14.754055142549124,14.79945223529543,14.844849328041736,14.89024642078804,14.935643513534345,14.98104060628065,15.026437699026955,15.071834791773261,15.117231884519565,15.16262897726587,15.208026070012176,15.253423162758478,15.298820255504785,15.34421734825109,15.389614440997397,15.4350115337437,15.480408626490005,15.525805719236311,15.571202811982614,15.61659990472892,15.661996997475226,15.70739409022153,15.752791182967835,15.798188275714141,15.843585368460445,15.888982461206751,15.934379553953054,15.97977664669936,16.025173739445666,16.07057083219197,16.115967924938275,16.16136501768458,16.206762110430887,16.25215920317719,16.297556295923496,16.3429533886698,16.388350481416104,16.43374757416241,16.479144666908716,16.524541759655023,16.569938852401325,16.61533594514763,16.660733037893937,16.70613013064024,16.751527223386546,16.796924316132852,16.842321408879155,16.88771850162546,16.933115594371763,16.97851268711807,17.023909779864375,17.069306872610678,17.114703965356984,17.16010105810329,17.205498150849593,17.2508952435959,17.296292336342205,17.34168942908851,17.387086521834814,17.43248361458112,17.477880707327426,17.52327780007373,17.568674892820034,17.61407198556634,17.659469078312647,17.70486617105895,17.750263263805255,17.79566035655156,17.841057449297868,17.88645454204417,17.931851634790476,17.977248727536782,18.022645820283085,18.06804291302939,18.113440005775697,18.158837098522,18.204234191268306,18.24963128401461,18.295028376760914,18.34042546950722,18.385822562253527,18.43121965499983,18.476616747746135,18.522013840492438,18.567410933238744,18.61280802598505,18.658205118731356,18.70360221147766,18.748999304223965,18.79439639697027,18.839793489716573,18.88519058246288,18.930587675209186,18.97598476795549,19.021381860701794,19.0667789534481,19.112176046194406,19.15757313894071,19.202970231687015,19.24836732443332,19.293764417179627,19.33916150992593,19.384558602672236,19.429955695418542,19.475352788164848,19.52074988091115,19.566146973657457,19.611544066403763,19.656941159150065,19.70233825189637,19.747735344642674,19.79313243738898,19.838529530135286,19.88392662288159,19.929323715627895,19.9747208083742,20.020117901120503,20.06551499386681,20.110912086613116,20.15630917935942,20.201706272105724,20.24710336485203,20.292500457598333,20.33789755034464,20.383294643090945,20.42869173583725,20.474088828583554,20.51948592132986,20.564883014076166,20.610280106822472,20.655677199568775,20.70107429231508,20.746471385061387,20.79186847780769,20.837265570553996,20.8826626633003,20.928059756046608,20.97345684879291,21.018853941539216,21.064251034285522,21.109648127031825,21.15504521977813,21.200442312524437,21.24583940527074,21.291236498017046,21.336633590763352,21.382030683509655,21.42742777625596,21.472824869002267,21.51822196174857,21.563619054494875,21.60901614724118,21.654413239987484,21.699810332733787,21.745207425480096,21.7906045182264,21.83600161097271,21.88139870371901,21.926795796465314,21.972192889211623,22.017589981957926,22.06298707470423,22.108384167450538,22.15378126019684,22.199178352943143,22.244575445689453,22.289972538435755,22.335369631182058,22.380766723928367,22.42616381667467,22.471560909420976,22.516958002167282,22.562355094913585,22.60775218765989,22.653149280406197,22.6985463731525,22.743943465898806,22.78934055864511,22.834737651391414,22.88013474413772,22.925531836884023,22.970928929630333,23.016326022376635,23.061723115122938,23.107120207869247,23.15251730061555,23.197914393361852,23.243311486108162,23.288708578854465,23.334105671600767,23.379502764347077,23.42489985709338,23.470296949839682,23.51569404258599,23.561091135332294,23.606488228078604,23.651885320824906,23.69728241357121,23.74267950631752,23.78807659906382,23.833473691810124,23.878870784556433,23.924267877302736,23.96966497004904,24.015062062795348,24.06045915554165,24.105856248287957,24.15125334103426,24.196650433780565,24.24204752652687,24.287444619273174,24.33284171201948,24.378238804765786,24.42363589751209,24.469032990258395,24.5144300830047,24.559827175751003,24.605224268497313,24.650621361243616,24.696018453989918,24.741415546736228,24.78681263948253,24.832209732228833,24.877606824975143,24.923003917721445,24.968401010467748,25.013798103214057,25.05919519596036,25.104592288706662,25.149989381452972,25.195386474199275,25.24078356694558,25.286180659691887,25.33157775243819,25.376974845184495,25.4223719379308,25.467769030677104,25.51316612342341,25.558563216169716,25.60396030891602,25.649357401662325,25.69475449440863,25.740151587154937,25.78554867990124,25.830945772647546,25.876342865393852,25.921739958140154,25.96713705088646,26.012534143632767,26.05793123637907,26.10332832912537,26.14872542187168,26.194122514617984,26.239519607364286,26.284916700110596,26.3303137928569,26.37571088560321,26.42110797834951,26.466505071095813,26.511902163842123,26.557299256588426,26.602696349334728,26.648093442081038,26.69349053482734,26.738887627573643,26.784284720319953,26.829681813066255,26.87507890581256,26.920475998558867,26.96587309130517,27.011270184051476,27.056667276797782,27.102064369544085,27.14746146229039,27.192858555036697,27.238255647783,27.283652740529305,27.329049833275608,27.374446926021918,27.41984401876822,27.465241111514523,27.510638204260832,27.556035297007135,27.601432389753437,27.646829482499747,27.69222657524605,27.737623667992352,27.783020760738662,27.828417853484964,27.873814946231267,27.919212038977577,27.96460913172388,28.01000622447019,28.05540331721649,28.100800409962794,28.146197502709104,28.191594595455406,28.23699168820171,28.28238878094802,28.32778587369432,28.373182966440623,28.418580059186933,28.463977151933236,28.50937424467954,28.554771337425844,28.60016843017215,28.645565522918456,28.69096261566476,28.736359708411065,28.78175680115737,28.827153893903674,28.87255098664998,28.917948079396286,28.96334517214259,29.008742264888898,29.0541393576352,29.099536450381503,29.144933543127813,29.190330635874115,29.235727728620418,29.281124821366728,29.32652191411303,29.371919006859333,29.417316099605642,29.462713192351945,29.508110285098248,29.553507377844557,29.59890447059086,29.644301563337166,29.689698656083472,29.735095748829774,29.78049284157608,29.825889934322387,29.87128702706869,29.916684119814995,29.9620812125613,30.007478305307604,30.05287539805391,30.098272490800216,30.143669583546522,30.189066676292825,30.23446376903913,30.279860861785437,30.32525795453174,30.370655047278046,30.41605214002435,30.461449232770654,30.506846325516957,30.552243418263267,30.59764051100957,30.64303760375587,30.68843469650218,30.733831789248484,30.779228881994793,30.824625974741096,30.8700230674874,30.915420160233708,30.96081725298001,31.006214345726313,31.051611438472623,31.097008531218925,31.142405623965228,31.187802716711538,31.23319980945784,31.278596902204146,31.323993994950452,31.369391087696755,31.41478818044306,31.460185273189367,31.50558236593567,31.550979458681976,31.596376551428282,31.641773644174584,31.68717073692089,31.732567829667193,31.777964922413503,31.823362015159805,31.868759107906108,31.914156200652418,31.95955329339872,32.004950386145026,32.05034747889133,32.09574457163764,32.14114166438394,32.18653875713024,32.23193584987655,32.277332942622856,32.32273003536916,32.36812712811547,32.413524220861774,32.45892131360807,32.50431840635438,32.549715499100685,32.59511259184699,32.64050968459329,32.6859067773396,32.7313038700859,32.77670096283221,32.822098055578515,32.86749514832482,32.91289224107113,32.95828933381743,33.00368642656373,33.049083519310045,33.094480612056344,33.13987770480265,33.185274797548956,33.23067189029526,33.27606898304156,33.321466075787875,33.366863168534174,33.41226026128048,33.457657354026786,33.50305444677309,33.5484515395194,33.593848632265704,33.639245725012,33.68464281775831,33.730039910504615,33.77543700325092,33.82083409599723,33.86623118874353,33.91162828148983,33.95702537423614,34.002422466982445,34.04781955972875,34.09321665247506,34.138613745221356,34.18401083796767,34.22940793071397,34.274805023460274,34.32020211620658,34.36559920895289,34.410996301699186,34.4563933944455,34.5017904871918,34.54718757993811,34.59258467268441,34.637981765430716,34.68337885817702,34.72877595092333,34.77417304366963,34.81957013641594,34.86496722916224,34.910364321908546,34.95576141465485,35.00115850740116,35.04655560014746,35.09195269289376,35.13734978564007,35.182746878386375,35.22814397113268,35.27354106387899,35.31893815662529,35.36433524937159,35.4097323421179,35.455129434864205,35.50052652761051,35.54592362035682,35.59132071310312,35.63671780584942,35.682114898595735,35.727511991342034,35.77290908408834,35.818306176834646,35.86370326958095,35.90910036232725,35.954497455073565,35.99989454781986,36.04529164056617,36.090688733312476,36.13608582605878,36.18148291880508,36.226880011551394,36.27227710429769,36.317674197044,36.363071289790305,36.40846838253661,36.45386547528292,36.49926256802922,36.54465966077552,36.59005675352183,36.635453846268135,36.68085093901444,36.72624803176075,36.77164512450705,36.81704221725336,36.86243930999966,36.907836402745964,36.95323349549227,36.998630588238576,37.044027680984875,37.08942477373119,37.13482186647749,37.180218959223794,37.2256160519701,37.271013144716406,37.31641023746271,37.36180733020902,37.40720442295532,37.45260151570163,37.49799860844793,37.543395701194235,37.58879279394054,37.63418988668685,37.67958697943315,37.72498407217946,37.77038116492576,37.815778257672065,37.86117535041837,37.90657244316468,37.95196953591098,37.99736662865729,38.04276372140359,38.088160814149894,38.1335579068962,38.17895499964251,38.22435209238881,38.26974918513511,38.31514627788142,38.360543370627724,38.40594046337403,38.451337556120336,38.49673464886664,38.54213174161294,38.587528834359254,38.63292592710555,38.67832301985186,38.723720112598166,38.76911720534447,38.81451429809077,38.859911390837084,38.90530848358338,38.950705576329696,38.996102669075995,39.0414997618223,39.08689685456861,39.13229394731491,39.17769104006121,39.223088132807526,39.268485225553825,39.31388231830013,39.35927941104644,39.40467650379274,39.45007359653904,39.49547068928535,39.540867782031654,39.58626487477796,39.631661967524266,39.67705906027057,39.72245615301688,39.76785324576318,39.813250338509484,39.85864743125579,39.904044524002096,39.9494416167484,39.99483870949471,40.04023580224101,40.08563289498732,40.13102998773362,40.176427080479925,40.22182417322623,40.26722126597254,40.31261835871884,40.35801545146515,40.40341254421145,40.448809636957755,40.49420672970406,40.53960382245037,40.585000915196666,40.63039800794298,40.67579510068928,40.721192193435584,40.76658928618189,40.8119863789282,40.8573834716745,40.90278056442081,40.94817765716711,40.993574749913414,41.03897184265972,41.084368935406026,41.12976602815233,41.17516312089864,41.220560213644944,41.26595730639124,41.31135439913755,41.356751491883855,41.40214858463016,41.44754567737646,41.492942770122774,41.53833986286907,41.58373695561538,41.629134048361685,41.67453114110799,41.7199282338543,41.7653253266006,41.8107224193469,41.856119512093215,41.901516604839514,41.94691369758582,41.99231079033213,42.03770788307843,42.08310497582473,42.128502068571045,42.173899161317344,42.21929625406365,42.264693346809956,42.31009043955626,42.35548753230257,42.400884625048874,42.44628171779517,42.49167881054148,42.537075903287786],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Acad Rule 2","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696],"y":[0,0.06038537718003777,0.12077075436007555,0.1811561315401133,0.2415415087201511,0.30192688590018885,0.3623122630802266,0.4226976402602644,0.4830830174403022,0.5434683946203399,0.6038537718003777,0.6642391489804156,0.7246245261604533,0.7850099033404911,0.8453952805205288,0.9057806577005667,0.9661660348806044,1.0265514120606423,1.0869367892406798,1.1473221664207178,1.2077075436007554,1.2680929207807934,1.3284782979608312,1.3888636751408687,1.4492490523209065,1.5096344295009443,1.5700198066809823,1.6304051838610198,1.6907905610410576,1.7511759382210954,1.8115613154011334,1.871946692581171,1.9323320697612087,1.9927174469412463,2.0531028241212845,2.113488201301322,2.1738735784813596,2.2342589556613976,2.2946443328414357,2.355029710021473,2.4154150872015108,2.4758004643815488,2.5361858415615868,2.5965712187416243,2.6569565959216623,2.7173419731017,2.7777273502817375,2.8381127274617755,2.898498104641813,2.9588834818218506,3.0192688590018886,3.079654236181926,3.1400396133619646,3.200424990542002,3.2608103677220397,3.3211957449020777,3.3815811220821153,3.441966499262153,3.502351876442191,3.5627372536222284,3.623122630802267,3.6835080079823044,3.743893385162342,3.8042787623423795,3.8646641395224175,3.925049516702455,3.9854348938824926,4.045820271062531,4.106205648242569,4.166591025422607,4.226976402602644,4.287361779782682,4.347747156962719,4.408132534142757,4.468517911322795,4.528903288502833,4.589288665682871,4.649674042862909,4.710059420042946,4.770444797222984,4.8308301744030215,4.891215551583059,4.9516009287630975,5.011986305943135,5.0723716831231735,5.132757060303211,5.193142437483249,5.253527814663286,5.313913191843325,5.374298569023361,5.4346839462034,5.495069323383437,5.555454700563475,5.6158400777435125,5.676225454923551,5.736610832103589,5.796996209283626,5.8573815864636645,5.917766963643701,5.97815234082374,6.038537718003777,6.098923095183816,6.159308472363852,6.219693849543891,6.280079226723929,6.340464603903966,6.400849981084004,6.461235358264041,6.521620735444079,6.582006112624117,6.642391489804155,6.702776866984193,6.7631622441642305,6.823547621344269,6.883932998524306,6.944318375704344,7.004703752884382,7.065089130064419,7.125474507244457,7.185859884424495,7.246245261604534,7.30663063878457,7.367016015964609,7.427401393144645,7.487786770324684,7.548172147504721,7.608557524684759,7.668942901864797,7.729328279044835,7.789713656224873,7.85009903340491,7.9104844105849486,7.970869787764985,8.031255164945025,8.091640542125061,8.1520259193051,8.212411296485138,8.272796673665175,8.333182050845213,8.39356742802525,8.453952805205288,8.514338182385325,8.574723559565363,8.635108936745402,8.695494313925439,8.755879691105477,8.816265068285514,8.876650445465552,8.93703582264559,8.997421199825629,9.057806577005666,9.118191954185704,9.178577331365743,9.23896270854578,9.299348085725818,9.359733462905854,9.420118840085893,9.48050421726593,9.540889594445968,9.601274971626006,9.661660348806043,9.722045725986082,9.782431103166118,9.842816480346157,9.903201857526195,9.963587234706232,10.02397261188627,10.084357989066309,10.144743366246347,10.205128743426384,10.265514120606422,10.325899497786459,10.386284874966497,10.446670252146534,10.507055629326572,10.567441006506611,10.62782638368665,10.688211760866684,10.748597138046723,10.808982515226761,10.8693678924068,10.929753269586836,10.990138646766875,11.050524023946913,11.11090940112695,11.171294778306988,11.231680155487025,11.292065532667063,11.352450909847102,11.41283628702714,11.473221664207179,11.533607041387214,11.593992418567252,11.65437779574729,11.714763172927329,11.775148550107364,11.835533927287402,11.89591930446744,11.95630468164748,12.016690058827518,12.077075436007554,12.137460813187593,12.197846190367631,12.258231567547668,12.318616944727705,12.379002321907743,12.439387699087781,12.49977307626782,12.560158453447858,12.620543830627893,12.680929207807932,12.74131458498797,12.801699962168009,12.862085339348045,12.922470716528082,12.98285609370812,13.043241470888159,13.103626848068197,13.164012225248234,13.224397602428272,13.28478297960831,13.34516835678835,13.405553733968386,13.465939111148423,13.526324488328461,13.5867098655085,13.647095242688538,13.707480619868573,13.767865997048611,13.82825137422865,13.888636751408688,13.949022128588727,14.009407505768763,14.0697928829488,14.130178260128838,14.190563637308877,14.250949014488913,14.311334391668952,14.37171976884899,14.432105146029029,14.492490523209067,14.552875900389102,14.61326127756914,14.673646654749179,14.734032031929218,14.794417409109252,14.85480278628929,14.91518816346933,14.975573540649368,15.035958917829406,15.096344295009443,15.156729672189481,15.217115049369518,15.277500426549556,15.337885803729595,15.398271180909632,15.45865655808967,15.519041935269708,15.579427312449747,15.639812689629782,15.70019806680982,15.760583443989859,15.820968821169897,15.881354198349936,15.94173957552997,16.00212495271001,16.06251032989005,16.122895707070086,16.183281084250122,16.24366646143016,16.3040518386102,16.364437215790236,16.424822592970276,16.48520797015031,16.54559334733035,16.605978724510386,16.666364101690426,16.726749478870463,16.7871348560505,16.84752023323054,16.907905610410577,16.968290987590613,17.02867636477065,17.08906174195069,17.149447119130727,17.209832496310767,17.270217873490804,17.33060325067084,17.390988627850877,17.451374005030917,17.511759382210954,17.57214475939099,17.632530136571027,17.692915513751068,17.753300890931104,17.813686268111145,17.87407164529118,17.934457022471218,17.994842399651258,18.055227776831295,18.11561315401133,18.175998531191368,18.23638390837141,18.296769285551445,18.357154662731485,18.41754003991152,18.47792541709156,18.538310794271595,18.598696171451635,18.659081548631672,18.71946692581171,18.779852302991745,18.840237680171786,18.900623057351822,18.96100843453186,19.0213938117119,19.081779188891936,19.142164566071976,19.202549943252013,19.26293532043205,19.323320697612086,19.383706074792126,19.444091451972163,19.5044768291522,19.564862206332236,19.625247583512277,19.685632960692313,19.746018337872353,19.80640371505239,19.866789092232427,19.927174469412464,19.987559846592504,20.04794522377254,20.108330600952577,20.168715978132617,20.229101355312654,20.289486732492694,20.349872109672727,20.410257486852768,20.470642864032804,20.531028241212844,20.59141361839288,20.651798995572918,20.712184372752954,20.772569749932995,20.83295512711303,20.893340504293068,20.953725881473108,21.014111258653145,21.07449663583318,21.134882013013222,21.19526739019326,21.2556527673733,21.316038144553335,21.37642352173337,21.436808898913412,21.497194276093445,21.557579653273486,21.617965030453522,21.67835040763356,21.7387357848136,21.799121161993636,21.859506539173672,21.919891916353713,21.98027729353375,22.04066267071379,22.101048047893826,22.16143342507386,22.2218188022539,22.282204179433936,22.342589556613976,22.402974933794013,22.46336031097405,22.52374568815409,22.584131065334127,22.644516442514167,22.704901819694204,22.76528719687424,22.82567257405428,22.886057951234314,22.946443328414357,23.00682870559439,23.067214082774427,23.127599459954467,23.187984837134504,23.24837021431454,23.30875559149458,23.369140968674618,23.429526345854658,23.489911723034695,23.550297100214728,23.61068247739477,23.671067854574805,23.73145323175485,23.79183860893488,23.852223986114918,23.91260936329496,23.972994740474995,24.033380117655035,24.093765494835072,24.15415087201511,24.21453624919515,24.274921626375185,24.33530700355522,24.395692380735262,24.456077757915295,24.516463135095336,24.576848512275372,24.63723388945541,24.69761926663545,24.758004643815486,24.818390020995526,24.878775398175563,24.9391607753556,24.99954615253564,25.059931529715676,25.120316906895717,25.18070228407575,25.241087661255786,25.301473038435827,25.361858415615863,25.422243792795904,25.48262916997594,25.543014547155977,25.603399924336017,25.663785301516054,25.72417067869609,25.78455605587613,25.844941433056164,25.905326810236208,25.96571218741624,26.026097564596277,26.086482941776318,26.146868318956354,26.207253696136394,26.26763907331643,26.328024450496468,26.388409827676508,26.448795204856545,26.509180582036585,26.56956595921662,26.629951336396655,26.6903367135767,26.75072209075673,26.811107467936772,26.87149284511681,26.931878222296845,26.992263599476885,27.052648976656922,27.11303435383696,27.173419731017,27.233805108197036,27.294190485377076,27.354575862557112,27.414961239737146,27.475346616917186,27.535731994097222,27.596117371277263,27.6565027484573,27.716888125637336,27.777273502817376,27.837658879997413,27.898044257177453,27.95842963435749,28.018815011537527,28.079200388717567,28.1395857658976,28.199971143077644,28.260356520257677,28.320741897437713,28.381127274617754,28.44151265179779,28.501898028977827,28.562283406157867,28.622668783337904,28.683054160517944,28.74343953769798,28.803824914878014,28.864210292058058,28.92459566923809,28.984981046418135,29.045366423598168,29.105751800778204,29.166137177958245,29.22652255513828,29.28690793231832,29.347293309498358,29.407678686678395,29.468064063858435,29.52844944103847,29.588834818218505,29.64922019539855,29.70960557257858,29.769990949758622,29.83037632693866,29.890761704118695,29.951147081298735,30.011532458478772,30.071917835658812,30.13230321283885,30.192688590018886,30.253073967198926,30.313459344378963,30.373844721559003,30.434230098739036,30.494615475919073,30.555000853099113,30.61538623027915,30.67577160745919,30.736156984639226,30.796542361819263,30.856927738999303,30.91731311617934,30.977698493359377,31.038083870539417,31.09846924771945,31.158854624899494,31.219240002079527,31.279625379259564,31.340010756439604,31.40039613361964,31.46078151079968,31.521166887979717,31.581552265159754,31.641937642339794,31.70232301951983,31.76270839669987,31.823093773879908,31.88347915105994,31.943864528239985,32.00424990542002,32.06463528260006,32.1250206597801,32.18540603696013,32.24579141414017,32.306176791320205,32.366562168500245,32.426947545680285,32.48733292286032,32.54771830004036,32.6081036772204,32.66848905440043,32.72887443158047,32.78925980876051,32.84964518594055,32.910030563120586,32.97041594030062,33.030801317480666,33.0911866946607,33.15157207184074,33.21195744902077,33.27234282620081,33.33272820338085,33.393113580560886,33.453498957740926,33.51388433492097,33.574269712101,33.63465508928104,33.69504046646108,33.75542584364111,33.81581122082115,33.87619659800119,33.93658197518123,33.99696735236127,34.0573527295413,34.11773810672134,34.17812348390138,34.23850886108142,34.298894238261454,34.359279615441494,34.419664992621534,34.48005036980157,34.54043574698161,34.60082112416164,34.66120650134168,34.72159187852172,34.781977255701754,34.842362632881795,34.902748010061835,34.96313338724187,35.02351876442191,35.08390414160195,35.14428951878198,35.20467489596202,35.265060273142055,35.3254456503221,35.385831027502135,35.44621640468217,35.50660178186221,35.56698715904225,35.62737253622229,35.68775791340232,35.74814329058236,35.8085286677624,35.868914044942436,35.929299422122476,35.989684799302516,36.05007017648255,36.11045555366259,36.17084093084262,36.23122630802266,36.2916116852027,36.351997062382736,36.412382439562776,36.47276781674282,36.53315319392285,36.59353857110289,36.65392394828293,36.71430932546297,36.774694702643,36.83508007982304,36.89546545700308,36.95585083418312,37.01623621136316,37.07662158854319,37.13700696572323,37.19739234290327,37.257777720083304,37.318163097263344,37.378548474443384,37.43893385162342,37.49931922880346,37.55970460598349,37.62008998316353,37.68047536034357,37.740860737523604,37.801246114703645,37.861631491883685,37.92201686906372,37.98240224624376,38.0427876234238,38.10317300060384,38.16355837778387,38.223943754963905,38.28432913214395,38.344714509323985,38.405099886504026,38.46548526368406,38.5258706408641,38.58625601804414,38.64664139522417,38.70702677240421,38.76741214958425,38.827797526764286,38.888182903944326,38.948568281124366,39.0089536583044,39.06933903548444,39.12972441266447,39.19010978984451,39.25049516702455,39.310880544204586,39.37126592138463,39.43165129856467,39.49203667574471,39.55242205292474,39.61280743010478,39.67319280728482,39.733578184464854,39.793963561644894,39.85434893882493,39.91473431600497,39.97511969318501,40.03550507036504,40.09589044754508,40.15627582472512,40.216661201905154,40.277046579085194,40.337431956265235,40.39781733344527,40.45820271062531,40.51858808780534,40.57897346498539,40.63935884216542,40.699744219345455,40.760129596525495,40.820514973705535,40.880900350885575,40.94128572806561,41.00167110524565,41.06205648242569,41.12244185960572,41.18282723678576,41.2432126139658,41.303597991145836,41.363983368325876,41.42436874550591,41.48475412268595,41.54513949986599,41.60552487704602,41.66591025422606,41.7262956314061,41.786681008586136,41.847066385766176,41.907451762946216,41.96783714012626,42.02822251730629],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199],"y":[0,0.17421425599120252,0.34842851198240504,0.5226427679736075,0.6968570239648101,0.8710712799560126,1.045285535947215,1.2194997919384176,1.3937140479296202,1.5679283039208227,1.7421425599120253,1.9163568159032278,2.09057107189443,2.2647853278856327,2.4389995838768352,2.6132138398680373,2.7874280958592403,2.9616423518504424,3.1358566078416454,3.3100708638328475,3.4842851198240505,3.6584993758152526,3.8327136318064556,4.006927887797658,4.18114214378886,4.355356399780063,4.529570655771265,4.703784911762468,4.8779991677536705,5.052213423744873,5.226427679736075,5.400641935727278,5.574856191718481,5.749070447709683,5.923284703700885,6.097498959692088,6.271713215683291,6.445927471674493,6.620141727665695,6.794355983656898,6.968570239648101,7.142784495639304,7.316998751630505,7.491213007621708,7.665427263612911,7.839641519604113,8.013855775595315,8.188070031586518,8.36228428757772,8.536498543568923,8.710712799560126,8.884927055551328,9.05914131154253,9.233355567533733,9.407569823524936,9.581784079516137,9.755998335507341,9.930212591498544,10.104426847489746,10.278641103480949,10.45285535947215,10.627069615463354,10.801283871454556,10.975498127445757,11.149712383436961,11.323926639428164,11.498140895419366,11.672355151410569,11.84656940740177,12.020783663392974,12.194997919384177,12.369212175375377,12.543426431366582,12.717640687357783,12.891854943348987,13.06606919934019,13.24028345533139,13.414497711322594,13.588711967313795,13.762926223304998,13.937140479296202,14.111354735287403,14.285568991278607,14.459783247269808,14.63399750326101,14.808211759252215,14.982426015243416,15.156640271234618,15.330854527225823,15.505068783217023,15.679283039208226,15.853497295199428,16.02771155119063,16.201925807181834,16.376140063173036,16.55035431916424,16.72456857515544,16.898782831146644,17.072997087137846,17.24721134312905,17.42142559912025,17.595639855111454,17.769854111102656,17.94406836709386,18.11828262308506,18.29249687907626,18.466711135067467,18.64092539105867,18.81513964704987,18.989353903041074,19.163568159032273,19.33778241502348,19.511996671014682,19.686210927005884,19.860425182997087,20.034639438988286,20.208853694979492,20.383067950970695,20.557282206961897,20.7314964629531,20.9057107189443,21.0799249749355,21.254139230926707,21.42835348691791,21.602567742909113,21.776781998900315,21.950996254891514,22.12521051088272,22.299424766873923,22.473639022865125,22.647853278856328,22.822067534847527,22.996281790838733,23.170496046829935,23.344710302821138,23.51892455881234,23.69313881480354,23.867353070794742,24.041567326785948,24.21578158277715,24.389995838768353,24.564210094759552,24.738424350750755,24.91263860674196,25.086852862733164,25.261067118724366,25.435281374715565,25.609495630706768,25.783709886697974,25.957924142689176,26.13213839868038,26.306352654671578,26.48056691066278,26.654781166653983,26.82899542264519,27.00320967863639,27.17742393462759,27.351638190618793,27.525852446609996,27.7000667026012,27.874280958592404,28.048495214583603,28.222709470574806,28.39692372656601,28.571137982557214,28.745352238548417,28.919566494539616,29.09378075053082,29.26799500652202,29.442209262513224,29.61642351850443,29.79063777449563,29.96485203048683,30.139066286478034,30.313280542469236,30.487494798460443,30.661709054451645,30.835923310442844,31.010137566434047,31.18435182242525,31.35856607841645,31.532780334407658,31.706994590398857,31.88120884639006,32.05542310238126,32.229637358372464,32.40385161436367,32.57806587035487,32.75228012634607,32.926494382337275,33.10070863832848,33.27492289431968,33.44913715031088,33.623351406302085,33.79756566229329,33.97177991828449,34.14599417427569,34.320208430266895,34.4944226862581,34.6686369422493],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Ext Rule 5: ATR Volatility Expansion</td>
                <td>UNKNOWN</td>
                <td class="positive">66.78%</td>
                <td>65.9%</td>
                <td>299</td>
                <td>1.29</td>
                <td>0.00</td>
                <td>8.03%</td>
                <td>76.5</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">43.32%</td>
                <td>64.6%</td>
                <td>553</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>30.69%</td>
                <td>66.7</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">42.54%</td>
                <td>64.9%</td>
                <td>937</td>
                <td>1.05</td>
                <td>0.00</td>
                <td>33.76%</td>
                <td>66.5</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Acad Rule 2: Mean Reversion Factor</td>
                <td>ACADEMIC</td>
                <td class="positive">42.03%</td>
                <td>65.4%</td>
                <td>696</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>26.22%</td>
                <td>66.4</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">34.67%</td>
                <td>68.3%</td>
                <td>199</td>
                <td>1.21</td>
                <td>0.00</td>
                <td>18.99%</td>
                <td>64.4</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Rule 27: Structure Break Up</td>
                <td>ORIGINAL</td>
                <td class="positive">30.64%</td>
                <td>64.2%</td>
                <td>520</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>25.22%</td>
                <td>61.5</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Acad Rule 3: Volatility Breakout</td>
                <td>ACADEMIC</td>
                <td class="positive">22.37%</td>
                <td>63.3%</td>
                <td>656</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>27.48%</td>
                <td>57.9</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Volume Rule 4: Volume Breakout Confirmation</td>
                <td>UNKNOWN</td>
                <td class="positive">16.87%</td>
                <td>64.4%</td>
                <td>160</td>
                <td>1.12</td>
                <td>0.00</td>
                <td>17.70%</td>
                <td>56.1</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">15.04%</td>
                <td>65.4%</td>
                <td>191</td>
                <td>1.10</td>
                <td>0.00</td>
                <td>16.79%</td>
                <td>55.7</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                <td>UNKNOWN</td>
                <td class="positive">17.41%</td>
                <td>67.0%</td>
                <td>94</td>
                <td>1.23</td>
                <td>0.00</td>
                <td>31.37%</td>
                <td>55.3</td>
            </tr>
            
            <tr>
                <td>11</td>
                <td>Volatility Rule 2: ATR Expansion Signal</td>
                <td>UNKNOWN</td>
                <td class="positive">14.00%</td>
                <td>63.0%</td>
                <td>270</td>
                <td>1.06</td>
                <td>0.00</td>
                <td>18.69%</td>
                <td>54.5</td>
            </tr>
            
            <tr>
                <td>12</td>
                <td>Rule 28: Volume Breakout</td>
                <td>ORIGINAL</td>
                <td class="positive">10.80%</td>
                <td>64.5%</td>
                <td>121</td>
                <td>1.10</td>
                <td>0.00</td>
                <td>12.88%</td>
                <td>53.7</td>
            </tr>
            
            <tr>
                <td>13</td>
                <td>Rule 2: Golden Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">24.28%</td>
                <td>80.6%</td>
                <td>31</td>
                <td>2.59</td>
                <td>0.00</td>
                <td>4.29%</td>
                <td>43.2</td>
            </tr>
            
            <tr>
                <td>14</td>
                <td>Volume Rule 3: Dark Pool Activity</td>
                <td>UNKNOWN</td>
                <td class="positive">6.87%</td>
                <td>66.7%</td>
                <td>24</td>
                <td>1.40</td>
                <td>0.00</td>
                <td>5.21%</td>
                <td>29.9</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 812,700<br>
            • Backtest Range: 300 to 813,000<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
