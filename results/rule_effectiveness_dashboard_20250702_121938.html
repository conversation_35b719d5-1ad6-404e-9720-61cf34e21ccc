
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 12:19:39 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">1867.59%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">66,204</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">64.8%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">inf</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>190.99%</strong></td>
                    <td>66.2%</td>
                    <td>3354</td>
                    <td>1.14</td>
                    <td class="negative">20.58%</td>
                    <td class="positive"><strong>1.3571</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>5h48m<br><small>(1.0m - 121h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>183.08%</strong></td>
                    <td>65.5%</td>
                    <td>4580</td>
                    <td>1.08</td>
                    <td class="negative">26.73%</td>
                    <td class="positive"><strong>1.3470</strong></td>
                    <td class="negative">+0.83% / -1.43%</td>
                    <td>5h6m</td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>159.86%</strong></td>
                    <td>65.1%</td>
                    <td>4860</td>
                    <td>1.07</td>
                    <td class="negative">28.96%</td>
                    <td class="positive"><strong>1.2898</strong></td>
                    <td class="negative">+0.83% / -1.43%</td>
                    <td>5h1m</td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>127.68%</strong></td>
                    <td>65.7%</td>
                    <td>2737</td>
                    <td>1.12</td>
                    <td class="negative">15.84%</td>
                    <td class="positive"><strong>1.1838</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>6h48m</td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>119.09%</strong></td>
                    <td>66.3%</td>
                    <td>1987</td>
                    <td>1.16</td>
                    <td class="negative">21.45%</td>
                    <td class="positive"><strong>1.1313</strong></td>
                    <td class="negative">+0.84% / -1.40%</td>
                    <td>7h27m<br><small>(3.0m - 117h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>117.18%</strong></td>
                    <td>64.9%</td>
                    <td>3475</td>
                    <td>1.08</td>
                    <td class="negative">17.88%</td>
                    <td class="positive"><strong>1.1705</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>6h19m<br><small>(1.0m - 118h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>105.67%</strong></td>
                    <td>64.6%</td>
                    <td>4759</td>
                    <td>1.05</td>
                    <td class="negative">27.13%</td>
                    <td class="positive"><strong>1.1512</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>5h13m</td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>101.13%</strong></td>
                    <td>64.3%</td>
                    <td>4115</td>
                    <td>1.06</td>
                    <td class="negative">27.81%</td>
                    <td class="positive"><strong>1.1279</strong></td>
                    <td class="negative">+0.84% / -1.40%</td>
                    <td>5h46m<br><small>(1.0m - 120h24m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>91.34%</strong></td>
                    <td>64.5%</td>
                    <td>4361</td>
                    <td>1.05</td>
                    <td class="negative">27.87%</td>
                    <td class="positive"><strong>1.1050</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>5h21m<br><small>(1.0m - 146h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>76.27%</strong></td>
                    <td>64.6%</td>
                    <td>3647</td>
                    <td>1.06</td>
                    <td class="negative">23.17%</td>
                    <td class="positive"><strong>1.0581</strong></td>
                    <td class="negative">+0.83% / -1.41%</td>
                    <td>5h57m</td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>64.55%</strong></td>
                    <td>64.5%</td>
                    <td>3108</td>
                    <td>1.06</td>
                    <td class="negative">22.86%</td>
                    <td class="positive"><strong>1.0140</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>6h24m<br><small>(2.0m - 119h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>61.12%</strong></td>
                    <td>64.9%</td>
                    <td>3003</td>
                    <td>1.06</td>
                    <td class="negative">20.15%</td>
                    <td class="positive"><strong>1.0048</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>6h15m<br><small>(1.0m - 121h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>60.56%</strong></td>
                    <td>64.9%</td>
                    <td>2465</td>
                    <td>1.07</td>
                    <td class="negative">20.58%</td>
                    <td class="positive"><strong>0.9876</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>7h2m<br><small>(1.0m - 119h32m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>59.69%</strong></td>
                    <td>64.6%</td>
                    <td>2017</td>
                    <td>1.09</td>
                    <td class="negative">18.63%</td>
                    <td class="positive"><strong>0.9735</strong></td>
                    <td class="negative">+0.84% / -1.39%</td>
                    <td>7h27m<br><small>(1.0m - 113h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>57.64%</strong></td>
                    <td>64.6%</td>
                    <td>2904</td>
                    <td>1.06</td>
                    <td class="negative">25.59%</td>
                    <td class="positive"><strong>0.9859</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>6h43m<br><small>(2.0m - 117h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>54.31%</strong></td>
                    <td>64.0%</td>
                    <td>2202</td>
                    <td>1.08</td>
                    <td class="negative">16.61%</td>
                    <td class="positive"><strong>0.9698</strong></td>
                    <td class="negative">+0.85% / -1.39%</td>
                    <td>5h58m</td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>43.97%</strong></td>
                    <td>65.5%</td>
                    <td>1343</td>
                    <td>1.11</td>
                    <td class="negative">11.14%</td>
                    <td class="positive"><strong>0.9080</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>9h12m<br><small>(2.0m - 112h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>43.95%</strong></td>
                    <td>63.3%</td>
                    <td>3809</td>
                    <td>1.04</td>
                    <td class="negative">42.47%</td>
                    <td class="positive"><strong>0.9496</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>5h9m</td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>41.02%</strong></td>
                    <td>63.9%</td>
                    <td>2077</td>
                    <td>1.06</td>
                    <td class="negative">15.95%</td>
                    <td class="positive"><strong>0.9275</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>7h0m<br><small>(1.0m - 113h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>28.64%</strong></td>
                    <td>63.4%</td>
                    <td>2088</td>
                    <td>1.05</td>
                    <td class="negative">14.37%</td>
                    <td class="positive"><strong>0.8961</strong></td>
                    <td class="negative">+0.85% / -1.39%</td>
                    <td>5h41m</td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>27.80%</strong></td>
                    <td>65.1%</td>
                    <td>895</td>
                    <td>1.11</td>
                    <td class="negative">12.26%</td>
                    <td class="positive"><strong>0.8264</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>8h47m</td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>19.47%</strong></td>
                    <td>63.9%</td>
                    <td>1627</td>
                    <td>1.04</td>
                    <td class="negative">18.69%</td>
                    <td class="positive"><strong>0.8398</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>8h11m<br><small>(1.0m - 113h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>9.33%</strong></td>
                    <td>79.4%</td>
                    <td>34</td>
                    <td>2.69</td>
                    <td class="neutral">2.07%</td>
                    <td class="positive"><strong>0.6899</strong></td>
                    <td class="negative">+1.04% / -1.44%</td>
                    <td>1h4m<br><small>(1.0m - 9h10m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>7.92%</strong></td>
                    <td>67.5%</td>
                    <td>154</td>
                    <td>1.22</td>
                    <td class="neutral">4.45%</td>
                    <td class="positive"><strong>0.6342</strong></td>
                    <td class="negative">+0.82% / -1.40%</td>
                    <td>7h2m</td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>7.62%</strong></td>
                    <td>100.0%</td>
                    <td>12</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.23% / 0.00%</td>
                    <td>1.4m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>7.32%</strong></td>
                    <td>63.9%</td>
                    <td>590</td>
                    <td>1.05</td>
                    <td class="negative">11.97%</td>
                    <td class="positive"><strong>0.7191</strong></td>
                    <td class="negative">+0.84% / -1.40%</td>
                    <td>11h46m<br><small>(7.0m - 117h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>SMC Rule 2: Fair Value Gap Fill</td>
                    <td class="positive"><strong>0.40%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.80% / 0.00%</td>
                    <td>19.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#28</strong></td>
                    <td>New Rule 4: Ultimate Oscillator Breakout</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#29</strong></td>
                    <td>Rule 1: MA Alignment with RSI Oversold</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#30</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>SMC Rule 2: Fair Value Gap Fill</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">0.40%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.80% / 0.00%</td>
                    <td>19.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">7.62%</td>
                    <td>12</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.23% / 0.00%</td>
                    <td>1.4m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>79.4%</strong></td>
                    <td class="positive">9.33%</td>
                    <td>34</td>
                    <td>2.69</td>
                    <td class="neutral">2.07%</td>
                    <td class="positive"><strong>0.6899</strong></td>
                    <td class="negative">+1.04% / -1.44%</td>
                    <td>1h4m<br><small>(1.0m - 9h10m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>67.5%</strong></td>
                    <td class="positive">7.92%</td>
                    <td>154</td>
                    <td>1.22</td>
                    <td class="neutral">4.45%</td>
                    <td class="positive"><strong>0.6342</strong></td>
                    <td class="negative">+0.82% / -1.40%</td>
                    <td>7h2m</td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>66.3%</strong></td>
                    <td class="positive">119.09%</td>
                    <td>1987</td>
                    <td>1.16</td>
                    <td class="negative">21.45%</td>
                    <td class="positive"><strong>1.1313</strong></td>
                    <td class="negative">+0.84% / -1.40%</td>
                    <td>7h27m<br><small>(3.0m - 117h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>66.2%</strong></td>
                    <td class="positive">190.99%</td>
                    <td>3354</td>
                    <td>1.14</td>
                    <td class="negative">20.58%</td>
                    <td class="positive"><strong>1.3571</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>5h48m<br><small>(1.0m - 121h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>65.7%</strong></td>
                    <td class="positive">127.68%</td>
                    <td>2737</td>
                    <td>1.12</td>
                    <td class="negative">15.84%</td>
                    <td class="positive"><strong>1.1838</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>6h48m</td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>65.5%</strong></td>
                    <td class="positive">183.08%</td>
                    <td>4580</td>
                    <td>1.08</td>
                    <td class="negative">26.73%</td>
                    <td class="positive"><strong>1.3470</strong></td>
                    <td class="negative">+0.83% / -1.43%</td>
                    <td>5h6m</td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>65.5%</strong></td>
                    <td class="positive">43.97%</td>
                    <td>1343</td>
                    <td>1.11</td>
                    <td class="negative">11.14%</td>
                    <td class="positive"><strong>0.9080</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>9h12m<br><small>(2.0m - 112h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>65.1%</strong></td>
                    <td class="positive">159.86%</td>
                    <td>4860</td>
                    <td>1.07</td>
                    <td class="negative">28.96%</td>
                    <td class="positive"><strong>1.2898</strong></td>
                    <td class="negative">+0.83% / -1.43%</td>
                    <td>5h1m</td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>65.1%</strong></td>
                    <td class="positive">27.80%</td>
                    <td>895</td>
                    <td>1.11</td>
                    <td class="negative">12.26%</td>
                    <td class="positive"><strong>0.8264</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>8h47m</td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>64.9%</strong></td>
                    <td class="positive">61.12%</td>
                    <td>3003</td>
                    <td>1.06</td>
                    <td class="negative">20.15%</td>
                    <td class="positive"><strong>1.0048</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>6h15m<br><small>(1.0m - 121h0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>64.9%</strong></td>
                    <td class="positive">60.56%</td>
                    <td>2465</td>
                    <td>1.07</td>
                    <td class="negative">20.58%</td>
                    <td class="positive"><strong>0.9876</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>7h2m<br><small>(1.0m - 119h32m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>64.9%</strong></td>
                    <td class="positive">117.18%</td>
                    <td>3475</td>
                    <td>1.08</td>
                    <td class="negative">17.88%</td>
                    <td class="positive"><strong>1.1705</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>6h19m<br><small>(1.0m - 118h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>64.6%</strong></td>
                    <td class="positive">76.27%</td>
                    <td>3647</td>
                    <td>1.06</td>
                    <td class="negative">23.17%</td>
                    <td class="positive"><strong>1.0581</strong></td>
                    <td class="negative">+0.83% / -1.41%</td>
                    <td>5h57m</td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>64.6%</strong></td>
                    <td class="positive">59.69%</td>
                    <td>2017</td>
                    <td>1.09</td>
                    <td class="negative">18.63%</td>
                    <td class="positive"><strong>0.9735</strong></td>
                    <td class="negative">+0.84% / -1.39%</td>
                    <td>7h27m<br><small>(1.0m - 113h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>64.6%</strong></td>
                    <td class="positive">105.67%</td>
                    <td>4759</td>
                    <td>1.05</td>
                    <td class="negative">27.13%</td>
                    <td class="positive"><strong>1.1512</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>5h13m</td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>64.6%</strong></td>
                    <td class="positive">57.64%</td>
                    <td>2904</td>
                    <td>1.06</td>
                    <td class="negative">25.59%</td>
                    <td class="positive"><strong>0.9859</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>6h43m<br><small>(2.0m - 117h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>64.5%</strong></td>
                    <td class="positive">91.34%</td>
                    <td>4361</td>
                    <td>1.05</td>
                    <td class="negative">27.87%</td>
                    <td class="positive"><strong>1.1050</strong></td>
                    <td class="negative">+0.83% / -1.42%</td>
                    <td>5h21m<br><small>(1.0m - 146h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>64.5%</strong></td>
                    <td class="positive">64.55%</td>
                    <td>3108</td>
                    <td>1.06</td>
                    <td class="negative">22.86%</td>
                    <td class="positive"><strong>1.0140</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>6h24m<br><small>(2.0m - 119h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>64.3%</strong></td>
                    <td class="positive">101.13%</td>
                    <td>4115</td>
                    <td>1.06</td>
                    <td class="negative">27.81%</td>
                    <td class="positive"><strong>1.1279</strong></td>
                    <td class="negative">+0.84% / -1.40%</td>
                    <td>5h46m<br><small>(1.0m - 120h24m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>64.0%</strong></td>
                    <td class="positive">54.31%</td>
                    <td>2202</td>
                    <td>1.08</td>
                    <td class="negative">16.61%</td>
                    <td class="positive"><strong>0.9698</strong></td>
                    <td class="negative">+0.85% / -1.39%</td>
                    <td>5h58m</td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>63.9%</strong></td>
                    <td class="positive">41.02%</td>
                    <td>2077</td>
                    <td>1.06</td>
                    <td class="negative">15.95%</td>
                    <td class="positive"><strong>0.9275</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>7h0m<br><small>(1.0m - 113h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>63.9%</strong></td>
                    <td class="positive">7.32%</td>
                    <td>590</td>
                    <td>1.05</td>
                    <td class="negative">11.97%</td>
                    <td class="positive"><strong>0.7191</strong></td>
                    <td class="negative">+0.84% / -1.40%</td>
                    <td>11h46m<br><small>(7.0m - 117h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>63.9%</strong></td>
                    <td class="positive">19.47%</td>
                    <td>1627</td>
                    <td>1.04</td>
                    <td class="negative">18.69%</td>
                    <td class="positive"><strong>0.8398</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>8h11m<br><small>(1.0m - 113h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>63.4%</strong></td>
                    <td class="positive">28.64%</td>
                    <td>2088</td>
                    <td>1.05</td>
                    <td class="negative">14.37%</td>
                    <td class="positive"><strong>0.8961</strong></td>
                    <td class="negative">+0.85% / -1.39%</td>
                    <td>5h41m</td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>63.3%</strong></td>
                    <td class="positive">43.95%</td>
                    <td>3809</td>
                    <td>1.04</td>
                    <td class="negative">42.47%</td>
                    <td class="positive"><strong>0.9496</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>5h9m</td>
                </tr>
            
                <tr>
                    <td><strong>#28</strong></td>
                    <td>New Rule 4: Ultimate Oscillator Breakout</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#29</strong></td>
                    <td>Rule 1: MA Alignment with RSI Oversold</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#30</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['New Rule 4: Ultimate Oscillato...', 'Rule 1: MA Alignment with RSI ...', 'SMC Rule 2: Fair Value Gap Fil...', 'Momentum Rule 5: Momentum Brea...', 'Acad Rule 2: Mean Reversion Fa...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 10: Composite Sentimen...', 'Price Action Rule 3: Engulfing...', 'Ext Rule 3: Bollinger Squeeze ...', 'Professional Rule 7: Chaikin M...', 'Ext Rule 6: Fibonacci Support ...', 'Rule 10: Volume Spike', 'AI Rule 3: Smart Money Flow Di...', 'Rule 6: Stochastic Oversold Cr...'],
                y: [0, 0, 100.0, 0, 100.0, 66.24925462134765, 65.48034934497817, 65.1440329218107, 65.69236390208258, 66.28082536487166, 64.86330935251799, 64.59340197520488, 64.34993924665856, 64.54941527172667, 64.60104195228955],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['New Rule 4: Ultimate Oscillato...', 'Rule 1: MA Alignment with RSI ...', 'SMC Rule 2: Fair Value Gap Fil...', 'Momentum Rule 5: Momentum Brea...', 'Acad Rule 2: Mean Reversion Fa...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 10: Composite Sentimen...', 'Price Action Rule 3: Engulfing...', 'Ext Rule 3: Bollinger Squeeze ...', 'Professional Rule 7: Chaikin M...', 'Ext Rule 6: Fibonacci Support ...', 'Rule 10: Volume Spike', 'AI Rule 3: Smart Money Flow Di...', 'Rule 6: Stochastic Oversold Cr...'],
                y: [0.0, 0.0, np.float64(0.3988800591760322), 0.0, np.float64(7.61628468743385), np.float64(190.99439237192772), np.float64(183.08042076507988), np.float64(159.86079516297718), np.float64(127.67640849538822), np.float64(119.09333422480945), np.float64(117.1750843940623), np.float64(105.66889103402559), np.float64(101.12746719707677), np.float64(91.33571478299005), np.float64(76.26849443106826)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
