
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 14:46:42 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">2.49%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">946</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.2%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1.09</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>8.58%</strong></td>
                    <td>63.6%</td>
                    <td>261</td>
                    <td>1.06</td>
                    <td class="negative">19.23%</td>
                    <td class="positive"><strong>0.6367</strong></td>
                    <td class="negative">+0.82% / -1.34%</td>
                    <td>6h15m<br><small>(2.0m - 64h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>7.89%</strong></td>
                    <td>82.4%</td>
                    <td>17</td>
                    <td>2.91</td>
                    <td class="positive">1.56%</td>
                    <td class="positive"><strong>0.6332</strong></td>
                    <td class="negative">+0.84% / -1.30%</td>
                    <td>3h36m<br><small>(6.0m - 22h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>7.77%</strong></td>
                    <td>75.0%</td>
                    <td>24</td>
                    <td>2.00</td>
                    <td class="neutral">3.76%</td>
                    <td class="positive"><strong>0.5709</strong></td>
                    <td class="negative">+0.84% / -1.33%</td>
                    <td>4h21m<br><small>(13.0m - 27h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>2.48%</strong></td>
                    <td>72.7%</td>
                    <td>11</td>
                    <td>1.56</td>
                    <td class="neutral">2.08%</td>
                    <td class="positive"><strong>0.4232</strong></td>
                    <td class="negative">+0.82% / -1.37%</td>
                    <td>4h31m<br><small>(13.0m - 21h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>1.94%</strong></td>
                    <td>100.0%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.78% / 0.00%</td>
                    <td>14h10m<br><small>(6h49m - 21h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>1.04%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.85% / 0.00%</td>
                    <td>1h59m</td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>0.87%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.75% / 0.00%</td>
                    <td>5h6m</td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>0.37%</strong></td>
                    <td>66.7%</td>
                    <td>3</td>
                    <td>1.27</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>0.2431</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>5h38m<br><small>(48.0m - 14h6m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-0.95%</strong></td>
                    <td>50.0%</td>
                    <td>2</td>
                    <td>0.43</td>
                    <td class="positive">1.66%</td>
                    <td class="negative"><strong>-0.0586</strong></td>
                    <td class="negative">+0.77% / -1.32%</td>
                    <td>2h11m<br><small>(46.0m - 3h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="negative"><strong>-1.26%</strong></td>
                    <td>0.0%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0493</strong></td>
                    <td class="negative">+0.00% / -1.25%</td>
                    <td>28h39m</td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-1.30%</strong></td>
                    <td>50.0%</td>
                    <td>8</td>
                    <td>0.72</td>
                    <td class="neutral">2.82%</td>
                    <td class="positive"><strong>0.2047</strong></td>
                    <td class="negative">+0.78% / -1.07%</td>
                    <td>10h5m<br><small>(23.0m - 27h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="negative"><strong>-2.54%</strong></td>
                    <td>50.0%</td>
                    <td>8</td>
                    <td>0.54</td>
                    <td class="neutral">4.67%</td>
                    <td class="positive"><strong>0.1225</strong></td>
                    <td class="negative">+0.84% / -1.34%</td>
                    <td>5h42m<br><small>(3.0m - 39h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-3.47%</strong></td>
                    <td>61.5%</td>
                    <td>234</td>
                    <td>0.97</td>
                    <td class="negative">15.70%</td>
                    <td class="positive"><strong>0.5775</strong></td>
                    <td class="negative">+0.82% / -1.35%</td>
                    <td>5h23m<br><small>(3.0m - 64h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="negative"><strong>-4.41%</strong></td>
                    <td>61.3%</td>
                    <td>315</td>
                    <td>0.97</td>
                    <td class="negative">22.18%</td>
                    <td class="positive"><strong>0.5961</strong></td>
                    <td class="negative">+0.83% / -1.35%</td>
                    <td>5h18m<br><small>(1.0m - 60h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="negative"><strong>-6.06%</strong></td>
                    <td>33.3%</td>
                    <td>9</td>
                    <td>0.29</td>
                    <td class="negative">6.83%</td>
                    <td class="negative"><strong>-0.0276</strong></td>
                    <td class="negative">+0.84% / -1.27%</td>
                    <td>4h37m<br><small>(7.0m - 13h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="negative"><strong>-8.45%</strong></td>
                    <td>55.1%</td>
                    <td>49</td>
                    <td>0.73</td>
                    <td class="negative">12.89%</td>
                    <td class="positive"><strong>0.3414</strong></td>
                    <td class="negative">+0.82% / -1.35%</td>
                    <td>6h0m<br><small>(3.0m - 64h4m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.94%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.78% / 0.00%</td>
                    <td>14h10m<br><small>(6h49m - 21h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.04%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.85% / 0.00%</td>
                    <td>1h59m</td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">0.87%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.75% / 0.00%</td>
                    <td>5h6m</td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>82.4%</strong></td>
                    <td class="positive">7.89%</td>
                    <td>17</td>
                    <td>2.91</td>
                    <td class="positive">1.56%</td>
                    <td class="positive"><strong>0.6332</strong></td>
                    <td class="negative">+0.84% / -1.30%</td>
                    <td>3h36m<br><small>(6.0m - 22h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">7.77%</td>
                    <td>24</td>
                    <td>2.00</td>
                    <td class="neutral">3.76%</td>
                    <td class="positive"><strong>0.5709</strong></td>
                    <td class="negative">+0.84% / -1.33%</td>
                    <td>4h21m<br><small>(13.0m - 27h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>72.7%</strong></td>
                    <td class="positive">2.48%</td>
                    <td>11</td>
                    <td>1.56</td>
                    <td class="neutral">2.08%</td>
                    <td class="positive"><strong>0.4232</strong></td>
                    <td class="negative">+0.82% / -1.37%</td>
                    <td>4h31m<br><small>(13.0m - 21h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">0.37%</td>
                    <td>3</td>
                    <td>1.27</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>0.2431</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>5h38m<br><small>(48.0m - 14h6m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>63.6%</strong></td>
                    <td class="positive">8.58%</td>
                    <td>261</td>
                    <td>1.06</td>
                    <td class="negative">19.23%</td>
                    <td class="positive"><strong>0.6367</strong></td>
                    <td class="negative">+0.82% / -1.34%</td>
                    <td>6h15m<br><small>(2.0m - 64h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>61.5%</strong></td>
                    <td class="negative">-3.47%</td>
                    <td>234</td>
                    <td>0.97</td>
                    <td class="negative">15.70%</td>
                    <td class="positive"><strong>0.5775</strong></td>
                    <td class="negative">+0.82% / -1.35%</td>
                    <td>5h23m<br><small>(3.0m - 64h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>61.3%</strong></td>
                    <td class="negative">-4.41%</td>
                    <td>315</td>
                    <td>0.97</td>
                    <td class="negative">22.18%</td>
                    <td class="positive"><strong>0.5961</strong></td>
                    <td class="negative">+0.83% / -1.35%</td>
                    <td>5h18m<br><small>(1.0m - 60h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>55.1%</strong></td>
                    <td class="negative">-8.45%</td>
                    <td>49</td>
                    <td>0.73</td>
                    <td class="negative">12.89%</td>
                    <td class="positive"><strong>0.3414</strong></td>
                    <td class="negative">+0.82% / -1.35%</td>
                    <td>6h0m<br><small>(3.0m - 64h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-1.30%</td>
                    <td>8</td>
                    <td>0.72</td>
                    <td class="neutral">2.82%</td>
                    <td class="positive"><strong>0.2047</strong></td>
                    <td class="negative">+0.78% / -1.07%</td>
                    <td>10h5m<br><small>(23.0m - 27h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-2.54%</td>
                    <td>8</td>
                    <td>0.54</td>
                    <td class="neutral">4.67%</td>
                    <td class="positive"><strong>0.1225</strong></td>
                    <td class="negative">+0.84% / -1.34%</td>
                    <td>5h42m<br><small>(3.0m - 39h58m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-0.95%</td>
                    <td>2</td>
                    <td>0.43</td>
                    <td class="positive">1.66%</td>
                    <td class="negative"><strong>-0.0586</strong></td>
                    <td class="negative">+0.77% / -1.32%</td>
                    <td>2h11m<br><small>(46.0m - 3h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="neutral"><strong>33.3%</strong></td>
                    <td class="negative">-6.06%</td>
                    <td>9</td>
                    <td>0.29</td>
                    <td class="negative">6.83%</td>
                    <td class="negative"><strong>-0.0276</strong></td>
                    <td class="negative">+0.84% / -1.27%</td>
                    <td>4h37m<br><small>(7.0m - 13h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">-1.26%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0493</strong></td>
                    <td class="negative">+0.00% / -1.25%</td>
                    <td>28h39m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 10: Composite Sentimen...', 'AI Rule 8: Momentum Divergence...', 'Rule 6: Stochastic Oversold Cr...', 'Price Action Rule 3: Engulfing...', 'Volume Rule 3: Dark Pool Activ...', 'Advanced Rule 7: DMI ADX Filte...', 'Prof Rule 7: Mean Reversion Vo...', 'Professional Rule 10: CCI Reve...', 'Rule 10: Volume Spike', 'Volume Rule 5: Smart Money Vol...', 'Momentum Rule 2: Momentum Dive...', 'Rule 2: Golden Cross', 'Professional Rule 7: Chaikin M...'],
                y: [63.601532567049816, 61.53846153846154, 61.26984126984127, 82.35294117647058, 75.0, 100.0, 100.0, 100.0, 55.10204081632652, 72.72727272727273, 66.66666666666666, 50.0, 50.0, 50.0, 33.33333333333333],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 10: Composite Sentimen...', 'AI Rule 8: Momentum Divergence...', 'Rule 6: Stochastic Oversold Cr...', 'Price Action Rule 3: Engulfing...', 'Volume Rule 3: Dark Pool Activ...', 'Advanced Rule 7: DMI ADX Filte...', 'Prof Rule 7: Mean Reversion Vo...', 'Professional Rule 10: CCI Reve...', 'Rule 10: Volume Spike', 'Volume Rule 5: Smart Money Vol...', 'Momentum Rule 2: Momentum Dive...', 'Rule 2: Golden Cross', 'Professional Rule 7: Chaikin M...'],
                y: [np.float64(8.576767780926254), np.float64(-3.4703852119578222), np.float64(-4.411027946887713), np.float64(7.889888538428989), np.float64(7.772678114124005), np.float64(1.941919119369835), np.float64(1.0424615204698784), np.float64(0.8662372792468814), np.float64(-8.451483992726585), np.float64(2.4802737115372295), np.float64(0.3742551173217216), np.float64(-1.3027811398149933), np.float64(-2.5420054424966363), np.float64(-0.9500145842454949), np.float64(-6.064964755864886)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
