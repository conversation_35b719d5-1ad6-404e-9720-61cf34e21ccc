
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 13:13:28 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">67.33%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">5,070</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.4%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">2.39</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>46.87%</strong></td>
                    <td>68.2%</td>
                    <td>292</td>
                    <td>1.27</td>
                    <td class="negative">11.76%</td>
                    <td class="positive"><strong>0.7904</strong></td>
                    <td class="negative">+0.88% / -1.50%</td>
                    <td>1h31m<br><small>(1.0m - 29h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>33.81%</strong></td>
                    <td>62.8%</td>
                    <td>1516</td>
                    <td>1.03</td>
                    <td class="negative">36.62%</td>
                    <td class="positive"><strong>0.8398</strong></td>
                    <td class="negative">+0.89% / -1.44%</td>
                    <td>1h59m<br><small>(1.0m - 45h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>7.03%</strong></td>
                    <td>65.2%</td>
                    <td>46</td>
                    <td>1.27</td>
                    <td class="negative">5.36%</td>
                    <td class="positive"><strong>0.5315</strong></td>
                    <td class="negative">+0.94% / -1.32%</td>
                    <td>3h2m<br><small>(2.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>6.67%</strong></td>
                    <td>100.0%</td>
                    <td>6</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.83% / 0.00%</td>
                    <td>1h23m<br><small>(15.0m - 5h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>6.02%</strong></td>
                    <td>62.9%</td>
                    <td>89</td>
                    <td>1.10</td>
                    <td class="negative">11.36%</td>
                    <td class="positive"><strong>0.5490</strong></td>
                    <td class="negative">+0.92% / -1.36%</td>
                    <td>3h19m<br><small>(1.0m - 30h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>5.15%</strong></td>
                    <td>73.3%</td>
                    <td>15</td>
                    <td>1.72</td>
                    <td class="neutral">3.84%</td>
                    <td class="positive"><strong>0.4813</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h26m<br><small>(13.0m - 9h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>4.25%</strong></td>
                    <td>65.9%</td>
                    <td>41</td>
                    <td>1.16</td>
                    <td class="negative">11.40%</td>
                    <td class="positive"><strong>0.4804</strong></td>
                    <td class="negative">+0.92% / -1.39%</td>
                    <td>2h17m<br><small>(1.0m - 13h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>3.82%</strong></td>
                    <td>70.6%</td>
                    <td>17</td>
                    <td>1.43</td>
                    <td class="neutral">4.05%</td>
                    <td class="positive"><strong>0.4556</strong></td>
                    <td class="negative">+0.92% / -1.37%</td>
                    <td>2h7m<br><small>(3.0m - 8h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>2.21%</strong></td>
                    <td>100.0%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.83% / 0.00%</td>
                    <td>50.5m<br><small>(13.0m - 1h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-0.39%</strong></td>
                    <td>62.8%</td>
                    <td>113</td>
                    <td>0.99</td>
                    <td class="negative">11.22%</td>
                    <td class="positive"><strong>0.5247</strong></td>
                    <td class="negative">+0.86% / -1.41%</td>
                    <td>1h39m<br><small>(1.0m - 25h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="negative"><strong>-1.06%</strong></td>
                    <td>50.0%</td>
                    <td>10</td>
                    <td>0.88</td>
                    <td class="neutral">4.10%</td>
                    <td class="positive"><strong>0.2498</strong></td>
                    <td class="negative">+1.11% / -1.56%</td>
                    <td>4.0m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-1.64%</strong></td>
                    <td>61.8%</td>
                    <td>2315</td>
                    <td>1.00</td>
                    <td class="negative">47.62%</td>
                    <td class="positive"><strong>0.7686</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h44m<br><small>(1.0m - 51h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="negative"><strong>-2.81%</strong></td>
                    <td>33.3%</td>
                    <td>3</td>
                    <td>0.31</td>
                    <td class="neutral">2.10%</td>
                    <td class="negative"><strong>-0.1126</strong></td>
                    <td class="negative">+0.81% / -1.26%</td>
                    <td>6h3m<br><small>(2h41m - 9h59m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-3.62%</strong></td>
                    <td>58.6%</td>
                    <td>29</td>
                    <td>0.84</td>
                    <td class="negative">8.47%</td>
                    <td class="positive"><strong>0.3467</strong></td>
                    <td class="negative">+0.89% / -1.40%</td>
                    <td>2h31m<br><small>(10.0m - 12h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-6.52%</strong></td>
                    <td>50.0%</td>
                    <td>22</td>
                    <td>0.66</td>
                    <td class="negative">7.21%</td>
                    <td class="positive"><strong>0.2472</strong></td>
                    <td class="negative">+0.84% / -1.34%</td>
                    <td>2h36m<br><small>(15.0m - 18h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-32.45%</strong></td>
                    <td>59.9%</td>
                    <td>554</td>
                    <td>0.92</td>
                    <td class="negative">45.77%</td>
                    <td class="positive"><strong>0.5323</strong></td>
                    <td class="negative">+0.89% / -1.41%</td>
                    <td>2h52m<br><small>(2.0m - 49h14m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">6.67%</td>
                    <td>6</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.83% / 0.00%</td>
                    <td>1h23m<br><small>(15.0m - 5h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">2.21%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.83% / 0.00%</td>
                    <td>50.5m<br><small>(13.0m - 1h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>73.3%</strong></td>
                    <td class="positive">5.15%</td>
                    <td>15</td>
                    <td>1.72</td>
                    <td class="neutral">3.84%</td>
                    <td class="positive"><strong>0.4813</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h26m<br><small>(13.0m - 9h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>70.6%</strong></td>
                    <td class="positive">3.82%</td>
                    <td>17</td>
                    <td>1.43</td>
                    <td class="neutral">4.05%</td>
                    <td class="positive"><strong>0.4556</strong></td>
                    <td class="negative">+0.92% / -1.37%</td>
                    <td>2h7m<br><small>(3.0m - 8h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>68.2%</strong></td>
                    <td class="positive">46.87%</td>
                    <td>292</td>
                    <td>1.27</td>
                    <td class="negative">11.76%</td>
                    <td class="positive"><strong>0.7904</strong></td>
                    <td class="negative">+0.88% / -1.50%</td>
                    <td>1h31m<br><small>(1.0m - 29h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>65.9%</strong></td>
                    <td class="positive">4.25%</td>
                    <td>41</td>
                    <td>1.16</td>
                    <td class="negative">11.40%</td>
                    <td class="positive"><strong>0.4804</strong></td>
                    <td class="negative">+0.92% / -1.39%</td>
                    <td>2h17m<br><small>(1.0m - 13h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>65.2%</strong></td>
                    <td class="positive">7.03%</td>
                    <td>46</td>
                    <td>1.27</td>
                    <td class="negative">5.36%</td>
                    <td class="positive"><strong>0.5315</strong></td>
                    <td class="negative">+0.94% / -1.32%</td>
                    <td>3h2m<br><small>(2.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>62.9%</strong></td>
                    <td class="positive">6.02%</td>
                    <td>89</td>
                    <td>1.10</td>
                    <td class="negative">11.36%</td>
                    <td class="positive"><strong>0.5490</strong></td>
                    <td class="negative">+0.92% / -1.36%</td>
                    <td>3h19m<br><small>(1.0m - 30h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>62.8%</strong></td>
                    <td class="negative">-0.39%</td>
                    <td>113</td>
                    <td>0.99</td>
                    <td class="negative">11.22%</td>
                    <td class="positive"><strong>0.5247</strong></td>
                    <td class="negative">+0.86% / -1.41%</td>
                    <td>1h39m<br><small>(1.0m - 25h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>62.8%</strong></td>
                    <td class="positive">33.81%</td>
                    <td>1516</td>
                    <td>1.03</td>
                    <td class="negative">36.62%</td>
                    <td class="positive"><strong>0.8398</strong></td>
                    <td class="negative">+0.89% / -1.44%</td>
                    <td>1h59m<br><small>(1.0m - 45h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>61.8%</strong></td>
                    <td class="negative">-1.64%</td>
                    <td>2315</td>
                    <td>1.00</td>
                    <td class="negative">47.62%</td>
                    <td class="positive"><strong>0.7686</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h44m<br><small>(1.0m - 51h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>59.9%</strong></td>
                    <td class="negative">-32.45%</td>
                    <td>554</td>
                    <td>0.92</td>
                    <td class="negative">45.77%</td>
                    <td class="positive"><strong>0.5323</strong></td>
                    <td class="negative">+0.89% / -1.41%</td>
                    <td>2h52m<br><small>(2.0m - 49h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>58.6%</strong></td>
                    <td class="negative">-3.62%</td>
                    <td>29</td>
                    <td>0.84</td>
                    <td class="negative">8.47%</td>
                    <td class="positive"><strong>0.3467</strong></td>
                    <td class="negative">+0.89% / -1.40%</td>
                    <td>2h31m<br><small>(10.0m - 12h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-6.52%</td>
                    <td>22</td>
                    <td>0.66</td>
                    <td class="negative">7.21%</td>
                    <td class="positive"><strong>0.2472</strong></td>
                    <td class="negative">+0.84% / -1.34%</td>
                    <td>2h36m<br><small>(15.0m - 18h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-1.06%</td>
                    <td>10</td>
                    <td>0.88</td>
                    <td class="neutral">4.10%</td>
                    <td class="positive"><strong>0.2498</strong></td>
                    <td class="negative">+1.11% / -1.56%</td>
                    <td>4.0m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="neutral"><strong>33.3%</strong></td>
                    <td class="negative">-2.81%</td>
                    <td>3</td>
                    <td>0.31</td>
                    <td class="neutral">2.10%</td>
                    <td class="negative"><strong>-0.1126</strong></td>
                    <td class="negative">+0.81% / -1.26%</td>
                    <td>6h3m<br><small>(2h41m - 9h59m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'AI Rule 8: Momentum Divergence...', 'Momentum Rule 2: Momentum Dive...', 'Rule 7: Bollinger Band Bounce', 'Professional Rule 7: Chaikin M...', 'Rule 6: Stochastic Oversold Cr...', 'Price Action Rule 3: Engulfing...', 'Volume Rule 5: Smart Money Vol...', 'Rule 27: Structure Break Up', 'Rule 10: Volume Spike', 'Rule 2: Golden Cross', 'Professional Rule 10: CCI Reve...', 'Advanced Rule 7: DMI ADX Filte...', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [68.15068493150685, 62.796833773087066, 62.83185840707964, 62.92134831460674, 61.77105831533477, 65.21739130434783, 59.92779783393502, 100.0, 65.85365853658537, 100.0, 73.33333333333333, 70.58823529411765, 58.620689655172406, 50.0, 50.0],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'AI Rule 8: Momentum Divergence...', 'Momentum Rule 2: Momentum Dive...', 'Rule 7: Bollinger Band Bounce', 'Professional Rule 7: Chaikin M...', 'Rule 6: Stochastic Oversold Cr...', 'Price Action Rule 3: Engulfing...', 'Volume Rule 5: Smart Money Vol...', 'Rule 27: Structure Break Up', 'Rule 10: Volume Spike', 'Rule 2: Golden Cross', 'Professional Rule 10: CCI Reve...', 'Advanced Rule 7: DMI ADX Filte...', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [np.float64(46.86544786881127), np.float64(33.80790075845665), np.float64(-0.39357126711333695), np.float64(6.020606467576232), np.float64(-1.638221377059279), np.float64(7.025232971012753), np.float64(-32.447463214117754), np.float64(6.674019479153183), np.float64(4.249405806962808), np.float64(2.212809322058849), np.float64(5.1506542935684125), np.float64(3.8212936820889736), np.float64(-3.619118515238923), np.float64(-6.523073049950151), np.float64(-1.0614967797311983)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
