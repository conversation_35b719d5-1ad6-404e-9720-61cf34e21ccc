
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 13:05:27 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">278.52%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">4,673</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">64.1%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">9.32</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>103.32%</strong></td>
                    <td>64.0%</td>
                    <td>1467</td>
                    <td>1.07</td>
                    <td class="negative">29.86%</td>
                    <td class="positive"><strong>1.0316</strong></td>
                    <td class="negative">+0.83% / -1.36%</td>
                    <td>5h13m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>88.97%</strong></td>
                    <td>64.2%</td>
                    <td>1499</td>
                    <td>1.06</td>
                    <td class="negative">31.92%</td>
                    <td class="positive"><strong>0.9896</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>4h41m<br><small>(1.0m - 85h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>61.38%</strong></td>
                    <td>64.2%</td>
                    <td>1138</td>
                    <td>1.05</td>
                    <td class="negative">32.02%</td>
                    <td class="positive"><strong>0.8899</strong></td>
                    <td class="negative">+0.83% / -1.41%</td>
                    <td>4h23m<br><small>(1.0m - 73h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>22.33%</strong></td>
                    <td>65.0%</td>
                    <td>177</td>
                    <td>1.13</td>
                    <td class="negative">21.02%</td>
                    <td class="positive"><strong>0.6334</strong></td>
                    <td class="negative">+0.82% / -1.45%</td>
                    <td>2h54m<br><small>(1.0m - 27h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>9.25%</strong></td>
                    <td>72.2%</td>
                    <td>18</td>
                    <td>1.62</td>
                    <td class="negative">9.75%</td>
                    <td class="positive"><strong>0.4837</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>2h49m<br><small>(5.0m - 14h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>8.00%</strong></td>
                    <td>64.5%</td>
                    <td>110</td>
                    <td>1.07</td>
                    <td class="negative">17.89%</td>
                    <td class="positive"><strong>0.5452</strong></td>
                    <td class="negative">+0.83% / -1.44%</td>
                    <td>4h10m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>7.81%</strong></td>
                    <td>100.0%</td>
                    <td>5</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.90% / 0.00%</td>
                    <td>1.4m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>6.62%</strong></td>
                    <td>69.2%</td>
                    <td>13</td>
                    <td>1.67</td>
                    <td class="neutral">4.52%</td>
                    <td class="positive"><strong>0.4520</strong></td>
                    <td class="negative">+0.80% / -1.35%</td>
                    <td>3h46m<br><small>(9.0m - 17h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>2.40%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.26% / 0.00%</td>
                    <td>8.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>1.06%</strong></td>
                    <td>71.4%</td>
                    <td>7</td>
                    <td>1.16</td>
                    <td class="neutral">4.00%</td>
                    <td class="positive"><strong>0.3161</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>14h12m<br><small>(28.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>0.84%</strong></td>
                    <td>75.0%</td>
                    <td>4</td>
                    <td>1.26</td>
                    <td class="neutral">3.07%</td>
                    <td class="positive"><strong>0.3320</strong></td>
                    <td class="negative">+1.40% / -1.40%</td>
                    <td>30h23m<br><small>(15.0m - 83h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="negative"><strong>-1.63%</strong></td>
                    <td>66.7%</td>
                    <td>18</td>
                    <td>0.92</td>
                    <td class="negative">12.87%</td>
                    <td class="positive"><strong>0.3242</strong></td>
                    <td class="negative">+0.80% / -1.38%</td>
                    <td>5h36m<br><small>(4.0m - 47h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-2.15%</strong></td>
                    <td>61.3%</td>
                    <td>31</td>
                    <td>0.94</td>
                    <td class="negative">8.94%</td>
                    <td class="positive"><strong>0.3828</strong></td>
                    <td class="negative">+0.82% / -1.34%</td>
                    <td>5h37m<br><small>(6.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="negative"><strong>-2.77%</strong></td>
                    <td>0.0%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0631</strong></td>
                    <td class="negative">+0.00% / -1.35%</td>
                    <td>2h48m</td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-5.16%</strong></td>
                    <td>60.7%</td>
                    <td>28</td>
                    <td>0.84</td>
                    <td class="negative">12.38%</td>
                    <td class="positive"><strong>0.3314</strong></td>
                    <td class="negative">+0.81% / -1.39%</td>
                    <td>8h9m<br><small>(25.0m - 38h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-5.27%</strong></td>
                    <td>40.0%</td>
                    <td>5</td>
                    <td>0.33</td>
                    <td class="negative">6.31%</td>
                    <td class="negative"><strong>-0.0449</strong></td>
                    <td class="negative">+0.89% / -1.29%</td>
                    <td>5h12m<br><small>(41.0m - 20h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-5.51%</strong></td>
                    <td>62.8%</td>
                    <td>94</td>
                    <td>0.95</td>
                    <td class="negative">20.93%</td>
                    <td class="positive"><strong>0.4698</strong></td>
                    <td class="negative">+0.82% / -1.35%</td>
                    <td>4h40m<br><small>(1.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-10.97%</strong></td>
                    <td>56.1%</td>
                    <td>57</td>
                    <td>0.83</td>
                    <td class="negative">20.28%</td>
                    <td class="positive"><strong>0.3696</strong></td>
                    <td class="negative">+0.81% / -1.33%</td>
                    <td>8h42m<br><small>(12.0m - 68h37m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">7.81%</td>
                    <td>5</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.90% / 0.00%</td>
                    <td>1.4m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">2.40%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.26% / 0.00%</td>
                    <td>8.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">0.84%</td>
                    <td>4</td>
                    <td>1.26</td>
                    <td class="neutral">3.07%</td>
                    <td class="positive"><strong>0.3320</strong></td>
                    <td class="negative">+1.40% / -1.40%</td>
                    <td>30h23m<br><small>(15.0m - 83h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>72.2%</strong></td>
                    <td class="positive">9.25%</td>
                    <td>18</td>
                    <td>1.62</td>
                    <td class="negative">9.75%</td>
                    <td class="positive"><strong>0.4837</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>2h49m<br><small>(5.0m - 14h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>71.4%</strong></td>
                    <td class="positive">1.06%</td>
                    <td>7</td>
                    <td>1.16</td>
                    <td class="neutral">4.00%</td>
                    <td class="positive"><strong>0.3161</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>14h12m<br><small>(28.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>69.2%</strong></td>
                    <td class="positive">6.62%</td>
                    <td>13</td>
                    <td>1.67</td>
                    <td class="neutral">4.52%</td>
                    <td class="positive"><strong>0.4520</strong></td>
                    <td class="negative">+0.80% / -1.35%</td>
                    <td>3h46m<br><small>(9.0m - 17h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="negative">-1.63%</td>
                    <td>18</td>
                    <td>0.92</td>
                    <td class="negative">12.87%</td>
                    <td class="positive"><strong>0.3242</strong></td>
                    <td class="negative">+0.80% / -1.38%</td>
                    <td>5h36m<br><small>(4.0m - 47h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>65.0%</strong></td>
                    <td class="positive">22.33%</td>
                    <td>177</td>
                    <td>1.13</td>
                    <td class="negative">21.02%</td>
                    <td class="positive"><strong>0.6334</strong></td>
                    <td class="negative">+0.82% / -1.45%</td>
                    <td>2h54m<br><small>(1.0m - 27h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>64.5%</strong></td>
                    <td class="positive">8.00%</td>
                    <td>110</td>
                    <td>1.07</td>
                    <td class="negative">17.89%</td>
                    <td class="positive"><strong>0.5452</strong></td>
                    <td class="negative">+0.83% / -1.44%</td>
                    <td>4h10m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>64.2%</strong></td>
                    <td class="positive">88.97%</td>
                    <td>1499</td>
                    <td>1.06</td>
                    <td class="negative">31.92%</td>
                    <td class="positive"><strong>0.9896</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>4h41m<br><small>(1.0m - 85h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>64.2%</strong></td>
                    <td class="positive">61.38%</td>
                    <td>1138</td>
                    <td>1.05</td>
                    <td class="negative">32.02%</td>
                    <td class="positive"><strong>0.8899</strong></td>
                    <td class="negative">+0.83% / -1.41%</td>
                    <td>4h23m<br><small>(1.0m - 73h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>64.0%</strong></td>
                    <td class="positive">103.32%</td>
                    <td>1467</td>
                    <td>1.07</td>
                    <td class="negative">29.86%</td>
                    <td class="positive"><strong>1.0316</strong></td>
                    <td class="negative">+0.83% / -1.36%</td>
                    <td>5h13m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>62.8%</strong></td>
                    <td class="negative">-5.51%</td>
                    <td>94</td>
                    <td>0.95</td>
                    <td class="negative">20.93%</td>
                    <td class="positive"><strong>0.4698</strong></td>
                    <td class="negative">+0.82% / -1.35%</td>
                    <td>4h40m<br><small>(1.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>61.3%</strong></td>
                    <td class="negative">-2.15%</td>
                    <td>31</td>
                    <td>0.94</td>
                    <td class="negative">8.94%</td>
                    <td class="positive"><strong>0.3828</strong></td>
                    <td class="negative">+0.82% / -1.34%</td>
                    <td>5h37m<br><small>(6.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>60.7%</strong></td>
                    <td class="negative">-5.16%</td>
                    <td>28</td>
                    <td>0.84</td>
                    <td class="negative">12.38%</td>
                    <td class="positive"><strong>0.3314</strong></td>
                    <td class="negative">+0.81% / -1.39%</td>
                    <td>8h9m<br><small>(25.0m - 38h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>56.1%</strong></td>
                    <td class="negative">-10.97%</td>
                    <td>57</td>
                    <td>0.83</td>
                    <td class="negative">20.28%</td>
                    <td class="positive"><strong>0.3696</strong></td>
                    <td class="negative">+0.81% / -1.33%</td>
                    <td>8h42m<br><small>(12.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="neutral"><strong>40.0%</strong></td>
                    <td class="negative">-5.27%</td>
                    <td>5</td>
                    <td>0.33</td>
                    <td class="negative">6.31%</td>
                    <td class="negative"><strong>-0.0449</strong></td>
                    <td class="negative">+0.89% / -1.29%</td>
                    <td>5h12m<br><small>(41.0m - 20h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">-2.77%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0631</strong></td>
                    <td class="negative">+0.00% / -1.35%</td>
                    <td>2h48m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Prof Rule 7: Mean Reversion Vo...', 'AI Rule 8: Momentum Divergence...', 'Rule 6: Stochastic Oversold Cr...', 'Acad Rule 2: Mean Reversion Fa...', 'Rule 27: Structure Break Up', 'Momentum Rule 2: Momentum Dive...', 'Volume Rule 5: Smart Money Vol...', 'Price Action Rule 3: Engulfing...', 'Professional Rule 10: CCI Reve...', 'Professional Rule 7: Chaikin M...', 'Rule 10: Volume Spike', 'Volume Rule 3: Dark Pool Activ...'],
                y: [64.0081799591002, 64.24282855236825, 64.23550087873463, 64.97175141242938, 64.54545454545455, 62.76595744680851, 100.0, 100.0, 72.22222222222221, 56.14035087719298, 69.23076923076923, 61.29032258064516, 66.66666666666666, 60.71428571428571, 75.0],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'AI Rule 10: Composite Sentimen...', 'Rule 7: Bollinger Band Bounce', 'Prof Rule 7: Mean Reversion Vo...', 'AI Rule 8: Momentum Divergence...', 'Rule 6: Stochastic Oversold Cr...', 'Acad Rule 2: Mean Reversion Fa...', 'Rule 27: Structure Break Up', 'Momentum Rule 2: Momentum Dive...', 'Volume Rule 5: Smart Money Vol...', 'Price Action Rule 3: Engulfing...', 'Professional Rule 10: CCI Reve...', 'Professional Rule 7: Chaikin M...', 'Rule 10: Volume Spike', 'Volume Rule 3: Dark Pool Activ...'],
                y: [np.float64(103.32044343552712), np.float64(88.9655891496733), np.float64(61.37717895328751), np.float64(22.33221770834124), np.float64(8.001065718450263), np.float64(-5.512044743908468), np.float64(7.806702451039587), np.float64(2.4006098140385292), np.float64(9.253689228156814), np.float64(-10.966872760663914), np.float64(6.623117568183851), np.float64(-2.1531654947824426), np.float64(-1.6346847230194979), np.float64(-5.158051597279875), np.float64(0.8354623126288352)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
