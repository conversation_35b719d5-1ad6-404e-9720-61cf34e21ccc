
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 10 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 13:09:15</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">10</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">62.5%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">12.5%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">44.1%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">67.4%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">3,700</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["44.1%","21.9%","22.9%","11.7%","3.7%","5.6%","5.2%","1.5%","4.6%","3.4%"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","Volume Rule 5: Smart Money Volume","Rule 6: Stochastic Oversold Cross","Professional Rule 10: CCI Reversal Enhanced","AI Rule 10: Composite Sentiment Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Rule 10: Volume Spike","Price Action Rule 3: Engulfing Pattern","Rule 27: Structure Break Up","Rule 2: Golden Cross"],"y":[44.135914658082065,21.906172090573193,22.8928128490197,11.664594688649043,3.665960176156543,5.62081498697371,5.162894357461642,1.4640825590535096,4.5600882245379175,3.44373412188211],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","Volume Rule 5: Smart Money Volume","Rule 6: Stochastic Oversold Cross","Professional Rule 10: CCI Reversal Enhanced","AI Rule 10: Composite Sentiment Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Rule 10: Volume Spike","Price Action Rule 3: Engulfing Pattern","Rule 27: Structure Break Up","Rule 2: Golden Cross"],"y":[215,92,207,92,1626,42,24,23,8,12],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","Volume Rule 5: Smart Money Volume","Rule 6: Stochastic Oversold Cross","Professional Rule 10: CCI Reversal Enhanced","AI Rule 10: Composite Sentiment Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Rule 10: Volume Spike","Price Action Rule 3: Engulfing Pattern","Rule 27: Structure Break Up","Rule 2: Golden Cross"],"y":[96,40,109,53,1003,23,11,16,2,6],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[44.135914658082065,21.906172090573193,22.8928128490197,11.664594688649043,3.665960176156543,5.62081498697371,5.162894357461642,1.4640825590535096,4.5600882245379175,3.44373412188211],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Prof Rule 7","Volume Rule 5","Rule 6","Professional Rule 10","AI Rule 10","Professional Rule 7","Rule 10","Price Action Rule 3","Rule 27","Rule 2"],"textposition":"top center","x":[11.507201034877806,6.435147365980315,14.234540544816454,10.501958472171989,39.05618326588733,5.143084252518134,3.2636001799470646,4.204254343390985,1.6161100070906969,4.14514609563003],"y":[44.135914658082065,21.906172090573193,22.8928128490197,11.664594688649043,3.665960176156543,5.62081498697371,5.162894357461642,1.4640825590535096,4.5600882245379175,3.44373412188211],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["44.1%","10.2%","9.0%","3.7%"],"textposition":"auto","x":["PROFESSIONAL","UNKNOWN","ORIGINAL","AI_GENERATED"],"y":[44.135914658082065,10.163916081312365,9.014882388225342,3.665960176156543],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["311","132","316","145","2629","65","35","39","10","18"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","Volume Rule 5: Smart Money Volume","Rule 6: Stochastic Oversold Cross","Professional Rule 10: CCI Reversal Enhanced","AI Rule 10: Composite Sentiment Reversal","Professional Rule 7: Chaikin Money Flow Reversal","Rule 10: Volume Spike","Price Action Rule 3: Engulfing Pattern","Rule 27: Structure Break Up","Rule 2: Golden Cross"],"y":[311,132,316,145,2629,65,35,39,10,18],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311],"y":[0,0.14191612430251468,0.28383224860502937,0.4257483729075441,0.5676644972100587,0.7095806215125734,0.8514967458150882,0.9934128701176028,1.1353289944201175,1.2772451187226321,1.4191612430251468,1.5610773673276614,1.7029934916301763,1.8449096159326908,1.9868257402352056,2.12874186453772,2.270657988840235,2.4125741131427496,2.5544902374452643,2.696406361747779,2.8383224860502936,2.9802386103528087,3.122154734655323,3.2640708589578376,3.4059869832603527,3.547903107562867,3.6898192318653815,3.8317353561678966,3.9736514804704113,4.1155676047729255,4.25748372907544,4.399399853377955,4.54131597768047,4.683232101982984,4.825148226285499,4.967064350588013,5.1089804748905285,5.250896599193044,5.392812723495558,5.534728847798072,5.676644972100587,5.818561096403101,5.960477220705617,6.102393345008132,6.244309469310646,6.38622559361316,6.528141717915675,6.670057842218189,6.811973966520705,6.9538900908232195,7.095806215125734,7.237722339428249,7.379638463730763,7.521554588033277,7.663470712335793,7.8053868366383075,7.947302960940823,8.089219085243336,8.231135209545851,8.373051333848366,8.51496745815088,8.656883582453396,8.79879970675591,8.940715831058425,9.08263195536094,9.224548079663453,9.366464203965968,9.508380328268483,9.650296452570998,9.792212576873514,9.934128701176027,10.076044825478542,10.217960949781057,10.359877074083572,10.501793198386087,10.6437093226886,10.785625446991116,10.92754157129363,11.069457695596144,11.211373819898661,11.353289944201174,11.49520606850369,11.637122192806203,11.779038317108718,11.920954441411235,12.062870565713748,12.204786690016263,12.346702814318776,12.488618938621292,12.630535062923807,12.77245118722632,12.914367311528837,13.05628343583135,13.198199560133865,13.340115684436379,13.482031808738894,13.62394793304141,13.765864057343924,13.907780181646439,14.049696305948952,14.191612430251467,14.33352855455398,14.475444678856498,14.617360803159013,14.759276927461526,14.901193051764041,15.043109176066555,15.185025300369071,15.326941424671586,15.4688575489741,15.610773673276615,15.752689797579128,15.894605921881645,16.036522046184157,16.17843817048667,16.32035429478919,16.462270419091702,16.604186543394217,16.746102667696732,16.888018791999247,17.02993491630176,17.171851040604277,17.313767164906793,17.455683289209304,17.59759941351182,17.739515537814334,17.88143166211685,18.023347786419365,18.16526391072188,18.307180035024395,18.449096159326906,18.591012283629425,18.732928407931936,18.87484453223445,19.016760656536967,19.158676780839482,19.300592905141997,19.44250902944451,19.584425153747027,19.72634127804954,19.868257402352054,20.01017352665457,20.152089650957084,20.2940057752596,20.435921899562114,20.57783802386463,20.719754148167144,20.861670272469656,21.003586396772175,21.145502521074686,21.2874186453772,21.429334769679716,21.57125089398223,21.713167018284746,21.85508314258726,21.996999266889777,22.13891539119229,22.280831515494803,22.422747639797322,22.564663764099834,22.70657988840235,22.848496012704864,22.99041213700738,23.13232826130989,23.274244385612405,23.416160509914924,23.558076634217436,23.69999275851995,23.84190888282247,23.98382500712498,24.125741131427496,24.267657255730008,24.409573380032526,24.551489504335038,24.693405628637553,24.83532175294007,24.977237877242583,25.1191540015451,25.261070125847613,25.40298625015013,25.54490237445264,25.686818498755155,25.828734623057674,25.970650747360185,26.1125668716627,26.25448299596522,26.39639912026773,26.538315244570246,26.680231368872757,26.822147493175276,26.964063617477787,27.105979741780303,27.24789586608282,27.389811990385333,27.531728114687848,27.67364423899036,27.815560363292878,27.957476487595393,28.099392611897905,28.241308736200423,28.383224860502935,28.52514098480545,28.66705710910796,28.80897323341048,28.950889357712995,29.092805482015507,29.234721606318026,29.37663773062054,29.518553854923052,29.660469979225564,29.802386103528082,29.944302227830597,30.08621835213311,30.228134476435628,30.370050600738143,30.511966725040654,30.653882849343173,30.795798973645685,30.9377150979482,31.07963122225071,31.22154734655323,31.363463470855745,31.505379595158256,31.647295719460775,31.78921184376329,31.931127968065802,32.07304409236831,32.21496021667083,32.35687634097334,32.49879246527586,32.64070858957838,32.78262471388089,32.924540838183404,33.066456962485915,33.208373086788434,33.35028921109095,33.492205335393464,33.63412145969598,33.776037583998495,33.917953708301006,34.05986983260352,34.201785956906036,34.343702081208555,34.48561820551107,34.627534329813585,34.7694504541161,34.91136657841861,35.05328270272112,35.19519882702364,35.33711495132616,35.47903107562867,35.62094719993119,35.7628633242337,35.90477944853621,36.04669557283873,36.18861169714124,36.33052782144376,36.47244394574627,36.61436007004879,36.7562761943513,36.89819231865381,37.04010844295633,37.18202456725885,37.32394069156136,37.46585681586387,37.60777294016639,37.7496890644689,37.891605188771415,38.03352131307393,38.17543743737645,38.317353561678964,38.459269685981475,38.601185810283994,38.743101934586505,38.88501805888902,39.026934183191536,39.168850307494054,39.310766431796566,39.45268255609908,39.594598680401596,39.73651480470411,39.878430929006626,40.02034705330914,40.162263177611656,40.30417930191417,40.44609542621668,40.5880115505192,40.72992767482171,40.87184379912423,41.01375992342675,41.15567604772926,41.29759217203177,41.43950829633429,41.5814244206368,41.72334054493931,41.86525666924183,42.00717279354435,42.14908891784686,42.29100504214937,42.43292116645189,42.5748372907544,42.71675341505692,42.85866953935943,43.00058566366195,43.14250178796446,43.284417912266974,43.42633403656949,43.568250160872005,43.71016628517452,43.852082409477035,43.99399853377955,44.135914658082065],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Volume Rule 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132],"y":[0,0.16595584917100906,0.3319116983420181,0.49786754751302714,0.6638233966840362,0.8297792458550453,0.9957350950260543,1.1616909441970633,1.3276467933680725,1.4936026425390811,1.6595584917100905,1.8255143408810994,1.9914701900521086,2.1574260392231173,2.3233818883941266,2.4893377375651355,2.655293586736145,2.8212494359071534,2.9872052850781623,3.153161134249172,3.319116983420181,3.48507283259119,3.651028681762199,3.816984530933208,3.982940380104217,4.148896229275226,4.3148520784462345,4.480807927617244,4.646763776788253,4.812719625959262,4.978675475130271,5.1446313243012805,5.31058717347229,5.476543022643298,5.642498871814307,5.808454720985316,5.974410570156325,6.140366419327334,6.306322268498344,6.472278117669353,6.638233966840362,6.8041898160113705,6.97014566518238,7.136101514353388,7.302057363524398,7.468013212695406,7.633969061866416,7.799924911037425,7.965880760208434,8.131836609379443,8.297792458550452,8.463748307721461,8.629704156892469,8.795660006063478,8.961615855234488,9.127571704405497,9.293527553576507,9.459483402747516,9.625439251918523,9.791395101089533,9.957350950260542,10.123306799431552,10.289262648602561,10.45521849777357,10.62117434694458,10.787130196115587,10.953086045286597,11.119041894457606,11.284997743628614,11.450953592799623,11.616909441970632,11.782865291141642,11.94882114031265,12.114776989483659,12.280732838654668,12.446688687825679,12.612644536996688,12.778600386167696,12.944556235338705,13.110512084509715,13.276467933680724,13.442423782851732,13.608379632022741,13.77433548119375,13.94029133036476,14.106247179535767,14.272203028706777,14.438158877877786,14.604114727048795,14.770070576219803,14.936026425390812,15.101982274561824,15.267938123732833,15.433893972903842,15.59984982207485,15.76580567124586,15.931761520416869,16.097717369587876,16.263673218758885,16.429629067929895,16.595584917100904,16.761540766271914,16.927496615442923,17.09345246461393,17.259408313784938,17.425364162955947,17.591320012126957,17.757275861297966,17.923231710468976,18.089187559639985,18.255143408810994,18.421099257982004,18.587055107153013,18.753010956324022,18.91896680549503,19.08492265466604,19.250878503837047,19.416834353008056,19.582790202179066,19.748746051350075,19.914701900521084,20.080657749692094,20.246613598863103,20.412569448034112,20.578525297205122,20.74448114637613,20.91043699554714,21.07639284471815,21.24234869388916,21.408304543060165,21.574260392231174,21.740216241402184,21.906172090573193],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316],"y":[0,0.0724456102817079,0.1448912205634158,0.21733683084512373,0.2897824411268316,0.36222805140853953,0.43467366169024746,0.5071192719719554,0.5795648822536632,0.6520104925353711,0.7244561028170791,0.7969017130987869,0.8693473233804949,0.9417929336622027,1.0142385439439108,1.0866841542256187,1.1591297645073264,1.2315753747890346,1.3040209850707423,1.3764665953524502,1.4489122056341581,1.5213578159158658,1.5938034261975738,1.666249036479282,1.7386946467609898,1.8111402570426978,1.8835858673244055,1.9560314776061134,2.0284770878878216,2.1009226981695295,2.1733683084512374,2.245813918732945,2.318259529014653,2.3907051392963607,2.463150749578069,2.5355963598597766,2.6080419701414845,2.6804875804231925,2.7529331907049004,2.8253788009866083,2.8978244112683162,2.970270021550024,3.0427156318317317,3.11516124211344,3.1876068523951475,3.260052462676856,3.332498072958564,3.4049436832402713,3.4773892935219797,3.549834903803687,3.6222805140853955,3.694726124367103,3.767171734648811,3.8396173449305193,3.912062955212227,3.984508565493935,4.056954175775643,4.12939978605735,4.201845396339059,4.274291006620766,4.346736616902475,4.419182227184183,4.49162783746589,4.564073447747599,4.636519058029306,4.7089646683110145,4.7814102785927215,4.853855888874429,4.926301499156138,4.998747109437845,5.071192719719553,5.143638330001261,5.216083940282969,5.288529550564677,5.360975160846385,5.433420771128093,5.505866381409801,5.578311991691509,5.650757601973217,5.723203212254925,5.7956488225366325,5.8680944328183395,5.940540043100048,6.012985653381756,6.085431263663463,6.157876873945172,6.23032248422688,6.302768094508588,6.375213704790295,6.447659315072004,6.520104925353712,6.592550535635419,6.664996145917128,6.737441756198836,6.809887366480543,6.8823329767622505,6.954778587043959,7.027224197325667,7.099669807607374,7.172115417889083,7.244561028170791,7.317006638452498,7.389452248734206,7.461897859015915,7.534343469297622,7.60678907957933,7.679234689861039,7.751680300142746,7.824125910424454,7.8965715207061615,7.96901713098787,8.041462741269576,8.113908351551286,8.186353961832994,8.2587995721147,8.331245182396408,8.403690792678118,8.476136402959824,8.548582013241532,8.621027623523242,8.69347323380495,8.765918844086656,8.838364454368365,8.910810064650073,8.98325567493178,9.055701285213487,9.128146895495197,9.200592505776903,9.273038116058611,9.345483726340321,9.417929336622029,9.490374946903735,9.562820557185443,9.635266167467153,9.707711777748859,9.780157388030567,9.852602998312276,9.925048608593983,9.99749421887569,10.069939829157398,10.142385439439106,10.214831049720814,10.287276660002522,10.359722270284232,10.432167880565938,10.504613490847646,10.577059101129354,10.649504711411062,10.72195032169277,10.794395931974478,10.866841542256186,10.939287152537894,11.011732762819602,11.08417837310131,11.156623983383017,11.229069593664725,11.301515203946433,11.373960814228141,11.44640642450985,11.518852034791557,11.591297645073265,11.663743255354975,11.736188865636679,11.808634475918389,11.881080086200097,11.953525696481805,12.025971306763513,12.09841691704522,12.170862527326927,12.243308137608635,12.315753747890344,12.388199358172052,12.46064496845376,12.533090578735468,12.605536189017176,12.677981799298882,12.75042740958059,12.8228730198623,12.895318630144008,12.967764240425716,13.040209850707424,13.112655460989131,13.185101071270838,13.257546681552546,13.329992291834255,13.402437902115963,13.474883512397671,13.547329122679379,13.619774732961085,13.692220343242793,13.764665953524501,13.83711156380621,13.909557174087919,13.982002784369627,14.054448394651335,14.12689400493304,14.199339615214749,14.271785225496457,14.344230835778166,14.416676446059874,14.489122056341582,14.561567666623288,14.634013276904996,14.706458887186704,14.778904497468412,14.851350107750122,14.92379571803183,14.996241328313538,15.068686938595244,15.141132548876952,15.21357815915866,15.286023769440368,15.358469379722077,15.430914990003785,15.503360600285491,15.5758062105672,15.648251820848907,15.720697431130615,15.793143041412323,15.865588651694033,15.93803426197574,16.010479872257445,16.082925482539153,16.15537109282086,16.227816703102572,16.30026231338428,16.37270792366599,16.445153533947696,16.5175991442294,16.59004475451111,16.662490364792816,16.734935975074528,16.807381585356236,16.879827195637944,16.952272805919648,17.024718416201356,17.097164026483064,17.169609636764775,17.242055247046483,17.31450085732819,17.3869464676099,17.459392077891604,17.53183768817331,17.60428329845502,17.67672890873673,17.74917451901844,17.821620129300147,17.89406573958185,17.96651134986356,18.038956960145267,18.111402570426975,18.183848180708686,18.256293790990394,18.328739401272102,18.401185011553807,18.473630621835515,18.546076232117223,18.61852184239893,18.690967452680642,18.76341306296235,18.835858673244058,18.908304283525762,18.98074989380747,19.053195504089178,19.125641114370886,19.198086724652597,19.270532334934305,19.34297794521601,19.415423555497718,19.487869165779426,19.560314776061134,19.63276038634284,19.705205996624553,19.77765160690626,19.850097217187965,19.922542827469673,19.99498843775138,20.06743404803309,20.139879658314797,20.21232526859651,20.284770878878213,20.35721648915992,20.42966209944163,20.502107709723337,20.574553320005045,20.646998930286752,20.719444540568464,20.79189015085017,20.864335761131876,20.936781371413584,21.009226981695292,21.081672591977,21.154118202258708,21.226563812540416,21.299009422822124,21.37145503310383,21.44390064338554,21.516346253667248,21.588791863948956,21.661237474230663,21.73368308451237,21.80612869479408,21.878574305075787,21.951019915357495,22.023465525639203,22.09591113592091,22.16835674620262,22.240802356484327,22.313247966766035,22.385693577047743,22.45813918732945,22.53058479761116,22.603030407892867,22.675476018174574,22.747921628456282,22.82036723873799,22.8928128490197],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Professional Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145],"y":[0,0.0804454806113727,0.1608909612227454,0.24133644183411812,0.3217819224454908,0.40222740305686355,0.48267288366823624,0.5631183642796089,0.6435638448909816,0.7240093255023544,0.8044548061137271,0.8849002867250998,0.9653457673364725,1.0457912479478453,1.1262367285592179,1.2066822091705907,1.2871276897819632,1.367573170393336,1.4480186510047088,1.5284641316160816,1.6089096122274542,1.689355092838827,1.7698005734501996,1.8502460540615724,1.930691534672945,2.011137015284318,2.0915824958956906,2.172027976507063,2.2524734571184357,2.3329189377298087,2.4133644183411813,2.493809898952554,2.5742553795639265,2.6547008601752995,2.735146340786672,2.815591821398045,2.8960373020094177,2.9764827826207902,3.0569282632321633,3.1373737438435354,3.2178192244549084,3.298264705066281,3.378710185677654,3.459155666289026,3.539601146900399,3.620046627511772,3.7004921081231448,3.780937588734517,3.86138306934589,3.941828549957263,4.022274030568636,4.102719511180008,4.183164991791381,4.263610472402753,4.344055953014126,4.424501433625498,4.504946914236871,4.5853923948482445,4.6658378754596175,4.74628335607099,4.826728836682363,4.907174317293736,4.987619797905108,5.068065278516481,5.148510759127853,5.228956239739226,5.309401720350599,5.389847200961971,5.470292681573344,5.550738162184717,5.63118364279609,5.711629123407462,5.792074604018835,5.8725200846302075,5.9529655652415805,6.0334110458529535,6.1138565264643265,6.194302007075698,6.274747487687071,6.355192968298444,6.435638448909817,6.51608392952119,6.596529410132562,6.676974890743935,6.757420371355308,6.837865851966679,6.918311332578052,6.998756813189425,7.079202293800798,7.159647774412171,7.240093255023544,7.3205387356349165,7.4009842162462895,7.481429696857662,7.561875177469034,7.642320658080407,7.72276613869178,7.803211619303153,7.883657099914526,7.964102580525899,8.044548061137272,8.124993541748642,8.205439022360016,8.285884502971388,8.366329983582762,8.446775464194134,8.527220944805507,8.60766642541688,8.688111906028253,8.768557386639625,8.849002867250997,8.92944834786237,9.009893828473743,9.090339309085117,9.170784789696489,9.251230270307861,9.331675750919235,9.412121231530607,9.49256671214198,9.573012192753351,9.653457673364725,9.733903153976097,9.814348634587471,9.894794115198843,9.975239595810216,10.055685076421588,10.136130557032962,10.216576037644334,10.297021518255706,10.37746699886708,10.457912479478452,10.538357960089826,10.618803440701198,10.69924892131257,10.779694401923942,10.860139882535316,10.940585363146688,11.02103084375806,11.101476324369434,11.181921804980806,11.26236728559218,11.34281276620355,11.423258246814925,11.503703727426297,11.58414920803767,11.664594688649043],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629],"y":[0,0.001394431409721013,0.002788862819442026,0.004183294229163038,0.005577725638884052,0.006972157048605064,0.008366588458326076,0.00976101986804709,0.011155451277768104,0.012549882687489116,0.013944314097210128,0.01533874550693114,0.016733176916652152,0.018127608326373168,0.01952203973609418,0.020916471145815192,0.022310902555536208,0.023705333965257216,0.025099765374978232,0.026494196784699244,0.027888628194420256,0.02928305960414127,0.03067749101386228,0.032071922423583296,0.033466353833304305,0.03486078524302532,0.036255216652746336,0.037649648062467345,0.03904407947218836,0.040438510881909376,0.041832942291630384,0.04322737370135139,0.044621805111072416,0.046016236520793424,0.04741066793051443,0.04880509934023545,0.050199530749956464,0.05159396215967747,0.05298839356939849,0.0543828249791195,0.05577725638884051,0.05717168779856153,0.05856611920828254,0.05996055061800355,0.06135498202772456,0.06274941343744558,0.06414384484716659,0.0655382762568876,0.06693270766660861,0.06832713907632963,0.06972157048605064,0.07111600189577166,0.07251043330549267,0.07390486471521368,0.07529929612493469,0.0766937275346557,0.07808815894437672,0.07948259035409774,0.08087702176381875,0.08227145317353976,0.08366588458326077,0.08506031599298178,0.08645474740270279,0.08784917881242381,0.08924361022214483,0.09063804163186584,0.09203247304158685,0.09342690445130786,0.09482133586102887,0.09621576727074989,0.0976101986804709,0.09900463009019192,0.10039906149991293,0.10179349290963394,0.10318792431935495,0.10458235572907597,0.10597678713879698,0.10737121854851799,0.108765649958239,0.11016008136796002,0.11155451277768103,0.11294894418740205,0.11434337559712306,0.11573780700684405,0.11713223841656507,0.1185266698262861,0.1199211012360071,0.12131553264572813,0.12270996405544912,0.12410439546517014,0.12549882687489117,0.12689325828461218,0.12828768969433318,0.1296821211040542,0.1310765525137752,0.13247098392349624,0.13386541533321722,0.13525984674293826,0.13665427815265926,0.13804870956238027,0.13944314097210128,0.1408375723818223,0.14223200379154333,0.1436264352012643,0.14502086661098534,0.14641529802070635,0.14780972943042736,0.1492041608401484,0.15059859224986938,0.15199302365959041,0.1533874550693114,0.15478188647903243,0.15617631788875344,0.15757074929847445,0.15896518070819549,0.16035961211791647,0.1617540435276375,0.16314847493735848,0.16454290634707952,0.16593733775680053,0.16733176916652154,0.16872620057624257,0.17012063198596356,0.1715150633956846,0.17290949480540557,0.1743039262151266,0.17569835762484762,0.17709278903456863,0.17848722044428966,0.17988165185401064,0.18127608326373168,0.1826705146734527,0.1840649460831737,0.1854593774928947,0.18685380890261571,0.18824824031233675,0.18964267172205773,0.19103710313177877,0.19243153454149978,0.19382596595122079,0.1952203973609418,0.1966148287706628,0.19800926018038384,0.19940369159010485,0.20079812299982586,0.20219255440954687,0.20358698581926787,0.20498141722898888,0.2063758486387099,0.20777028004843093,0.20916471145815194,0.21055914286787294,0.21195357427759395,0.21334800568731496,0.21474243709703597,0.216136868506757,0.217531299916478,0.21892573132619902,0.22032016273592003,0.22171459414564104,0.22310902555536205,0.22450345696508306,0.2258978883748041,0.22729231978452508,0.2286867511942461,0.23008118260396712,0.2314756140136881,0.23287004542340917,0.23426447683313015,0.23565890824285116,0.2370533396525722,0.2384477710622932,0.2398422024720142,0.24123663388173525,0.24263106529145625,0.24402549670117724,0.24541992811089824,0.24681435952061928,0.2482087909303403,0.2496032223400613,0.25099765374978233,0.25239208515950334,0.25378651656922435,0.2551809479789453,0.25657537938866637,0.2579698107983874,0.2593642422081084,0.2607586736178294,0.2621531050275504,0.2635475364372714,0.2649419678469925,0.2663363992567135,0.26773083066643444,0.26912526207615545,0.2705196934858765,0.2719141248955975,0.27330855630531853,0.27470298771503954,0.27609741912476055,0.27749185053448155,0.27888628194420256,0.28028071335392357,0.2816751447636446,0.2830695761733656,0.28446400758308665,0.28585843899280766,0.2872528704025286,0.2886473018122496,0.2900417332219707,0.2914361646316917,0.2928305960414127,0.2942250274511337,0.2956194588608547,0.29701389027057573,0.2984083216802968,0.29980275309001775,0.30119718449973876,0.30259161590945977,0.30398604731918083,0.30538047872890184,0.3067749101386228,0.30816934154834386,0.30956377295806486,0.3109582043677859,0.3123526357775069,0.3137470671872279,0.3151414985969489,0.3165359300066699,0.31793036141639097,0.3193247928261119,0.32071922423583293,0.32211365564555394,0.323508087055275,0.324902518464996,0.32629694987471697,0.32769138128443803,0.32908581269415904,0.33048024410388005,0.33187467551360106,0.33326910692332207,0.3346635383330431,0.3360579697427641,0.33745240115248515,0.3388468325622061,0.3402412639719271,0.3416356953816482,0.3430301267913692,0.3444245582010902,0.34581898961081114,0.3472134210205322,0.3486078524302532,0.3500022838399742,0.35139671524969524,0.35279114665941624,0.35418557806913725,0.35558000947885826,0.3569744408885793,0.3583688722983003,0.3597633037080213,0.36115773511774235,0.36255216652746336,0.36394659793718437,0.3653410293469054,0.3667354607566264,0.3681298921663474,0.3695243235760684,0.3709187549857894,0.3723131863955104,0.37370761780523143,0.3751020492149525,0.3764964806246735,0.37789091203439445,0.37928534344411546,0.38067977485383653,0.38207420626355754,0.38346863767327855,0.38486306908299955,0.38625750049272056,0.38765193190244157,0.38904636331216264,0.3904407947218836,0.3918352261316046,0.3932296575413256,0.39462408895104667,0.3960185203607677,0.39741295177048863,0.3988073831802097,0.4002018145899307,0.4015962459996517,0.40299067740937267,0.40438510881909373,0.40577954022881474,0.40717397163853575,0.4085684030482568,0.40996283445797777,0.4113572658676988,0.4127516972774198,0.41414612868714085,0.41554056009686186,0.4169349915065828,0.4183294229163039,0.4197238543260249,0.4211182857357459,0.4225127171454669,0.4239071485551879,0.4253015799649089,0.4266960113746299,0.428090442784351,0.42948487419407194,0.43087930560379295,0.432273737013514,0.433668168423235,0.435062599832956,0.436457031242677,0.43785146265239805,0.43924589406211906,0.44064032547184007,0.4420347568815611,0.4434291882912821,0.4448236197010031,0.4462180511107241,0.44761248252044517,0.4490069139301661,0.4504013453398871,0.4517957767496082,0.4531902081593292,0.45458463956905015,0.4559790709787712,0.4573735023884922,0.45876793379821323,0.46016236520793424,0.4615567966176552,0.4629512280273762,0.4643456594370973,0.46574009084681833,0.4671345222565393,0.4685289536662603,0.4699233850759813,0.4713178164857023,0.47271224789542343,0.4741066793051444,0.4755011107148654,0.4768955421245864,0.4782899735343074,0.4796844049440284,0.4810788363537494,0.4824732677634705,0.4838676991731915,0.4852621305829125,0.48665656199263346,0.48805099340235447,0.4894454248120755,0.4908398562217965,0.4922342876315176,0.49362871904123856,0.49502315045095957,0.4964175818606806,0.4978120132704016,0.4992064446801226,0.5006008760898436,0.5019953074995647,0.5033897389092856,0.5047841703190067,0.5061786017287276,0.5075730331384487,0.5089674645481697,0.5103618959578906,0.5117563273676118,0.5131507587773327,0.5145451901870538,0.5159396215967748,0.5173340530064957,0.5187284844162168,0.5201229158259377,0.5215173472356588,0.5229117786453799,0.5243062100551008,0.5257006414648219,0.5270950728745428,0.5284895042842639,0.529883935693985,0.5312783671037059,0.532672798513427,0.5340672299231479,0.5354616613328689,0.5368560927425899,0.5382505241523109,0.5396449555620321,0.541039386971753,0.542433818381474,0.543828249791195,0.545222681200916,0.5466171126106371,0.548011544020358,0.5494059754300791,0.5508004068398001,0.5521948382495211,0.5535892696592422,0.5549837010689631,0.5563781324786841,0.5577725638884051,0.5591669952981262,0.5605614267078471,0.5619558581175682,0.5633502895272892,0.5647447209370102,0.5661391523467312,0.5675335837564521,0.5689280151661733,0.5703224465758943,0.5717168779856153,0.5731113093953363,0.5745057408050572,0.5759001722147783,0.5772946036244992,0.5786890350342204,0.5800834664439414,0.5814778978536623,0.5828723292633834,0.5842667606731043,0.5856611920828254,0.5870556234925464,0.5884500549022674,0.5898444863119885,0.5912389177217094,0.5926333491314305,0.5940277805411515,0.5954222119508724,0.5968166433605936,0.5982110747703145,0.5996055061800355,0.6009999375897566,0.6023943689994775,0.6037888004091986,0.6051832318189195,0.6065776632286406,0.6079720946383617,0.6093665260480826,0.6107609574578037,0.6121553888675246,0.6135498202772456,0.6149442516869666,0.6163386830966877,0.6177331145064088,0.6191275459161297,0.6205219773258507,0.6219164087355717,0.6233108401452927,0.6247052715550138,0.6260997029647348,0.6274941343744558,0.6288885657841768,0.6302829971938978,0.6316774286036188,0.6330718600133398,0.6344662914230608,0.6358607228327819,0.6372551542425029,0.6386495856522239,0.6400440170619449,0.6414384484716659,0.6428328798813869,0.6442273112911079,0.645621742700829,0.64701617411055,0.648410605520271,0.649805036929992,0.651199468339713,0.6525938997494339,0.6539883311591551,0.6553827625688761,0.6567771939785971,0.6581716253883181,0.659566056798039,0.6609604882077601,0.662354919617481,0.6637493510272021,0.6651437824369232,0.6665382138466441,0.6679326452563652,0.6693270766660862,0.6707215080758071,0.6721159394855282,0.6735103708952492,0.6749048023049703,0.6762992337146913,0.6776936651244122,0.6790880965341333,0.6804825279438542,0.6818769593535753,0.6832713907632963,0.6846658221730173,0.6860602535827384,0.6874546849924593,0.6888491164021804,0.6902435478119013,0.6916379792216223,0.6930324106313435,0.6944268420410644,0.6958212734507854,0.6972157048605064,0.6986101362702274,0.7000045676799485,0.7013989990896694,0.7027934304993905,0.7041878619091115,0.7055822933188325,0.7069767247285536,0.7083711561382745,0.7097655875479955,0.7111600189577165,0.7125544503674376,0.7139488817771587,0.7153433131868796,0.7167377445966006,0.7181321760063216,0.7195266074160426,0.7209210388257637,0.7223154702354847,0.7237099016452057,0.7251043330549267,0.7264987644646477,0.7278931958743687,0.7292876272840897,0.7306820586938108,0.7320764901035318,0.7334709215132528,0.7348653529229737,0.7362597843326948,0.7376542157424157,0.7390486471521368,0.7404430785618579,0.7418375099715788,0.7432319413812999,0.7446263727910208,0.7460208042007419,0.7474152356104629,0.7488096670201838,0.750204098429905,0.7515985298396259,0.752992961249347,0.754387392659068,0.7557818240687889,0.75717625547851,0.7585706868882309,0.759965118297952,0.7613595497076731,0.762753981117394,0.7641484125271151,0.765542843936836,0.7669372753465571,0.768331706756278,0.7697261381659991,0.7711205695757202,0.7725150009854411,0.7739094323951621,0.7753038638048831,0.7766982952146041,0.7780927266243253,0.7794871580340462,0.7808815894437672,0.7822760208534882,0.7836704522632092,0.7850648836729303,0.7864593150826512,0.7878537464923723,0.7892481779020933,0.7906426093118143,0.7920370407215354,0.7934314721312563,0.7948259035409773,0.7962203349506983,0.7976147663604194,0.7990091977701403,0.8004036291798614,0.8017980605895824,0.8031924919993034,0.8045869234090244,0.8059813548187453,0.8073757862284665,0.8087702176381875,0.8101646490479085,0.8115590804576295,0.8129535118673504,0.8143479432770715,0.8157423746867924,0.8171368060965136,0.8185312375062346,0.8199256689159555,0.8213201003256766,0.8227145317353975,0.8241089631451186,0.8255033945548396,0.8268978259645606,0.8282922573742817,0.8296866887840026,0.8310811201937237,0.8324755516034447,0.8338699830131656,0.8352644144228867,0.8366588458326077,0.8380532772423287,0.8394477086520498,0.8408421400617707,0.8422365714714918,0.8436310028812127,0.8450254342909338,0.8464198657006549,0.8478142971103758,0.8492087285200969,0.8506031599298178,0.8519975913395388,0.8533920227492598,0.8547864541589809,0.856180885568702,0.8575753169784229,0.8589697483881439,0.860364179797865,0.8617586112075859,0.863153042617307,0.864547474027028,0.865941905436749,0.86733633684647,0.868730768256191,0.870125199665912,0.871519631075633,0.872914062485354,0.8743084938950751,0.8757029253047961,0.877097356714517,0.8784917881242381,0.8798862195339591,0.8812806509436801,0.8826750823534011,0.8840695137631222,0.8854639451728432,0.8868583765825642,0.8882528079922852,0.8896472394020062,0.8910416708117271,0.8924361022214482,0.8938305336311693,0.8952249650408903,0.8966193964506113,0.8980138278603322,0.8994082592700533,0.9008026906797743,0.9021971220894953,0.9035915534992164,0.9049859849089373,0.9063804163186584,0.9077748477283794,0.9091692791381003,0.9105637105478214,0.9119581419575424,0.9133525733672635,0.9147470047769845,0.9161414361867054,0.9175358675964265,0.9189302990061474,0.9203247304158685,0.9217191618255894,0.9231135932353104,0.9245080246450315,0.9259024560547524,0.9272968874644736,0.9286913188741946,0.9300857502839156,0.9314801816936367,0.9328746131033576,0.9342690445130786,0.9356634759227996,0.9370579073325206,0.9384523387422417,0.9398467701519626,0.9412412015616837,0.9426356329714046,0.9440300643811256,0.9454244957908469,0.9468189272005678,0.9482133586102888,0.9496077900200098,0.9510022214297308,0.9523966528394519,0.9537910842491728,0.9551855156588938,0.9565799470686148,0.9579743784783358,0.9593688098880568,0.9607632412977778,0.9621576727074987,0.9635521041172198,0.964946535526941,0.9663409669366619,0.967735398346383,0.969129829756104,0.970524261165825,0.971918692575546,0.9733131239852669,0.974707555394988,0.9761019868047089,0.97749641821443,0.978890849624151,0.9802852810338719,0.981679712443593,0.9830741438533139,0.9844685752630352,0.9858630066727562,0.9872574380824771,0.9886518694921982,0.9900463009019191,0.9914407323116402,0.9928351637213612,0.9942295951310821,0.9956240265408032,0.9970184579505241,0.9984128893602452,0.9998073207699661,1.0012017521796872,1.0025961835894084,1.0039906149991293,1.0053850464088503,1.0067794778185712,1.0081739092282924,1.0095683406380134,1.0109627720477343,1.0123572034574553,1.0137516348671762,1.0151460662768974,1.0165404976866184,1.0179349290963393,1.0193293605060603,1.0207237919157812,1.0221182233255026,1.0235126547352236,1.0249070861449445,1.0263015175546655,1.0276959489643864,1.0290903803741076,1.0304848117838286,1.0318792431935495,1.0332736746032705,1.0346681060129914,1.0360625374227126,1.0374569688324335,1.0388514002421545,1.0402458316518755,1.0416402630615966,1.0430346944713176,1.0444291258810388,1.0458235572907597,1.0472179887004807,1.0486124201102016,1.0500068515199228,1.0514012829296437,1.0527957143393647,1.0541901457490856,1.0555845771588066,1.0569790085685278,1.0583734399782487,1.05976787138797,1.0611623027976909,1.0625567342074118,1.0639511656171328,1.065345597026854,1.066740028436575,1.0681344598462958,1.0695288912560168,1.0709233226657378,1.072317754075459,1.0737121854851799,1.0751066168949008,1.0765010483046218,1.0778954797143427,1.0792899111240641,1.080684342533785,1.082078773943506,1.083473205353227,1.084867636762948,1.0862620681726691,1.08765649958239,1.089050930992111,1.090445362401832,1.091839793811553,1.0932342252212741,1.094628656630995,1.096023088040716,1.097417519450437,1.0988119508601581,1.1002063822698793,1.1016008136796003,1.1029952450893212,1.1043896764990422,1.1057841079087631,1.1071785393184843,1.1085729707282053,1.1099674021379262,1.1113618335476472,1.1127562649573681,1.1141506963670893,1.1155451277768103,1.1169395591865312,1.1183339905962524,1.1197284220059733,1.1211228534156943,1.1225172848254155,1.1239117162351364,1.1253061476448574,1.1267005790545783,1.1280950104642993,1.1294894418740205,1.1308838732837414,1.1322783046934624,1.1336727361031833,1.1350671675129043,1.1364615989226257,1.1378560303323466,1.1392504617420676,1.1406448931517885,1.1420393245615095,1.1434337559712306,1.1448281873809516,1.1462226187906726,1.1476170502003935,1.1490114816101145,1.1504059130198356,1.1518003444295566,1.1531947758392775,1.1545892072489985,1.1559836386587197,1.1573780700684408,1.1587725014781618,1.1601669328878828,1.1615613642976037,1.1629557957073247,1.1643502271170458,1.1657446585267668,1.1671390899364877,1.1685335213462087,1.1699279527559296,1.1713223841656508,1.1727168155753718,1.1741112469850927,1.175505678394814,1.1769001098045349,1.178294541214256,1.179688972623977,1.181083404033698,1.182477835443419,1.1838722668531398,1.185266698262861,1.186661129672582,1.188055561082303,1.1894499924920239,1.1908444239017448,1.192238855311466,1.1936332867211872,1.1950277181309081,1.196422149540629,1.19781658095035,1.199211012360071,1.2006054437697922,1.2019998751795131,1.203394306589234,1.204788737998955,1.206183169408676,1.2075776008183972,1.208972032228118,1.210366463637839,1.21176089504756,1.2131553264572812,1.2145497578670024,1.2159441892767233,1.2173386206864443,1.2187330520961652,1.2201274835058862,1.2215219149156074,1.2229163463253283,1.2243107777350493,1.2257052091447702,1.2270996405544912,1.2284940719642123,1.2298885033739333,1.2312829347836542,1.2326773661933754,1.2340717976030964,1.2354662290128176,1.2368606604225385,1.2382550918322595,1.2396495232419804,1.2410439546517014,1.2424383860614225,1.2438328174711435,1.2452272488808644,1.2466216802905854,1.2480161117003064,1.2494105431100275,1.2508049745197487,1.2521994059294697,1.2535938373391906,1.2549882687489116,1.2563827001586325,1.2577771315683537,1.2591715629780746,1.2605659943877956,1.2619604257975165,1.2633548572072375,1.2647492886169587,1.2661437200266796,1.2675381514364006,1.2689325828461215,1.2703270142558427,1.2717214456655639,1.2731158770752848,1.2745103084850058,1.2759047398947267,1.2772991713044477,1.2786936027141689,1.2800880341238898,1.2814824655336108,1.2828768969433317,1.2842713283530527,1.2856657597627739,1.2870601911724948,1.2884546225822158,1.289849053991937,1.291243485401658,1.292637916811379,1.2940323482211,1.295426779630821,1.296821211040542,1.2982156424502629,1.299610073859984,1.301004505269705,1.302398936679426,1.303793368089147,1.3051877994988679,1.306582230908589,1.3079766623183102,1.3093710937280312,1.3107655251377521,1.312159956547473,1.3135543879571943,1.3149488193669152,1.3163432507766362,1.3177376821863571,1.319132113596078,1.3205265450057992,1.3219209764155202,1.3233154078252412,1.324709839234962,1.326104270644683,1.3274987020544042,1.3288931334641254,1.3302875648738464,1.3316819962835673,1.3330764276932883,1.3344708591030092,1.3358652905127304,1.3372597219224513,1.3386541533321723,1.3400485847418933,1.3414430161516142,1.3428374475613354,1.3442318789710563,1.3456263103807773,1.3470207417904985,1.3484151732002194,1.3498096046099406,1.3512040360196615,1.3525984674293825,1.3539928988391035,1.3553873302488244,1.3567817616585456,1.3581761930682665,1.3595706244779875,1.3609650558877084,1.3623594872974294,1.3637539187071506,1.3651483501168715,1.3665427815265927,1.3679372129363137,1.3693316443460346,1.3707260757557558,1.3721205071654767,1.3735149385751977,1.3749093699849186,1.3763038013946396,1.3776982328043608,1.3790926642140817,1.3804870956238027,1.3818815270335236,1.3832759584432446,1.3846703898529658,1.386064821262687,1.3874592526724079,1.3888536840821288,1.3902481154918498,1.3916425469015707,1.393036978311292,1.3944314097210129,1.3958258411307338,1.3972202725404548,1.398614703950176,1.400009135359897,1.4014035667696179,1.4027979981793388,1.40419242958906,1.405586860998781,1.4069812924085021,1.408375723818223,1.409770155227944,1.411164586637665,1.412559018047386,1.413953449457107,1.415347880866828,1.416742312276549,1.41813674368627,1.419531175095991,1.420925606505712,1.422320037915433,1.4237144693251542,1.4251089007348752,1.4265033321445961,1.4278977635543173,1.4292921949640383,1.4306866263737592,1.4320810577834802,1.4334754891932011,1.4348699206029223,1.4362643520126432,1.4376587834223642,1.4390532148320851,1.440447646241806,1.4418420776515275,1.4432365090612485,1.4446309404709694,1.4460253718806904,1.4474198032904113,1.4488142347001325,1.4502086661098534,1.4516030975195744,1.4529975289292953,1.4543919603390163,1.4557863917487375,1.4571808231584584,1.4585752545681794,1.4599696859779003,1.4613641173876215,1.4627585487973425,1.4641529802070636,1.4655474116167846,1.4669418430265055,1.4683362744362265,1.4697307058459474,1.4711251372556686,1.4725195686653896,1.4739140000751105,1.4753084314848315,1.4767028628945524,1.4780972943042736,1.4794917257139946,1.4808861571237157,1.4822805885334367,1.4836750199431576,1.4850694513528788,1.4864638827625998,1.4878583141723207,1.4892527455820417,1.4906471769917626,1.4920416084014838,1.4934360398112048,1.4948304712209257,1.4962249026306467,1.4976193340403676,1.499013765450089,1.50040819685981,1.501802628269531,1.5031970596792519,1.5045914910889728,1.505985922498694,1.507380353908415,1.508774785318136,1.5101692167278569,1.5115636481375778,1.512958079547299,1.51435251095702,1.515746942366741,1.5171413737764619,1.518535805186183,1.519930236595904,1.5213246680056252,1.5227190994153461,1.524113530825067,1.525507962234788,1.526902393644509,1.5282968250542301,1.529691256463951,1.531085687873672,1.532480119283393,1.5338745506931142,1.5352689821028351,1.536663413512556,1.5380578449222773,1.5394522763319982,1.5408467077417192,1.5422411391514403,1.5436355705611613,1.5450300019708822,1.5464244333806032,1.5478188647903242,1.5492132962000453,1.5506077276097663,1.5520021590194872,1.5533965904292082,1.5547910218389291,1.5561854532486505,1.5575798846583715,1.5589743160680924,1.5603687474778134,1.5617631788875344,1.5631576102972555,1.5645520417069765,1.5659464731166974,1.5673409045264184,1.5687353359361393,1.5701297673458605,1.5715241987555815,1.5729186301653024,1.5743130615750234,1.5757074929847446,1.5771019243944657,1.5784963558041867,1.5798907872139076,1.5812852186236286,1.5826796500333495,1.5840740814430707,1.5854685128527917,1.5868629442625126,1.5882573756722336,1.5896518070819545,1.5910462384916757,1.5924406699013967,1.5938351013111176,1.5952295327208388,1.5966239641305597,1.5980183955402807,1.5994128269500019,1.6008072583597228,1.6022016897694438,1.6035961211791647,1.6049905525888857,1.6063849839986069,1.6077794154083278,1.6091738468180488,1.6105682782277697,1.6119627096374907,1.6133571410472118,1.614751572456933,1.616146003866654,1.617540435276375,1.6189348666860959,1.620329298095817,1.621723729505538,1.623118160915259,1.62451259232498,1.6259070237347009,1.627301455144422,1.628695886554143,1.630090317963864,1.631484749373585,1.632879180783306,1.6342736121930272,1.6356680436027482,1.6370624750124692,1.63845690642219,1.639851337831911,1.6412457692416322,1.6426402006513532,1.6440346320610741,1.645429063470795,1.646823494880516,1.6482179262902372,1.6496123576999582,1.6510067891096791,1.6524012205194003,1.6537956519291213,1.6551900833388422,1.6565845147485634,1.6579789461582843,1.6593733775680053,1.6607678089777262,1.6621622403874474,1.6635566717971684,1.6649511032068893,1.6663455346166103,1.6677399660263312,1.6691343974360524,1.6705288288457734,1.6719232602554945,1.6733176916652155,1.6747121230749364,1.6761065544846574,1.6775009858943786,1.6788954173040995,1.6802898487138205,1.6816842801235414,1.6830787115332624,1.6844731429429836,1.6858675743527045,1.6872620057624255,1.6886564371721464,1.6900508685818676,1.6914452999915888,1.6928397314013097,1.6942341628110307,1.6956285942207516,1.6970230256304726,1.6984174570401938,1.6998118884499147,1.7012063198596357,1.7026007512693566,1.7039951826790776,1.7053896140887987,1.7067840454985197,1.7081784769082407,1.7095729083179618,1.7109673397276828,1.712361771137404,1.713756202547125,1.7151506339568459,1.7165450653665668,1.7179394967762878,1.719333928186009,1.72072835959573,1.7221227910054508,1.7235172224151718,1.7249116538248928,1.726306085234614,1.7277005166443349,1.729094948054056,1.730489379463777,1.731883810873498,1.733278242283219,1.73467267369294,1.736067105102661,1.737461536512382,1.738855967922103,1.740250399331824,1.741644830741545,1.743039262151266,1.744433693560987,1.745828124970708,1.7472225563804291,1.7486169877901503,1.7500114191998712,1.7514058506095922,1.7528002820193131,1.754194713429034,1.7555891448387553,1.7569835762484762,1.7583780076581972,1.7597724390679181,1.761166870477639,1.7625613018873603,1.7639557332970812,1.7653501647068022,1.7667445961165233,1.7681390275262443,1.7695334589359655,1.7709278903456864,1.7723223217554074,1.7737167531651283,1.7751111845748493,1.7765056159845705,1.7779000473942914,1.7792944788040124,1.7806889102137333,1.7820833416234543,1.7834777730331755,1.7848722044428964,1.7862666358526176,1.7876610672623385,1.7890554986720595,1.7904499300817807,1.7918443614915016,1.7932387929012226,1.7946332243109435,1.7960276557206645,1.7974220871303856,1.7988165185401066,1.8002109499498276,1.8016053813595485,1.8029998127692695,1.8043942441789906,1.8057886755887118,1.8071831069984328,1.8085775384081537,1.8099719698178747,1.8113664012275956,1.8127608326373168,1.8141552640470378,1.8155496954567587,1.8169441268664797,1.8183385582762006,1.8197329896859218,1.8211274210956427,1.8225218525053637,1.8239162839150849,1.8253107153248058,1.826705146734527,1.828099578144248,1.829494009553969,1.8308884409636899,1.8322828723734108,1.833677303783132,1.835071735192853,1.836466166602574,1.8378605980122948,1.8392550294220158,1.840649460831737,1.842043892241458,1.8434383236511789,1.8448327550608998,1.8462271864706208,1.847621617880342,1.849016049290063,1.8504104806997839,1.8518049121095048,1.8531993435192262,1.8545937749289472,1.8559882063386683,1.8573826377483893,1.8587770691581103,1.8601715005678312,1.8615659319775522,1.8629603633872733,1.8643547947969943,1.8657492262067152,1.8671436576164362,1.8685380890261571,1.8699325204358783,1.8713269518455993,1.8727213832553202,1.8741158146650412,1.8755102460747621,1.8769046774844833,1.8782991088942043,1.8796935403039252,1.8810879717136462,1.8824824031233673,1.8838768345330883,1.8852712659428092,1.8866656973525302,1.8880601287622512,1.8894545601719723,1.8908489915816937,1.8922434229914147,1.8936378544011356,1.8950322858108566,1.8964267172205775,1.8978211486302987,1.8992155800400197,1.9006100114497406,1.9020044428594616,1.9033988742691825,1.9047933056789037,1.9061877370886247,1.9075821684983456,1.9089765999080666,1.9103710313177875,1.9117654627275087,1.9131598941372296,1.9145543255469506,1.9159487569566716,1.9173431883663925,1.9187376197761137,1.9201320511858346,1.9215264825955556,1.9229209140052765,1.9243153454149975,1.9257097768247187,1.9271042082344396,1.928498639644161,1.929893071053882,1.931287502463603,1.9326819338733239,1.934076365283045,1.935470796692766,1.936865228102487,1.938259659512208,1.9396540909219289,1.94104852233165,1.942442953741371,1.943837385151092,1.945231816560813,1.9466262479705339,1.948020679380255,1.949415110789976,1.950809542199697,1.9522039736094179,1.9535984050191388,1.95499283642886,1.956387267838581,1.957781699248302,1.9591761306580229,1.9605705620677438,1.961964993477465,1.963359424887186,1.964753856296907,1.9661482877066279,1.9675427191163493,1.9689371505260704,1.9703315819357914,1.9717260133455123,1.9731204447552333,1.9745148761649542,1.9759093075746754,1.9773037389843964,1.9786981703941173,1.9800926018038383,1.9814870332135592,1.9828814646232804,1.9842758960330014,1.9856703274427223,1.9870647588524433,1.9884591902621642,1.9898536216718854,1.9912480530816064,1.9926424844913273,1.9940369159010483,1.9954313473107692,1.9968257787204904,1.9982202101302113,1.9996146415399323,2.0010090729496532,2.0024035043593744,2.003797935769095,2.0051923671788168,2.0065867985885375,2.0079812299982587,2.00937566140798,2.0107700928177006,2.0121645242274218,2.0135589556371425,2.0149533870468637,2.016347818456585,2.0177422498663056,2.0191366812760267,2.0205311126857475,2.0219255440954687,2.02331997550519,2.0247144069149106,2.0261088383246317,2.0275032697343525,2.0288977011440736,2.030292132553795,2.0316865639635155,2.0330809953732367,2.0344754267829575,2.0358698581926786,2.0372642896024,2.0386587210121205,2.0400531524218417,2.0414475838315624,2.0428420152412836,2.0442364466510052,2.045630878060726,2.047025309470447,2.048419740880168,2.049814172289889,2.0512086036996102,2.052603035109331,2.053997466519052,2.055391897928773,2.056786329338494,2.058180760748215,2.059575192157936,2.060969623567657,2.062364054977378,2.063758486387099,2.06515291779682,2.066547349206541,2.067941780616262,2.069336212025983,2.070730643435704,2.072125074845425,2.073519506255146,2.074913937664867,2.076308369074588,2.077702800484309,2.07909723189403,2.080491663303751,2.0818860947134725,2.0832805261231933,2.0846749575329144,2.086069388942635,2.0874638203523563,2.0888582517620775,2.0902526831717982,2.0916471145815194,2.0930415459912406,2.0944359774009613,2.0958304088106825,2.0972248402204032,2.0986192716301244,2.1000137030398456,2.1014081344495663,2.1028025658592875,2.104196997269008,2.1055914286787294,2.1069858600884506,2.1083802914981713,2.1097747229078925,2.111169154317613,2.1125635857273344,2.1139580171370556,2.1153524485467763,2.1167468799564975,2.118141311366218,2.11953574277594,2.1209301741856605,2.1223246055953817,2.123719037005103,2.1251134684148236,2.126507899824545,2.1279023312342655,2.1292967626439867,2.130691194053708,2.1320856254634286,2.13348005687315,2.1348744882828705,2.1362689196925917,2.137663351102313,2.1390577825120336,2.1404522139217548,2.1418466453314755,2.1432410767411967,2.144635508150918,2.1460299395606386,2.1474243709703598,2.1488188023800805,2.1502132337898017,2.151607665199523,2.1530020966092436,2.1543965280189648,2.1557909594286855,2.1571853908384067,2.1585798222481283,2.159974253657849,2.16136868506757,2.162763116477291,2.164157547887012,2.1655519792967333,2.166946410706454,2.168340842116175,2.169735273525896,2.171129704935617,2.1725241363453383,2.173918567755059,2.17531299916478,2.176707430574501,2.178101861984222,2.1794962933939432,2.180890724803664,2.182285156213385,2.183679587623106,2.185074019032827,2.1864684504425482,2.187862881852269,2.18925731326199,2.190651744671711,2.192046176081432,2.193440607491153,2.194835038900874,2.1962294703105956,2.1976239017203163,2.1990183331300375,2.2004127645397586,2.2018071959494794,2.2032016273592006,2.2045960587689213,2.2059904901786425,2.2073849215883636,2.2087793529980844,2.2101737844078055,2.2115682158175263,2.2129626472272474,2.2143570786369686,2.2157515100466894,2.2171459414564105,2.2185403728661313,2.2199348042758524,2.2213292356855736,2.2227236670952943,2.2241180985050155,2.2255125299147362,2.2269069613244574,2.2283013927341786,2.2296958241438993,2.2310902555536205,2.2324846869633412,2.2338791183730624,2.2352735497827836,2.2366679811925048,2.238062412602226,2.2394568440119467,2.240851275421668,2.2422457068313886,2.2436401382411097,2.245034569650831,2.2464290010605517,2.247823432470273,2.2492178638799936,2.2506122952897147,2.252006726699436,2.2534011581091566,2.254795589518878,2.2561900209285985,2.2575844523383197,2.258978883748041,2.2603733151577616,2.261767746567483,2.2631621779772035,2.2645566093869247,2.265951040796646,2.2673454722063666,2.268739903616088,2.2701343350258085,2.2715287664355297,2.2729231978452513,2.274317629254972,2.2757120606646932,2.277106492074414,2.278500923484135,2.2798953548938563,2.281289786303577,2.282684217713298,2.284078649123019,2.28547308053274,2.2868675119424613,2.288261943352182,2.289656374761903,2.291050806171624,2.292445237581345,2.2938396689910663,2.295234100400787,2.296628531810508,2.298022963220229,2.29941739462995,2.3008118260396713,2.302206257449392,2.303600688859113,2.304995120268834,2.306389551678555,2.3077839830882763,2.309178414497997,2.3105728459077186,2.3119672773174393,2.3133617087271605,2.3147561401368817,2.3161505715466024,2.3175450029563236,2.3189394343660443,2.3203338657757655,2.3217282971854867,2.3231227285952074,2.3245171600049286,2.3259115914146493,2.3273060228243705,2.3287004542340917,2.3300948856438124,2.3314893170535336,2.3328837484632543,2.3342781798729755,2.3356726112826967,2.3370670426924174,2.3384614741021386,2.3398559055118593,2.3412503369215805,2.3426447683313016,2.3440391997410224,2.3454336311507435,2.3468280625604643,2.3482224939701855,2.3496169253799066,2.351011356789628,2.352405788199349,2.3538002196090697,2.355194651018791,2.356589082428512,2.357983513838233,2.359377945247954,2.3607723766576747,2.362166808067396,2.363561239477117,2.364955670886838,2.366350102296559,2.3677445337062797,2.369138965116001,2.370533396525722,2.3719278279354428,2.373322259345164,2.3747166907548847,2.376111122164606,2.377505553574327,2.3788999849840478,2.380294416393769,2.3816888478034897,2.383083279213211,2.384477710622932,2.3858721420326527,2.3872665734423744,2.388661004852095,2.3900554362618163,2.391449867671537,2.392844299081258,2.3942387304909793,2.3956331619007,2.3970275933104213,2.398422024720142,2.399816456129863,2.4012108875395843,2.402605318949305,2.4039997503590262,2.405394181768747,2.406788613178468,2.4081830445881893,2.40957747599791,2.4109719074076312,2.412366338817352,2.413760770227073,2.4151552016367943,2.416549633046515,2.417944064456236,2.419338495865957,2.420732927275678,2.4221273586853993,2.42352179009512,2.4249162215048417,2.4263106529145624,2.4277050843242836,2.4290995157340047,2.4304939471437255,2.4318883785534466,2.4332828099631674,2.4346772413728885,2.4360716727826097,2.4374661041923305,2.4388605356020516,2.4402549670117724,2.4416493984214935,2.4430438298312147,2.4444382612409354,2.4458326926506566,2.4472271240603773,2.4486215554700985,2.4500159868798197,2.4514104182895404,2.4528048496992616,2.4541992811089823,2.4555937125187035,2.4569881439284247,2.4583825753381454,2.4597770067478666,2.4611714381575873,2.4625658695673085,2.46396030097703,2.465354732386751,2.466749163796472,2.4681435952061928,2.469538026615914,2.470932458025635,2.472326889435356,2.473721320845077,2.4751157522547977,2.476510183664519,2.47790461507424,2.479299046483961,2.480693477893682,2.4820879093034027,2.483482340713124,2.484876772122845,2.486271203532566,2.487665634942287,2.4890600663520077,2.490454497761729,2.49184892917145,2.493243360581171,2.494637791990892,2.4960322234006127,2.497426654810334,2.498821086220055,2.500215517629776,2.5016099490394974,2.503004380449218,2.5043988118589393,2.50579324326866,2.507187674678381,2.5085821060881024,2.509976537497823,2.5113709689075443,2.512765400317265,2.514159831726986,2.5155542631367074,2.516948694546428,2.5183431259561493,2.51973755736587,2.521131988775591,2.5225264201853124,2.523920851595033,2.5253152830047543,2.526709714414475,2.528104145824196,2.5294985772339174,2.530893008643638,2.5322874400533593,2.53368187146308,2.535076302872801,2.5364707342825223,2.537865165692243,2.5392595971019642,2.5406540285116854,2.5420484599214066,2.5434428913311278,2.5448373227408485,2.5462317541505697,2.5476261855602904,2.5490206169700116,2.5504150483797328,2.5518094797894535,2.5532039111991747,2.5545983426088954,2.5559927740186166,2.5573872054283378,2.5587816368380585,2.5601760682477797,2.5615704996575004,2.5629649310672216,2.5643593624769427,2.5657537938866635,2.5671482252963846,2.5685426567061054,2.5699370881158266,2.5713315195255477,2.5727259509352685,2.5741203823449896,2.5755148137547104,2.5769092451644315,2.578303676574153,2.579698107983874,2.581092539393595,2.582486970803316,2.583881402213037,2.585275833622758,2.586670265032479,2.5880646964422,2.589459127851921,2.590853559261642,2.592247990671363,2.593642422081084,2.595036853490805,2.5964312849005258,2.597825716310247,2.599220147719968,2.600614579129689,2.60200901053941,2.6034034419491308,2.604797873358852,2.606192304768573,2.607586736178294,2.608981167588015,2.6103755989977357,2.611770030407457,2.613164461817178,2.614558893226899,2.6159533246366204,2.617347756046341,2.6187421874560624,2.6201366188657835,2.6215310502755043,2.6229254816852254,2.624319913094946,2.6257143445046673,2.6271087759143885,2.6285032073241092,2.6298976387338304,2.631292070143551,2.6326865015532723,2.6340809329629935,2.6354753643727142,2.6368697957824354,2.638264227192156,2.6396586586018773,2.6410530900115985,2.6424475214213192,2.6438419528310404,2.645236384240761,2.6466308156504823,2.6480252470602035,2.649419678469924,2.6508141098796454,2.652208541289366,2.6536029726990873,2.6549974041088085,2.6563918355185296,2.657786266928251,2.6591806983379715,2.6605751297476927,2.6619695611574135,2.6633639925671346,2.664758423976856,2.6661528553865765,2.6675472867962977,2.6689417182060184,2.6703361496157396,2.671730581025461,2.6731250124351815,2.6745194438449027,2.6759138752546234,2.6773083066643446,2.678702738074066,2.6800971694837865,2.6814916008935077,2.6828860323032284,2.6842804637129496,2.6856748951226708,2.6870693265323915,2.6884637579421127,2.6898581893518334,2.6912526207615546,2.692647052171276,2.694041483580997,2.695435914990718,2.696830346400439,2.69822477781016,2.699619209219881,2.701013640629602,2.702408072039323,2.703802503449044,2.705196934858765,2.706591366268486,2.707985797678207,2.709380229087928,2.710774660497649,2.71216909190737,2.713563523317091,2.714957954726812,2.716352386136533,2.717746817546254,2.719141248955975,2.720535680365696,2.721930111775417,2.723324543185138,2.724718974594859,2.72611340600458,2.727507837414301,2.728902268824022,2.730296700233743,2.731691131643464,2.7330855630531854,2.7344799944629066,2.7358744258726273,2.7372688572823485,2.738663288692069,2.7400577201017904,2.7414521515115116,2.7428465829212323,2.7442410143309535,2.745635445740674,2.7470298771503954,2.7484243085601165,2.7498187399698373,2.7512131713795585,2.752607602789279,2.7540020341990004,2.7553964656087215,2.7567908970184423,2.7581853284281634,2.759579759837884,2.7609741912476053,2.7623686226573265,2.7637630540670473,2.7651574854767684,2.766551916886489,2.7679463482962103,2.7693407797059315,2.7707352111156527,2.772129642525374,2.7735240739350946,2.7749185053448158,2.7763129367545365,2.7777073681642577,2.779101799573979,2.7804962309836996,2.7818906623934208,2.7832850938031415,2.7846795252128627,2.786073956622584,2.7874683880323046,2.7888628194420257,2.7902572508517465,2.7916516822614676,2.793046113671189,2.7944405450809096,2.7958349764906307,2.797229407900352,2.7986238393100726,2.800018270719794,2.8014127021295145,2.8028071335392357,2.804201564948957,2.8055959963586776,2.8069904277683992,2.80838485917812,2.809779290587841,2.811173721997562,2.812568153407283,2.8139625848170042,2.815357016226725,2.816751447636446,2.818145879046167,2.819540310455888,2.820934741865609,2.82232917327533,2.823723604685051,2.825118036094772,2.826512467504493,2.827906898914214,2.829301330323935,2.830695761733656,2.832090193143377,2.833484624553098,2.834879055962819,2.83627348737254,2.837667918782261,2.839062350191982,2.840456781601703,2.841851213011424,2.843245644421145,2.844640075830866,2.8460345072405873,2.8474289386503084,2.8488233700600296,2.8502178014697503,2.8516122328794715,2.8530066642891923,2.8544010956989134,2.8557955271086346,2.8571899585183553,2.8585843899280765,2.8599788213377972,2.8613732527475184,2.8627676841572396,2.8641621155669603,2.8655565469766815,2.8669509783864022,2.8683454097961234,2.8697398412058446,2.8711342726155653,2.8725287040252865,2.873923135435007,2.8753175668447284,2.8767119982544496,2.8781064296641703,2.8795008610738915,2.880895292483612,2.8822897238933334,2.883684155303055,2.8850785867127757,2.886473018122497,2.8878674495322176,2.889261880941939,2.89065631235166,2.8920507437613807,2.893445175171102,2.8948396065808226,2.896234037990544,2.897628469400265,2.8990229008099857,2.900417332219707,2.9018117636294276,2.903206195039149,2.90460062644887,2.9059950578585907,2.907389489268312,2.9087839206780326,2.9101783520877538,2.911572783497475,2.9129672149071957,2.914361646316917,2.9157560777266376,2.9171505091363588,2.91854494054608,2.9199393719558007,2.9213338033655223,2.922728234775243,2.924122666184964,2.925517097594685,2.926911529004406,2.9283059604141273,2.929700391823848,2.931094823233569,2.93248925464329,2.933883686053011,2.9352781174627323,2.936672548872453,2.938066980282174,2.939461411691895,2.940855843101616,2.9422502745113372,2.943644705921058,2.945039137330779,2.9464335687405,2.947828000150221,2.9492224315599422,2.950616862969663,2.952011294379384,2.953405725789105,2.954800157198826,2.9561945886085472,2.957589020018268,2.958983451427989,2.9603778828377103,2.9617723142474315,2.9631667456571527,2.9645611770668734,2.9659556084765946,2.9673500398863153,2.9687444712960365,2.9701389027057576,2.9715333341154784,2.9729277655251996,2.9743221969349203,2.9757166283446415,2.9771110597543626,2.9785054911640834,2.9798999225738045,2.9812943539835253,2.9826887853932464,2.9840832168029676,2.9854776482126884,2.9868720796224095,2.9882665110321303,2.9896609424418514,2.9910553738515726,2.9924498052612933,2.9938442366710145,2.9952386680807352,2.9966330994904564,2.998027530900178,2.9994219623098988,3.00081639371962,3.0022108251293407,3.003605256539062,3.004999687948783,3.0063941193585038,3.007788550768225,3.0091829821779457,3.010577413587667,3.011971844997388,3.0133662764071087,3.01476070781683,3.0161551392265507,3.017549570636272,3.018944002045993,3.0203384334557137,3.021732864865435,3.0231272962751556,3.024521727684877,3.025916159094598,3.0273105905043187,3.02870502191404,3.0300994533237606,3.031493884733482,3.032888316143203,3.0342827475529237,3.035677178962645,3.037071610372366,3.0384660417820872,3.039860473191808,3.041254904601529,3.0426493360112503,3.044043767420971,3.0454381988306922,3.046832630240413,3.048227061650134,3.0496214930598553,3.051015924469576,3.052410355879297,3.053804787289018,3.055199218698739,3.0565936501084603,3.057988081518181,3.059382512927902,3.0607769443376234,3.062171375747344,3.0635658071570653,3.064960238566786,3.066354669976507,3.0677491013862284,3.069143532795949,3.0705379642056703,3.071932395615391,3.073326827025112,3.0747212584348333,3.0761156898445545,3.0775101212542757,3.0789045526639964,3.0802989840737176,3.0816934154834383,3.0830878468931595,3.0844822783028807,3.0858767097126014,3.0872711411223226,3.0886655725320433,3.0900600039417645,3.0914544353514857,3.0928488667612064,3.0942432981709276,3.0956377295806483,3.0970321609903695,3.0984265924000907,3.0998210238098114,3.1012154552195326,3.1026098866292533,3.1040043180389745,3.1053987494486956,3.1067931808584164,3.1081876122681376,3.1095820436778583,3.1109764750875795,3.112370906497301,3.113765337907022,3.115159769316743,3.1165542007264637,3.117948632136185,3.119343063545906,3.120737494955627,3.122131926365348,3.1235263577750687,3.12492078918479,3.126315220594511,3.127709652004232,3.129104083413953,3.1304985148236737,3.131892946233395,3.133287377643116,3.1346818090528368,3.136076240462558,3.1374706718722787,3.138865103282,3.140259534691721,3.1416539661014418,3.143048397511163,3.1444428289208837,3.145837260330605,3.147231691740326,3.1486261231500468,3.150020554559768,3.151414985969489,3.1528094173792103,3.1542038487889315,3.155598280198652,3.1569927116083734,3.158387143018094,3.1597815744278153,3.1611760058375364,3.162570437247257,3.1639648686569783,3.165359300066699,3.1667537314764203,3.1681481628861414,3.169542594295862,3.1709370257055833,3.172331457115304,3.1737258885250252,3.1751203199347464,3.176514751344467,3.1779091827541883,3.179303614163909,3.1806980455736302,3.1820924769833514,3.183486908393072,3.1848813398027933,3.186275771212514,3.187670202622235,3.1890646340319564,3.1904590654416776,3.1918534968513987,3.1932479282611195,3.1946423596708406,3.1960367910805614,3.1974312224902826,3.1988256539000037,3.2002200853097245,3.2016145167194456,3.2030089481291664,3.2044033795388875,3.2057978109486087,3.2071922423583294,3.2085866737680506,3.2099811051777714,3.2113755365874925,3.2127699679972137,3.2141643994069344,3.2155588308166556,3.2169532622263763,3.2183476936360975,3.2197421250458187,3.2211365564555394,3.2225309878652606,3.2239254192749813,3.2253198506847025,3.2267142820944237,3.228108713504145,3.229503144913866,3.2308975763235868,3.232292007733308,3.233686439143029,3.23508087055275,3.236475301962471,3.2378697333721917,3.239264164781913,3.240658596191634,3.242053027601355,3.243447459011076,3.2448418904207967,3.246236321830518,3.247630753240239,3.24902518464996,3.250419616059681,3.2518140474694017,3.253208478879123,3.254602910288844,3.255997341698565,3.257391773108286,3.2587862045180067,3.260180635927728,3.261575067337449,3.26296949874717,3.264363930156891,3.265758361566612,3.2671527929763333,3.2685472243860545,3.2699416557957752,3.2713360872054964,3.272730518615217,3.2741249500249383,3.2755193814346595,3.27691381284438,3.2783082442541014,3.279702675663822,3.2810971070735433,3.2824915384832645,3.283885969892985,3.2852804013027064,3.286674832712427,3.2880692641221483,3.2894636955318695,3.29085812694159,3.2922525583513114,3.293646989761032,3.2950414211707533,3.2964358525804744,3.297830283990195,3.2992247153999164,3.300619146809637,3.3020135782193583,3.3034080096290794,3.3048024410388006,3.306196872448522,3.3075913038582425,3.3089857352679637,3.3103801666776844,3.3117745980874056,3.3131690294971268,3.3145634609068475,3.3159578923165687,3.31735232372629,3.3187467551360106,3.3201411865457318,3.3215356179554525,3.3229300493651737,3.324324480774895,3.3257189121846156,3.3271133435943367,3.3285077750040575,3.3299022064137787,3.3312966378235,3.3326910692332206,3.3340855006429417,3.3354799320526625,3.3368743634623836,3.338268794872105,3.3396632262818255,3.3410576576915467,3.342452089101268,3.343846520510989,3.34524095192071,3.346635383330431,3.348029814740152,3.349424246149873,3.350818677559594,3.352213108969315,3.353607540379036,3.355001971788757,3.356396403198478,3.357790834608199,3.35918526601792,3.360579697427641,3.361974128837362,3.363368560247083,3.364762991656804,3.3661574230665248,3.367551854476246,3.368946285885967,3.370340717295688,3.371735148705409,3.3731295801151298,3.374524011524851,3.375918442934572,3.377312874344293,3.378707305754014,3.380101737163735,3.3814961685734564,3.3828905999831775,3.3842850313928983,3.3856794628026194,3.38707389421234,3.3884683256220614,3.3898627570317825,3.3912571884415033,3.3926516198512244,3.394046051260945,3.3954404826706663,3.3968349140803875,3.3982293454901082,3.3996237768998294,3.40101820830955,3.4024126397192713,3.4038070711289925,3.4052015025387132,3.4065959339484344,3.407990365358155,3.4093847967678763,3.4107792281775975,3.412173659587318,3.4135680909970394,3.41496252240676,3.4163569538164813,3.417751385226203,3.4191458166359237,3.420540248045645,3.4219346794553656,3.4233291108650867,3.424723542274808,3.4261179736845286,3.42751240509425,3.4289068365039705,3.4303012679136917,3.431695699323413,3.4330901307331336,3.434484562142855,3.4358789935525755,3.4372734249622967,3.438667856372018,3.4400622877817386,3.44145671919146,3.4428511506011805,3.4442455820109017,3.445640013420623,3.4470344448303436,3.4484288762400648,3.4498233076497855,3.4512177390595067,3.452612170469228,3.4540066018789486,3.4554010332886698,3.456795464698391,3.458189896108112,3.459584327517833,3.460978758927554,3.462373190337275,3.463767621746996,3.465162053156717,3.466556484566438,3.467950915976159,3.46934534738588,3.470739778795601,3.472134210205322,3.473528641615043,3.474923073024764,3.476317504434485,3.477711935844206,3.479106367253927,3.480500798663648,3.481895230073369,3.48328966148309,3.484684092892811,3.486078524302532,3.487472955712253,3.488867387121974,3.490261818531695,3.491656249941416,3.493050681351137,3.4944451127608582,3.4958395441705794,3.4972339755803006,3.4986284069900213,3.5000228383997425,3.501417269809463,3.5028117012191844,3.5042061326289056,3.5056005640386263,3.5069949954483475,3.508389426858068,3.5097838582677894,3.5111782896775106,3.5125727210872313,3.5139671524969525,3.515361583906673,3.5167560153163944,3.5181504467261155,3.5195448781358363,3.5209393095455574,3.522333740955278,3.5237281723649994,3.5251226037747205,3.5265170351844413,3.5279114665941624,3.529305898003883,3.5307003294136043,3.5320947608233255,3.5334891922330467,3.534883623642768,3.5362780550524886,3.5376724864622098,3.539066917871931,3.5404613492816517,3.541855780691373,3.5432502121010936,3.5446446435108148,3.546039074920536,3.5474335063302567,3.548827937739978,3.5502223691496986,3.5516168005594198,3.553011231969141,3.5544056633788617,3.555800094788583,3.5571945261983036,3.5585889576080247,3.559983389017746,3.5613778204274666,3.562772251837188,3.5641666832469086,3.5655611146566297,3.566955546066351,3.5683499774760716,3.569744408885793,3.571138840295514,3.572533271705235,3.5739277031149563,3.575322134524677,3.5767165659343982,3.578110997344119,3.57950542875384,3.5808998601635613,3.582294291573282,3.5836887229830032,3.585083154392724,3.586477585802445,3.5878720172121663,3.589266448621887,3.590660880031608,3.592055311441329,3.59344974285105,3.5948441742607713,3.596238605670492,3.597633037080213,3.599027468489934,3.600421899899655,3.6018163313093763,3.603210762719097,3.604605194128818,3.605999625538539,3.60739405694826,3.6087884883579813,3.6101829197677024,3.6115773511774236,3.6129717825871444,3.6143662139968655,3.6157606454065863,3.6171550768163074,3.6185495082260286,3.6199439396357493,3.6213383710454705,3.6227328024551912,3.6241272338649124,3.6255216652746336,3.6269160966843543,3.6283105280940755,3.6297049595037962,3.6310993909135174,3.6324938223232386,3.6338882537329593,3.6352826851426805,3.6366771165524012,3.6380715479621224,3.6394659793718436,3.6408604107815643,3.6422548421912855,3.643649273601006,3.6450437050107274,3.6464381364204486,3.6478325678301697,3.649226999239891,3.6506214306496116,3.652015862059333,3.653410293469054,3.6548047248787747,3.656199156288496,3.6575935876982166,3.658988019107938,3.660382450517659,3.6617768819273797,3.663171313337101,3.6645657447468216,3.665960176156543],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">44.14%</td>
                <td>69.1%</td>
                <td>311</td>
                <td>1.29</td>
                <td>0.00</td>
                <td>11.51%</td>
                <td>68.4</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">21.91%</td>
                <td>69.7%</td>
                <td>132</td>
                <td>1.34</td>
                <td>0.00</td>
                <td>6.44%</td>
                <td>59.7</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">22.89%</td>
                <td>65.5%</td>
                <td>316</td>
                <td>1.14</td>
                <td>0.00</td>
                <td>14.23%</td>
                <td>58.8</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">11.66%</td>
                <td>63.4%</td>
                <td>145</td>
                <td>1.15</td>
                <td>0.00</td>
                <td>10.50%</td>
                <td>53.7</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">3.67%</td>
                <td>61.8%</td>
                <td>2629</td>
                <td>1.00</td>
                <td>0.00</td>
                <td>39.06%</td>
                <td>50.0</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">5.62%</td>
                <td>64.6%</td>
                <td>65</td>
                <td>1.16</td>
                <td>0.00</td>
                <td>5.14%</td>
                <td>41.1</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">5.16%</td>
                <td>68.6%</td>
                <td>35</td>
                <td>1.30</td>
                <td>0.00</td>
                <td>3.26%</td>
                <td>33.1</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">1.46%</td>
                <td>59.0%</td>
                <td>39</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>4.20%</td>
                <td>30.0</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Rule 27: Structure Break Up</td>
                <td>ORIGINAL</td>
                <td class="positive">4.56%</td>
                <td>80.0%</td>
                <td>10</td>
                <td>2.51</td>
                <td>0.00</td>
                <td>1.62%</td>
                <td>28.8</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Rule 2: Golden Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">3.44%</td>
                <td>72.2%</td>
                <td>18</td>
                <td>1.43</td>
                <td>0.00</td>
                <td>4.15%</td>
                <td>28.4</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
