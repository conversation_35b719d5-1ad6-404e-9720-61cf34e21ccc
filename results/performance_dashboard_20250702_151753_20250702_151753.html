
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 9 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 15:17:53</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">9</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">50.0%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">26.9%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">68.8%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">69.5%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">3,142</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["68.8%","50.3%","36.8%","39.3%","7.0%","16.9%","14.3%","7.9%","1.1%"],"textposition":"auto","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[68.75868007488758,50.340081387768066,36.799765911257474,39.31289027066771,6.9706728288889135,16.89678900581879,14.308383159603776,7.933104169538957,1.141987459186639],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[626,633,95,523,69,52,20,10,11],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[352,351,40,286,37,22,6,2,7],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[68.75868007488758,50.340081387768066,36.799765911257474,39.31289027066771,6.9706728288889135,16.89678900581879,14.308383159603776,7.933104169538957,1.141987459186639],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Ext Rule 6","AI Rule 10","Prof Rule 7","Rule 7","Rule 6","AI Rule 8","Momentum Rule 2","Price Action Rule 3","Rule 10"],"textposition":"top center","x":[21.988499330847596,19.633343140281767,12.046160548913445,21.98493886155417,10.370646128153709,6.017093275880547,2.4537000334167405,2.4952336332148595,4.041269247803138],"y":[68.75868007488758,50.340081387768066,36.799765911257474,39.31289027066771,6.9706728288889135,16.89678900581879,14.308383159603776,7.933104169538957,1.141987459186639],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["30.3%","33.6%","36.8%","15.8%"],"textposition":"auto","x":["UNKNOWN","AI_GENERATED","PROFESSIONAL","ORIGINAL"],"y":[30.333389134676775,33.61843519679343,36.799765911257474,15.80851685291442],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["978","984","135","809","106","74","26","12","18"],"textposition":"auto","x":["Ext Rule 6: Fibonacci Support Confluence","AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Rule 6: Stochastic Oversold Cross","AI Rule 8: Momentum Divergence Reversal","Momentum Rule 2: Momentum Divergence Recovery","Price Action Rule 3: Engulfing Pattern","Rule 10: Volume Spike"],"y":[978,984,135,809,106,74,26,12,18],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Ext Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978],"y":[0,0.0703053988495783,0.1406107976991566,0.21091619654873492,0.2812215953983132,0.3515269942478915,0.42183239309746984,0.4921377919470481,0.5624431907966264,0.6327485896462047,0.703053988495783,0.7733593873453614,0.8436647861949397,0.9139701850445179,0.9842755838940962,1.0545809827436745,1.1248863815932528,1.1951917804428311,1.2654971792924095,1.3358025781419878,1.406107976991566,1.4764133758411444,1.5467187746907227,1.617024173540301,1.6873295723898794,1.7576349712394577,1.8279403700890358,1.898245768938614,1.9685511677881924,2.038856566637771,2.109161965487349,2.179467364336927,2.2497727631865057,2.3200781620360837,2.3903835608856623,2.4606889597352404,2.530994358584819,2.601299757434397,2.6716051562839755,2.7419105551335536,2.812215953983132,2.8825213528327103,2.952826751682289,3.023132150531867,3.0934375493814454,3.1637429482310235,3.234048347080602,3.30435374593018,3.3746591447797587,3.444964543629337,3.5152699424789153,3.585575341328493,3.6558807401780715,3.7261861390276496,3.796491537877228,3.8667969367268062,3.937102335576385,4.007407734425963,4.077713133275542,4.14801853212512,4.218323930974698,4.288629329824277,4.358934728673854,4.429240127523433,4.499545526373011,4.569850925222589,4.6401563240721675,4.710461722921746,4.780767121771325,4.851072520620902,4.921377919470481,4.991683318320059,5.061988717169638,5.1322941160192155,5.202599514868794,5.2729049137183726,5.343210312567951,5.413515711417529,5.483821110267107,5.554126509116686,5.624431907966264,5.694737306815842,5.7650427056654205,5.835348104514999,5.905653503364578,5.975958902214155,6.046264301063734,6.116569699913312,6.186875098762891,6.257180497612469,6.327485896462047,6.397791295311626,6.468096694161204,6.538402093010783,6.60870749186036,6.679012890709939,6.749318289559517,6.819623688409096,6.889929087258674,6.960234486108252,7.030539884957831,7.100845283807409,7.171150682656986,7.2414560815065645,7.311761480356143,7.382066879205722,7.452372278055299,7.522677676904878,7.592983075754456,7.663288474604035,7.7335938734536125,7.803899272303191,7.87420467115277,7.944510070002348,8.014815468851927,8.085120867701505,8.155426266551084,8.225731665400662,8.29603706425024,8.366342463099818,8.436647861949396,8.506953260798975,8.577258659648553,8.64756405849813,8.717869457347708,8.788174856197287,8.858480255046866,8.928785653896444,8.999091052746023,9.0693964515956,9.139701850445178,9.210007249294756,9.280312648144335,9.350618046993914,9.420923445843492,9.49122884469307,9.56153424354265,9.631839642392226,9.702145041241804,9.772450440091383,9.842755838940962,9.91306123779054,9.983366636640119,10.053672035489697,10.123977434339276,10.194282833188854,10.264588232038431,10.33489363088801,10.405199029737588,10.475504428587167,10.545809827436745,10.616115226286324,10.686420625135902,10.75672602398548,10.827031422835057,10.897336821684636,10.967642220534215,11.037947619383793,11.108253018233372,11.17855841708295,11.248863815932529,11.319169214782107,11.389474613631684,11.459780012481263,11.530085411330841,11.60039081018042,11.670696209029998,11.741001607879577,11.811307006729155,11.881612405578734,11.95191780442831,12.022223203277889,12.092528602127468,12.162834000977046,12.233139399826625,12.303444798676203,12.373750197525782,12.44405559637536,12.514360995224939,12.584666394074516,12.654971792924094,12.725277191773673,12.795582590623251,12.86588798947283,12.936193388322408,13.006498787171987,13.076804186021565,13.147109584871142,13.21741498372072,13.2877203825703,13.358025781419878,13.428331180269456,13.498636579119035,13.568941977968613,13.639247376818192,13.709552775667769,13.779858174517347,13.850163573366926,13.920468972216504,13.990774371066083,14.061079769915661,14.13138516876524,14.201690567614818,14.271995966464397,14.342301365313972,14.41260676416355,14.482912163013129,14.553217561862708,14.623522960712286,14.693828359561865,14.764133758411443,14.834439157261022,14.904744556110598,14.975049954960177,15.045355353809756,15.115660752659334,15.185966151508913,15.256271550358491,15.32657694920807,15.396882348057648,15.467187746907225,15.537493145756804,15.607798544606382,15.67810394345596,15.74840934230554,15.818714741155118,15.889020140004696,15.959325538854275,16.029630937703853,16.09993633655343,16.17024173540301,16.240547134252587,16.310852533102167,16.381157931951744,16.451463330801325,16.5217687296509,16.59207412850048,16.66237952735006,16.732684926199635,16.802990325049215,16.873295723898792,16.943601122748372,17.01390652159795,17.08421192044753,17.154517319297106,17.224822718146687,17.29512811699626,17.365433515845844,17.435738914695417,17.506044313544994,17.576349712394574,17.64665511124415,17.71696051009373,17.787265908943308,17.857571307792888,17.927876706642465,17.998182105492045,18.068487504341622,18.1387929031912,18.20909830204078,18.279403700890356,18.349709099739936,18.420014498589513,18.490319897439093,18.56062529628867,18.63093069513825,18.701236093987827,18.771541492837404,18.841846891686984,18.91215229053656,18.98245768938614,19.052763088235718,19.1230684870853,19.193373885934875,19.263679284784452,19.333984683634032,19.40429008248361,19.47459548133319,19.544900880182766,19.615206279032346,19.685511677881923,19.755817076731503,19.82612247558108,19.896427874430657,19.966733273280237,20.037038672129814,20.107344070979394,20.17764946982897,20.24795486867855,20.318260267528128,20.38856566637771,20.458871065227285,20.529176464076862,20.599481862926442,20.66978726177602,20.7400926606256,20.810398059475176,20.880703458324756,20.951008857174333,21.02131425602391,21.09161965487349,21.161925053723067,21.232230452572647,21.302535851422224,21.372841250271804,21.44314664912138,21.51345204797096,21.583757446820538,21.654062845670115,21.724368244519695,21.794673643369272,21.864979042218852,21.93528444106843,22.00558983991801,22.075895238767586,22.146200637617163,22.216506036466743,22.28681143531632,22.3571168341659,22.427422233015477,22.497727631865057,22.568033030714634,22.638338429564214,22.70864382841379,22.778949227263368,22.84925462611295,22.919560024962525,22.989865423812105,23.060170822661682,23.130476221511262,23.20078162036084,23.27108701921042,23.341392418059996,23.411697816909573,23.482003215759153,23.55230861460873,23.62261401345831,23.692919412307887,23.763224811157468,23.833530210007044,23.90383560885662,23.9741410077062,24.044446406555778,24.11475180540536,24.185057204254935,24.255362603104516,24.325668001954092,24.395973400803673,24.46627879965325,24.536584198502826,24.606889597352406,24.677194996201983,24.747500395051564,24.81780579390114,24.88811119275072,24.958416591600297,25.028721990449878,25.099027389299454,25.16933278814903,25.23963818699861,25.30994358584819,25.38024898469777,25.450554383547345,25.520859782396926,25.591165181246502,25.66147058009608,25.73177597894566,25.802081377795236,25.872386776644817,25.942692175494393,26.012997574343974,26.08330297319355,26.15360837204313,26.223913770892707,26.294219169742284,26.364524568591865,26.43482996744144,26.50513536629102,26.5754407651406,26.64574616399018,26.716051562839755,26.786356961689336,26.856662360538913,26.92696775938849,26.99727315823807,27.067578557087646,27.137883955937227,27.208189354786803,27.278494753636384,27.34880015248596,27.419105551335537,27.489410950185118,27.559716349034694,27.630021747884275,27.70032714673385,27.77063254558343,27.84093794443301,27.91124334328259,27.981548742132166,28.051854140981742,28.122159539831323,28.1924649386809,28.26277033753048,28.333075736380056,28.403381135229637,28.473686534079214,28.543991932928794,28.61429733177837,28.684602730627944,28.754908129477528,28.8252135283271,28.895518927176685,28.965824326026258,29.03612972487584,29.106435123725415,29.176740522574992,29.247045921424572,29.31735132027415,29.38765671912373,29.457962117973306,29.528267516822886,29.598572915672463,29.668878314522043,29.73918371337162,29.809489112221197,29.879794511070777,29.950099909920354,30.020405308769934,30.09071070761951,30.16101610646909,30.231321505318668,30.301626904168245,30.371932303017825,30.442237701867402,30.512543100716982,30.58284849956656,30.65315389841614,30.723459297265716,30.793764696115296,30.864070094964873,30.93437549381445,31.00468089266403,31.074986291513607,31.145291690363187,31.215597089212764,31.285902488062344,31.35620788691192,31.4265132857615,31.49681868461108,31.567124083460655,31.637429482310235,31.707734881159812,31.778040280009392,31.84834567885897,31.91865107770855,31.988956476558126,32.05926187540771,32.12956727425728,32.19987267310686,32.270178071956444,32.34048347080602,32.4107888696556,32.481094268505174,32.55139966735476,32.621705066204335,32.69201046505391,32.76231586390349,32.832621262753065,32.90292666160265,32.973232060452226,33.0435374593018,33.11384285815138,33.18414825700096,33.25445365585054,33.32475905470012,33.39506445354969,33.46536985239927,33.535675251248854,33.60598065009843,33.67628604894801,33.746591447797584,33.81689684664716,33.887202245496745,33.95750764434632,34.0278130431959,34.098118442045475,34.16842384089506,34.238729239744636,34.30903463859421,34.37934003744379,34.44964543629337,34.51995083514294,34.59025623399252,34.660561632842104,34.73086703169169,34.80117243054126,34.871477829390834,34.94178322824042,35.01208862708999,35.08239402593957,35.15269942478915,35.22300482363873,35.2933102224883,35.363615621337885,35.43392102018746,35.504226419037046,35.574531817886616,35.6448372167362,35.715142615585776,35.78544801443535,35.85575341328493,35.926058812134514,35.99636421098409,36.06666960983367,36.136975008683244,36.20728040753283,36.2775858063824,36.34789120523198,36.41819660408156,36.48850200293114,36.55880740178071,36.629112800630296,36.69941819947987,36.76972359832945,36.840028997179026,36.91033439602861,36.980639794878186,37.05094519372776,37.12125059257734,37.191555991426924,37.2618613902765,37.33216678912608,37.402472187975654,37.47277758682524,37.54308298567481,37.61338838452439,37.68369378337397,37.75399918222355,37.82430458107312,37.894609979922706,37.96491537877228,38.03522077762186,38.105526176471436,38.17583157532102,38.2461369741706,38.31644237302017,38.38674777186975,38.457053170719334,38.527358569568904,38.59766396841849,38.667969367268064,38.73827476611765,38.80858016496722,38.8788855638168,38.94919096266638,39.01949636151596,39.08980176036553,39.160107159215116,39.23041255806469,39.30071795691427,39.371023355763846,39.44132875461343,39.51163415346301,39.58193955231258,39.65224495116216,39.722550350011744,39.792855748861314,39.8631611477109,39.933466546560474,40.00377194541006,40.07407734425963,40.14438274310921,40.21468814195879,40.284993540808365,40.35529893965794,40.425604338507526,40.4959097373571,40.56621513620668,40.636520535056256,40.70682593390584,40.77713133275542,40.847436731604994,40.91774213045457,40.988047529304154,41.058352928153724,41.12865832700331,41.198963725852884,41.26926912470247,41.33957452355204,41.40987992240162,41.4801853212512,41.550490720100775,41.62079611895035,41.691101517799936,41.76140691664951,41.83171231549909,41.902017714348666,41.97232311319825,42.04262851204782,42.112933910897404,42.18323930974698,42.253544708596564,42.323850107446134,42.39415550629572,42.464460905145295,42.53476630399488,42.60507170284445,42.67537710169403,42.74568250054361,42.815987899393186,42.88629329824276,42.956598697092346,43.02690409594192,43.0972094947915,43.167514893641076,43.23782029249066,43.30812569134023,43.378431090189814,43.44873648903939,43.519041887888974,43.589347286738544,43.65965268558813,43.729958084437705,43.80026348328728,43.87056888213686,43.94087428098644,44.01117967983602,44.081485078685596,44.15179047753517,44.222095876384756,44.292401275234326,44.36270667408391,44.43301207293349,44.50331747178307,44.57362287063264,44.643928269482224,44.7142336683318,44.784539067181385,44.854844466030954,44.92514986488054,44.995455263730115,45.06576066257969,45.13606606142927,45.20637146027885,45.27667685912843,45.346982257978,45.41728765682758,45.487593055677166,45.557898454526736,45.62820385337631,45.6985092522259,45.76881465107548,45.83912004992505,45.90942544877463,45.97973084762421,46.05003624647378,46.120341645323364,46.19064704417294,46.260952443022525,46.331257841872095,46.40156324072168,46.471868639571255,46.54217403842084,46.61247943727041,46.68278483611999,46.75309023496957,46.823395633819146,46.89370103266872,46.96400643151831,47.03431183036788,47.10461722921746,47.17492262806704,47.24522802691662,47.31553342576619,47.385838824615774,47.45614422346535,47.526449622314935,47.596755021164505,47.66706042001409,47.737365818863665,47.80767121771324,47.87797661656282,47.9482820154124,48.01858741426198,48.088892813111556,48.15919821196113,48.22950361081072,48.299809009660294,48.37011440850987,48.44041980735945,48.51072520620903,48.5810306050586,48.651336003908185,48.72164140275776,48.791946801607345,48.862252200456915,48.9325575993065,49.002862998156075,49.07316839700565,49.14347379585523,49.21377919470481,49.28408459355439,49.354389992403966,49.42469539125354,49.49500079010313,49.5653061889527,49.63561158780228,49.70591698665186,49.77622238550144,49.84652778435101,49.916833183200595,49.98713858205017,50.057443980899755,50.127749379749325,50.19805477859891,50.268360177448486,50.33866557629806,50.40897097514764,50.47927637399722,50.5495817728468,50.61988717169638,50.69019257054595,50.76049796939554,50.83080336824511,50.90110876709469,50.97141416594427,51.04171956479385,51.11202496364342,51.182330362493005,51.25263576134258,51.32294116019216,51.393246559041735,51.46355195789132,51.533857356740896,51.60416275559047,51.67446815444005,51.74477355328963,51.81507895213921,51.88538435098879,51.95568974983836,52.02599514868795,52.09630054753752,52.1666059463871,52.23691134523668,52.30721674408626,52.37752214293583,52.447827541785415,52.51813294063499,52.58843833948457,52.658743738334145,52.72904913718373,52.799354536033306,52.86965993488288,52.93996533373246,53.01027073258204,53.08057613143161,53.1508815302812,53.22118692913077,53.29149232798036,53.36179772682993,53.43210312567951,53.50240852452909,53.57271392337867,53.64301932222824,53.713324721077825,53.7836301199274,53.85393551877698,53.924240917626555,53.99454631647614,54.064851715325716,54.13515711417529,54.20546251302487,54.27576791187445,54.34607331072402,54.41637870957361,54.486684108423184,54.55698950727277,54.62729490612234,54.69760030497192,54.7679057038215,54.838211102671075,54.90851650152065,54.978821900370235,55.04912729921981,55.11943269806939,55.189738096918965,55.26004349576855,55.330348894618126,55.4006542934677,55.47095969231728,55.54126509116686,55.61157049001643,55.68187588886602,55.752181287715594,55.82248668656518,55.89279208541475,55.96309748426433,56.03340288311391,56.103708281963485,56.17401368081306,56.244319079662645,56.31462447851222,56.3849298773618,56.455235276211376,56.52554067506096,56.59584607391053,56.66615147276011,56.73645687160969,56.806762270459274,56.87706766930884,56.94737306815843,57.017678467008004,57.08798386585759,57.15828926470716,57.22859466355674,57.29890006240632,57.36920546125589,57.43951086010547,57.509816258955055,57.58012165780463,57.6504270566542,57.720732455503786,57.79103785435337,57.86134325320294,57.931648652052516,58.0019540509021,58.07225944975168,58.14256484860125,58.21287024745083,58.283175646300414,58.353481045149984,58.42378644399957,58.494091842849144,58.56439724169873,58.6347026405483,58.70500803939788,58.77531343824746,58.84561883709704,58.91592423594661,58.986229634796196,59.05653503364577,59.12684043249535,59.197145831344926,59.26745123019451,59.33775662904409,59.40806202789366,59.47836742674324,59.548672825592824,59.618978224442394,59.68928362329198,59.759589022141554,59.82989442099114,59.90019981984071,59.97050521869029,60.04081061753987,60.111116016389445,60.18142141523902,60.251726814088606,60.32203221293818,60.39233761178776,60.462643010637336,60.53294840948692,60.60325380833649,60.673559207186074,60.74386460603565,60.814170004885234,60.884475403734804,60.95478080258439,61.025086201433965,61.09539160028355,61.16569699913312,61.2360023979827,61.30630779683228,61.376613195681855,61.44691859453143,61.517223993381016,61.58752939223059,61.65783479108017,61.728140189929746,61.79844558877933,61.8687509876289,61.939056386478484,62.00936178532806,62.079667184177644,62.149972583027214,62.2202779818768,62.290583380726375,62.36088877957595,62.43119417842553,62.50149957727511,62.57180497612469,62.642110374974266,62.71241577382384,62.782721172673426,62.853026571523,62.92333197037258,62.99363736922216,63.06394276807174,63.13424816692131,63.204553565770894,63.27485896462047,63.345164363470055,63.415469762319624,63.48577516116921,63.556080560018785,63.62638595886836,63.69669135771794,63.76699675656752,63.8373021554171,63.907607554266676,63.97791295311625,64.04821835196583,64.11852375081541,64.18882914966498,64.25913454851457,64.32943994736415,64.39974534621372,64.4700507450633,64.54035614391289,64.61066154276246,64.68096694161204,64.75127234046161,64.8215777393112,64.89188313816076,64.96218853701035,65.03249393585993,65.10279933470952,65.17310473355909,65.24341013240867,65.31371553125824,65.38402093010782,65.45432632895739,65.52463172780698,65.59493712665656,65.66524252550613,65.73554792435571,65.8058533232053,65.87615872205487,65.94646412090445,66.01676951975402,66.0870749186036,66.15738031745317,66.22768571630276,66.29799111515234,66.36829651400193,66.4386019128515,66.50890731170108,66.57921271055065,66.64951810940023,66.7198235082498,66.79012890709939,66.86043430594897,66.93073970479854,67.00104510364812,67.07135050249771,67.14165590134728,67.21196130019686,67.28226669904643,67.35257209789602,67.42287749674558,67.49318289559517,67.56348829444475,67.63379369329432,67.7040990921439,67.77440449099349,67.84470988984306,67.91501528869264,67.98532068754221,68.0556260863918,68.12593148524138,68.19623688409095,68.26654228294053,68.33684768179012,68.40715308063969,68.47745847948927,68.54776387833884,68.61806927718843,68.688374676038,68.75868007488758],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984],"y":[0,0.05115861929651227,0.10231723859302454,0.1534758578895368,0.20463447718604907,0.2557930964825613,0.3069517157790736,0.3581103350755858,0.40926895437209815,0.4604275736686103,0.5115861929651226,0.5627448122616349,0.6139034315581472,0.6650620508546594,0.7162206701511716,0.767379289447684,0.8185379087441963,0.8696965280407085,0.9208551473372206,0.9720137666337331,1.0231723859302453,1.0743310052267576,1.1254896245232697,1.176648243819782,1.2278068631162944,1.2789654824128065,1.3301241017093188,1.3812827210058312,1.4324413403023433,1.4835999595988556,1.534758578895368,1.58591719819188,1.6370758174883926,1.6882344367849047,1.739393056081417,1.7905516753779291,1.8417102946744413,1.8928689139709538,1.9440275332674661,1.9951861525639782,2.0463447718604906,2.0975033911570025,2.1486620104535152,2.1998206297500276,2.2509792490465395,2.302137868343052,2.353296487639564,2.4044551069360764,2.455613726232589,2.5067723455291007,2.557930964825613,2.6090895841221253,2.6602482034186377,2.71140682271515,2.7625654420116623,2.813724061308174,2.8648826806046865,2.9160412999011993,2.967199919197711,3.0183585384942235,3.069517157790736,3.1206757770872477,3.17183439638376,3.2229930156802724,3.274151634976785,3.325310254273297,3.3764688735698094,3.4276274928663213,3.478786112162834,3.5299447314593464,3.5811033507558583,3.6322619700523706,3.6834205893488825,3.7345792086453953,3.7857378279419076,3.8368964472384195,3.8880550665349323,3.939213685831444,3.9903723051279565,4.041530924424469,4.092689543720981,4.143848163017493,4.195006782314005,4.246165401610518,4.2973240209070305,4.348482640203542,4.399641259500055,4.450799878796567,4.501958498093079,4.553117117389592,4.604275736686104,4.655434355982616,4.706592975279128,4.75775159457564,4.808910213872153,4.860068833168665,4.911227452465178,4.962386071761689,5.013544691058201,5.064703310354714,5.115861929651226,5.167020548947739,5.218179168244251,5.269337787540763,5.320496406837275,5.371655026133787,5.4228136454303,5.473972264726812,5.525130884023325,5.5762895033198365,5.627448122616348,5.678606741912861,5.729765361209373,5.780923980505886,5.832082599802399,5.88324121909891,5.934399838395422,5.985558457691934,6.036717076988447,6.08787569628496,6.139034315581472,6.190192934877984,6.2413515541744955,6.292510173471008,6.34366879276752,6.394827412064033,6.445986031360545,6.497144650657057,6.54830326995357,6.599461889250082,6.650620508546594,6.701779127843106,6.752937747139619,6.804096366436131,6.855254985732643,6.906413605029156,6.957572224325668,7.00873084362218,7.059889462918693,7.111048082215205,7.162206701511717,7.2133653208082285,7.264523940104741,7.315682559401253,7.366841178697765,7.417999797994279,7.4691584172907906,7.520317036587302,7.571475655883815,7.622634275180327,7.673792894476839,7.724951513773351,7.7761101330698645,7.827268752366376,7.878427371662888,7.929585990959401,7.980744610255913,8.031903229552425,8.083061848848939,8.13422046814545,8.185379087441962,8.236537706738474,8.287696326034986,8.338854945331498,8.39001356462801,8.441172183924524,8.492330803221035,8.543489422517547,8.594648041814061,8.645806661110573,8.696965280407085,8.748123899703597,8.79928251900011,8.850441138296622,8.901599757593134,8.952758376889646,9.003916996186158,9.05507561548267,9.106234234779183,9.157392854075695,9.208551473372207,9.259710092668719,9.310868711965233,9.362027331261745,9.413185950558256,9.464344569854768,9.51550318915128,9.566661808447792,9.617820427744306,9.668979047040818,9.72013766633733,9.771296285633841,9.822454904930355,9.873613524226867,9.924772143523379,9.97593076281989,10.027089382116403,10.078248001412915,10.129406620709428,10.18056524000594,10.231723859302452,10.282882478598964,10.334041097895478,10.38519971719199,10.436358336488501,10.487516955785015,10.538675575081527,10.589834194378039,10.64099281367455,10.692151432971063,10.743310052267574,10.794468671564086,10.8456272908606,10.896785910157112,10.947944529453624,10.999103148750137,11.05026176804665,11.101420387343161,11.152579006639673,11.203737625936185,11.254896245232697,11.306054864529209,11.357213483825722,11.408372103122234,11.459530722418746,11.51068934171526,11.561847961011772,11.613006580308284,11.664165199604797,11.715323818901307,11.76648243819782,11.817641057494331,11.868799676790845,11.919958296087357,11.971116915383869,12.022275534680382,12.073434153976894,12.124592773273406,12.17575139256992,12.226910011866432,12.278068631162943,12.329227250459455,12.380385869755967,12.431544489052479,12.482703108348991,12.533861727645505,12.585020346942017,12.63617896623853,12.68733758553504,12.738496204831554,12.789654824128066,12.840813443424578,12.89197206272109,12.943130682017603,12.994289301314113,13.045447920610627,13.09660653990714,13.14776515920365,13.198923778500165,13.250082397796675,13.301241017093188,13.352399636389702,13.403558255686212,13.454716874982726,13.505875494279238,13.557034113575748,13.608192732872261,13.659351352168775,13.710509971465285,13.761668590761799,13.812827210058312,13.863985829354823,13.915144448651336,13.966303067947848,14.01746168724436,14.068620306540872,14.119778925837386,14.170937545133896,14.22209616443041,14.27325478372692,14.324413403023433,14.375572022319947,14.426730641616457,14.47788926091297,14.529047880209482,14.580206499505994,14.631365118802506,14.68252373809902,14.73368235739553,14.784840976692044,14.835999595988557,14.887158215285067,14.938316834581581,14.989475453878095,15.040634073174605,15.091792692471119,15.14295131176763,15.194109931064142,15.245268550360654,15.296427169657164,15.347585788953678,15.398744408250192,15.449903027546702,15.501061646843215,15.552220266139729,15.60337888543624,15.654537504732753,15.705696124029265,15.756854743325777,15.808013362622289,15.859171981918802,15.910330601215312,15.961489220511826,16.012647839808338,16.06380645910485,16.11496507840136,16.166123697697877,16.217282316994385,16.2684409362909,16.31959955558741,16.370758174883925,16.421916794180436,16.47307541347695,16.52423403277346,16.575392652069972,16.626551271366484,16.677709890662996,16.72886850995951,16.78002712925602,16.831185748552535,16.882344367849047,16.93350298714556,16.98466160644207,17.035820225738586,17.086978845035095,17.138137464331606,17.189296083628122,17.24045470292463,17.291613322221146,17.342771941517654,17.39393056081417,17.44508918011068,17.496247799407193,17.547406418703705,17.59856503800022,17.64972365729673,17.700882276593244,17.752040895889756,17.803199515186268,17.85435813448278,17.905516753779292,17.956675373075804,18.007833992372316,18.05899261166883,18.11015123096534,18.161309850261855,18.212468469558367,18.26362708885488,18.31478570815139,18.365944327447902,18.417102946744414,18.468261566040926,18.519420185337438,18.57057880463395,18.621737423930465,18.672896043226974,18.72405466252349,18.77521328182,18.826371901116513,18.877530520413025,18.928689139709537,18.97984775900605,19.03100637830256,19.082164997599076,19.133323616895584,19.1844822361921,19.23564085548861,19.286799474785123,19.337958094081635,19.389116713378147,19.44027533267466,19.49143395197117,19.542592571267683,19.593751190564195,19.64490980986071,19.69606842915722,19.747227048453734,19.798385667750246,19.849544287046758,19.90070290634327,19.95186152563978,20.003020144936293,20.054178764232805,20.10533738352932,20.15649600282583,20.207654622122345,20.258813241418856,20.30997186071537,20.36113048001188,20.412289099308396,20.463447718604904,20.51460633790142,20.565764957197928,20.616923576494443,20.668082195790955,20.719240815087463,20.77039943438398,20.82155805368049,20.872716672977003,20.923875292273515,20.97503391157003,21.02619253086654,21.077351150163054,21.128509769459566,21.179668388756077,21.23082700805259,21.2819856273491,21.333144246645613,21.384302865942125,21.43546148523864,21.48662010453515,21.537778723831664,21.588937343128173,21.640095962424688,21.6912545817212,21.742413201017712,21.793571820314224,21.844730439610736,21.895889058907247,21.94704767820376,21.998206297500275,22.049364916796783,22.1005235360933,22.15168215538981,22.202840774686322,22.253999393982834,22.305158013279346,22.356316632575858,22.40747525187237,22.458633871168885,22.509792490465394,22.56095110976191,22.612109729058417,22.663268348354933,22.714426967651445,22.765585586947957,22.81674420624447,22.86790282554098,22.919061444837492,22.970220064134004,23.02137868343052,23.072537302727028,23.123695922023543,23.174854541320055,23.226013160616567,23.27717177991308,23.328330399209595,23.379489018506103,23.430647637802615,23.48180625709913,23.53296487639564,23.584123495692154,23.635282114988662,23.686440734285178,23.73759935358169,23.7887579728782,23.839916592174713,23.89107521147123,23.942233830767737,23.993392450064253,24.044551069360764,24.095709688657276,24.14686830795379,24.1980269272503,24.249185546546812,24.300344165843324,24.35150278513984,24.402661404436348,24.453820023732863,24.504978643029375,24.556137262325887,24.6072958816224,24.65845450091891,24.709613120215423,24.760771739511934,24.811930358808446,24.863088978104958,24.914247597401474,24.965406216697982,25.016564835994497,25.06772345529101,25.11888207458752,25.170040693884033,25.221199313180545,25.27235793247706,25.32351655177357,25.37467517107008,25.425833790366596,25.476992409663108,25.528151028959616,25.57930964825613,25.630468267552644,25.681626886849156,25.73278550614567,25.78394412544218,25.83510274473869,25.886261364035207,25.93741998333172,25.988578602628227,26.039737221924746,26.090895841221254,26.142054460517766,26.19321307981428,26.24437169911079,26.2955303184073,26.346688937703814,26.39784755700033,26.449006176296837,26.50016479559335,26.551323414889865,26.602482034186377,26.653640653482885,26.704799272779404,26.755957892075912,26.807116511372424,26.85827513066894,26.90943374996545,26.96059236926196,27.011750988558475,27.062909607854987,27.114068227151495,27.165226846448014,27.216385465744523,27.267544085041035,27.31870270433755,27.369861323634062,27.42101994293057,27.472178562227086,27.523337181523598,27.57449580082011,27.625654420116625,27.676813039413133,27.727971658709645,27.77913027800616,27.830288897302673,27.88144751659918,27.932606135895696,27.983764755192208,28.03492337448872,28.086081993785236,28.137240613081744,28.188399232378256,28.23955785167477,28.290716470971283,28.34187509026779,28.393033709564303,28.44419232886082,28.49535094815733,28.54650956745384,28.597668186750354,28.648826806046866,28.699985425343378,28.751144044639894,28.802302663936402,28.853461283232914,28.90461990252943,28.95577852182594,29.00693714112245,29.058095760418965,29.109254379715477,29.16041299901199,29.211571618308504,29.262730237605012,29.313888856901524,29.36504747619804,29.41620609549455,29.46736471479106,29.51852333408758,29.569681953384087,29.6208405726806,29.671999191977115,29.723157811273623,29.774316430570135,29.82547504986665,29.876633669163162,29.92779228845967,29.97895090775619,30.030109527052698,30.08126814634921,30.132426765645725,30.183585384942237,30.234744004238745,30.28590262353526,30.337061242831773,30.388219862128285,30.439378481424793,30.49053710072131,30.54169572001782,30.59285433931433,30.644012958610848,30.695171577907356,30.746330197203868,30.797488816500383,30.848647435796895,30.899806055093404,30.95096467438992,31.00212329368643,31.053281912982943,31.104440532279458,31.155599151575966,31.20675777087248,31.257916390168994,31.309075009465506,31.360233628762014,31.41139224805853,31.46255086735504,31.513709486651553,31.56486810594807,31.616026725244577,31.66718534454109,31.718343963837604,31.769502583134116,31.820661202430625,31.87181982172714,31.922978441023652,31.974137060320164,32.025295679616676,32.07645429891319,32.1276129182097,32.178771537506215,32.22993015680272,32.28108877609924,32.332247395395754,32.38340601469226,32.43456463398877,32.485723253285286,32.5368818725818,32.58804049187831,32.63919911117482,32.690357730471334,32.74151634976785,32.79267496906436,32.84383358836087,32.89499220765738,32.9461508269539,32.99730944625041,33.04846806554692,33.09962668484343,33.150785304139944,33.20194392343646,33.25310254273297,33.30426116202948,33.35541978132599,33.40657840062251,33.45773701991902,33.50889563921553,33.56005425851204,33.611212877808555,33.66237149710507,33.71353011640158,33.764688735698094,33.8158473549946,33.86700597429112,33.91816459358763,33.96932321288414,34.02048183218065,34.07164045147717,34.12279907077368,34.17395769007019,34.225116309366705,34.27627492866321,34.32743354795973,34.378592167256244,34.42975078655275,34.48090940584926,34.53206802514578,34.58322664444229,34.6343852637388,34.68554388303531,34.73670250233183,34.78786112162834,34.83901974092485,34.89017836022136,34.94133697951788,34.992495598814386,35.0436542181109,35.09481283740741,35.14597145670392,35.19713007600044,35.24828869529695,35.29944731459346,35.35060593388997,35.40176455318649,35.452923172483,35.50408179177951,35.55524041107602,35.606399030372536,35.65755764966905,35.70871626896556,35.75987488826207,35.811033507558584,35.8621921268551,35.91335074615161,35.96450936544812,36.01566798474463,36.06682660404115,36.11798522333766,36.16914384263417,36.22030246193068,36.271461081227194,36.32261970052371,36.37377831982022,36.42493693911673,36.47609555841324,36.52725417770976,36.57841279700627,36.62957141630278,36.68073003559929,36.731888654895805,36.78304727419232,36.83420589348883,36.88536451278534,36.93652313208185,36.98768175137837,37.038840370674876,37.08999898997139,37.1411576092679,37.192316228564415,37.24347484786093,37.29463346715744,37.34579208645395,37.39695070575046,37.44810932504698,37.49926794434349,37.55042656364,37.60158518293651,37.652743802233026,37.70390242152954,37.75506104082605,37.80621966012256,37.85737827941907,37.90853689871559,37.9596955180121,38.01085413730861,38.06201275660512,38.113171375901636,38.16432999519815,38.21548861449466,38.26664723379117,38.317805853087684,38.3689644723842,38.42012309168071,38.47128171097722,38.52244033027373,38.57359894957025,38.62475756886676,38.67591618816327,38.72707480745978,38.778233426756294,38.82939204605281,38.88055066534932,38.93170928464583,38.98286790394234,39.03402652323886,39.085185142535366,39.13634376183188,39.18750238112839,39.238661000424905,39.28981961972142,39.34097823901793,39.39213685831444,39.44329547761095,39.49445409690747,39.545612716203976,39.59677133550049,39.647929954797,39.699088574093516,39.75024719339003,39.80140581268654,39.85256443198305,39.90372305127956,39.95488167057608,40.00604028987259,40.0571989091691,40.10835752846561,40.159516147762126,40.21067476705864,40.26183338635515,40.31299200565166,40.36415062494818,40.41530924424469,40.4664678635412,40.51762648283771,40.56878510213422,40.61994372143074,40.67110234072725,40.72226096002376,40.77341957932027,40.82457819861679,40.8757368179133,40.92689543720981,40.978054056506316,41.02921267580284,41.08037129509935,41.131529914395855,41.18268853369237,41.233847152988886,41.285005772285395,41.33616439158191,41.38732301087842,41.43848163017493,41.48964024947145,41.54079886876796,41.591957488064466,41.64311610736098,41.6942747266575,41.745433345954005,41.79659196525052,41.84775058454703,41.898909203843544,41.95006782314006,42.00122644243657,42.05238506173308,42.10354368102959,42.15470230032611,42.205860919622616,42.25701953891913,42.30817815821564,42.359336777512155,42.41049539680867,42.46165401610518,42.51281263540169,42.5639712546982,42.61512987399472,42.666288493291226,42.71744711258774,42.76860573188425,42.819764351180766,42.87092297047728,42.92208158977379,42.9732402090703,43.02439882836681,43.07555744766333,43.12671606695984,43.177874686256345,43.22903330555286,43.280191924849376,43.331350544145884,43.3825091634424,43.43366778273891,43.484826402035424,43.53598502133194,43.58714364062845,43.638302259924956,43.68946087922147,43.74061949851799,43.791778117814495,43.84293673711101,43.89409535640752,43.945253975704034,43.99641259500055,44.04757121429706,44.098729833593566,44.14988845289008,44.2010470721866,44.252205691483105,44.30336431077962,44.35452293007613,44.405681549372645,44.45684016866916,44.50799878796567,44.55915740726218,44.61031602655869,44.66147464585521,44.712633265151716,44.76379188444823,44.81495050374474,44.866109123041255,44.91726774233777,44.96842636163428,45.01958498093079,45.0707436002273,45.12190221952382,45.17306083882033,45.224219458116835,45.27537807741335,45.326536696709866,45.377695316006374,45.42885393530289,45.4800125545994,45.53117117389591,45.58232979319243,45.63348841248894,45.684647031785445,45.73580565108196,45.786964270378476,45.838122889674985,45.8892815089715,45.94044012826801,45.991598747564524,46.04275736686104,46.09391598615755,46.145074605454056,46.19623322475057,46.24739184404709,46.298550463343595,46.34970908264011,46.40086770193662,46.452026321233134,46.50318494052965,46.55434355982616,46.60550217912267,46.65666079841919,46.7078194177157,46.758978037012206,46.81013665630872,46.86129527560523,46.912453894901745,46.96361251419826,47.01477113349477,47.06592975279128,47.1170883720878,47.16824699138431,47.219405610680816,47.270564229977325,47.32172284927385,47.372881468570355,47.424040087866864,47.47519870716338,47.526357326459895,47.5775159457564,47.62867456505292,47.67983318434943,47.730991803645935,47.78215042294246,47.833309042238966,47.884467661535474,47.93562628083199,47.986784900128505,48.03794351942501,48.08910213872153,48.14026075801804,48.19141937731455,48.24257799661107,48.29373661590758,48.344895235204085,48.3960538545006,48.447212473797116,48.498371093093624,48.54952971239014,48.60068833168665,48.65184695098316,48.70300557027968,48.75416418957619,48.805322808872695,48.85648142816921,48.907640047465726,48.958798666762235,49.00995728605875,49.06111590535526,49.112274524651774,49.16343314394829,49.2145917632448,49.265750382541306,49.31690900183782,49.36806762113434,49.419226240430845,49.47038485972735,49.52154347902387,49.572702098320384,49.62386071761689,49.67501933691341,49.726177956209916,49.77733657550643,49.82849519480295,49.879653814099456,49.930812433395964,49.98197105269248,50.033129671988995,50.0842882912855,50.13544691058202,50.18660552987853,50.23776414917504,50.28892276847156,50.340081387768066],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135],"y":[0,0.27259085860190724,0.5451817172038145,0.8177725758057217,1.090363434407629,1.362954293009536,1.6355451516114434,1.9081360102133504,2.180726868815258,2.453317727417165,2.725908586019072,2.9984994446209794,3.271090303222887,3.543681161824794,3.816272020426701,4.088862879028608,4.361453737630516,4.634044596232422,4.90663545483433,5.179226313436238,5.451817172038144,5.724408030640052,5.996998889241959,6.269589747843866,6.542180606445774,6.81477146504768,7.087362323649588,7.359953182251495,7.632544040853402,7.90513489945531,8.177725758057216,8.450316616659123,8.722907475261032,8.995498333862939,9.268089192464844,9.540680051066753,9.81327090966866,10.085861768270567,10.358452626872475,10.63104348547438,10.903634344076288,11.176225202678195,11.448816061280104,11.72140691988201,11.993997778483918,12.266588637085825,12.539179495687732,12.811770354289639,13.084361212891547,13.356952071493454,13.62954293009536,13.902133788697267,14.174724647299175,14.447315505901082,14.71990636450299,14.992497223104897,15.265088081706804,15.53767894030871,15.81026979891062,16.082860657512526,16.35545151611443,16.62804237471634,16.900633233318246,17.173224091920154,17.445814950522063,17.71840580912397,17.990996667725877,18.263587526327782,18.536178384929688,18.808769243531597,19.081360102133505,19.35395096073541,19.62654181933732,19.899132677939228,20.171723536541133,20.444314395143042,20.71690525374495,20.989496112346856,21.26208697094876,21.534677829550667,21.807268688152575,22.079859546754484,22.35245040535639,22.6250412639583,22.897632122560207,23.170222981162112,23.44281383976402,23.71540469836593,23.987995556967835,24.26058641556974,24.53317727417165,24.805768132773554,25.078358991375463,25.350949849977372,25.623540708579277,25.896131567181186,26.168722425783095,26.441313284385,26.71390414298691,26.98649500158881,27.25908586019072,27.531676718792628,27.804267577394533,28.076858435996442,28.34944929459835,28.622040153200256,28.894631011802165,29.167221870404074,29.43981272900598,29.712403587607888,29.984994446209793,30.2575853048117,30.530176163413607,30.802767022015516,31.07535788061742,31.34794873921933,31.62053959782124,31.893130456423144,32.16572131502505,32.43831217362696,32.71090303222886,32.98349389083077,33.25608474943268,33.52867560803459,33.80126646663649,34.0738573252384,34.34644818384031,34.61903904244222,34.891629901044126,35.164220759646035,35.43681161824794,35.709402476849846,35.981993335451754,36.254584194053656,36.527175052655565,36.799765911257474],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809],"y":[0,0.04859442555088715,0.0971888511017743,0.14578327665266147,0.1943777022035486,0.24297212775443577,0.29156655330532294,0.34016097885621005,0.3887554044070972,0.4373498299579844,0.48594425550887155,0.5345386810597587,0.5831331066106459,0.631727532161533,0.6803219577124201,0.7289163832633073,0.7775108088141944,0.8261052343650817,0.8746996599159688,0.923294085466856,0.9718885110177431,1.0204829365686303,1.0690773621195173,1.1176717876704045,1.1662662132212918,1.214860638772179,1.263455064323066,1.3120494898739532,1.3606439154248402,1.4092383409757274,1.4578327665266146,1.5064271920775016,1.5550216176283889,1.6036160431792763,1.6522104687301633,1.7008048942810505,1.7493993198319375,1.7979937453828247,1.846588170933712,1.895182596484599,1.9437770220354862,1.9923714475863732,2.0409658731372606,2.0895602986881476,2.1381547242390346,2.186749149789922,2.235343575340809,2.2839380008916965,2.3325324264425835,2.3811268519934705,2.429721277544358,2.4783157030952445,2.526910128646132,2.5755045541970194,2.6240989797479064,2.672693405298794,2.7212878308496804,2.769882256400568,2.818476681951455,2.8670711075023423,2.9156655330532293,2.9642599586041163,3.0128543841550033,3.0614488097058907,3.1100432352567777,3.158637660807665,3.2072320863585526,3.255826511909439,3.3044209374603266,3.3530153630112136,3.401609788562101,3.450204214112988,3.498798639663875,3.547393065214762,3.5959874907656495,3.6445819163165365,3.693176341867424,3.7417707674183105,3.790365192969198,3.8389596185200854,3.8875540440709724,3.93614846962186,3.9847428951727464,4.033337320723634,4.081931746274521,4.130526171825408,4.179120597376295,4.227715022927183,4.276309448478069,4.324903874028957,4.373498299579844,4.422092725130731,4.470687150681618,4.519281576232506,4.567876001783393,4.61647042733428,4.665064852885167,4.713659278436054,4.762253703986941,4.8108481295378285,4.859442555088716,4.9080369806396025,4.956631406190489,5.005225831741377,5.053820257292264,5.102414682843151,5.151009108394039,5.199603533944925,5.248197959495813,5.296792385046699,5.345386810597588,5.393981236148474,5.442575661699361,5.491170087250248,5.539764512801136,5.588358938352023,5.63695336390291,5.685547789453798,5.734142215004685,5.782736640555571,5.831331066106459,5.879925491657346,5.928519917208233,5.97711434275912,6.025708768310007,6.074303193860895,6.1228976194117815,6.171492044962668,6.2200864705135555,6.268680896064443,6.31727532161533,6.365869747166217,6.414464172717105,6.463058598267992,6.511653023818878,6.560247449369766,6.608841874920653,6.657436300471541,6.706030726022427,6.754625151573314,6.803219577124202,6.851814002675089,6.900408428225976,6.9490028537768636,6.99759727932775,7.0461917048786376,7.094786130429524,7.143380555980412,7.191974981531299,7.2405694070821855,7.289163832633073,7.33775825818396,7.386352683734848,7.434947109285734,7.483541534836621,7.532135960387509,7.580730385938396,7.629324811489283,7.677919237040171,7.726513662591058,7.775108088141945,7.823702513692831,7.87229693924372,7.920891364794606,7.969485790345493,8.01808021589638,8.066674641447268,8.115269066998154,8.163863492549043,8.212457918099929,8.261052343650816,8.309646769201704,8.35824119475259,8.406835620303479,8.455430045854365,8.504024471405252,8.552618896956139,8.601213322507027,8.649807748057913,8.6984021736088,8.746996599159688,8.795591024710575,8.844185450261461,8.89277987581235,8.941374301363236,8.989968726914125,9.038563152465011,9.087157578015898,9.135752003566786,9.184346429117673,9.23294085466856,9.281535280219446,9.330129705770334,9.37872413132122,9.427318556872107,9.475912982422996,9.524507407973882,9.573101833524769,9.621696259075657,9.670290684626544,9.718885110177432,9.767479535728318,9.816073961279205,9.864668386830092,9.913262812380978,9.961857237931868,10.010451663482755,10.059046089033641,10.107640514584528,10.156234940135414,10.204829365686303,10.25342379123719,10.302018216788078,10.350612642338964,10.39920706788985,10.447801493440739,10.496395918991626,10.544990344542512,10.593584770093399,10.642179195644289,10.690773621195175,10.739368046746062,10.787962472296948,10.836556897847835,10.885151323398722,10.93374574894961,10.982340174500496,11.030934600051385,11.079529025602271,11.128123451153158,11.176717876704046,11.225312302254933,11.27390672780582,11.322501153356706,11.371095578907596,11.419690004458483,11.46828443000937,11.516878855560256,11.565473281111142,11.614067706662029,11.662662132212917,11.711256557763804,11.759850983314692,11.808445408865579,11.857039834416465,11.905634259967353,11.95422868551824,12.002823111069127,12.051417536620013,12.100011962170903,12.14860638772179,12.197200813272676,12.245795238823563,12.29438966437445,12.342984089925336,12.391578515476224,12.440172941027111,12.488767366578,12.537361792128886,12.585956217679774,12.63455064323066,12.683145068781547,12.731739494332434,12.78033391988332,12.82892834543421,12.877522770985097,12.926117196535984,12.97471162208687,13.023306047637757,13.071900473188643,13.120494898739532,13.16908932429042,13.217683749841306,13.266278175392193,13.314872600943081,13.363467026493968,13.412061452044854,13.460655877595741,13.509250303146628,13.557844728697518,13.606439154248404,13.65503357979929,13.703628005350177,13.752222430901064,13.800816856451952,13.849411282002839,13.898005707553727,13.946600133104614,13.9951945586555,14.043788984206389,14.092383409757275,14.140977835308162,14.189572260859048,14.238166686409935,14.286761111960825,14.335355537511711,14.383949963062598,14.432544388613485,14.481138814164371,14.52973323971526,14.578327665266146,14.626922090817034,14.67551651636792,14.724110941918807,14.772705367469696,14.821299793020582,14.869894218571469,14.918488644122355,14.967083069673242,15.015677495224132,15.064271920775019,15.112866346325905,15.161460771876792,15.210055197427678,15.258649622978567,15.307244048529453,15.355838474080342,15.404432899631228,15.453027325182116,15.501621750733003,15.55021617628389,15.598810601834776,15.647405027385663,15.695999452936553,15.74459387848744,15.793188304038326,15.841782729589212,15.890377155140099,15.938971580690986,15.987566006241874,16.03616043179276,16.08475485734365,16.133349282894535,16.181943708445424,16.23053813399631,16.279132559547197,16.327726985098085,16.37632141064897,16.424915836199858,16.473510261750747,16.52210468730163,16.57069911285252,16.619293538403408,16.667887963954293,16.71648238950518,16.765076815056066,16.813671240606958,16.862265666157843,16.91086009170873,16.959454517259616,17.008048942810504,17.056643368361392,17.105237793912277,17.153832219463165,17.202426645014054,17.251021070564942,17.299615496115827,17.348209921666715,17.3968043472176,17.44539877276849,17.493993198319377,17.542587623870265,17.59118204942115,17.639776474972038,17.688370900522923,17.73696532607381,17.7855597516247,17.834154177175584,17.882748602726473,17.93134302827736,17.97993745382825,18.028531879379134,18.077126304930022,18.125720730480907,18.174315156031795,18.222909581582684,18.271504007133572,18.320098432684457,18.368692858235345,18.41728728378623,18.46588170933712,18.514476134888007,18.56307056043889,18.61166498598978,18.660259411540668,18.708853837091556,18.75744826264244,18.80604268819333,18.854637113744214,18.903231539295103,18.95182596484599,19.00042039039688,19.049014815947764,19.097609241498652,19.146203667049537,19.194798092600426,19.243392518151314,19.2919869437022,19.340581369253087,19.389175794803975,19.437770220354864,19.48636464590575,19.534959071456637,19.58355349700752,19.63214792255841,19.6807423481093,19.729336773660183,19.77793119921107,19.826525624761956,19.875120050312844,19.923714475863736,19.97230890141462,20.02090332696551,20.069497752516394,20.118092178067283,20.166686603618167,20.215281029169056,20.263875454719944,20.31246988027083,20.36106430582172,20.409658731372605,20.458253156923494,20.50684758247438,20.555442008025267,20.604036433576155,20.65263085912704,20.70122528467793,20.749819710228813,20.7984141357797,20.847008561330586,20.895602986881478,20.944197412432366,20.99279183798325,21.04138626353414,21.089980689085024,21.138575114635913,21.187169540186797,21.235763965737686,21.284358391288578,21.33295281683946,21.38154724239035,21.430141667941236,21.478736093492124,21.52733051904301,21.575924944593897,21.62451937014478,21.67311379569567,21.72170822124656,21.770302646797443,21.818897072348335,21.86749149789922,21.916085923450108,21.964680349000993,22.01327477455188,22.06186920010277,22.110463625653654,22.159058051204543,22.207652476755428,22.256246902306316,22.3048413278572,22.353435753408093,22.40203017895898,22.450624604509866,22.499219030060754,22.54781345561164,22.596407881162527,22.645002306713412,22.6935967322643,22.742191157815192,22.790785583366077,22.839380008916965,22.88797443446785,22.93656886001874,22.985163285569623,23.03375771112051,23.082352136671396,23.130946562222285,23.179540987773173,23.228135413324058,23.27672983887495,23.325324264425834,23.373918689976723,23.422513115527607,23.471107541078496,23.519701966629384,23.56829639218027,23.616890817731157,23.665485243282042,23.71407966883293,23.762674094383815,23.811268519934707,23.859862945485595,23.90845737103648,23.95705179658737,24.005646222138253,24.05424064768914,24.102835073240026,24.151429498790915,24.200023924341806,24.24861834989269,24.29721277544358,24.345807200994464,24.394401626545353,24.442996052096237,24.491590477647126,24.540184903198014,24.5887793287489,24.637373754299787,24.685968179850672,24.734562605401564,24.78315703095245,24.831751456503337,24.880345882054222,24.92894030760511,24.977534733156,25.026129158706883,25.07472358425777,25.123318009808656,25.17191243535955,25.22050686091043,25.26910128646132,25.31769571201221,25.366290137563094,25.414884563113983,25.463478988664868,25.512073414215756,25.56066783976664,25.60926226531753,25.65785669086842,25.706451116419306,25.755045541970194,25.80363996752108,25.852234393071967,25.900828818622852,25.94942324417374,25.99801766972463,26.046612095275513,26.095206520826405,26.143800946377286,26.19239537192818,26.240989797479063,26.28958422302995,26.33817864858084,26.386773074131725,26.435367499682613,26.483961925233498,26.532556350784386,26.58115077633527,26.629745201886163,26.678339627437047,26.726934052987936,26.775528478538824,26.82412290408971,26.872717329640597,26.921311755191482,26.96990618074237,27.018500606293255,27.067095031844143,27.115689457395035,27.16428388294592,27.21287830849681,27.261472734047693,27.31006715959858,27.358661585149466,27.407256010700355,27.455850436251243,27.504444861802128,27.55303928735302,27.601633712903904,27.650228138454793,27.698822564005678,27.747416989556566,27.796011415107454,27.84460584065834,27.893200266209227,27.941794691760112,27.990389117311,28.038983542861885,28.087577968412777,28.136172393963665,28.18476681951455,28.23336124506544,28.281955670616323,28.33055009616721,28.379144521718096,28.427738947268985,28.47633337281987,28.524927798370758,28.57352222392165,28.622116649472535,28.670711075023423,28.719305500574308,28.767899926125196,28.81649435167608,28.86508877722697,28.913683202777857,28.962277628328742,29.010872053879634,29.05946647943052,29.108060904981407,29.156655330532292,29.20524975608318,29.25384418163407,29.302438607184953,29.35103303273584,29.399627458286727,29.448221883837615,29.4968163093885,29.54541073493939,29.59400516049028,29.642599586041165,29.691194011592053,29.739788437142938,29.788382862693826,29.83697728824471,29.8855717137956,29.934166139346484,29.982760564897376,30.031354990448264,30.07994941599915,30.128543841550037,30.177138267100922,30.22573269265181,30.274327118202695,30.322921543753584,30.371515969304472,30.420110394855357,30.46870482040625,30.517299245957133,30.56589367150802,30.614488097058906,30.663082522609795,30.711676948160683,30.760271373711568,30.808865799262456,30.85746022481334,30.906054650364233,30.954649075915114,31.003243501466006,31.051837927016894,31.10043235256778,31.149026778118667,31.197621203669552,31.24621562922044,31.294810054771325,31.343404480322214,31.391998905873105,31.44059333142399,31.48918775697488,31.537782182525763,31.58637660807665,31.634971033627536,31.683565459178425,31.73215988472931,31.780754310280198,31.829348735831086,31.87794316138197,31.926537586932863,31.975132012483748,32.023726438034636,32.07232086358552,32.120915289136406,32.1695097146873,32.21810414023818,32.26669856578907,32.31529299133996,32.36388741689085,32.41248184244173,32.46107626799262,32.509670693543505,32.55826511909439,32.60685954464528,32.65545397019617,32.70404839574706,32.75264282129794,32.80123724684883,32.849831672399716,32.898426097950605,32.94702052350149,32.995614949052374,33.04420937460326,33.09280380015415,33.14139822570504,33.18999265125593,33.238587076806816,33.287181502357704,33.335775927908585,33.384370353459474,33.43296477901036,33.48155920456125,33.53015363011213,33.57874805566303,33.627342481213915,33.6759369067648,33.724531332315685,33.77312575786657,33.82172018341746,33.87031460896834,33.91890903451923,33.96750346007012,34.01609788562101,34.064692311171896,34.113286736722785,34.16188116227367,34.210475587824554,34.25907001337544,34.30766443892633,34.35625886447722,34.40485329002811,34.45344771557899,34.502042141129884,34.550636566680765,34.599230992231654,34.64782541778254,34.69641984333343,34.74501426888432,34.7936086944352,34.84220311998609,34.89079754553698,34.939391971087865,34.98798639663875,35.03658082218964,35.08517524774053,35.13376967329141,35.1823640988423,35.23095852439319,35.279552949944076,35.32814737549496,35.376741801045846,35.425336226596734,35.47393065214762,35.52252507769851,35.5711195032494,35.61971392880029,35.66830835435117,35.71690277990206,35.765497205452945,35.81409163100383,35.86268605655472,35.9112804821056,35.9598749076565,36.00846933320738,36.05706375875827,36.105658184309156,36.154252609860045,36.20284703541093,36.251441460961814,36.3000358865127,36.34863031206359,36.39722473761448,36.44581916316537,36.494413588716256,36.543008014267144,36.591602439818026,36.640196865368914,36.6887912909198,36.73738571647069,36.78598014202157,36.83457456757246,36.883168993123356,36.93176341867424,36.980357844225125,37.02895226977601,37.0775466953269,37.12614112087778,37.17473554642867,37.22332997197956,37.27192439753045,37.320518823081336,37.36911324863222,37.41770767418311,37.466302099733994,37.51489652528488,37.56349095083577,37.61208537638666,37.66067980193755,37.70927422748843,37.75786865303932,37.806463078590205,37.855057504141094,37.90365192969198,37.95224635524287,38.00084078079376,38.04943520634464,38.09802963189553,38.14662405744642,38.195218482997305,38.24381290854819,38.292407334099074,38.34100175964997,38.38959618520085,38.43819061075174,38.48678503630263,38.535379461853516,38.5839738874044,38.632568312955286,38.681162738506174,38.72975716405706,38.77835158960795,38.82694601515883,38.87554044070973,38.92413486626061,38.9727292918115,39.021323717362385,39.069918142913274,39.11851256846416,39.16710699401504,39.21570141956593,39.26429584511682,39.31289027066771],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106],"y":[0,0.06576106442348031,0.13152212884696063,0.19728319327044094,0.26304425769392126,0.3288053221174016,0.3945663865408819,0.46032745096436223,0.5260885153878425,0.5918495798113228,0.6576106442348032,0.7233717086582835,0.7891327730817638,0.8548938375052441,0.9206549019287245,0.9864159663522047,1.052177030775685,1.1179380951991653,1.1836991596226456,1.249460224046126,1.3152212884696064,1.3809823528930867,1.446743417316567,1.5125044817400473,1.5782655461635275,1.6440266105870078,1.7097876750104881,1.7755487394339686,1.841309803857449,1.9070708682809292,1.9728319327044095,2.0385929971278896,2.10435406155137,2.17011512597485,2.2358761903983306,2.301637254821811,2.367398319245291,2.4331593836687717,2.498920448092252,2.5646815125157323,2.630442576939213,2.6962036413626933,2.7619647057861734,2.827725770209654,2.893486834633134,2.9592478990566145,3.0250089634800945,3.090770027903575,3.156531092327055,3.2222921567505356,3.2880532211740157,3.353814285597496,3.4195753500209762,3.4853364144444567,3.5510974788679373,3.6168585432914173,3.682619607714898,3.748380672138378,3.8141417365618584,3.8799028009853385,3.945663865408819,4.011424929832299,4.077185994255779,4.14294705867926,4.20870812310274,4.27446918752622,4.3402302519497,4.405991316373181,4.471752380796661,4.537513445220141,4.603274509643622,4.669035574067102,4.734796638490582,4.8005577029140625,4.866318767337543,4.9320798317610235,4.997840896184504,5.063601960607984,5.129363025031465,5.195124089454945,5.260885153878426,5.326646218301906,5.392407282725387,5.458168347148867,5.523929411572347,5.589690475995827,5.655451540419308,5.721212604842788,5.786973669266268,5.852734733689748,5.918495798113229,5.984256862536709,6.050017926960189,6.11577899138367,6.18154005580715,6.24730112023063,6.31306218465411,6.378823249077591,6.444584313501071,6.510345377924551,6.576106442348031,6.641867506771512,6.707628571194992,6.773389635618472,6.8391507000419525,6.904911764465433,6.9706728288889135],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Ext Rule 6: Fibonacci Support Confluence</td>
                <td>UNKNOWN</td>
                <td class="positive">68.76%</td>
                <td>64.0%</td>
                <td>978</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>21.99%</td>
                <td>76.7</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">50.34%</td>
                <td>64.4%</td>
                <td>984</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>19.63%</td>
                <td>69.5</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">36.80%</td>
                <td>70.4%</td>
                <td>135</td>
                <td>1.44</td>
                <td>0.00</td>
                <td>12.05%</td>
                <td>65.8</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">39.31%</td>
                <td>64.6%</td>
                <td>809</td>
                <td>1.06</td>
                <td>0.00</td>
                <td>21.98%</td>
                <td>65.1</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">6.97%</td>
                <td>65.1%</td>
                <td>106</td>
                <td>1.09</td>
                <td>0.00</td>
                <td>10.37%</td>
                <td>52.3</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">16.90%</td>
                <td>70.3%</td>
                <td>74</td>
                <td>1.34</td>
                <td>0.00</td>
                <td>6.02%</td>
                <td>50.0</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                <td>UNKNOWN</td>
                <td class="positive">14.31%</td>
                <td>76.9%</td>
                <td>26</td>
                <td>2.23</td>
                <td>0.00</td>
                <td>2.45%</td>
                <td>36.6</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">7.93%</td>
                <td>83.3%</td>
                <td>12</td>
                <td>2.81</td>
                <td>0.00</td>
                <td>2.50%</td>
                <td>31.8</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">1.14%</td>
                <td>66.7%</td>
                <td>18</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>4.04%</td>
                <td>25.9</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 525,300<br>
            • Backtest Range: 300 to 525,600<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
