
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 15:08:32 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">-23.03%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">472</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">58.9%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.27</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>2.69%</strong></td>
                    <td>71.0%</td>
                    <td>31</td>
                    <td>1.43</td>
                    <td class="neutral">2.77%</td>
                    <td class="positive"><strong>0.5044</strong></td>
                    <td class="negative">+0.80% / -1.36%</td>
                    <td>8h56m<br><small>(14.0m - 40h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>2.28%</strong></td>
                    <td>69.2%</td>
                    <td>39</td>
                    <td>1.27</td>
                    <td class="neutral">3.27%</td>
                    <td class="positive"><strong>0.4934</strong></td>
                    <td class="negative">+0.79% / -1.39%</td>
                    <td>8h43m<br><small>(14.0m - 51h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>2.24%</strong></td>
                    <td>68.1%</td>
                    <td>47</td>
                    <td>1.21</td>
                    <td class="neutral">3.27%</td>
                    <td class="positive"><strong>0.5020</strong></td>
                    <td class="negative">+0.79% / -1.37%</td>
                    <td>9h28m<br><small>(8.0m - 46h24m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>1.25%</strong></td>
                    <td>66.7%</td>
                    <td>27</td>
                    <td>1.21</td>
                    <td class="neutral">2.66%</td>
                    <td class="positive"><strong>0.4474</strong></td>
                    <td class="negative">+0.81% / -1.33%</td>
                    <td>14h10m<br><small>(30.0m - 46h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>New Rule 4: Ultimate Oscillator Breakout</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 1: MA Alignment with RSI Oversold</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-0.15%</strong></td>
                    <td>61.8%</td>
                    <td>34</td>
                    <td>0.98</td>
                    <td class="neutral">3.03%</td>
                    <td class="positive"><strong>0.4169</strong></td>
                    <td class="negative">+0.82% / -1.33%</td>
                    <td>8h51m<br><small>(38.0m - 50h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-0.25%</strong></td>
                    <td>61.5%</td>
                    <td>13</td>
                    <td>0.93</td>
                    <td class="neutral">2.06%</td>
                    <td class="positive"><strong>0.3048</strong></td>
                    <td class="negative">+0.79% / -1.35%</td>
                    <td>16h30m<br><small>(46.0m - 58h50m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="negative"><strong>-0.32%</strong></td>
                    <td>63.2%</td>
                    <td>19</td>
                    <td>0.94</td>
                    <td class="neutral">3.11%</td>
                    <td class="positive"><strong>0.3386</strong></td>
                    <td class="negative">+0.78% / -1.41%</td>
                    <td>8h3m<br><small>(14.0m - 35h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="negative"><strong>-0.72%</strong></td>
                    <td>60.0%</td>
                    <td>15</td>
                    <td>0.83</td>
                    <td class="neutral">2.11%</td>
                    <td class="positive"><strong>0.2862</strong></td>
                    <td class="negative">+0.78% / -1.40%</td>
                    <td>8h41m<br><small>(14.0m - 21h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-0.90%</strong></td>
                    <td>61.4%</td>
                    <td>44</td>
                    <td>0.92</td>
                    <td class="negative">5.97%</td>
                    <td class="positive"><strong>0.4179</strong></td>
                    <td class="negative">+0.79% / -1.36%</td>
                    <td>9h15m<br><small>(30.0m - 44h22m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="negative"><strong>-1.19%</strong></td>
                    <td>40.0%</td>
                    <td>5</td>
                    <td>0.47</td>
                    <td class="neutral">2.22%</td>
                    <td class="positive"><strong>0.0382</strong></td>
                    <td class="negative">+1.05% / -1.49%</td>
                    <td>27.0m<br><small>(9.0m - 40.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="negative"><strong>-1.21%</strong></td>
                    <td>40.0%</td>
                    <td>5</td>
                    <td>0.39</td>
                    <td class="positive">1.98%</td>
                    <td class="negative"><strong>-0.0061</strong></td>
                    <td class="negative">+0.79% / -1.33%</td>
                    <td>22h40m<br><small>(55.0m - 49h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="negative"><strong>-1.26%</strong></td>
                    <td>50.0%</td>
                    <td>8</td>
                    <td>0.57</td>
                    <td class="neutral">2.86%</td>
                    <td class="positive"><strong>0.1248</strong></td>
                    <td class="negative">+0.82% / -1.44%</td>
                    <td>18h9m<br><small>(49.0m - 35h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>SMC Rule 2: Fair Value Gap Fill</td>
                    <td class="negative"><strong>-1.27%</strong></td>
                    <td>40.0%</td>
                    <td>5</td>
                    <td>0.40</td>
                    <td class="positive">1.72%</td>
                    <td class="negative"><strong>-0.0056</strong></td>
                    <td class="negative">+0.84% / -1.41%</td>
                    <td>9h14m<br><small>(18.0m - 21h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="negative"><strong>-1.40%</strong></td>
                    <td>50.0%</td>
                    <td>10</td>
                    <td>0.58</td>
                    <td class="positive">1.99%</td>
                    <td class="positive"><strong>0.1592</strong></td>
                    <td class="negative">+0.77% / -1.33%</td>
                    <td>12h1m<br><small>(2h58m - 36h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-1.40%</strong></td>
                    <td>60.0%</td>
                    <td>35</td>
                    <td>0.85</td>
                    <td class="neutral">3.74%</td>
                    <td class="positive"><strong>0.3769</strong></td>
                    <td class="negative">+0.78% / -1.36%</td>
                    <td>9h57m<br><small>(30.0m - 54h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="negative"><strong>-1.46%</strong></td>
                    <td>50.0%</td>
                    <td>10</td>
                    <td>0.58</td>
                    <td class="neutral">2.64%</td>
                    <td class="positive"><strong>0.1556</strong></td>
                    <td class="negative">+0.80% / -1.38%</td>
                    <td>14h20m<br><small>(49.0m - 35h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="negative"><strong>-1.68%</strong></td>
                    <td>50.0%</td>
                    <td>12</td>
                    <td>0.59</td>
                    <td class="neutral">3.24%</td>
                    <td class="positive"><strong>0.1770</strong></td>
                    <td class="negative">+0.79% / -1.34%</td>
                    <td>16h16m<br><small>(2h27m - 44h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-1.70%</strong></td>
                    <td>60.0%</td>
                    <td>45</td>
                    <td>0.86</td>
                    <td class="neutral">4.96%</td>
                    <td class="positive"><strong>0.4024</strong></td>
                    <td class="negative">+0.78% / -1.35%</td>
                    <td>9h49m<br><small>(29.0m - 54h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="negative"><strong>-1.74%</strong></td>
                    <td>42.9%</td>
                    <td>7</td>
                    <td>0.40</td>
                    <td class="neutral">2.85%</td>
                    <td class="positive"><strong>0.0196</strong></td>
                    <td class="negative">+0.76% / -1.43%</td>
                    <td>9h36m<br><small>(14.0m - 35h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-1.80%</strong></td>
                    <td>50.0%</td>
                    <td>12</td>
                    <td>0.56</td>
                    <td class="neutral">3.29%</td>
                    <td class="positive"><strong>0.1637</strong></td>
                    <td class="negative">+0.76% / -1.36%</td>
                    <td>16h39m<br><small>(46.0m - 49h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-1.86%</strong></td>
                    <td>33.3%</td>
                    <td>6</td>
                    <td>0.30</td>
                    <td class="neutral">2.62%</td>
                    <td class="negative"><strong>-0.0605</strong></td>
                    <td class="negative">+0.78% / -1.32%</td>
                    <td>18h24m<br><small>(1h25m - 44h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="negative"><strong>-2.39%</strong></td>
                    <td>50.0%</td>
                    <td>16</td>
                    <td>0.57</td>
                    <td class="neutral">3.93%</td>
                    <td class="positive"><strong>0.1928</strong></td>
                    <td class="negative">+0.78% / -1.38%</td>
                    <td>12h9m<br><small>(1h40m - 43h29m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-2.87%</strong></td>
                    <td>33.3%</td>
                    <td>9</td>
                    <td>0.29</td>
                    <td class="neutral">3.61%</td>
                    <td class="negative"><strong>-0.0323</strong></td>
                    <td class="negative">+0.78% / -1.35%</td>
                    <td>20h32m<br><small>(2h28m - 47h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#28</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="negative"><strong>-2.91%</strong></td>
                    <td>33.3%</td>
                    <td>9</td>
                    <td>0.29</td>
                    <td class="neutral">3.66%</td>
                    <td class="negative"><strong>-0.0327</strong></td>
                    <td class="negative">+0.79% / -1.37%</td>
                    <td>18h49m<br><small>(56.0m - 49h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#29</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="negative"><strong>-3.02%</strong></td>
                    <td>33.3%</td>
                    <td>9</td>
                    <td>0.27</td>
                    <td class="neutral">3.02%</td>
                    <td class="negative"><strong>-0.0489</strong></td>
                    <td class="negative">+0.76% / -1.39%</td>
                    <td>19h31m<br><small>(1h12m - 47h1m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>71.0%</strong></td>
                    <td class="positive">2.69%</td>
                    <td>31</td>
                    <td>1.43</td>
                    <td class="neutral">2.77%</td>
                    <td class="positive"><strong>0.5044</strong></td>
                    <td class="negative">+0.80% / -1.36%</td>
                    <td>8h56m<br><small>(14.0m - 40h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>69.2%</strong></td>
                    <td class="positive">2.28%</td>
                    <td>39</td>
                    <td>1.27</td>
                    <td class="neutral">3.27%</td>
                    <td class="positive"><strong>0.4934</strong></td>
                    <td class="negative">+0.79% / -1.39%</td>
                    <td>8h43m<br><small>(14.0m - 51h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>68.1%</strong></td>
                    <td class="positive">2.24%</td>
                    <td>47</td>
                    <td>1.21</td>
                    <td class="neutral">3.27%</td>
                    <td class="positive"><strong>0.5020</strong></td>
                    <td class="negative">+0.79% / -1.37%</td>
                    <td>9h28m<br><small>(8.0m - 46h24m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">1.25%</td>
                    <td>27</td>
                    <td>1.21</td>
                    <td class="neutral">2.66%</td>
                    <td class="positive"><strong>0.4474</strong></td>
                    <td class="negative">+0.81% / -1.33%</td>
                    <td>14h10m<br><small>(30.0m - 46h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>63.2%</strong></td>
                    <td class="negative">-0.32%</td>
                    <td>19</td>
                    <td>0.94</td>
                    <td class="neutral">3.11%</td>
                    <td class="positive"><strong>0.3386</strong></td>
                    <td class="negative">+0.78% / -1.41%</td>
                    <td>8h3m<br><small>(14.0m - 35h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>61.8%</strong></td>
                    <td class="negative">-0.15%</td>
                    <td>34</td>
                    <td>0.98</td>
                    <td class="neutral">3.03%</td>
                    <td class="positive"><strong>0.4169</strong></td>
                    <td class="negative">+0.82% / -1.33%</td>
                    <td>8h51m<br><small>(38.0m - 50h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>61.5%</strong></td>
                    <td class="negative">-0.25%</td>
                    <td>13</td>
                    <td>0.93</td>
                    <td class="neutral">2.06%</td>
                    <td class="positive"><strong>0.3048</strong></td>
                    <td class="negative">+0.79% / -1.35%</td>
                    <td>16h30m<br><small>(46.0m - 58h50m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>61.4%</strong></td>
                    <td class="negative">-0.90%</td>
                    <td>44</td>
                    <td>0.92</td>
                    <td class="negative">5.97%</td>
                    <td class="positive"><strong>0.4179</strong></td>
                    <td class="negative">+0.79% / -1.36%</td>
                    <td>9h15m<br><small>(30.0m - 44h22m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="negative">-0.72%</td>
                    <td>15</td>
                    <td>0.83</td>
                    <td class="neutral">2.11%</td>
                    <td class="positive"><strong>0.2862</strong></td>
                    <td class="negative">+0.78% / -1.40%</td>
                    <td>8h41m<br><small>(14.0m - 21h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="negative">-1.40%</td>
                    <td>35</td>
                    <td>0.85</td>
                    <td class="neutral">3.74%</td>
                    <td class="positive"><strong>0.3769</strong></td>
                    <td class="negative">+0.78% / -1.36%</td>
                    <td>9h57m<br><small>(30.0m - 54h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="negative">-1.70%</td>
                    <td>45</td>
                    <td>0.86</td>
                    <td class="neutral">4.96%</td>
                    <td class="positive"><strong>0.4024</strong></td>
                    <td class="negative">+0.78% / -1.35%</td>
                    <td>9h49m<br><small>(29.0m - 54h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-1.26%</td>
                    <td>8</td>
                    <td>0.57</td>
                    <td class="neutral">2.86%</td>
                    <td class="positive"><strong>0.1248</strong></td>
                    <td class="negative">+0.82% / -1.44%</td>
                    <td>18h9m<br><small>(49.0m - 35h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-1.40%</td>
                    <td>10</td>
                    <td>0.58</td>
                    <td class="positive">1.99%</td>
                    <td class="positive"><strong>0.1592</strong></td>
                    <td class="negative">+0.77% / -1.33%</td>
                    <td>12h1m<br><small>(2h58m - 36h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-1.46%</td>
                    <td>10</td>
                    <td>0.58</td>
                    <td class="neutral">2.64%</td>
                    <td class="positive"><strong>0.1556</strong></td>
                    <td class="negative">+0.80% / -1.38%</td>
                    <td>14h20m<br><small>(49.0m - 35h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-1.68%</td>
                    <td>12</td>
                    <td>0.59</td>
                    <td class="neutral">3.24%</td>
                    <td class="positive"><strong>0.1770</strong></td>
                    <td class="negative">+0.79% / -1.34%</td>
                    <td>16h16m<br><small>(2h27m - 44h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-1.80%</td>
                    <td>12</td>
                    <td>0.56</td>
                    <td class="neutral">3.29%</td>
                    <td class="positive"><strong>0.1637</strong></td>
                    <td class="negative">+0.76% / -1.36%</td>
                    <td>16h39m<br><small>(46.0m - 49h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-2.39%</td>
                    <td>16</td>
                    <td>0.57</td>
                    <td class="neutral">3.93%</td>
                    <td class="positive"><strong>0.1928</strong></td>
                    <td class="negative">+0.78% / -1.38%</td>
                    <td>12h9m<br><small>(1h40m - 43h29m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="neutral"><strong>42.9%</strong></td>
                    <td class="negative">-1.74%</td>
                    <td>7</td>
                    <td>0.40</td>
                    <td class="neutral">2.85%</td>
                    <td class="positive"><strong>0.0196</strong></td>
                    <td class="negative">+0.76% / -1.43%</td>
                    <td>9h36m<br><small>(14.0m - 35h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="neutral"><strong>40.0%</strong></td>
                    <td class="negative">-1.19%</td>
                    <td>5</td>
                    <td>0.47</td>
                    <td class="neutral">2.22%</td>
                    <td class="positive"><strong>0.0382</strong></td>
                    <td class="negative">+1.05% / -1.49%</td>
                    <td>27.0m<br><small>(9.0m - 40.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="neutral"><strong>40.0%</strong></td>
                    <td class="negative">-1.21%</td>
                    <td>5</td>
                    <td>0.39</td>
                    <td class="positive">1.98%</td>
                    <td class="negative"><strong>-0.0061</strong></td>
                    <td class="negative">+0.79% / -1.33%</td>
                    <td>22h40m<br><small>(55.0m - 49h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>SMC Rule 2: Fair Value Gap Fill</td>
                    <td class="neutral"><strong>40.0%</strong></td>
                    <td class="negative">-1.27%</td>
                    <td>5</td>
                    <td>0.40</td>
                    <td class="positive">1.72%</td>
                    <td class="negative"><strong>-0.0056</strong></td>
                    <td class="negative">+0.84% / -1.41%</td>
                    <td>9h14m<br><small>(18.0m - 21h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="neutral"><strong>33.3%</strong></td>
                    <td class="negative">-1.86%</td>
                    <td>6</td>
                    <td>0.30</td>
                    <td class="neutral">2.62%</td>
                    <td class="negative"><strong>-0.0605</strong></td>
                    <td class="negative">+0.78% / -1.32%</td>
                    <td>18h24m<br><small>(1h25m - 44h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="neutral"><strong>33.3%</strong></td>
                    <td class="negative">-2.87%</td>
                    <td>9</td>
                    <td>0.29</td>
                    <td class="neutral">3.61%</td>
                    <td class="negative"><strong>-0.0323</strong></td>
                    <td class="negative">+0.78% / -1.35%</td>
                    <td>20h32m<br><small>(2h28m - 47h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="neutral"><strong>33.3%</strong></td>
                    <td class="negative">-2.91%</td>
                    <td>9</td>
                    <td>0.29</td>
                    <td class="neutral">3.66%</td>
                    <td class="negative"><strong>-0.0327</strong></td>
                    <td class="negative">+0.79% / -1.37%</td>
                    <td>18h49m<br><small>(56.0m - 49h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="neutral"><strong>33.3%</strong></td>
                    <td class="negative">-3.02%</td>
                    <td>9</td>
                    <td>0.27</td>
                    <td class="neutral">3.02%</td>
                    <td class="negative"><strong>-0.0489</strong></td>
                    <td class="negative">+0.76% / -1.39%</td>
                    <td>19h31m<br><small>(1h12m - 47h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>New Rule 4: Ultimate Oscillator Breakout</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Rule 1: MA Alignment with RSI Oversold</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#28</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#29</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['New Rule 4: Ultimate Oscillato...', 'Rule 1: MA Alignment with RSI ...', 'Momentum Rule 5: Momentum Brea...', 'Acad Rule 2: Mean Reversion Fa...', 'Price Action Rule 3: Engulfing...', 'Rule 10: Volume Spike', 'Professional Rule 7: Chaikin M...', 'Momentum Rule 2: Momentum Dive...', 'Volume Rule 4: Volume Breakout...', 'AI Rule 8: Momentum Divergence...', 'Rule 2: Golden Cross', 'Professional Rule 10: CCI Reve...', 'Rule 28: Volume Breakout', 'Ext Rule 6: Fibonacci Support ...', 'Rule 6: Stochastic Oversold Cr...'],
                y: [0, 0, 0, 0, 70.96774193548387, 69.23076923076923, 68.08510638297872, 66.66666666666666, 63.1578947368421, 61.76470588235294, 61.53846153846154, 61.36363636363637, 60.0, 60.0, 60.0],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['New Rule 4: Ultimate Oscillato...', 'Rule 1: MA Alignment with RSI ...', 'Momentum Rule 5: Momentum Brea...', 'Acad Rule 2: Mean Reversion Fa...', 'Price Action Rule 3: Engulfing...', 'Rule 10: Volume Spike', 'Professional Rule 7: Chaikin M...', 'Momentum Rule 2: Momentum Dive...', 'Volume Rule 4: Volume Breakout...', 'AI Rule 8: Momentum Divergence...', 'Rule 2: Golden Cross', 'Professional Rule 10: CCI Reve...', 'Rule 28: Volume Breakout', 'Ext Rule 6: Fibonacci Support ...', 'Rule 6: Stochastic Oversold Cr...'],
                y: [0.0, 0.0, 0.0, 0.0, np.float64(2.692758762011249), np.float64(2.2827880157561653), np.float64(2.237770921604043), np.float64(1.2512551949436013), np.float64(-0.31920633108384333), np.float64(-0.14506199993099544), np.float64(-0.2481305649811527), np.float64(-0.9005831166443823), np.float64(-0.7228787483534282), np.float64(-1.402235891742254), np.float64(-1.6979199398179823)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
