
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 15:17:53 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">225.58%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">3,250</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">64.7%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">12.39</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>68.76%</strong></td>
                    <td>64.0%</td>
                    <td>978</td>
                    <td>1.09</td>
                    <td class="negative">21.99%</td>
                    <td class="positive"><strong>0.9224</strong></td>
                    <td class="negative">+0.83% / -1.34%</td>
                    <td>4h31m<br><small>(2.0m - 63h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>50.34%</strong></td>
                    <td>64.4%</td>
                    <td>984</td>
                    <td>1.07</td>
                    <td class="negative">19.63%</td>
                    <td class="positive"><strong>0.8722</strong></td>
                    <td class="negative">+0.83% / -1.38%</td>
                    <td>3h59m<br><small>(1.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>39.31%</strong></td>
                    <td>64.6%</td>
                    <td>809</td>
                    <td>1.06</td>
                    <td class="negative">21.98%</td>
                    <td class="positive"><strong>0.8198</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>3h47m<br><small>(1.0m - 54h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>36.80%</strong></td>
                    <td>70.4%</td>
                    <td>135</td>
                    <td>1.44</td>
                    <td class="negative">12.05%</td>
                    <td class="positive"><strong>0.7230</strong></td>
                    <td class="negative">+0.81% / -1.36%</td>
                    <td>2h50m<br><small>(1.0m - 31h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>16.90%</strong></td>
                    <td>70.3%</td>
                    <td>74</td>
                    <td>1.34</td>
                    <td class="negative">6.02%</td>
                    <td class="positive"><strong>0.6059</strong></td>
                    <td class="negative">+0.82% / -1.39%</td>
                    <td>2h56m<br><small>(1.0m - 40h15m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>14.31%</strong></td>
                    <td>76.9%</td>
                    <td>26</td>
                    <td>2.23</td>
                    <td class="neutral">2.45%</td>
                    <td class="positive"><strong>0.6245</strong></td>
                    <td class="negative">+0.82% / -1.30%</td>
                    <td>3h24m<br><small>(5.0m - 17h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>7.93%</strong></td>
                    <td>83.3%</td>
                    <td>12</td>
                    <td>2.81</td>
                    <td class="neutral">2.50%</td>
                    <td class="positive"><strong>0.5870</strong></td>
                    <td class="negative">+0.80% / -1.31%</td>
                    <td>3h0m<br><small>(9.0m - 17h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>6.97%</strong></td>
                    <td>65.1%</td>
                    <td>106</td>
                    <td>1.09</td>
                    <td class="negative">10.37%</td>
                    <td class="positive"><strong>0.5640</strong></td>
                    <td class="negative">+0.82% / -1.34%</td>
                    <td>3h50m<br><small>(1.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>2.78%</strong></td>
                    <td>100.0%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.92% / 0.00%</td>
                    <td>2.0m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>1.14%</strong></td>
                    <td>66.7%</td>
                    <td>18</td>
                    <td>1.08</td>
                    <td class="neutral">4.04%</td>
                    <td class="positive"><strong>0.3768</strong></td>
                    <td class="negative">+0.78% / -1.37%</td>
                    <td>8h7m<br><small>(28.0m - 38h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>0.14%</strong></td>
                    <td>66.7%</td>
                    <td>6</td>
                    <td>1.03</td>
                    <td class="neutral">2.81%</td>
                    <td class="positive"><strong>0.2668</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>15h58m<br><small>(28.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="negative"><strong>-0.38%</strong></td>
                    <td>50.0%</td>
                    <td>2</td>
                    <td>0.81</td>
                    <td class="positive">1.98%</td>
                    <td class="positive"><strong>0.1248</strong></td>
                    <td class="positive">+1.26% / -1.25%</td>
                    <td>1h14m<br><small>(8.0m - 2h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="negative"><strong>-1.95%</strong></td>
                    <td>0.0%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0610</strong></td>
                    <td class="negative">+0.00% / -1.35%</td>
                    <td>2h48m</td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="negative"><strong>-2.27%</strong></td>
                    <td>0.0%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0671</strong></td>
                    <td class="negative">+0.00% / -1.40%</td>
                    <td>31h18m</td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-2.53%</strong></td>
                    <td>50.0%</td>
                    <td>6</td>
                    <td>0.58</td>
                    <td class="neutral">4.57%</td>
                    <td class="positive"><strong>0.1147</strong></td>
                    <td class="negative">+0.88% / -1.29%</td>
                    <td>3h45m<br><small>(41.0m - 11h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-2.58%</strong></td>
                    <td>58.5%</td>
                    <td>41</td>
                    <td>0.92</td>
                    <td class="negative">8.94%</td>
                    <td class="positive"><strong>0.4026</strong></td>
                    <td class="negative">+0.84% / -1.34%</td>
                    <td>7h30m<br><small>(12.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-4.67%</strong></td>
                    <td>60.0%</td>
                    <td>35</td>
                    <td>0.85</td>
                    <td class="negative">11.41%</td>
                    <td class="positive"><strong>0.3638</strong></td>
                    <td class="negative">+0.82% / -1.33%</td>
                    <td>4h59m<br><small>(11.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="negative"><strong>-5.41%</strong></td>
                    <td>57.1%</td>
                    <td>14</td>
                    <td>0.64</td>
                    <td class="negative">11.23%</td>
                    <td class="positive"><strong>0.1960</strong></td>
                    <td class="negative">+0.79% / -1.43%</td>
                    <td>1h45m<br><small>(19.0m - 4h13m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">2.78%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.92% / 0.00%</td>
                    <td>2.0m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>83.3%</strong></td>
                    <td class="positive">7.93%</td>
                    <td>12</td>
                    <td>2.81</td>
                    <td class="neutral">2.50%</td>
                    <td class="positive"><strong>0.5870</strong></td>
                    <td class="negative">+0.80% / -1.31%</td>
                    <td>3h0m<br><small>(9.0m - 17h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>76.9%</strong></td>
                    <td class="positive">14.31%</td>
                    <td>26</td>
                    <td>2.23</td>
                    <td class="neutral">2.45%</td>
                    <td class="positive"><strong>0.6245</strong></td>
                    <td class="negative">+0.82% / -1.30%</td>
                    <td>3h24m<br><small>(5.0m - 17h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>70.4%</strong></td>
                    <td class="positive">36.80%</td>
                    <td>135</td>
                    <td>1.44</td>
                    <td class="negative">12.05%</td>
                    <td class="positive"><strong>0.7230</strong></td>
                    <td class="negative">+0.81% / -1.36%</td>
                    <td>2h50m<br><small>(1.0m - 31h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>70.3%</strong></td>
                    <td class="positive">16.90%</td>
                    <td>74</td>
                    <td>1.34</td>
                    <td class="negative">6.02%</td>
                    <td class="positive"><strong>0.6059</strong></td>
                    <td class="negative">+0.82% / -1.39%</td>
                    <td>2h56m<br><small>(1.0m - 40h15m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">1.14%</td>
                    <td>18</td>
                    <td>1.08</td>
                    <td class="neutral">4.04%</td>
                    <td class="positive"><strong>0.3768</strong></td>
                    <td class="negative">+0.78% / -1.37%</td>
                    <td>8h7m<br><small>(28.0m - 38h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">0.14%</td>
                    <td>6</td>
                    <td>1.03</td>
                    <td class="neutral">2.81%</td>
                    <td class="positive"><strong>0.2668</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>15h58m<br><small>(28.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>65.1%</strong></td>
                    <td class="positive">6.97%</td>
                    <td>106</td>
                    <td>1.09</td>
                    <td class="negative">10.37%</td>
                    <td class="positive"><strong>0.5640</strong></td>
                    <td class="negative">+0.82% / -1.34%</td>
                    <td>3h50m<br><small>(1.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>64.6%</strong></td>
                    <td class="positive">39.31%</td>
                    <td>809</td>
                    <td>1.06</td>
                    <td class="negative">21.98%</td>
                    <td class="positive"><strong>0.8198</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>3h47m<br><small>(1.0m - 54h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>64.4%</strong></td>
                    <td class="positive">50.34%</td>
                    <td>984</td>
                    <td>1.07</td>
                    <td class="negative">19.63%</td>
                    <td class="positive"><strong>0.8722</strong></td>
                    <td class="negative">+0.83% / -1.38%</td>
                    <td>3h59m<br><small>(1.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>64.0%</strong></td>
                    <td class="positive">68.76%</td>
                    <td>978</td>
                    <td>1.09</td>
                    <td class="negative">21.99%</td>
                    <td class="positive"><strong>0.9224</strong></td>
                    <td class="negative">+0.83% / -1.34%</td>
                    <td>4h31m<br><small>(2.0m - 63h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="negative">-4.67%</td>
                    <td>35</td>
                    <td>0.85</td>
                    <td class="negative">11.41%</td>
                    <td class="positive"><strong>0.3638</strong></td>
                    <td class="negative">+0.82% / -1.33%</td>
                    <td>4h59m<br><small>(11.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>58.5%</strong></td>
                    <td class="negative">-2.58%</td>
                    <td>41</td>
                    <td>0.92</td>
                    <td class="negative">8.94%</td>
                    <td class="positive"><strong>0.4026</strong></td>
                    <td class="negative">+0.84% / -1.34%</td>
                    <td>7h30m<br><small>(12.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>57.1%</strong></td>
                    <td class="negative">-5.41%</td>
                    <td>14</td>
                    <td>0.64</td>
                    <td class="negative">11.23%</td>
                    <td class="positive"><strong>0.1960</strong></td>
                    <td class="negative">+0.79% / -1.43%</td>
                    <td>1h45m<br><small>(19.0m - 4h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-2.53%</td>
                    <td>6</td>
                    <td>0.58</td>
                    <td class="neutral">4.57%</td>
                    <td class="positive"><strong>0.1147</strong></td>
                    <td class="negative">+0.88% / -1.29%</td>
                    <td>3h45m<br><small>(41.0m - 11h28m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-0.38%</td>
                    <td>2</td>
                    <td>0.81</td>
                    <td class="positive">1.98%</td>
                    <td class="positive"><strong>0.1248</strong></td>
                    <td class="positive">+1.26% / -1.25%</td>
                    <td>1h14m<br><small>(8.0m - 2h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">-1.95%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0610</strong></td>
                    <td class="negative">+0.00% / -1.35%</td>
                    <td>2h48m</td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">-2.27%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0671</strong></td>
                    <td class="negative">+0.00% / -1.40%</td>
                    <td>31h18m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 8: Momentum Divergence...', 'Momentum Rule 2: Momentum Dive...', 'Price Action Rule 3: Engulfing...', 'Acad Rule 2: Mean Reversion Fa...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'Rule 10: Volume Spike', 'Advanced Rule 7: DMI ADX Filte...', 'Professional Rule 7: Chaikin M...', 'Rule 2: Golden Cross'],
                y: [64.0081799591002, 64.43089430894308, 70.37037037037037, 64.6477132262052, 65.09433962264151, 70.27027027027027, 76.92307692307693, 83.33333333333334, 100.0, 58.536585365853654, 60.0, 66.66666666666666, 66.66666666666666, 57.14285714285714, 50.0],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 8: Momentum Divergence...', 'Momentum Rule 2: Momentum Dive...', 'Price Action Rule 3: Engulfing...', 'Acad Rule 2: Mean Reversion Fa...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'Rule 10: Volume Spike', 'Advanced Rule 7: DMI ADX Filte...', 'Professional Rule 7: Chaikin M...', 'Rule 2: Golden Cross'],
                y: [np.float64(68.75868007488758), np.float64(50.340081387768066), np.float64(36.799765911257474), np.float64(39.31289027066771), np.float64(6.9706728288889135), np.float64(16.89678900581879), np.float64(14.308383159603776), np.float64(7.933104169538957), np.float64(2.777920976984344), np.float64(-2.5791996030341395), np.float64(-4.67418896389271), np.float64(1.141987459186639), np.float64(0.1356833733848616), np.float64(-5.412673004026277), np.float64(-2.5313519419356014)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
