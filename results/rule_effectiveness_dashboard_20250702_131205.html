
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 13:12:05 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">249.57%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">2,813</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">65.3%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">60.30</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>77.11%</strong></td>
                    <td>65.4%</td>
                    <td>882</td>
                    <td>1.11</td>
                    <td class="negative">31.53%</td>
                    <td class="positive"><strong>0.9169</strong></td>
                    <td class="negative">+0.83% / -1.41%</td>
                    <td>4h34m<br><small>(1.0m - 82h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>56.61%</strong></td>
                    <td>71.0%</td>
                    <td>155</td>
                    <td>1.56</td>
                    <td class="negative">15.25%</td>
                    <td class="positive"><strong>0.7975</strong></td>
                    <td class="negative">+0.81% / -1.38%</td>
                    <td>2h47m<br><small>(1.0m - 20h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>35.88%</strong></td>
                    <td>81.2%</td>
                    <td>48</td>
                    <td>2.69</td>
                    <td class="neutral">2.38%</td>
                    <td class="positive"><strong>0.7874</strong></td>
                    <td class="negative">+0.84% / -1.31%</td>
                    <td>5h22m<br><small>(5.0m - 29h32m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>33.41%</strong></td>
                    <td>63.7%</td>
                    <td>1310</td>
                    <td>1.03</td>
                    <td class="negative">34.51%</td>
                    <td class="positive"><strong>0.8253</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>5h4m<br><small>(1.0m - 119h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>11.48%</strong></td>
                    <td>100.0%</td>
                    <td>8</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.83% / 0.00%</td>
                    <td>5h8m<br><small>(16.0m - 19h50m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>8.97%</strong></td>
                    <td>65.2%</td>
                    <td>46</td>
                    <td>1.25</td>
                    <td class="negative">12.18%</td>
                    <td class="positive"><strong>0.5051</strong></td>
                    <td class="negative">+0.82% / -1.39%</td>
                    <td>4h47m<br><small>(1.0m - 58h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>6.90%</strong></td>
                    <td>87.5%</td>
                    <td>8</td>
                    <td>3.63</td>
                    <td class="neutral">2.44%</td>
                    <td class="positive"><strong>0.5833</strong></td>
                    <td class="negative">+0.81% / -1.52%</td>
                    <td>3h58m<br><small>(20.0m - 12h32m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>5.49%</strong></td>
                    <td>85.7%</td>
                    <td>7</td>
                    <td>3.19</td>
                    <td class="neutral">2.36%</td>
                    <td class="positive"><strong>0.5510</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>11h3m<br><small>(15.0m - 31h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>5.09%</strong></td>
                    <td>73.7%</td>
                    <td>19</td>
                    <td>1.41</td>
                    <td class="negative">6.01%</td>
                    <td class="positive"><strong>0.4620</strong></td>
                    <td class="negative">+0.84% / -1.35%</td>
                    <td>12h42m<br><small>(16.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>3.62%</strong></td>
                    <td>75.0%</td>
                    <td>12</td>
                    <td>1.45</td>
                    <td class="negative">5.30%</td>
                    <td class="positive"><strong>0.4092</strong></td>
                    <td class="negative">+0.82% / -1.46%</td>
                    <td>12h8m<br><small>(30.0m - 54h39m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>3.42%</strong></td>
                    <td>100.0%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.92% / 0.00%</td>
                    <td>2.0m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>2.19%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.26% / 0.00%</td>
                    <td>8.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>1.53%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.77% / 0.00%</td>
                    <td>6h43m</td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>1.13%</strong></td>
                    <td>62.5%</td>
                    <td>8</td>
                    <td>1.18</td>
                    <td class="neutral">4.57%</td>
                    <td class="positive"><strong>0.3113</strong></td>
                    <td class="negative">+0.80% / -1.30%</td>
                    <td>5h28m<br><small>(19.0m - 20h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>0.56%</strong></td>
                    <td>62.5%</td>
                    <td>8</td>
                    <td>1.08</td>
                    <td class="neutral">2.94%</td>
                    <td class="positive"><strong>0.3004</strong></td>
                    <td class="negative">+0.88% / -1.32%</td>
                    <td>14h46m<br><small>(11.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>0.40%</strong></td>
                    <td>66.7%</td>
                    <td>15</td>
                    <td>1.03</td>
                    <td class="negative">5.88%</td>
                    <td class="positive"><strong>0.3469</strong></td>
                    <td class="negative">+0.80% / -1.36%</td>
                    <td>4h30m<br><small>(16.0m - 17h8m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-4.21%</strong></td>
                    <td>62.9%</td>
                    <td>283</td>
                    <td>0.98</td>
                    <td class="negative">23.15%</td>
                    <td class="positive"><strong>0.5854</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>5h16m<br><small>(1.0m - 63h37m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">11.48%</td>
                    <td>8</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.83% / 0.00%</td>
                    <td>5h8m<br><small>(16.0m - 19h50m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">3.42%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.92% / 0.00%</td>
                    <td>2.0m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">2.19%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.26% / 0.00%</td>
                    <td>8.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.53%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.77% / 0.00%</td>
                    <td>6h43m</td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>87.5%</strong></td>
                    <td class="positive">6.90%</td>
                    <td>8</td>
                    <td>3.63</td>
                    <td class="neutral">2.44%</td>
                    <td class="positive"><strong>0.5833</strong></td>
                    <td class="negative">+0.81% / -1.52%</td>
                    <td>3h58m<br><small>(20.0m - 12h32m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>85.7%</strong></td>
                    <td class="positive">5.49%</td>
                    <td>7</td>
                    <td>3.19</td>
                    <td class="neutral">2.36%</td>
                    <td class="positive"><strong>0.5510</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>11h3m<br><small>(15.0m - 31h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>81.2%</strong></td>
                    <td class="positive">35.88%</td>
                    <td>48</td>
                    <td>2.69</td>
                    <td class="neutral">2.38%</td>
                    <td class="positive"><strong>0.7874</strong></td>
                    <td class="negative">+0.84% / -1.31%</td>
                    <td>5h22m<br><small>(5.0m - 29h32m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">3.62%</td>
                    <td>12</td>
                    <td>1.45</td>
                    <td class="negative">5.30%</td>
                    <td class="positive"><strong>0.4092</strong></td>
                    <td class="negative">+0.82% / -1.46%</td>
                    <td>12h8m<br><small>(30.0m - 54h39m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>73.7%</strong></td>
                    <td class="positive">5.09%</td>
                    <td>19</td>
                    <td>1.41</td>
                    <td class="negative">6.01%</td>
                    <td class="positive"><strong>0.4620</strong></td>
                    <td class="negative">+0.84% / -1.35%</td>
                    <td>12h42m<br><small>(16.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>71.0%</strong></td>
                    <td class="positive">56.61%</td>
                    <td>155</td>
                    <td>1.56</td>
                    <td class="negative">15.25%</td>
                    <td class="positive"><strong>0.7975</strong></td>
                    <td class="negative">+0.81% / -1.38%</td>
                    <td>2h47m<br><small>(1.0m - 20h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">0.40%</td>
                    <td>15</td>
                    <td>1.03</td>
                    <td class="negative">5.88%</td>
                    <td class="positive"><strong>0.3469</strong></td>
                    <td class="negative">+0.80% / -1.36%</td>
                    <td>4h30m<br><small>(16.0m - 17h8m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>65.4%</strong></td>
                    <td class="positive">77.11%</td>
                    <td>882</td>
                    <td>1.11</td>
                    <td class="negative">31.53%</td>
                    <td class="positive"><strong>0.9169</strong></td>
                    <td class="negative">+0.83% / -1.41%</td>
                    <td>4h34m<br><small>(1.0m - 82h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>65.2%</strong></td>
                    <td class="positive">8.97%</td>
                    <td>46</td>
                    <td>1.25</td>
                    <td class="negative">12.18%</td>
                    <td class="positive"><strong>0.5051</strong></td>
                    <td class="negative">+0.82% / -1.39%</td>
                    <td>4h47m<br><small>(1.0m - 58h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>63.7%</strong></td>
                    <td class="positive">33.41%</td>
                    <td>1310</td>
                    <td>1.03</td>
                    <td class="negative">34.51%</td>
                    <td class="positive"><strong>0.8253</strong></td>
                    <td class="negative">+0.83% / -1.40%</td>
                    <td>5h4m<br><small>(1.0m - 119h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>62.9%</strong></td>
                    <td class="negative">-4.21%</td>
                    <td>283</td>
                    <td>0.98</td>
                    <td class="negative">23.15%</td>
                    <td class="positive"><strong>0.5854</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>5h16m<br><small>(1.0m - 63h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>62.5%</strong></td>
                    <td class="positive">1.13%</td>
                    <td>8</td>
                    <td>1.18</td>
                    <td class="neutral">4.57%</td>
                    <td class="positive"><strong>0.3113</strong></td>
                    <td class="negative">+0.80% / -1.30%</td>
                    <td>5h28m<br><small>(19.0m - 20h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>62.5%</strong></td>
                    <td class="positive">0.56%</td>
                    <td>8</td>
                    <td>1.08</td>
                    <td class="neutral">2.94%</td>
                    <td class="positive"><strong>0.3004</strong></td>
                    <td class="negative">+0.88% / -1.32%</td>
                    <td>14h46m<br><small>(11.0m - 68h37m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'Momentum Rule 2: Momentum Dive...', 'Rule 6: Stochastic Oversold Cr...', 'Professional Rule 10: CCI Reve...', 'AI Rule 8: Momentum Divergence...', 'Acad Rule 2: Mean Reversion Fa...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Ext Rule 5: ATR Volatility Exp...', 'Volume Rule 3: Dark Pool Activ...', 'Volume Rule 5: Smart Money Vol...', 'Rule 10: Volume Spike', 'Professional Rule 7: Chaikin M...'],
                y: [65.41950113378685, 70.96774193548387, 63.664122137404576, 81.25, 62.89752650176679, 100.0, 65.21739130434783, 100.0, 87.5, 100.0, 100.0, 85.71428571428571, 73.68421052631578, 75.0, 66.66666666666666],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'Momentum Rule 2: Momentum Dive...', 'Rule 6: Stochastic Oversold Cr...', 'Professional Rule 10: CCI Reve...', 'AI Rule 8: Momentum Divergence...', 'Acad Rule 2: Mean Reversion Fa...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Ext Rule 5: ATR Volatility Exp...', 'Volume Rule 3: Dark Pool Activ...', 'Volume Rule 5: Smart Money Vol...', 'Rule 10: Volume Spike', 'Professional Rule 7: Chaikin M...'],
                y: [np.float64(77.10666997549983), np.float64(56.6085226952959), np.float64(33.406885483012374), np.float64(35.88205414419627), np.float64(-4.208783595681147), np.float64(11.475543645337297), np.float64(8.967487003600255), np.float64(3.4176935579493586), np.float64(6.904157119801763), np.float64(2.186084208190121), np.float64(1.5305621964154417), np.float64(5.4880322559267665), np.float64(5.090620242474528), np.float64(3.6202535546842842), np.float64(0.40334120862142303)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
