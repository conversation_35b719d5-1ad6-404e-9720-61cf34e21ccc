
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 17:10:00 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">-19.10%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">3,310</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">61.5%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.71</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>12.70%</strong></td>
                    <td>67.6%</td>
                    <td>136</td>
                    <td>1.19</td>
                    <td class="negative">12.13%</td>
                    <td class="positive"><strong>0.6103</strong></td>
                    <td class="negative">+0.91% / -1.56%</td>
                    <td>1h33m<br><small>(1.0m - 12h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>10.03%</strong></td>
                    <td>66.7%</td>
                    <td>111</td>
                    <td>1.20</td>
                    <td class="negative">9.65%</td>
                    <td class="positive"><strong>0.5962</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h1m<br><small>(2.0m - 12h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>7.70%</strong></td>
                    <td>90.9%</td>
                    <td>11</td>
                    <td>6.73</td>
                    <td class="positive">1.31%</td>
                    <td class="positive"><strong>0.7839</strong></td>
                    <td class="negative">+0.90% / -1.27%</td>
                    <td>3h19m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>7.21%</strong></td>
                    <td>68.2%</td>
                    <td>44</td>
                    <td>1.39</td>
                    <td class="neutral">4.74%</td>
                    <td class="positive"><strong>0.5438</strong></td>
                    <td class="negative">+0.89% / -1.36%</td>
                    <td>3h2m<br><small>(7.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>3.90%</strong></td>
                    <td>67.3%</td>
                    <td>52</td>
                    <td>1.15</td>
                    <td class="negative">6.61%</td>
                    <td class="positive"><strong>0.4920</strong></td>
                    <td class="negative">+0.88% / -1.54%</td>
                    <td>2h58m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>1.85%</strong></td>
                    <td>66.7%</td>
                    <td>6</td>
                    <td>1.66</td>
                    <td class="positive">1.36%</td>
                    <td class="positive"><strong>0.3813</strong></td>
                    <td class="negative">+1.19% / -1.54%</td>
                    <td>1.8m<br><small>(1.0m - 4.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>1.68%</strong></td>
                    <td>75.0%</td>
                    <td>8</td>
                    <td>1.56</td>
                    <td class="positive">1.48%</td>
                    <td class="positive"><strong>0.3914</strong></td>
                    <td class="negative">+0.81% / -1.39%</td>
                    <td>1h50m<br><small>(6.0m - 4h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>1.54%</strong></td>
                    <td>100.0%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.82% / 0.00%</td>
                    <td>34.5m<br><small>(13.0m - 56.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>0.87%</strong></td>
                    <td>64.3%</td>
                    <td>28</td>
                    <td>1.06</td>
                    <td class="neutral">3.96%</td>
                    <td class="positive"><strong>0.4156</strong></td>
                    <td class="negative">+0.88% / -1.43%</td>
                    <td>3h0m<br><small>(4.0m - 24h50m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>0.41%</strong></td>
                    <td>59.3%</td>
                    <td>27</td>
                    <td>1.03</td>
                    <td class="negative">7.83%</td>
                    <td class="positive"><strong>0.4114</strong></td>
                    <td class="negative">+1.02% / -1.34%</td>
                    <td>2h2m<br><small>(2.0m - 11h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-0.26%</strong></td>
                    <td>61.5%</td>
                    <td>801</td>
                    <td>1.00</td>
                    <td class="negative">40.62%</td>
                    <td class="positive"><strong>0.6761</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h11m<br><small>(1.0m - 45h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-1.12%</strong></td>
                    <td>63.6%</td>
                    <td>11</td>
                    <td>0.83</td>
                    <td class="neutral">2.05%</td>
                    <td class="positive"><strong>0.2472</strong></td>
                    <td class="negative">+0.81% / -1.57%</td>
                    <td>2h52m<br><small>(19.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="negative"><strong>-2.12%</strong></td>
                    <td>61.5%</td>
                    <td>1016</td>
                    <td>1.00</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.7073</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h21m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-3.52%</strong></td>
                    <td>37.5%</td>
                    <td>8</td>
                    <td>0.46</td>
                    <td class="negative">5.26%</td>
                    <td class="positive"><strong>0.0731</strong></td>
                    <td class="negative">+0.97% / -1.37%</td>
                    <td>1h22m<br><small>(13.0m - 3h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-13.56%</strong></td>
                    <td>53.8%</td>
                    <td>78</td>
                    <td>0.72</td>
                    <td class="negative">17.54%</td>
                    <td class="positive"><strong>0.3594</strong></td>
                    <td class="negative">+0.90% / -1.49%</td>
                    <td>1h47m<br><small>(1.0m - 15h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-46.42%</strong></td>
                    <td>59.5%</td>
                    <td>971</td>
                    <td>0.91</td>
                    <td class="negative">51.65%</td>
                    <td class="positive"><strong>0.5423</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h44m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.54%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.82% / 0.00%</td>
                    <td>34.5m<br><small>(13.0m - 56.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>90.9%</strong></td>
                    <td class="positive">7.70%</td>
                    <td>11</td>
                    <td>6.73</td>
                    <td class="positive">1.31%</td>
                    <td class="positive"><strong>0.7839</strong></td>
                    <td class="negative">+0.90% / -1.27%</td>
                    <td>3h19m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">1.68%</td>
                    <td>8</td>
                    <td>1.56</td>
                    <td class="positive">1.48%</td>
                    <td class="positive"><strong>0.3914</strong></td>
                    <td class="negative">+0.81% / -1.39%</td>
                    <td>1h50m<br><small>(6.0m - 4h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>68.2%</strong></td>
                    <td class="positive">7.21%</td>
                    <td>44</td>
                    <td>1.39</td>
                    <td class="neutral">4.74%</td>
                    <td class="positive"><strong>0.5438</strong></td>
                    <td class="negative">+0.89% / -1.36%</td>
                    <td>3h2m<br><small>(7.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>67.6%</strong></td>
                    <td class="positive">12.70%</td>
                    <td>136</td>
                    <td>1.19</td>
                    <td class="negative">12.13%</td>
                    <td class="positive"><strong>0.6103</strong></td>
                    <td class="negative">+0.91% / -1.56%</td>
                    <td>1h33m<br><small>(1.0m - 12h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>67.3%</strong></td>
                    <td class="positive">3.90%</td>
                    <td>52</td>
                    <td>1.15</td>
                    <td class="negative">6.61%</td>
                    <td class="positive"><strong>0.4920</strong></td>
                    <td class="negative">+0.88% / -1.54%</td>
                    <td>2h58m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">10.03%</td>
                    <td>111</td>
                    <td>1.20</td>
                    <td class="negative">9.65%</td>
                    <td class="positive"><strong>0.5962</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h1m<br><small>(2.0m - 12h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">1.85%</td>
                    <td>6</td>
                    <td>1.66</td>
                    <td class="positive">1.36%</td>
                    <td class="positive"><strong>0.3813</strong></td>
                    <td class="negative">+1.19% / -1.54%</td>
                    <td>1.8m<br><small>(1.0m - 4.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>64.3%</strong></td>
                    <td class="positive">0.87%</td>
                    <td>28</td>
                    <td>1.06</td>
                    <td class="neutral">3.96%</td>
                    <td class="positive"><strong>0.4156</strong></td>
                    <td class="negative">+0.88% / -1.43%</td>
                    <td>3h0m<br><small>(4.0m - 24h50m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>63.6%</strong></td>
                    <td class="negative">-1.12%</td>
                    <td>11</td>
                    <td>0.83</td>
                    <td class="neutral">2.05%</td>
                    <td class="positive"><strong>0.2472</strong></td>
                    <td class="negative">+0.81% / -1.57%</td>
                    <td>2h52m<br><small>(19.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>61.5%</strong></td>
                    <td class="negative">-0.26%</td>
                    <td>801</td>
                    <td>1.00</td>
                    <td class="negative">40.62%</td>
                    <td class="positive"><strong>0.6761</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h11m<br><small>(1.0m - 45h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>61.5%</strong></td>
                    <td class="negative">-2.12%</td>
                    <td>1016</td>
                    <td>1.00</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.7073</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h21m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>59.5%</strong></td>
                    <td class="negative">-46.42%</td>
                    <td>971</td>
                    <td>0.91</td>
                    <td class="negative">51.65%</td>
                    <td class="positive"><strong>0.5423</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h44m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>59.3%</strong></td>
                    <td class="positive">0.41%</td>
                    <td>27</td>
                    <td>1.03</td>
                    <td class="negative">7.83%</td>
                    <td class="positive"><strong>0.4114</strong></td>
                    <td class="negative">+1.02% / -1.34%</td>
                    <td>2h2m<br><small>(2.0m - 11h16m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>53.8%</strong></td>
                    <td class="negative">-13.56%</td>
                    <td>78</td>
                    <td>0.72</td>
                    <td class="negative">17.54%</td>
                    <td class="positive"><strong>0.3594</strong></td>
                    <td class="negative">+0.90% / -1.49%</td>
                    <td>1h47m<br><small>(1.0m - 15h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="neutral"><strong>37.5%</strong></td>
                    <td class="negative">-3.52%</td>
                    <td>8</td>
                    <td>0.46</td>
                    <td class="negative">5.26%</td>
                    <td class="positive"><strong>0.0731</strong></td>
                    <td class="negative">+0.97% / -1.37%</td>
                    <td>1h22m<br><small>(13.0m - 3h51m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'Rule 6: Stochastic Oversold Cr...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 10: Composite Sentimen...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'AI Rule 8: Momentum Divergence...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Ext Rule 6: Fibonacci Support ...', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Rule 2: Golden Cross', 'Acad Rule 2: Mean Reversion Fa...', 'Rule 10: Volume Spike'],
                y: [67.64705882352942, 66.66666666666666, 61.54806491885143, 61.51574803149607, 67.3076923076923, 68.18181818181817, 53.84615384615385, 90.9090909090909, 100.0, 59.52626158599382, 64.28571428571429, 59.25925925925925, 75.0, 66.66666666666666, 63.63636363636363],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'Rule 6: Stochastic Oversold Cr...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 10: Composite Sentimen...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'AI Rule 8: Momentum Divergence...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Ext Rule 6: Fibonacci Support ...', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Rule 2: Golden Cross', 'Acad Rule 2: Mean Reversion Fa...', 'Rule 10: Volume Spike'],
                y: [np.float64(12.696553949765935), np.float64(10.032919613643026), np.float64(-0.2552174580320716), np.float64(-2.121866577055087), np.float64(3.900296049551471), np.float64(7.2064285603914175), np.float64(-13.556961375212339), np.float64(7.701031326983925), np.float64(1.5405873138908646), np.float64(-46.423294684794584), np.float64(0.8745035452652228), np.float64(0.4093039534913259), np.float64(1.6779757834178892), np.float64(1.851627457552604), np.float64(-1.1214445009345508)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
