
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 11:52:50 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">29.98%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">6,417</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.0%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1.40</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>34.15%</strong></td>
                    <td>62.7%</td>
                    <td>1921</td>
                    <td>1.03</td>
                    <td class="negative">29.28%</td>
                    <td class="positive"><strong>0.8737</strong></td>
                    <td class="negative">+0.89% / -1.44%</td>
                    <td>2h3m<br><small>(1.0m - 48h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>27.78%</strong></td>
                    <td>64.6%</td>
                    <td>381</td>
                    <td>1.12</td>
                    <td class="negative">16.20%</td>
                    <td class="positive"><strong>0.7345</strong></td>
                    <td class="negative">+0.89% / -1.46%</td>
                    <td>1h41m<br><small>(1.0m - 30h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>12.40%</strong></td>
                    <td>62.0%</td>
                    <td>2467</td>
                    <td>1.01</td>
                    <td class="negative">34.30%</td>
                    <td class="positive"><strong>0.8326</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h34m<br><small>(1.0m - 51h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>10.36%</strong></td>
                    <td>68.2%</td>
                    <td>44</td>
                    <td>1.46</td>
                    <td class="neutral">3.66%</td>
                    <td class="positive"><strong>0.5751</strong></td>
                    <td class="negative">+0.95% / -1.31%</td>
                    <td>2h47m<br><small>(2.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>5.14%</strong></td>
                    <td>100.0%</td>
                    <td>5</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.83% / 0.00%</td>
                    <td>1h34m<br><small>(15.0m - 5h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>4.21%</strong></td>
                    <td>70.6%</td>
                    <td>17</td>
                    <td>1.52</td>
                    <td class="neutral">3.68%</td>
                    <td class="positive"><strong>0.4683</strong></td>
                    <td class="negative">+0.92% / -1.37%</td>
                    <td>1h58m<br><small>(3.0m - 8h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>4.16%</strong></td>
                    <td>75.0%</td>
                    <td>12</td>
                    <td>1.74</td>
                    <td class="neutral">2.49%</td>
                    <td class="positive"><strong>0.4595</strong></td>
                    <td class="negative">+0.85% / -1.41%</td>
                    <td>2h45m<br><small>(21.0m - 9h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>3.70%</strong></td>
                    <td>66.7%</td>
                    <td>33</td>
                    <td>1.19</td>
                    <td class="negative">8.38%</td>
                    <td class="positive"><strong>0.4624</strong></td>
                    <td class="negative">+0.94% / -1.45%</td>
                    <td>2h30m<br><small>(1.0m - 13h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>1.52%</strong></td>
                    <td>58.3%</td>
                    <td>12</td>
                    <td>1.19</td>
                    <td class="neutral">2.92%</td>
                    <td class="positive"><strong>0.3693</strong></td>
                    <td class="negative">+1.23% / -1.52%</td>
                    <td>3.6m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>1.04%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.84% / 0.00%</td>
                    <td>13.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="negative"><strong>-0.55%</strong></td>
                    <td>58.6%</td>
                    <td>70</td>
                    <td>0.99</td>
                    <td class="negative">8.19%</td>
                    <td class="positive"><strong>0.4854</strong></td>
                    <td class="negative">+0.94% / -1.35%</td>
                    <td>2h50m<br><small>(4.0m - 18h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-3.92%</strong></td>
                    <td>56.2%</td>
                    <td>16</td>
                    <td>0.71</td>
                    <td class="negative">7.00%</td>
                    <td class="positive"><strong>0.2432</strong></td>
                    <td class="negative">+0.90% / -1.47%</td>
                    <td>3h15m<br><small>(14.0m - 12h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-5.48%</strong></td>
                    <td>52.2%</td>
                    <td>23</td>
                    <td>0.69</td>
                    <td class="negative">6.85%</td>
                    <td class="positive"><strong>0.2693</strong></td>
                    <td class="negative">+0.84% / -1.34%</td>
                    <td>2h40m<br><small>(15.0m - 18h2m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-14.49%</strong></td>
                    <td>58.9%</td>
                    <td>180</td>
                    <td>0.88</td>
                    <td class="negative">23.34%</td>
                    <td class="positive"><strong>0.4860</strong></td>
                    <td class="negative">+0.89% / -1.44%</td>
                    <td>1h36m<br><small>(1.0m - 25h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="negative"><strong>-19.25%</strong></td>
                    <td>61.0%</td>
                    <td>774</td>
                    <td>0.96</td>
                    <td class="negative">30.47%</td>
                    <td class="positive"><strong>0.6312</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h3m<br><small>(1.0m - 48h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-30.78%</strong></td>
                    <td>59.7%</td>
                    <td>461</td>
                    <td>0.90</td>
                    <td class="negative">38.68%</td>
                    <td class="positive"><strong>0.5233</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>3h4m<br><small>(2.0m - 49h14m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">5.14%</td>
                    <td>5</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.83% / 0.00%</td>
                    <td>1h34m<br><small>(15.0m - 5h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.04%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.84% / 0.00%</td>
                    <td>13.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">4.16%</td>
                    <td>12</td>
                    <td>1.74</td>
                    <td class="neutral">2.49%</td>
                    <td class="positive"><strong>0.4595</strong></td>
                    <td class="negative">+0.85% / -1.41%</td>
                    <td>2h45m<br><small>(21.0m - 9h7m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>70.6%</strong></td>
                    <td class="positive">4.21%</td>
                    <td>17</td>
                    <td>1.52</td>
                    <td class="neutral">3.68%</td>
                    <td class="positive"><strong>0.4683</strong></td>
                    <td class="negative">+0.92% / -1.37%</td>
                    <td>1h58m<br><small>(3.0m - 8h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>68.2%</strong></td>
                    <td class="positive">10.36%</td>
                    <td>44</td>
                    <td>1.46</td>
                    <td class="neutral">3.66%</td>
                    <td class="positive"><strong>0.5751</strong></td>
                    <td class="negative">+0.95% / -1.31%</td>
                    <td>2h47m<br><small>(2.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">3.70%</td>
                    <td>33</td>
                    <td>1.19</td>
                    <td class="negative">8.38%</td>
                    <td class="positive"><strong>0.4624</strong></td>
                    <td class="negative">+0.94% / -1.45%</td>
                    <td>2h30m<br><small>(1.0m - 13h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>64.6%</strong></td>
                    <td class="positive">27.78%</td>
                    <td>381</td>
                    <td>1.12</td>
                    <td class="negative">16.20%</td>
                    <td class="positive"><strong>0.7345</strong></td>
                    <td class="negative">+0.89% / -1.46%</td>
                    <td>1h41m<br><small>(1.0m - 30h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>62.7%</strong></td>
                    <td class="positive">34.15%</td>
                    <td>1921</td>
                    <td>1.03</td>
                    <td class="negative">29.28%</td>
                    <td class="positive"><strong>0.8737</strong></td>
                    <td class="negative">+0.89% / -1.44%</td>
                    <td>2h3m<br><small>(1.0m - 48h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>62.0%</strong></td>
                    <td class="positive">12.40%</td>
                    <td>2467</td>
                    <td>1.01</td>
                    <td class="negative">34.30%</td>
                    <td class="positive"><strong>0.8326</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h34m<br><small>(1.0m - 51h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>61.0%</strong></td>
                    <td class="negative">-19.25%</td>
                    <td>774</td>
                    <td>0.96</td>
                    <td class="negative">30.47%</td>
                    <td class="positive"><strong>0.6312</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h3m<br><small>(1.0m - 48h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>59.7%</strong></td>
                    <td class="negative">-30.78%</td>
                    <td>461</td>
                    <td>0.90</td>
                    <td class="negative">38.68%</td>
                    <td class="positive"><strong>0.5233</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>3h4m<br><small>(2.0m - 49h14m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>58.9%</strong></td>
                    <td class="negative">-14.49%</td>
                    <td>180</td>
                    <td>0.88</td>
                    <td class="negative">23.34%</td>
                    <td class="positive"><strong>0.4860</strong></td>
                    <td class="negative">+0.89% / -1.44%</td>
                    <td>1h36m<br><small>(1.0m - 25h48m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>58.6%</strong></td>
                    <td class="negative">-0.55%</td>
                    <td>70</td>
                    <td>0.99</td>
                    <td class="negative">8.19%</td>
                    <td class="positive"><strong>0.4854</strong></td>
                    <td class="negative">+0.94% / -1.35%</td>
                    <td>2h50m<br><small>(4.0m - 18h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>58.3%</strong></td>
                    <td class="positive">1.52%</td>
                    <td>12</td>
                    <td>1.19</td>
                    <td class="neutral">2.92%</td>
                    <td class="positive"><strong>0.3693</strong></td>
                    <td class="negative">+1.23% / -1.52%</td>
                    <td>3.6m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>56.2%</strong></td>
                    <td class="negative">-3.92%</td>
                    <td>16</td>
                    <td>0.71</td>
                    <td class="negative">7.00%</td>
                    <td class="positive"><strong>0.2432</strong></td>
                    <td class="negative">+0.90% / -1.47%</td>
                    <td>3h15m<br><small>(14.0m - 12h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>52.2%</strong></td>
                    <td class="negative">-5.48%</td>
                    <td>23</td>
                    <td>0.69</td>
                    <td class="negative">6.85%</td>
                    <td class="positive"><strong>0.2693</strong></td>
                    <td class="negative">+0.84% / -1.34%</td>
                    <td>2h40m<br><small>(15.0m - 18h2m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 8: Momentum Divergence...', 'AI Rule 3: Smart Money Flow Di...', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Rule 6: Stochastic Oversold Cr...', 'Price Action Rule 3: Engulfing...', 'Volume Rule 5: Smart Money Vol...', 'Rule 27: Structure Break Up', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike', 'Acad Rule 2: Mean Reversion Fa...', 'Advanced Rule 7: DMI ADX Filte...'],
                y: [62.675689744924526, 64.56692913385827, 62.0186461289015, 58.88888888888889, 60.98191214470285, 58.57142857142858, 68.18181818181817, 59.65292841648589, 100.0, 66.66666666666666, 100.0, 70.58823529411765, 75.0, 58.333333333333336, 52.17391304347826],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 8: Momentum Divergence...', 'AI Rule 3: Smart Money Flow Di...', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Rule 6: Stochastic Oversold Cr...', 'Price Action Rule 3: Engulfing...', 'Volume Rule 5: Smart Money Vol...', 'Rule 27: Structure Break Up', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike', 'Acad Rule 2: Mean Reversion Fa...', 'Advanced Rule 7: DMI ADX Filte...'],
                y: [np.float64(34.151804751139586), np.float64(27.776159792094983), np.float64(12.397068199497022), np.float64(-14.485664605359053), np.float64(-19.246807034971162), np.float64(-0.5476486922945769), np.float64(10.356945806297487), np.float64(-30.78267254350292), np.float64(5.137353100307606), np.float64(3.700545950294982), np.float64(1.0367427607564896), np.float64(4.210055788956495), np.float64(4.164340250722685), np.float64(1.5164327895708993), np.float64(-5.483960029755166)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
