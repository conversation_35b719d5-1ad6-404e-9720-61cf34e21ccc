
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 7 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 13:12:05</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">7</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">41.2%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">31.5%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">77.1%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">70.7%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">2,472</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["77.1%","56.6%","33.4%","35.9%","9.0%","5.1%","3.6%"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","AI Rule 8: Momentum Divergence Reversal","Volume Rule 5: Smart Money Volume","Rule 10: Volume Spike"],"y":[77.10666997549983,56.6085226952959,33.406885483012374,35.88205414419627,8.967487003600255,5.090620242474528,3.6202535546842842],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","AI Rule 8: Momentum Divergence Reversal","Volume Rule 5: Smart Money Volume","Rule 10: Volume Spike"],"y":[577,110,834,39,30,13,9],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","AI Rule 8: Momentum Divergence Reversal","Volume Rule 5: Smart Money Volume","Rule 10: Volume Spike"],"y":[305,45,476,9,16,6,3],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[77.10666997549983,56.6085226952959,33.406885483012374,35.88205414419627,8.967487003600255,5.090620242474528,3.6202535546842842],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["AI Rule 10","Prof Rule 7","Rule 7","Momentum Rule 2","AI Rule 8","Volume Rule 5","Rule 10"],"textposition":"top center","x":[31.529896128838775,15.245994107367721,34.50941331752725,2.3824242664740845,12.177078830814771,6.012721735705527,5.2953874594348616],"y":[77.10666997549983,56.6085226952959,33.406885483012374,35.88205414419627,8.967487003600255,5.090620242474528,3.6202535546842842],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["43.0%","56.6%","18.5%","20.5%"],"textposition":"auto","x":["AI_GENERATED","PROFESSIONAL","ORIGINAL","UNKNOWN"],"y":[43.03707848955004,56.6085226952959,18.51356951884833,20.486337193335398],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["882","155","1310","48","46","19","12"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","Momentum Rule 2: Momentum Divergence Recovery","AI Rule 8: Momentum Divergence Reversal","Volume Rule 5: Smart Money Volume","Rule 10: Volume Spike"],"y":[882,155,1310,48,46,19,12],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882],"y":[0,0.08742252831689323,0.17484505663378647,0.2622675849506797,0.34969011326757293,0.4371126415844661,0.5245351699013594,0.6119576982182525,0.6993802265351459,0.786802754852039,0.8742252831689322,0.9616478114858256,1.0490703398027188,1.136492868119612,1.223915396436505,1.3113379247533985,1.3987604530702917,1.4861829813871847,1.573605509704078,1.6610280380209714,1.7484505663378644,1.8358730946547577,1.9232956229716511,2.010718151288544,2.0981406796054376,2.1855632079223306,2.272985736239224,2.360408264556117,2.44783079287301,2.5352533211899035,2.622675849506797,2.71009837782369,2.7975209061405835,2.8849434344574765,2.9723659627743695,3.059788491091263,3.147211019408156,3.2346335477250494,3.322056076041943,3.409478604358836,3.496901132675729,3.5843236609926223,3.6717461893095154,3.7591687176264093,3.8465912459433023,3.9340137742601953,4.021436302577088,4.108858830893982,4.196281359210875,4.283703887527768,4.371126415844661,4.458548944161555,4.545971472478448,4.633394000795341,4.720816529112234,4.808239057429128,4.89566158574602,4.9830841140629145,5.070506642379807,5.1579291706967005,5.245351699013594,5.332774227330487,5.42019675564738,5.5076192839642735,5.595041812281167,5.6824643405980595,5.769886868914953,5.857309397231846,5.944731925548739,6.032154453865633,6.119576982182526,6.206999510499419,6.294422038816312,6.381844567133205,6.469267095450099,6.556689623766992,6.644112152083886,6.731534680400778,6.818957208717672,6.906379737034565,6.993802265351458,7.081224793668352,7.168647321985245,7.256069850302138,7.343492378619031,7.430914906935924,7.5183374352528185,7.605759963569711,7.6931824918866045,7.780605020203497,7.8680275485203905,7.955450076837283,8.042872605154177,8.130295133471071,8.217717661787963,8.305140190104856,8.39256271842175,8.479985246738643,8.567407775055536,8.65483030337243,8.742252831689322,8.829675360006215,8.91709788832311,9.004520416640002,9.091942944956896,9.179365473273789,9.266788001590681,9.354210529907576,9.441633058224468,9.529055586541363,9.616478114858255,9.703900643175148,9.79132317149204,9.878745699808936,9.966168228125829,10.053590756442722,10.141013284759614,10.228435813076509,10.315858341393401,10.403280869710294,10.490703398027188,10.57812592634408,10.665548454660973,10.752970982977866,10.84039351129476,10.927816039611653,11.015238567928547,11.102661096245441,11.190083624562334,11.277506152879226,11.364928681196119,11.452351209513013,11.539773737829906,11.627196266146798,11.714618794463693,11.802041322780585,11.889463851097478,11.97688637941437,12.064308907731267,12.15173143604816,12.239153964365052,12.326576492681946,12.413999020998839,12.501421549315731,12.588844077632624,12.676266605949518,12.76368913426641,12.851111662583303,12.938534190900198,13.025956719217092,13.113379247533985,13.200801775850877,13.288224304167771,13.375646832484664,13.463069360801557,13.550491889118451,13.637914417435343,13.725336945752236,13.81275947406913,13.900182002386023,13.987604530702916,14.075027059019808,14.162449587336704,14.249872115653597,14.33729464397049,14.424717172287384,14.512139700604276,14.599562228921169,14.686984757238061,14.774407285554956,14.861829813871848,14.94925234218874,15.036674870505637,15.12409739882253,15.211519927139422,15.298942455456315,15.386364983773209,15.473787512090102,15.561210040406994,15.648632568723889,15.736055097040781,15.823477625357674,15.910900153674566,15.99832268199146,16.085745210308353,16.173167738625246,16.260590266942142,16.348012795259034,16.435435323575927,16.52285785189282,16.610280380209712,16.697702908526608,16.7851254368435,16.872547965160393,16.959970493477286,17.04739302179418,17.13481555011107,17.222238078427967,17.30966060674486,17.397083135061752,17.484505663378645,17.571928191695537,17.65935072001243,17.746773248329323,17.83419577664622,17.92161830496311,18.009040833280004,18.0964633615969,18.183885889913793,18.271308418230685,18.358730946547578,18.44615347486447,18.533576003181363,18.620998531498255,18.70842105981515,18.795843588132044,18.883266116448937,18.97068864476583,19.058111173082725,19.145533701399618,19.23295622971651,19.320378758033403,19.407801286350296,19.49522381466719,19.58264634298408,19.670068871300977,19.757491399617873,19.844913927934762,19.932336456251658,20.019758984568547,20.107181512885443,20.194604041202336,20.28202656951923,20.36944909783612,20.456871626153017,20.544294154469906,20.631716682786802,20.7191392111037,20.806561739420587,20.893984267737483,20.981406796054376,21.06882932437127,21.15625185268816,21.243674381005054,21.331096909321946,21.418519437638842,21.50594196595573,21.593364494272627,21.68078702258952,21.768209550906413,21.855632079223305,21.9430546075402,22.030477135857094,22.117899664173986,22.205322192490883,22.29274472080777,22.380167249124668,22.46758977744156,22.555012305758453,22.642434834075345,22.729857362392238,22.81727989070913,22.904702419026027,22.992124947342916,23.079547475659812,23.166970003976708,23.254392532293597,23.341815060610493,23.429237588927386,23.516660117244278,23.60408264556117,23.691505173878067,23.778927702194956,23.866350230511852,23.95377275882874,24.041195287145637,24.128617815462533,24.216040343779422,24.30346287209632,24.39088540041321,24.478307928730104,24.565730457046996,24.653152985363892,24.74057551368078,24.827998041997677,24.915420570314573,25.002843098631462,25.09026562694836,25.177688155265248,25.265110683582144,25.352533211899036,25.43995574021593,25.52737826853282,25.614800796849718,25.702223325166607,25.789645853483503,25.877068381800395,25.964490910117288,26.051913438434184,26.139335966751077,26.22675849506797,26.31418102338486,26.401603551701754,26.489026080018647,26.576448608335543,26.663871136652432,26.751293664969328,26.83871619328622,26.926138721603113,27.013561249920006,27.100983778236902,27.18840630655379,27.275828834870687,27.363251363187583,27.450673891504472,27.53809641982137,27.62551894813826,27.712941476455153,27.800364004772046,27.88778653308894,27.97520906140583,28.062631589722727,28.150054118039616,28.237476646356512,28.32489917467341,28.412321702990297,28.499744231307194,28.587166759624086,28.67458928794098,28.76201181625787,28.849434344574767,28.936856872891656,29.024279401208553,29.11170192952544,29.199124457842338,29.286546986159234,29.373969514476123,29.46139204279302,29.54881457110991,29.636237099426804,29.723659627743697,29.811082156060593,29.89850468437748,29.985927212694378,30.073349741011274,30.160772269328163,30.24819479764506,30.335617325961948,30.423039854278844,30.510462382595737,30.59788491091263,30.685307439229522,30.772729967546418,30.860152495863307,30.947575024180203,31.034997552497096,31.12242008081399,31.20984260913088,31.297265137447777,31.38468766576467,31.472110194081562,31.559532722398455,31.646955250715347,31.734377779032243,31.821800307349132,31.90922283566603,31.99664536398292,32.084067892299814,32.171490420616706,32.258912948933606,32.34633547725049,32.43375800556739,32.521180533884284,32.608603062201176,32.69602559051807,32.78344811883496,32.870870647151854,32.95829317546875,33.04571570378564,33.13313823210253,33.220560760419424,33.30798328873632,33.395405817053216,33.48282834537011,33.570250873687,33.657673402003894,33.74509593032079,33.83251845863768,33.91994098695457,34.007363515271464,34.09478604358836,34.18220857190525,34.26963110022214,34.357053628539035,34.444476156855934,34.53189868517282,34.61932121348972,34.70674374180661,34.794166270123505,34.8815887984404,34.96901132675729,35.05643385507418,35.143856383391075,35.231278911707975,35.31870144002486,35.40612396834176,35.493546496658645,35.580969024975545,35.66839155329244,35.75581408160933,35.84323660992622,35.930659138243115,36.01808166656001,36.1055041948769,36.1929267231938,36.280349251510685,36.367771779827585,36.45519430814448,36.54261683646137,36.63003936477826,36.717461893095155,36.80488442141205,36.89230694972894,36.97972947804583,37.067152006362726,37.154574534679625,37.24199706299651,37.32941959131341,37.4168421196303,37.504264647947195,37.59168717626409,37.67910970458098,37.76653223289787,37.853954761214766,37.94137728953166,38.02879981784855,38.11622234616545,38.203644874482336,38.291067402799236,38.37848993111613,38.46591245943302,38.55333498774991,38.640757516066806,38.728180044383706,38.81560257270059,38.903025101017484,38.99044762933438,39.077870157651276,39.16529268596816,39.25271521428506,39.340137742601954,39.427560270918846,39.514982799235746,39.60240532755263,39.689827855869524,39.777250384186424,39.864672912503316,39.9520954408202,40.039517969137094,40.126940497453994,40.214363025770886,40.30178555408777,40.38920808240467,40.476630610721564,40.56405313903846,40.651475667355356,40.73889819567224,40.826320723989134,40.913743252306034,41.00116578062293,41.08858830893981,41.17601083725671,41.263433365573604,41.3508558938905,41.4382784222074,41.52570095052428,41.613123478841175,41.700546007158074,41.78796853547497,41.87539106379185,41.96281359210875,42.050236120425645,42.13765864874254,42.22508117705943,42.31250370537632,42.399926233693215,42.48734876201011,42.57477129032701,42.66219381864389,42.749616346960785,42.837038875277685,42.92446140359458,43.01188393191146,43.09930646022836,43.186728988545255,43.27415151686215,43.36157404517904,43.44899657349593,43.536419101812825,43.623841630129725,43.71126415844661,43.7986866867635,43.8861092150804,43.973531743397295,44.06095427171419,44.14837680003108,44.23579932834797,44.323221856664865,44.410644384981765,44.49806691329865,44.58548944161554,44.67291196993244,44.760334498249335,44.84775702656622,44.93517955488312,45.02260208320001,45.110024611516906,45.19744713983379,45.28486966815069,45.37229219646758,45.459714724784476,45.547137253101376,45.63455978141826,45.721982309735154,45.80940483805205,45.896827366368946,45.98424989468583,46.07167242300273,46.159094951319624,46.246517479636516,46.333940007953416,46.4213625362703,46.508785064587194,46.59620759290409,46.683630121220986,46.77105264953787,46.85847517785477,46.945897706171664,47.033320234488556,47.120742762805456,47.20816529112234,47.295587819439234,47.383010347756134,47.470432876073026,47.55785540438991,47.645277932706804,47.732700461023704,47.8201229893406,47.90754551765748,47.99496804597438,48.082390574291274,48.16981310260817,48.25723563092507,48.34465815924195,48.432080687558845,48.519503215875744,48.60692574419264,48.69434827250952,48.78177080082642,48.869193329143314,48.95661585746021,49.04403838577711,49.13146091409399,49.218883442410885,49.306305970727784,49.39372849904468,49.48115102736156,49.56857355567846,49.655996083995355,49.74341861231225,49.83084114062915,49.91826366894603,50.005686197262925,50.093108725579825,50.18053125389672,50.2679537822136,50.355376310530495,50.442798838847395,50.53022136716429,50.61764389548117,50.70506642379807,50.792488952114965,50.87991148043186,50.96733400874876,51.05475653706564,51.142179065382535,51.229601593699435,51.31702412201633,51.40444665033321,51.49186917865011,51.579291706967005,51.6667142352839,51.75413676360079,51.84155929191768,51.928981820234576,52.016404348551475,52.10382687686837,52.19124940518525,52.27867193350215,52.366094461819046,52.45351699013594,52.54093951845283,52.62836204676972,52.715784575086616,52.80320710340351,52.8906296317204,52.978052160037294,53.065474688354186,53.152897216671086,53.24031974498798,53.327742273304864,53.41516480162176,53.502587329938656,53.59000985825555,53.67743238657244,53.764854914889334,53.852277443206226,53.939699971523126,54.02712249984001,54.114545028156904,54.201967556473804,54.289390084790696,54.37681261310758,54.46423514142448,54.551657669741374,54.63908019805827,54.726502726375166,54.81392525469205,54.901347783008944,54.988770311325844,55.07619283964274,55.16361536795962,55.25103789627652,55.338460424593414,55.42588295291031,55.51330548122719,55.60072800954409,55.688150537860984,55.77557306617788,55.86299559449478,55.95041812281166,56.037840651128555,56.125263179445454,56.21268570776235,56.30010823607923,56.38753076439613,56.474953292713025,56.56237582102992,56.64979834934682,56.7372208776637,56.824643405980595,56.912065934297495,56.99948846261439,57.08691099093127,57.17433351924817,57.261756047565065,57.34917857588196,57.43660110419886,57.52402363251574,57.611446160832635,57.698868689149535,57.78629121746643,57.87371374578331,57.961136274100205,58.048558802417105,58.135981330734,58.22340385905088,58.31082638736778,58.398248915684675,58.48567144400157,58.57309397231847,58.66051650063535,58.747939028952246,58.835361557269145,58.92278408558604,59.01020661390292,59.09762914221982,59.185051670536716,59.27247419885361,59.35989672717051,59.44731925548739,59.534741783804286,59.622164312121186,59.70958684043808,59.79700936875496,59.88443189707186,59.971854425388756,60.05927695370565,60.14669948202255,60.23412201033943,60.321544538656326,60.40896706697322,60.49638959529012,60.583812123607004,60.671234651923896,60.758657180240796,60.84607970855769,60.933502236874574,61.020924765191474,61.108347293508366,61.19576982182526,61.28319235014216,61.370614878459044,61.45803740677594,61.545459935092836,61.63288246340973,61.720304991726614,61.807727520043514,61.89515004836041,61.9825725766773,62.06999510499419,62.157417633311084,62.24484016162798,62.332262689944876,62.41968521826176,62.507107746578654,62.594530274895554,62.68195280321245,62.76937533152934,62.85679785984623,62.944220388163124,63.03164291648002,63.11906544479691,63.2064879731138,63.293910501430695,63.38133302974759,63.46875555806449,63.55617808638137,63.643600614698265,63.731023143015165,63.81844567133206,63.90586819964895,63.99329072796584,64.08071325628273,64.16813578459963,64.25555831291652,64.34298084123341,64.4304033695503,64.51782589786721,64.60524842618409,64.69267095450098,64.78009348281789,64.86751601113478,64.95493853945166,65.04236106776857,65.12978359608546,65.21720612440235,65.30462865271924,65.39205118103614,65.47947370935303,65.56689623766992,65.65431876598682,65.74174129430371,65.8291638226206,65.9165863509375,66.00400887925439,66.09143140757128,66.17885393588817,66.26627646420506,66.35369899252196,66.44112152083885,66.52854404915574,66.61596657747263,66.70338910578954,66.79081163410643,66.87823416242331,66.96565669074022,67.05307921905711,67.140501747374,67.2279242756909,67.31534680400779,67.40276933232468,67.49019186064157,67.57761438895847,67.66503691727536,67.75245944559225,67.83988197390914,67.92730450222604,68.01472703054293,68.10214955885982,68.18957208717671,68.2769946154936,68.3644171438105,68.45183967212739,68.53926220044428,68.62668472876119,68.71410725707807,68.80152978539496,68.88895231371187,68.97637484202876,69.06379737034564,69.15121989866255,69.23864242697944,69.32606495529633,69.41348748361322,69.50091001193012,69.58833254024701,69.6757550685639,69.7631775968808,69.85060012519769,69.93802265351458,70.02544518183147,70.11286771014836,70.20029023846527,70.28771276678215,70.37513529509904,70.46255782341595,70.54998035173284,70.63740288004972,70.72482540836661,70.81224793668352,70.89967046500041,70.98709299331729,71.0745155216342,71.16193804995109,71.24936057826798,71.33678310658487,71.42420563490177,71.51162816321866,71.59905069153555,71.68647321985245,71.77389574816934,71.86131827648623,71.94874080480312,72.03616333312002,72.12358586143692,72.2110083897538,72.2984309180707,72.3858534463876,72.47327597470449,72.56069850302137,72.64812103133828,72.73554355965517,72.82296608797206,72.91038861628896,72.99781114460585,73.08523367292274,73.17265620123963,73.26007872955653,73.34750125787342,73.43492378619031,73.5223463145072,73.6097688428241,73.69719137114099,73.78461389945788,73.87203642777477,73.95945895609167,74.04688148440857,74.13430401272545,74.22172654104234,74.30914906935925,74.39657159767614,74.48399412599302,74.57141665430993,74.65883918262682,74.74626171094371,74.8336842392606,74.9211067675775,75.00852929589439,75.09595182421128,75.18337435252818,75.27079688084507,75.35821940916196,75.44564193747885,75.53306446579575,75.62048699411264,75.70790952242953,75.79533205074642,75.88275457906332,75.97017710738022,76.0575996356971,76.145022164014,76.2324446923309,76.3198672206478,76.40728974896467,76.49471227728158,76.58213480559847,76.66955733391536,76.75697986223226,76.84440239054915,76.93182491886604,77.01924744718293,77.10666997549983],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155],"y":[0,0.365216275453522,0.730432550907044,1.095648826360566,1.460865101814088,1.8260813772676097,2.191297652721132,2.5565139281746534,2.921730203628176,3.286946479081698,3.6521627545352193,4.017379029988741,4.382595305442264,4.747811580895786,5.113027856349307,5.478244131802829,5.843460407256352,6.208676682709873,6.573892958163396,6.939109233616917,7.304325509070439,7.669541784523962,8.034758059977483,8.399974335431004,8.765190610884527,9.130406886338049,9.495623161791572,9.860839437245092,10.226055712698614,10.591271988152137,10.956488263605658,11.321704539059182,11.686920814512703,12.052137089966225,12.417353365419746,12.782569640873268,13.147785916326791,13.513002191780313,13.878218467233834,14.243434742687356,14.608651018140877,14.9738672935944,15.339083569047924,15.704299844501444,16.069516119954965,16.43473239540849,16.79994867086201,17.16516494631553,17.530381221769055,17.895597497222575,18.260813772676098,18.62603004812962,18.991246323583145,19.356462599036664,19.721678874490184,20.086895149943707,20.452111425397227,20.81732770085075,21.182543976304274,21.547760251757794,21.912976527211317,22.27819280266484,22.643409078118363,23.008625353571883,23.373841629025407,23.73905790447893,24.10427417993245,24.46949045538597,24.834706730839493,25.199923006293016,25.565139281746536,25.93035555720006,26.295571832653582,26.660788108107102,27.026004383560625,27.39122065901415,27.75643693446767,28.12165320992119,28.48686948537471,28.85208576082823,29.217302036281755,29.582518311735278,29.9477345871888,30.312950862642325,30.678167138095848,31.043383413549364,31.408599689002887,31.77381596445641,32.13903223990993,32.50424851536346,32.86946479081698,33.2346810662705,33.59989734172402,33.96511361717754,34.33032989263106,34.69554616808459,35.06076244353811,35.42597871899163,35.79119499444515,36.15641126989867,36.521627545352196,36.886843820805716,37.25206009625924,37.61727637171276,37.98249264716629,38.3477089226198,38.71292519807333,39.07814147352685,39.44335774898037,39.808574024433895,40.173790299887415,40.53900657534094,40.904222850794454,41.26943912624798,41.6346554017015,41.99987167715503,42.36508795260855,42.730304228062074,43.09552050351559,43.460736778969114,43.825953054422634,44.19116932987615,44.55638560532968,44.9216018807832,45.28681815623673,45.65203443169024,46.017250707143766,46.382466982597286,46.74768325805081,47.11289953350433,47.47811580895786,47.84333208441138,48.2085483598649,48.57376463531842,48.93898091077194,49.304197186225466,49.669413461678985,50.03462973713251,50.39984601258603,50.76506228803955,51.13027856349307,51.4954948389466,51.86071111440012,52.22592738985364,52.591143665307165,52.95635994076068,53.321576216214204,53.686792491667724,54.05200876712125,54.41722504257477,54.7824413180283,55.14765759348182,55.51287386893534,55.87809014438886,56.24330641984238,56.6085226952959],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310],"y":[0,0.025501439300009445,0.05100287860001889,0.07650431790002835,0.10200575720003778,0.12750719650004722,0.1530086358000567,0.1785100751000661,0.20401151440007556,0.229512953700085,0.25501439300009443,0.2805158323001039,0.3060172716001134,0.3315187109001228,0.3570201502001322,0.3825215895001417,0.4080230288001511,0.4335244681001606,0.45902590740017,0.48452734670017944,0.5100287860001889,0.5355302253001984,0.5610316646002078,0.5865331039002173,0.6120345432002268,0.6375359825002361,0.6630374218002456,0.6885388611002551,0.7140403004002644,0.7395417397002739,0.7650431790002834,0.7905446183002928,0.8160460576003022,0.8415474969003117,0.8670489362003212,0.8925503755003306,0.91805181480034,0.9435532541003495,0.9690546934003589,0.9945561327003684,1.0200575720003777,1.0455590113003874,1.071060450600397,1.0965618899004062,1.1220633292004156,1.147564768500425,1.1730662078004346,1.198567647100444,1.2240690864004535,1.2495705257004628,1.2750719650004723,1.3005734043004817,1.3260748436004912,1.3515762829005007,1.3770777222005102,1.4025791615005196,1.428080600800529,1.4535820401005384,1.4790834794005479,1.5045849187005573,1.5300863580005668,1.5555877973005763,1.5810892366005855,1.606590675900595,1.6320921152006045,1.657593554500614,1.6830949938006234,1.708596433100633,1.7340978724006424,1.7595993117006516,1.7851007510006611,1.8106021903006706,1.83610362960068,1.8616050689006896,1.887106508200699,1.9126079475007083,1.9381093868007178,1.9636108261007272,1.9891122654007367,2.014613704700746,2.0401151440007554,2.065616583300765,2.091118022600775,2.116619461900784,2.142120901200794,2.167622340500803,2.1931237798008123,2.2186252191008218,2.2441266584008313,2.2696280977008407,2.29512953700085,2.3206309763008592,2.346132415600869,2.371633854900878,2.397135294200888,2.422636733500897,2.448138172800907,2.473639612100916,2.4991410514009256,2.524642490700935,2.5501439300009445,2.575645369300954,2.6011468086009635,2.626648247900973,2.6521496872009824,2.6776511265009915,2.7031525658010014,2.7286540051010104,2.7541554444010203,2.7796568837010294,2.8051583230010393,2.8306597623010483,2.856161201601058,2.8816626409010673,2.9071640802010768,2.9326655195010862,2.9581669588010957,2.9836683981011047,3.0091698374011147,3.0346712767011237,3.0601727160011336,3.085674155301143,3.1111755946011526,3.136677033901162,3.162178473201171,3.187679912501181,3.21318135180119,3.2386827911012,3.264184230401209,3.289685669701219,3.315187109001228,3.3406885483012374,3.366189987601247,3.3916914269012564,3.417192866201266,3.4426943055012753,3.468195744801285,3.4936971841012943,3.5191986234013033,3.544700062701313,3.5702015020013222,3.595702941301332,3.621204380601341,3.6467058199013507,3.67220725920136,3.6977086985013696,3.723210137801379,3.7487115771013886,3.774213016401398,3.7997144557014075,3.8252158950014166,3.8507173343014265,3.8762187736014355,3.9017202129014454,3.9272216522014545,3.9527230915014644,3.9782245308014734,4.003725970101483,4.029227409401492,4.054728848701502,4.080230288001511,4.105731727301521,4.13123316660153,4.15673460590154,4.18223604520155,4.207737484501558,4.233238923801568,4.258740363101578,4.284241802401588,4.309743241701596,4.335244681001606,4.360746120301616,4.386247559601625,4.411748998901634,4.4372504382016436,4.4627518775016535,4.4882533168016625,4.5137547561016715,4.5392561954016815,4.5647576347016905,4.5902590740017,4.61576051330171,4.6412619526017185,4.666763391901728,4.692264831201738,4.717766270501748,4.743267709801756,4.768769149101766,4.794270588401776,4.819772027701785,4.845273467001794,4.870774906301804,4.896276345601814,4.921777784901823,4.947279224201832,4.972780663501842,4.998282102801851,5.023783542101861,5.04928498140187,5.07478642070188,5.100287860001889,5.125789299301899,5.151290738601908,5.176792177901917,5.202293617201927,5.227795056501937,5.253296495801946,5.278797935101955,5.304299374401965,5.329800813701975,5.355302253001983,5.380803692301993,5.406305131602003,5.431806570902013,5.457308010202021,5.482809449502031,5.508310888802041,5.53381232810205,5.559313767402059,5.584815206702069,5.610316646002079,5.635818085302088,5.661319524602097,5.686820963902107,5.712322403202116,5.7378238425021255,5.7633252818021345,5.788826721102144,5.8143281604021535,5.839829599702163,5.8653310390021725,5.8908324783021815,5.916333917602191,5.941835356902201,5.9673367962022095,5.992838235502219,6.018339674802229,6.043841114102239,6.069342553402247,6.094843992702257,6.120345432002267,6.145846871302276,6.171348310602286,6.196849749902295,6.222351189202305,6.247852628502314,6.273354067802324,6.298855507102333,6.324356946402342,6.349858385702352,6.375359825002362,6.400861264302371,6.42636270360238,6.45186414290239,6.4773655822024,6.502867021502408,6.528368460802418,6.553869900102428,6.579371339402438,6.604872778702446,6.630374218002456,6.655875657302466,6.681377096602475,6.706878535902484,6.732379975202494,6.757881414502504,6.783382853802513,6.808884293102522,6.834385732402532,6.859887171702541,6.885388611002551,6.91089005030256,6.93639148960257,6.961892928902579,6.9873943682025885,7.012895807502598,7.038397246802607,7.0638986861026165,7.089400125402626,7.114901564702635,7.1404030040026445,7.165904443302654,7.191405882602664,7.2169073219026725,7.242408761202682,7.267910200502692,7.293411639802701,7.31891307910271,7.34441451840272,7.36991595770273,7.395417397002739,7.420918836302748,7.446420275602758,7.471921714902767,7.497423154202777,7.522924593502786,7.548426032802796,7.573927472102805,7.599428911402815,7.624930350702825,7.650431790002833,7.675933229302843,7.701434668602853,7.726936107902863,7.752437547202871,7.777938986502881,7.803440425802891,7.8289418651029,7.854443304402909,7.879944743702919,7.905446183002929,7.930947622302938,7.956449061602947,7.981950500902957,8.007451940202966,8.032953379502976,8.058454818802984,8.083956258102994,8.109457697403004,8.134959136703014,8.160460576003022,8.185962015303032,8.211463454603042,8.236964893903052,8.26246633320306,8.28796777250307,8.31346921180308,8.33897065110309,8.3644720904031,8.389973529703108,8.415474969003116,8.440976408303127,8.466477847603135,8.491979286903145,8.517480726203155,8.542982165503163,8.568483604803175,8.593985044103183,8.619486483403191,8.644987922703203,8.670489362003211,8.695990801303221,8.721492240603231,8.74699367990324,8.77249511920325,8.79799655850326,8.823497997803267,8.848999437103277,8.874500876403287,8.900002315703297,8.925503755003307,8.951005194303315,8.976506633603325,9.002008072903335,9.027509512203343,9.053010951503353,9.078512390803363,9.104013830103373,9.129515269403381,9.155016708703391,9.1805181480034,9.206019587303409,9.23152102660342,9.257022465903429,9.282523905203437,9.308025344503449,9.333526783803457,9.359028223103467,9.384529662403477,9.410031101703485,9.435532541003496,9.461033980303505,9.486535419603513,9.512036858903524,9.537538298203533,9.56303973750354,9.588541176803552,9.61404261610356,9.63954405540357,9.66504549470358,9.690546934003589,9.7160483733036,9.741549812603608,9.767051251903617,9.792552691203628,9.818054130503636,9.843555569803646,9.869057009103656,9.894558448403664,9.920059887703674,9.945561327003684,9.971062766303692,9.996564205603702,10.022065644903712,10.047567084203722,10.073068523503732,10.09856996280374,10.12407140210375,10.14957284140376,10.175074280703768,10.200575720003778,10.226077159303788,10.251578598603798,10.277080037903806,10.302581477203816,10.328082916503826,10.353584355803834,10.379085795103844,10.404587234403854,10.430088673703862,10.455590113003874,10.481091552303882,10.506592991603892,10.532094430903902,10.55759587020391,10.58309730950392,10.60859874880393,10.634100188103938,10.65960162740395,10.685103066703958,10.710604506003966,10.736105945303978,10.761607384603986,10.787108823903996,10.812610263204006,10.838111702504014,10.863613141804025,10.889114581104034,10.914616020404042,10.940117459704053,10.965618899004062,10.991120338304071,11.016621777604081,11.04212321690409,11.0676246562041,11.09312609550411,11.118627534804117,11.144128974104127,11.169630413404137,11.195131852704147,11.220633292004157,11.246134731304165,11.271636170604175,11.297137609904185,11.322639049204193,11.348140488504203,11.373641927804213,11.399143367104223,11.424644806404231,11.450146245704241,11.475647685004251,11.50114912430426,11.526650563604269,11.552152002904279,11.577653442204287,11.603154881504299,11.628656320804307,11.654157760104317,11.679659199404327,11.705160638704335,11.730662078004345,11.756163517304355,11.781664956604363,11.807166395904375,11.832667835204383,11.858169274504391,11.883670713804403,11.90917215310441,11.934673592404419,11.96017503170443,11.985676471004439,12.01117791030445,12.036679349604459,12.062180788904467,12.087682228204478,12.113183667504487,12.138685106804495,12.164186546104506,12.189687985404515,12.215189424704525,12.240690864004534,12.266192303304543,12.291693742604552,12.317195181904562,12.342696621204572,12.368198060504582,12.39369949980459,12.4192009391046,12.44470237840461,12.470203817704618,12.495705257004628,12.521206696304638,12.546708135604648,12.572209574904656,12.597711014204666,12.623212453504676,12.648713892804684,12.674215332104694,12.699716771404704,12.725218210704712,12.750719650004724,12.776221089304732,12.801722528604742,12.827223967904752,12.85272540720476,12.87822684650477,12.90372828580478,12.929229725104788,12.9547311644048,12.980232603704808,13.005734043004816,13.031235482304828,13.056736921604836,13.082238360904844,13.107739800204856,13.133241239504864,13.158742678804876,13.184244118104884,13.209745557404892,13.235246996704904,13.260748436004912,13.28624987530492,13.311751314604932,13.33725275390494,13.36275419320495,13.38825563250496,13.413757071804968,13.439258511104978,13.464759950404988,13.490261389704996,13.515762829005007,13.541264268305016,13.566765707605025,13.592267146905035,13.617768586205043,13.643270025505053,13.668771464805063,13.694272904105073,13.719774343405081,13.745275782705091,13.770777222005101,13.79627866130511,13.82178010060512,13.84728153990513,13.87278297920514,13.898284418505149,13.923785857805157,13.949287297105167,13.974788736405177,14.000290175705185,14.025791615005195,14.051293054305205,14.076794493605213,14.102295932905225,14.127797372205233,14.153298811505241,14.178800250805253,14.204301690105261,14.22980312940527,14.25530456870528,14.280806008005289,14.3063074473053,14.331808886605309,14.357310325905317,14.382811765205329,14.408313204505337,14.433814643805345,14.459316083105357,14.484817522405365,14.510318961705375,14.535820401005385,14.561321840305393,14.586823279605403,14.612324718905413,14.63782615820542,14.663327597505432,14.68882903680544,14.71433047610545,14.73983191540546,14.765333354705469,14.790834794005479,14.816336233305488,14.841837672605497,14.867339111905506,14.892840551205516,14.918341990505526,14.943843429805534,14.969344869105544,14.994846308405554,15.020347747705564,15.045849187005572,15.071350626305582,15.096852065605592,15.122353504905602,15.14785494420561,15.17335638350562,15.19885782280563,15.224359262105638,15.24986070140565,15.275362140705658,15.300863580005666,15.326365019305678,15.351866458605686,15.377367897905694,15.402869337205706,15.428370776505714,15.453872215805726,15.479373655105734,15.504875094405742,15.530376533705754,15.555877973005762,15.58137941230577,15.606880851605782,15.63238229090579,15.6578837302058,15.68338516950581,15.708886608805818,15.734388048105828,15.759889487405838,15.785390926705846,15.810892366005858,15.836393805305866,15.861895244605876,15.887396683905886,15.912898123205894,15.938399562505904,15.963901001805914,15.989402441105922,16.01490388040593,16.04040531970594,16.06590675900595,16.09140819830596,16.116909637605968,16.14241107690598,16.167912516205988,16.193413955505996,16.218915394806007,16.244416834106016,16.269918273406027,16.295419712706035,16.320921152006044,16.346422591306055,16.371924030606063,16.39742546990607,16.422926909206083,16.44842834850609,16.473929787806103,16.49943122710611,16.52493266640612,16.55043410570613,16.57593554500614,16.601436984306147,16.62693842360616,16.652439862906167,16.67794130220618,16.703442741506187,16.7289441808062,16.754445620106203,16.779947059406215,16.805448498706227,16.83094993800623,16.856451377306243,16.881952816606255,16.907454255906263,16.93295569520627,16.958457134506283,16.98395857380629,17.0094600131063,17.03496145240631,17.060462891706322,17.085964331006327,17.11146577030634,17.13696720960635,17.162468648906355,17.187970088206367,17.21347152750638,17.238972966806383,17.264474406106395,17.289975845406406,17.315477284706414,17.340978724006423,17.366480163306434,17.391981602606442,17.41748304190645,17.442984481206462,17.46848592050647,17.49398735980648,17.51948879910649,17.5449902384065,17.570491677706507,17.59599311700652,17.621494556306526,17.646995995606535,17.672497434906546,17.697998874206554,17.723500313506566,17.749001752806574,17.774503192106582,17.800004631406594,17.825506070706602,17.851007510006614,17.876508949306622,17.90201038860663,17.927511827906642,17.95301326720665,17.978514706506658,18.00401614580667,18.029517585106678,18.055019024406686,18.080520463706698,18.106021903006706,18.131523342306718,18.157024781606726,18.182526220906734,18.208027660206746,18.233529099506754,18.259030538806762,18.284531978106774,18.310033417406782,18.33553485670679,18.3610362960068,18.38653773530681,18.412039174606818,18.43754061390683,18.46304205320684,18.488543492506846,18.514044931806858,18.53954637110687,18.565047810406874,18.590549249706886,18.616050689006897,18.641552128306905,18.667053567606914,18.692555006906925,18.718056446206933,18.74355788550694,18.769059324806953,18.79456076410696,18.82006220340697,18.84556364270698,18.871065082006993,18.896566521306998,18.92206796060701,18.94756939990702,18.973070839207026,18.998572278507037,19.02407371780705,19.049575157107054,19.075076596407065,19.100578035707077,19.12607947500708,19.151580914307093,19.177082353607105,19.20258379290711,19.22808523220712,19.253586671507133,19.27908811080714,19.30458955010715,19.33009098940716,19.355592428707173,19.381093868007177,19.40659530730719,19.4320967466072,19.457598185907205,19.483099625207217,19.50860106450723,19.534102503807233,19.559603943107245,19.585105382407257,19.61060682170726,19.636108261007273,19.661609700307284,19.687111139607293,19.7126125789073,19.738114018207312,19.76361545750732,19.78911689680733,19.81461833610734,19.84011977540735,19.865621214707357,19.89112265400737,19.916624093307377,19.942125532607385,19.967626971907396,19.993128411207405,20.018629850507416,20.044131289807424,20.069632729107433,20.095134168407444,20.120635607707452,20.146137047007464,20.171638486307472,20.19713992560748,20.222641364907492,20.2481428042075,20.27364424350751,20.29914568280752,20.324647122107528,20.350148561407536,20.375650000707548,20.401151440007556,20.426652879307568,20.452154318607576,20.477655757907584,20.503157197207596,20.528658636507604,20.554160075807612,20.579661515107624,20.605162954407632,20.63066439370764,20.656165833007652,20.68166727230766,20.707168711607668,20.73267015090768,20.758171590207688,20.783673029507696,20.809174468807708,20.83467590810772,20.860177347407724,20.885678786707736,20.911180226007748,20.936681665307756,20.962183104607764,20.987684543907775,21.013185983207784,21.038687422507792,21.064188861807803,21.08969030110781,21.11519174040782,21.14069317970783,21.16619461900784,21.191696058307848,21.21719749760786,21.24269893690787,21.268200376207876,21.293701815507887,21.3192032548079,21.344704694107904,21.370206133407915,21.395707572707927,21.42120901200793,21.446710451307943,21.472211890607955,21.49771332990796,21.52321476920797,21.548716208507983,21.57421764780799,21.599719087108,21.62522052640801,21.650721965708023,21.676223405008027,21.70172484430804,21.72722628360805,21.752727722908055,21.778229162208067,21.80373060150808,21.829232040808083,21.854733480108095,21.880234919408107,21.90573635870811,21.931237798008123,21.956739237308135,21.982240676608143,22.00774211590815,22.033243555208163,22.05874499450817,22.08424643380818,22.10974787310819,22.1352493124082,22.160750751708207,22.18625219100822,22.211753630308227,22.237255069608235,22.262756508908247,22.288257948208255,22.313759387508263,22.339260826808275,22.364762266108283,22.390263705408294,22.415765144708303,22.441266584008314,22.466768023308322,22.49226946260833,22.517770901908342,22.54327234120835,22.56877378050836,22.59427521980837,22.61977665910838,22.645278098408387,22.6707795377084,22.696280977008406,22.721782416308415,22.747283855608426,22.772785294908434,22.798286734208446,22.823788173508454,22.849289612808462,22.874791052108474,22.900292491408482,22.92579393070849,22.951295370008502,22.97679680930851,23.00229824860852,23.02779968790853,23.053301127208538,23.078802566508546,23.104304005808558,23.12980544510857,23.155306884408574,23.180808323708586,23.206309763008598,23.231811202308606,23.257312641608614,23.282814080908626,23.308315520208634,23.333816959508642,23.359318398808654,23.384819838108662,23.41032127740867,23.43582271670868,23.46132415600869,23.486825595308698,23.51232703460871,23.53782847390872,23.563329913208726,23.588831352508738,23.61433279180875,23.639834231108754,23.665335670408766,23.690837109708777,23.716338549008782,23.741839988308794,23.767341427608805,23.79284286690881,23.81834430620882,23.843845745508833,23.869347184808838,23.89484862410885,23.92035006340886,23.945851502708873,23.971352942008878,23.99685438130889,24.0223558206089,24.047857259908906,24.073358699208917,24.09886013850893,24.124361577808934,24.149863017108945,24.175364456408957,24.20086589570896,24.226367335008973,24.251868774308985,24.27737021360899,24.302871652909,24.328373092209013,24.35387453150902,24.37937597080903,24.40487741010904,24.43037884940905,24.455880288709057,24.48138172800907,24.506883167309077,24.532384606609085,24.557886045909097,24.583387485209105,24.608888924509113,24.634390363809125,24.659891803109137,24.685393242409145,24.710894681709153,24.736396121009165,24.761897560309173,24.78739899960918,24.812900438909193,24.8384018782092,24.86390331750921,24.88940475680922,24.91490619610923,24.940407635409237,24.96590907470925,24.991410514009257,25.016911953309265,25.042413392609276,25.067914831909285,25.093416271209296,25.118917710509304,25.144419149809313,25.169920589109324,25.195422028409332,25.22092346770934,25.246424907009352,25.27192634630936,25.29742778560937,25.32292922490938,25.34843066420939,25.373932103509397,25.39943354280941,25.424934982109416,25.450436421409425,25.475937860709436,25.501439300009448,25.526940739309456,25.552442178609464,25.577943617909476,25.603445057209484,25.628946496509492,25.654447935809504,25.679949375109512,25.70545081440952,25.730952253709532,25.75645369300954,25.781955132309548,25.80745657160956,25.83295801090957,25.858459450209576,25.883960889509588,25.9094623288096,25.934963768109604,25.960465207409616,25.985966646709628,26.011468086009632,26.036969525309644,26.062470964609656,26.08797240390966,26.113473843209672,26.138975282509683,26.164476721809688,26.1899781611097,26.21547960040971,26.240981039709723,26.266482479009728,26.29198391830974,26.31748535760975,26.342986796909756,26.368488236209767,26.39398967550978,26.419491114809784,26.444992554109795,26.470493993409807,26.49599543270981,26.521496872009823,26.546998311309835,26.57249975060984,26.59800118990985,26.623502629209863,26.64900406850987,26.67450550780988,26.70000694710989,26.7255083864099,26.751009825709907,26.77651126500992,26.802012704309927,26.827514143609935,26.853015582909947,26.878517022209955,26.904018461509963,26.929519900809975,26.955021340109987,26.98052277940999,27.006024218710003,27.031525658010015,27.057027097310023,27.08252853661003,27.108029975910043,27.13353141521005,27.15903285451006,27.18453429381007,27.21003573311008,27.235537172410087,27.2610386117101,27.286540051010107,27.312041490310115,27.337542929610127,27.363044368910135,27.388545808210147,27.414047247510155,27.439548686810163,27.465050126110174,27.490551565410183,27.51605300471019,27.541554444010202,27.56705588331021,27.59255732261022,27.61805876191023,27.64356020121024,27.669061640510247,27.69456307981026,27.720064519110267,27.74556595841028,27.771067397710286,27.796568837010298,27.822070276310306,27.847571715610314,27.873073154910326,27.898574594210334,27.924076033510342,27.949577472810354,27.975078912110362,28.00058035141037,28.026081790710382,28.05158323001039,28.0770846693104,28.10258610861041,28.128087547910418,28.153588987210426,28.179090426510438,28.20459186581045,28.230093305110454,28.255594744410466,28.281096183710478,28.306597623010482,28.332099062310494,28.357600501610506,28.38310194091051,28.408603380210522,28.434104819510534,28.45960625881054,28.48510769811055,28.51060913741056,28.53611057671057,28.561612016010578,28.58711345531059,28.6126148946106,28.638116333910606,28.663617773210618,28.68911921251063,28.714620651810634,28.740122091110646,28.765623530410657,28.791124969710662,28.816626409010674,28.842127848310685,28.86762928761069,28.8931307269107,28.918632166210713,28.94413360551072,28.96963504481073,28.99513648411074,29.02063792341075,29.046139362710758,29.07164080201077,29.097142241310777,29.122643680610786,29.148145119910797,29.173646559210805,29.199147998510814,29.224649437810825,29.250150877110837,29.27565231641084,29.301153755710853,29.326655195010865,29.352156634310873,29.37765807361088,29.403159512910893,29.4286609522109,29.45416239151091,29.47966383081092,29.50516527011093,29.530666709410937,29.55616814871095,29.581669588010957,29.607171027310965,29.632672466610977,29.658173905910985,29.683675345210993,29.709176784511005,29.734678223811013,29.760179663111025,29.785681102411033,29.81118254171104,29.836683981011053,29.86218542031106,29.88768685961107,29.91318829891108,29.93868973821109,29.964191177511097,29.98969261681111,30.015194056111117,30.04069549541113,30.066196934711137,30.091698374011145,30.117199813311156,30.142701252611165,30.168202691911176,30.193704131211184,30.219205570511193,30.244707009811204,30.270208449111212,30.29570988841122,30.321211327711232,30.34671276701124,30.37221420631125,30.39771564561126,30.42321708491127,30.448718524211277,30.47421996351129,30.4997214028113,30.525222842111305,30.550724281411316,30.576225720711328,30.601727160011333,30.627228599311344,30.652730038611356,30.67823147791136,30.703732917211372,30.729234356511384,30.75473579581139,30.7802372351114,30.805738674411412,30.83124011371142,30.856741553011428,30.88224299231144,30.90774443161145,30.933245870911456,30.958747310211468,30.98424874951148,31.009750188811484,31.035251628111496,31.060753067411508,31.086254506711512,31.111755946011524,31.137257385311536,31.16275882461154,31.188260263911552,31.213761703211564,31.239263142511568,31.26476458181158,31.29026602111159,31.3157674604116,31.341268899711608,31.36677033901162,31.392271778311628,31.417773217611636,31.443274656911647,31.468776096211656,31.494277535511664,31.519778974811675,31.545280414111687,31.57078185341169,31.596283292711703,31.621784732011715,31.64728617131172,31.67278761061173,31.698289049911743,31.72379048921175,31.74929192851176,31.77479336781177,31.80029480711178,31.825796246411787,31.8512976857118,31.876799125011807,31.902300564311815,31.927802003611827,31.953303442911835,31.978804882211843,32.004306321511855,32.02980776081186,32.05530920011188,32.08081063941188,32.106312078711895,32.1318135180119,32.15731495731191,32.18281639661192,32.208317835911934,32.233819275211935,32.25932071451195,32.28482215381196,32.31032359311197,32.335825032411975,32.36132647171199,32.38682791101199,32.41232935031201,32.437830789612015,32.46333222891202,32.48883366821203,32.514335107512046,32.539836546812055,32.56533798611206,32.59083942541207,32.616340864712086,32.64184230401209,32.6673437433121,32.69284518261211,32.71834662191212,32.74384806121213,32.76934950051214,32.79485093981214,32.82035237911216,32.845853818412166,32.871355257712175,32.89685669701218,32.9223581363122,32.947859575612206,32.973361014912214,32.99886245421222,33.02436389351223,33.04986533281224,33.075366772112254,33.10086821141226,33.12636965071227,33.15187109001228,33.17737252931229,33.202873968612295,33.22837540791231,33.25387684721232,33.279378286512326,33.304879725812334,33.33038116511234,33.35588260441236,33.381384043712366,33.406885483012374],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Momentum Rule 2","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],"y":[0,0.7475427946707556,1.4950855893415111,2.2426283840122667,2.9901711786830223,3.737713973353778,4.485256768024533,5.2327995626952895,5.980342357366045,6.7278851520368,7.475427946707556,8.222970741378312,8.970513536049067,9.718056330719822,10.465599125390579,11.213141920061334,11.96068471473209,12.708227509402846,13.4557703040736,14.203313098744355,14.950855893415111,15.698398688085867,16.445941482756623,17.19348427742738,17.941027072098134,18.68856986676889,19.436112661439644,20.1836554561104,20.931198250781158,21.67874104545191,22.426283840122668,23.173826634793425,23.92136942946418,24.668912224134935,25.416455018805692,26.163997813476445,26.9115406081472,27.659083402817956,28.40662619748871,29.154168992159466,29.901711786830223,30.649254581500976,31.396797376171733,32.14434017084249,32.89188296551325,33.639425760184004,34.38696855485476,35.13451134952551,35.88205414419627],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"AI Rule 8","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46],"y":[0,0.1949453696434838,0.3898907392869676,0.5848361089304513,0.7797814785739352,0.974726848217419,1.1696722178609027,1.3646175875043867,1.5595629571478704,1.7545083267913542,1.949453696434838,2.144399066078322,2.3393444357218054,2.534289805365289,2.7292351750087733,2.924180544652257,3.119125914295741,3.3140712839392243,3.5090166535827083,3.7039620232261923,3.898907392869676,4.093852762513159,4.288798132156644,4.483743501800127,4.678688871443611,4.873634241087094,5.068579610730578,5.263524980374063,5.458470350017547,5.65341571966103,5.848361089304514,6.043306458947997,6.238251828591482,6.433197198234965,6.628142567878449,6.823087937521933,7.018033307165417,7.2129786768089,7.407924046452385,7.602869416095868,7.797814785739352,7.992760155382835,8.187705525026319,8.382650894669803,8.577596264313287,8.77254163395677,8.967487003600255],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">77.11%</td>
                <td>65.4%</td>
                <td>882</td>
                <td>1.11</td>
                <td>0.00</td>
                <td>31.53%</td>
                <td>80.5</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">56.61%</td>
                <td>71.0%</td>
                <td>155</td>
                <td>1.56</td>
                <td>0.00</td>
                <td>15.25%</td>
                <td>73.9</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">33.41%</td>
                <td>63.7%</td>
                <td>1310</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>34.51%</td>
                <td>62.5</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                <td>UNKNOWN</td>
                <td class="positive">35.88%</td>
                <td>81.2%</td>
                <td>48</td>
                <td>2.69</td>
                <td>0.00</td>
                <td>2.38%</td>
                <td>53.1</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>AI Rule 8: Momentum Divergence Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">8.97%</td>
                <td>65.2%</td>
                <td>46</td>
                <td>1.25</td>
                <td>0.00</td>
                <td>12.18%</td>
                <td>37.0</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">5.09%</td>
                <td>73.7%</td>
                <td>19</td>
                <td>1.41</td>
                <td>0.00</td>
                <td>6.01%</td>
                <td>29.8</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">3.62%</td>
                <td>75.0%</td>
                <td>12</td>
                <td>1.45</td>
                <td>0.00</td>
                <td>5.30%</td>
                <td>27.5</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 812,700<br>
            • Backtest Range: 300 to 813,000<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
