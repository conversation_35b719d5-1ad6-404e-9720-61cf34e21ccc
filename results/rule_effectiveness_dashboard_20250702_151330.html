
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 15:13:30 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">-4.73%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">4,151</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">61.7%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.93</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>30.82%</strong></td>
                    <td>73.9%</td>
                    <td>142</td>
                    <td>1.63</td>
                    <td class="negative">6.77%</td>
                    <td class="positive"><strong>0.7467</strong></td>
                    <td class="negative">+0.89% / -1.50%</td>
                    <td>2h20m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>14.77%</strong></td>
                    <td>62.2%</td>
                    <td>1256</td>
                    <td>1.03</td>
                    <td class="negative">33.87%</td>
                    <td class="positive"><strong>0.7771</strong></td>
                    <td class="negative">+0.88% / -1.40%</td>
                    <td>3h1m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>5.58%</strong></td>
                    <td>69.0%</td>
                    <td>42</td>
                    <td>1.31</td>
                    <td class="neutral">2.99%</td>
                    <td class="positive"><strong>0.5221</strong></td>
                    <td class="negative">+0.91% / -1.44%</td>
                    <td>3h13m<br><small>(13.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>4.33%</strong></td>
                    <td>64.9%</td>
                    <td>37</td>
                    <td>1.26</td>
                    <td class="neutral">4.87%</td>
                    <td class="positive"><strong>0.4954</strong></td>
                    <td class="negative">+0.98% / -1.44%</td>
                    <td>4h19m<br><small>(1.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>3.12%</strong></td>
                    <td>83.3%</td>
                    <td>6</td>
                    <td>3.71</td>
                    <td class="positive">1.12%</td>
                    <td class="positive"><strong>0.5756</strong></td>
                    <td class="negative">+0.99% / -1.38%</td>
                    <td>1h2m<br><small>(3.0m - 2h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>1.03%</strong></td>
                    <td>75.0%</td>
                    <td>4</td>
                    <td>1.72</td>
                    <td class="positive">1.40%</td>
                    <td class="positive"><strong>0.3372</strong></td>
                    <td class="negative">+0.85% / -1.46%</td>
                    <td>7h41m<br><small>(36.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>0.76%</strong></td>
                    <td>62.5%</td>
                    <td>32</td>
                    <td>1.05</td>
                    <td class="neutral">4.30%</td>
                    <td class="positive"><strong>0.4276</strong></td>
                    <td class="negative">+0.87% / -1.36%</td>
                    <td>2h19m<br><small>(1.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>0.70%</strong></td>
                    <td>60.0%</td>
                    <td>55</td>
                    <td>1.03</td>
                    <td class="negative">5.97%</td>
                    <td class="positive"><strong>0.4725</strong></td>
                    <td class="negative">+0.91% / -1.37%</td>
                    <td>3h40m<br><small>(1.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>0.41%</strong></td>
                    <td>60.0%</td>
                    <td>15</td>
                    <td>1.06</td>
                    <td class="neutral">2.74%</td>
                    <td class="positive"><strong>0.3474</strong></td>
                    <td class="negative">+0.88% / -1.40%</td>
                    <td>2h20m<br><small>(15.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-0.10%</strong></td>
                    <td>61.5%</td>
                    <td>13</td>
                    <td>0.98</td>
                    <td class="neutral">3.22%</td>
                    <td class="positive"><strong>0.3100</strong></td>
                    <td class="negative">+0.83% / -1.45%</td>
                    <td>2h0m<br><small>(13.0m - 5h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="negative"><strong>-1.94%</strong></td>
                    <td>50.0%</td>
                    <td>4</td>
                    <td>0.41</td>
                    <td class="positive">1.59%</td>
                    <td class="negative"><strong>-0.0474</strong></td>
                    <td class="negative">+0.86% / -1.87%</td>
                    <td>3.8m<br><small>(1.0m - 9.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-2.71%</strong></td>
                    <td>61.4%</td>
                    <td>132</td>
                    <td>0.96</td>
                    <td class="negative">12.59%</td>
                    <td class="positive"><strong>0.5258</strong></td>
                    <td class="negative">+0.89% / -1.40%</td>
                    <td>2h37m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-5.35%</strong></td>
                    <td>42.9%</td>
                    <td>14</td>
                    <td>0.45</td>
                    <td class="negative">6.48%</td>
                    <td class="positive"><strong>0.1300</strong></td>
                    <td class="negative">+0.96% / -1.33%</td>
                    <td>2h1m<br><small>(15.0m - 7h10m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-7.75%</strong></td>
                    <td>59.2%</td>
                    <td>125</td>
                    <td>0.87</td>
                    <td class="negative">13.81%</td>
                    <td class="positive"><strong>0.4812</strong></td>
                    <td class="negative">+0.85% / -1.37%</td>
                    <td>2h18m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-9.71%</strong></td>
                    <td>61.3%</td>
                    <td>1034</td>
                    <td>0.98</td>
                    <td class="negative">30.36%</td>
                    <td class="positive"><strong>0.6896</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h53m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-38.69%</strong></td>
                    <td>60.4%</td>
                    <td>1240</td>
                    <td>0.94</td>
                    <td class="negative">47.29%</td>
                    <td class="positive"><strong>0.5989</strong></td>
                    <td class="negative">+0.86% / -1.39%</td>
                    <td>3h31m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>83.3%</strong></td>
                    <td class="positive">3.12%</td>
                    <td>6</td>
                    <td>3.71</td>
                    <td class="positive">1.12%</td>
                    <td class="positive"><strong>0.5756</strong></td>
                    <td class="negative">+0.99% / -1.38%</td>
                    <td>1h2m<br><small>(3.0m - 2h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">1.03%</td>
                    <td>4</td>
                    <td>1.72</td>
                    <td class="positive">1.40%</td>
                    <td class="positive"><strong>0.3372</strong></td>
                    <td class="negative">+0.85% / -1.46%</td>
                    <td>7h41m<br><small>(36.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>73.9%</strong></td>
                    <td class="positive">30.82%</td>
                    <td>142</td>
                    <td>1.63</td>
                    <td class="negative">6.77%</td>
                    <td class="positive"><strong>0.7467</strong></td>
                    <td class="negative">+0.89% / -1.50%</td>
                    <td>2h20m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>69.0%</strong></td>
                    <td class="positive">5.58%</td>
                    <td>42</td>
                    <td>1.31</td>
                    <td class="neutral">2.99%</td>
                    <td class="positive"><strong>0.5221</strong></td>
                    <td class="negative">+0.91% / -1.44%</td>
                    <td>3h13m<br><small>(13.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>64.9%</strong></td>
                    <td class="positive">4.33%</td>
                    <td>37</td>
                    <td>1.26</td>
                    <td class="neutral">4.87%</td>
                    <td class="positive"><strong>0.4954</strong></td>
                    <td class="negative">+0.98% / -1.44%</td>
                    <td>4h19m<br><small>(1.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>62.5%</strong></td>
                    <td class="positive">0.76%</td>
                    <td>32</td>
                    <td>1.05</td>
                    <td class="neutral">4.30%</td>
                    <td class="positive"><strong>0.4276</strong></td>
                    <td class="negative">+0.87% / -1.36%</td>
                    <td>2h19m<br><small>(1.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>62.2%</strong></td>
                    <td class="positive">14.77%</td>
                    <td>1256</td>
                    <td>1.03</td>
                    <td class="negative">33.87%</td>
                    <td class="positive"><strong>0.7771</strong></td>
                    <td class="negative">+0.88% / -1.40%</td>
                    <td>3h1m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>61.5%</strong></td>
                    <td class="negative">-0.10%</td>
                    <td>13</td>
                    <td>0.98</td>
                    <td class="neutral">3.22%</td>
                    <td class="positive"><strong>0.3100</strong></td>
                    <td class="negative">+0.83% / -1.45%</td>
                    <td>2h0m<br><small>(13.0m - 5h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>61.4%</strong></td>
                    <td class="negative">-2.71%</td>
                    <td>132</td>
                    <td>0.96</td>
                    <td class="negative">12.59%</td>
                    <td class="positive"><strong>0.5258</strong></td>
                    <td class="negative">+0.89% / -1.40%</td>
                    <td>2h37m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>61.3%</strong></td>
                    <td class="negative">-9.71%</td>
                    <td>1034</td>
                    <td>0.98</td>
                    <td class="negative">30.36%</td>
                    <td class="positive"><strong>0.6896</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h53m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>60.4%</strong></td>
                    <td class="negative">-38.69%</td>
                    <td>1240</td>
                    <td>0.94</td>
                    <td class="negative">47.29%</td>
                    <td class="positive"><strong>0.5989</strong></td>
                    <td class="negative">+0.86% / -1.39%</td>
                    <td>3h31m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="positive">0.70%</td>
                    <td>55</td>
                    <td>1.03</td>
                    <td class="negative">5.97%</td>
                    <td class="positive"><strong>0.4725</strong></td>
                    <td class="negative">+0.91% / -1.37%</td>
                    <td>3h40m<br><small>(1.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="positive">0.41%</td>
                    <td>15</td>
                    <td>1.06</td>
                    <td class="neutral">2.74%</td>
                    <td class="positive"><strong>0.3474</strong></td>
                    <td class="negative">+0.88% / -1.40%</td>
                    <td>2h20m<br><small>(15.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>59.2%</strong></td>
                    <td class="negative">-7.75%</td>
                    <td>125</td>
                    <td>0.87</td>
                    <td class="negative">13.81%</td>
                    <td class="positive"><strong>0.4812</strong></td>
                    <td class="negative">+0.85% / -1.37%</td>
                    <td>2h18m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-1.94%</td>
                    <td>4</td>
                    <td>0.41</td>
                    <td class="positive">1.59%</td>
                    <td class="negative"><strong>-0.0474</strong></td>
                    <td class="negative">+0.86% / -1.87%</td>
                    <td>3.8m<br><small>(1.0m - 9.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="neutral"><strong>42.9%</strong></td>
                    <td class="negative">-5.35%</td>
                    <td>14</td>
                    <td>0.45</td>
                    <td class="negative">6.48%</td>
                    <td class="positive"><strong>0.1300</strong></td>
                    <td class="negative">+0.96% / -1.33%</td>
                    <td>2h1m<br><small>(15.0m - 7h10m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 8: Momentum Divergence...', 'Rule 7: Bollinger Band Bounce', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'Ext Rule 6: Fibonacci Support ...', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Rule 2: Golden Cross', 'Rule 27: Structure Break Up', 'Price Action Rule 3: Engulfing...', 'Rule 10: Volume Spike', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [73.94366197183099, 62.181528662420384, 61.36363636363637, 59.199999999999996, 61.315280464216634, 69.04761904761905, 60.0, 60.40322580645161, 64.86486486486487, 62.5, 83.33333333333334, 75.0, 60.0, 61.53846153846154, 50.0],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 8: Momentum Divergence...', 'Rule 7: Bollinger Band Bounce', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'Ext Rule 6: Fibonacci Support ...', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Rule 2: Golden Cross', 'Rule 27: Structure Break Up', 'Price Action Rule 3: Engulfing...', 'Rule 10: Volume Spike', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [np.float64(30.82089180485316), np.float64(14.774983158635113), np.float64(-2.7117182225895884), np.float64(-7.753448655112566), np.float64(-9.711382088952385), np.float64(5.575064542577515), np.float64(0.6960086460095845), np.float64(-38.68796246295537), np.float64(4.328101622805799), np.float64(0.7630188917275081), np.float64(3.115116326659103), np.float64(1.032519819542329), np.float64(0.4130316121055657), np.float64(-0.10030874378271983), np.float64(-1.9353601721778104)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
