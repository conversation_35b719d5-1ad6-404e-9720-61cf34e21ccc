
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 13:53:22 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">294.39%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">3,899</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">64.6%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">19.52</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>94.00%</strong></td>
                    <td>64.4%</td>
                    <td>1200</td>
                    <td>1.09</td>
                    <td class="negative">26.53%</td>
                    <td class="positive"><strong>0.9987</strong></td>
                    <td class="negative">+0.83% / -1.34%</td>
                    <td>5h29m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>88.65%</strong></td>
                    <td>64.9%</td>
                    <td>1179</td>
                    <td>1.09</td>
                    <td class="negative">15.62%</td>
                    <td class="positive"><strong>0.9964</strong></td>
                    <td class="negative">+0.82% / -1.38%</td>
                    <td>4h57m<br><small>(1.0m - 73h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>45.81%</strong></td>
                    <td>69.0%</td>
                    <td>145</td>
                    <td>1.44</td>
                    <td class="negative">10.49%</td>
                    <td class="positive"><strong>0.7551</strong></td>
                    <td class="negative">+0.81% / -1.33%</td>
                    <td>3h36m<br><small>(1.0m - 31h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>17.83%</strong></td>
                    <td>68.3%</td>
                    <td>82</td>
                    <td>1.27</td>
                    <td class="negative">7.82%</td>
                    <td class="positive"><strong>0.6020</strong></td>
                    <td class="negative">+0.81% / -1.37%</td>
                    <td>4h38m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>17.54%</strong></td>
                    <td>79.2%</td>
                    <td>24</td>
                    <td>2.40</td>
                    <td class="neutral">3.67%</td>
                    <td class="positive"><strong>0.6439</strong></td>
                    <td class="negative">+0.84% / -1.30%</td>
                    <td>3h48m<br><small>(5.0m - 17h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>15.06%</strong></td>
                    <td>67.5%</td>
                    <td>126</td>
                    <td>1.15</td>
                    <td class="negative">11.64%</td>
                    <td class="positive"><strong>0.6095</strong></td>
                    <td class="negative">+0.81% / -1.38%</td>
                    <td>5h36m<br><small>(1.0m - 71h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>11.67%</strong></td>
                    <td>81.2%</td>
                    <td>16</td>
                    <td>2.61</td>
                    <td class="neutral">2.76%</td>
                    <td class="positive"><strong>0.6061</strong></td>
                    <td class="negative">+0.80% / -1.30%</td>
                    <td>2h45m<br><small>(9.0m - 17h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>8.06%</strong></td>
                    <td>62.8%</td>
                    <td>965</td>
                    <td>1.01</td>
                    <td class="negative">45.55%</td>
                    <td class="positive"><strong>0.7091</strong></td>
                    <td class="negative">+0.82% / -1.40%</td>
                    <td>4h39m<br><small>(1.0m - 92h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>4.26%</strong></td>
                    <td>100.0%</td>
                    <td>3</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.88% / 0.00%</td>
                    <td>1.7m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>4.13%</strong></td>
                    <td>68.2%</td>
                    <td>22</td>
                    <td>1.23</td>
                    <td class="neutral">4.63%</td>
                    <td class="positive"><strong>0.4340</strong></td>
                    <td class="negative">+0.80% / -1.37%</td>
                    <td>8h53m<br><small>(28.0m - 38h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>1.78%</strong></td>
                    <td>70.0%</td>
                    <td>20</td>
                    <td>1.11</td>
                    <td class="negative">11.87%</td>
                    <td class="positive"><strong>0.3829</strong></td>
                    <td class="negative">+0.79% / -1.43%</td>
                    <td>5h3m<br><small>(16.0m - 47h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>1.48%</strong></td>
                    <td>71.4%</td>
                    <td>7</td>
                    <td>1.26</td>
                    <td class="neutral">3.16%</td>
                    <td class="positive"><strong>0.3350</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>14h12m<br><small>(28.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="negative"><strong>-0.12%</strong></td>
                    <td>50.0%</td>
                    <td>2</td>
                    <td>0.95</td>
                    <td class="neutral">2.41%</td>
                    <td class="positive"><strong>0.1566</strong></td>
                    <td class="positive">+1.26% / -1.25%</td>
                    <td>1h14m<br><small>(8.0m - 2h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="negative"><strong>-0.79%</strong></td>
                    <td>60.0%</td>
                    <td>5</td>
                    <td>0.84</td>
                    <td class="neutral">4.64%</td>
                    <td class="positive"><strong>0.2241</strong></td>
                    <td class="negative">+1.40% / -1.52%</td>
                    <td>35h56m<br><small>(15.0m - 83h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="negative"><strong>-2.24%</strong></td>
                    <td>0.0%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0617</strong></td>
                    <td class="negative">+0.00% / -1.35%</td>
                    <td>2h48m</td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-3.82%</strong></td>
                    <td>50.0%</td>
                    <td>8</td>
                    <td>0.60</td>
                    <td class="negative">6.04%</td>
                    <td class="positive"><strong>0.1428</strong></td>
                    <td class="negative">+0.85% / -1.29%</td>
                    <td>5h39m<br><small>(41.0m - 20h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-3.85%</strong></td>
                    <td>63.6%</td>
                    <td>44</td>
                    <td>0.91</td>
                    <td class="negative">12.77%</td>
                    <td class="positive"><strong>0.4056</strong></td>
                    <td class="negative">+0.81% / -1.33%</td>
                    <td>7h17m<br><small>(11.0m - 71h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-5.07%</strong></td>
                    <td>58.0%</td>
                    <td>50</td>
                    <td>0.89</td>
                    <td class="negative">13.40%</td>
                    <td class="positive"><strong>0.4011</strong></td>
                    <td class="negative">+0.83% / -1.34%</td>
                    <td>7h32m<br><small>(16.0m - 68h37m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">4.26%</td>
                    <td>3</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.88% / 0.00%</td>
                    <td>1.7m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>81.2%</strong></td>
                    <td class="positive">11.67%</td>
                    <td>16</td>
                    <td>2.61</td>
                    <td class="neutral">2.76%</td>
                    <td class="positive"><strong>0.6061</strong></td>
                    <td class="negative">+0.80% / -1.30%</td>
                    <td>2h45m<br><small>(9.0m - 17h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>79.2%</strong></td>
                    <td class="positive">17.54%</td>
                    <td>24</td>
                    <td>2.40</td>
                    <td class="neutral">3.67%</td>
                    <td class="positive"><strong>0.6439</strong></td>
                    <td class="negative">+0.84% / -1.30%</td>
                    <td>3h48m<br><small>(5.0m - 17h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>71.4%</strong></td>
                    <td class="positive">1.48%</td>
                    <td>7</td>
                    <td>1.26</td>
                    <td class="neutral">3.16%</td>
                    <td class="positive"><strong>0.3350</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>14h12m<br><small>(28.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>70.0%</strong></td>
                    <td class="positive">1.78%</td>
                    <td>20</td>
                    <td>1.11</td>
                    <td class="negative">11.87%</td>
                    <td class="positive"><strong>0.3829</strong></td>
                    <td class="negative">+0.79% / -1.43%</td>
                    <td>5h3m<br><small>(16.0m - 47h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>69.0%</strong></td>
                    <td class="positive">45.81%</td>
                    <td>145</td>
                    <td>1.44</td>
                    <td class="negative">10.49%</td>
                    <td class="positive"><strong>0.7551</strong></td>
                    <td class="negative">+0.81% / -1.33%</td>
                    <td>3h36m<br><small>(1.0m - 31h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>68.3%</strong></td>
                    <td class="positive">17.83%</td>
                    <td>82</td>
                    <td>1.27</td>
                    <td class="negative">7.82%</td>
                    <td class="positive"><strong>0.6020</strong></td>
                    <td class="negative">+0.81% / -1.37%</td>
                    <td>4h38m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>68.2%</strong></td>
                    <td class="positive">4.13%</td>
                    <td>22</td>
                    <td>1.23</td>
                    <td class="neutral">4.63%</td>
                    <td class="positive"><strong>0.4340</strong></td>
                    <td class="negative">+0.80% / -1.37%</td>
                    <td>8h53m<br><small>(28.0m - 38h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>67.5%</strong></td>
                    <td class="positive">15.06%</td>
                    <td>126</td>
                    <td>1.15</td>
                    <td class="negative">11.64%</td>
                    <td class="positive"><strong>0.6095</strong></td>
                    <td class="negative">+0.81% / -1.38%</td>
                    <td>5h36m<br><small>(1.0m - 71h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>64.9%</strong></td>
                    <td class="positive">88.65%</td>
                    <td>1179</td>
                    <td>1.09</td>
                    <td class="negative">15.62%</td>
                    <td class="positive"><strong>0.9964</strong></td>
                    <td class="negative">+0.82% / -1.38%</td>
                    <td>4h57m<br><small>(1.0m - 73h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>64.4%</strong></td>
                    <td class="positive">94.00%</td>
                    <td>1200</td>
                    <td>1.09</td>
                    <td class="negative">26.53%</td>
                    <td class="positive"><strong>0.9987</strong></td>
                    <td class="negative">+0.83% / -1.34%</td>
                    <td>5h29m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>63.6%</strong></td>
                    <td class="negative">-3.85%</td>
                    <td>44</td>
                    <td>0.91</td>
                    <td class="negative">12.77%</td>
                    <td class="positive"><strong>0.4056</strong></td>
                    <td class="negative">+0.81% / -1.33%</td>
                    <td>7h17m<br><small>(11.0m - 71h47m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>62.8%</strong></td>
                    <td class="positive">8.06%</td>
                    <td>965</td>
                    <td>1.01</td>
                    <td class="negative">45.55%</td>
                    <td class="positive"><strong>0.7091</strong></td>
                    <td class="negative">+0.82% / -1.40%</td>
                    <td>4h39m<br><small>(1.0m - 92h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>60.0%</strong></td>
                    <td class="negative">-0.79%</td>
                    <td>5</td>
                    <td>0.84</td>
                    <td class="neutral">4.64%</td>
                    <td class="positive"><strong>0.2241</strong></td>
                    <td class="negative">+1.40% / -1.52%</td>
                    <td>35h56m<br><small>(15.0m - 83h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>58.0%</strong></td>
                    <td class="negative">-5.07%</td>
                    <td>50</td>
                    <td>0.89</td>
                    <td class="negative">13.40%</td>
                    <td class="positive"><strong>0.4011</strong></td>
                    <td class="negative">+0.83% / -1.34%</td>
                    <td>7h32m<br><small>(16.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-3.82%</td>
                    <td>8</td>
                    <td>0.60</td>
                    <td class="negative">6.04%</td>
                    <td class="positive"><strong>0.1428</strong></td>
                    <td class="negative">+0.85% / -1.29%</td>
                    <td>5h39m<br><small>(41.0m - 20h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>50.0%</strong></td>
                    <td class="negative">-0.12%</td>
                    <td>2</td>
                    <td>0.95</td>
                    <td class="neutral">2.41%</td>
                    <td class="positive"><strong>0.1566</strong></td>
                    <td class="positive">+1.26% / -1.25%</td>
                    <td>1h14m<br><small>(8.0m - 2h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">-2.24%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0617</strong></td>
                    <td class="negative">+0.00% / -1.35%</td>
                    <td>2h48m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 8: Momentum Divergence...', 'Rule 7: Bollinger Band Bounce', 'Momentum Rule 2: Momentum Dive...', 'Price Action Rule 3: Engulfing...', 'Acad Rule 2: Mean Reversion Fa...', 'Professional Rule 10: CCI Reve...', 'Volume Rule 5: Smart Money Vol...', 'Rule 10: Volume Spike', 'Professional Rule 7: Chaikin M...', 'Advanced Rule 7: DMI ADX Filte...', 'Volume Rule 3: Dark Pool Activ...'],
                y: [64.41666666666667, 64.8854961832061, 68.96551724137932, 67.46031746031747, 68.29268292682927, 62.7979274611399, 79.16666666666666, 81.25, 100.0, 63.63636363636363, 57.99999999999999, 68.18181818181817, 70.0, 71.42857142857143, 60.0],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 8: Momentum Divergence...', 'Rule 7: Bollinger Band Bounce', 'Momentum Rule 2: Momentum Dive...', 'Price Action Rule 3: Engulfing...', 'Acad Rule 2: Mean Reversion Fa...', 'Professional Rule 10: CCI Reve...', 'Volume Rule 5: Smart Money Vol...', 'Rule 10: Volume Spike', 'Professional Rule 7: Chaikin M...', 'Advanced Rule 7: DMI ADX Filte...', 'Volume Rule 3: Dark Pool Activ...'],
                y: [np.float64(94.00089590856908), np.float64(88.64836494254284), np.float64(45.81117961435876), np.float64(15.057120438142563), np.float64(17.832418585445296), np.float64(8.059959952512683), np.float64(17.5446439221921), np.float64(11.674975889427136), np.float64(4.263213124208472), np.float64(-3.853419985982677), np.float64(-5.068811131087511), np.float64(4.1341305647352415), np.float64(1.7785699352802185), np.float64(1.475394000095228), np.float64(-0.7930438393898948)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
