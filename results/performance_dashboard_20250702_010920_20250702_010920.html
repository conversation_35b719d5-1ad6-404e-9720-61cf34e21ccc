
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 15 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 01:09:20</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">15</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">57.7%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">29.7%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">76.8%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">66.6%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">5,137</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["76.8%","71.9%","46.1%","41.7%","31.8%","31.3%","32.8%","29.4%","13.5%","7.7%"],"textposition":"auto","x":["Rule 10: Volume Spike","Ext Rule 5: ATR Volatility Expansion","Acad Rule 3: Volatility Breakout","Rule 27: Structure Break Up","Ext Rule 3: Bollinger Squeeze Breakout","Professional Rule 7: Chaikin Money Flow Reversal","AI Rule 10: Composite Sentiment Reversal","Acad Rule 2: Mean Reversion Factor","Volume Rule 4: Volume Breakout Confirmation","Prof Rule 7: Mean Reversion Volatility Filter"],"y":[76.82959728738268,71.940273251672,46.05195943249707,41.714684526271725,31.755023264410948,31.27464483948937,32.76125080498738,29.357521424543652,13.53109003577492,7.726944959600209],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Rule 10: Volume Spike","Ext Rule 5: ATR Volatility Expansion","Acad Rule 3: Volatility Breakout","Rule 27: Structure Break Up","Ext Rule 3: Bollinger Squeeze Breakout","Professional Rule 7: Chaikin Money Flow Reversal","AI Rule 10: Composite Sentiment Reversal","Acad Rule 2: Mean Reversion Factor","Volume Rule 4: Volume Breakout Confirmation","Prof Rule 7: Mean Reversion Volatility Filter"],"y":[384,203,449,349,72,131,677,503,105,143],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Rule 10: Volume Spike","Ext Rule 5: ATR Volatility Expansion","Acad Rule 3: Volatility Breakout","Rule 27: Structure Break Up","Ext Rule 3: Bollinger Squeeze Breakout","Professional Rule 7: Chaikin Money Flow Reversal","AI Rule 10: Composite Sentiment Reversal","Acad Rule 2: Mean Reversion Factor","Volume Rule 4: Volume Breakout Confirmation","Prof Rule 7: Mean Reversion Volatility Filter"],"y":[203,104,253,193,35,65,369,272,60,76],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[76.82959728738268,71.940273251672,46.05195943249707,41.714684526271725,31.755023264410948,31.27464483948937,32.76125080498738,29.357521424543652,13.53109003577492,7.726944959600209,38.66795929506372,5.390060215865466,4.41061194190933,12.560024747684745,1.271319204015148],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Rule 10","Ext Rule 5","Acad Rule 3","Rule 27","Ext Rule 3","Professional Rule 7","AI Rule 10","Acad Rule 2","Volume Rule 4","Prof Rule 7","Rule 2","Rule 28","Volatility Rule 2","Volume Rule 3","Advanced Rule 7"],"textposition":"top center","x":[24.059980239222607,9.307842155130496,27.482509626665586,23.694614903124062,26.879420322550203,10.246528156254621,41.48494887605117,37.35450340641844,20.68578385664106,35.74020640888037,4.933263030686865,15.746362019301824,24.3408305156349,5.211907920675015,7.386478648755578],"y":[76.82959728738268,71.940273251672,46.05195943249707,41.714684526271725,31.755023264410948,31.27464483948937,32.76125080498738,29.357521424543652,13.53109003577492,7.726944959600209,38.66795929506372,5.390060215865466,4.41061194190933,12.560024747684745,1.271319204015148],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["40.7%","23.8%","37.7%","32.8%","7.7%"],"textposition":"auto","x":["ORIGINAL","UNKNOWN","ACADEMIC","AI_GENERATED","PROFESSIONAL"],"y":[40.6505753311459,23.82042675499378,37.70474042852036,32.76125080498738,7.726944959600209],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["587","307","702","542","107","196","1046","775","165","219"],"textposition":"auto","x":["Rule 10: Volume Spike","Ext Rule 5: ATR Volatility Expansion","Acad Rule 3: Volatility Breakout","Rule 27: Structure Break Up","Ext Rule 3: Bollinger Squeeze Breakout","Professional Rule 7: Chaikin Money Flow Reversal","AI Rule 10: Composite Sentiment Reversal","Acad Rule 2: Mean Reversion Factor","Volume Rule 4: Volume Breakout Confirmation","Prof Rule 7: Mean Reversion Volatility Filter"],"y":[587,307,702,542,107,196,1046,775,165,219],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587],"y":[0,0.13088517425448498,0.26177034850896996,0.3926555227634549,0.5235406970179399,0.6544258712724249,0.7853110455269098,0.9161962197813949,1.0470813940358799,1.1779665682903648,1.3088517425448498,1.4397369167993348,1.5706220910538196,1.7015072653083048,1.8323924395627897,1.9632776138172745,2.0941627880717597,2.225047962326245,2.3559331365807297,2.4868183108352144,2.6177034850896996,2.7485886593441844,2.8794738335986696,3.010359007853155,3.141244182107639,3.2721293563621243,3.4030145306166095,3.5338997048710947,3.6647848791255795,3.7956700533800642,3.926555227634549,4.057440401889035,4.188325576143519,4.319210750398004,4.45009592465249,4.580981098906974,4.711866273161459,4.842751447415944,4.973636621670429,5.104521795924914,5.235406970179399,5.366292144433884,5.497177318688369,5.628062492942854,5.758947667197339,5.889832841451824,6.02071801570631,6.151603189960794,6.282488364215278,6.413373538469764,6.544258712724249,6.675143886978733,6.806029061233219,6.936914235487704,7.0677994097421895,7.198684583996674,7.329569758251159,7.460454932505645,7.5913401067601285,7.722225281014613,7.853110455269098,7.983995629523584,8.11488080377807,8.245765978032553,8.376651152287039,8.507536326541524,8.638421500796008,8.769306675050494,8.90019184930498,9.031077023559464,9.161962197813947,9.292847372068433,9.423732546322919,9.554617720577403,9.685502894831888,9.816388069086372,9.947273243340858,10.078158417595343,10.209043591849827,10.339928766104313,10.470813940358799,10.601699114613282,10.732584288867768,10.863469463122254,10.994354637376738,11.125239811631223,11.256124985885709,11.387010160140193,11.517895334394678,11.648780508649164,11.779665682903648,11.910550857158134,12.04143603141262,12.172321205667105,12.303206379921589,12.434091554176073,12.564976728430556,12.695861902685042,12.826747076939528,12.957632251194012,13.088517425448497,13.219402599702983,13.350287773957467,13.481172948211952,13.612058122466438,13.742943296720922,13.873828470975408,14.004713645229893,14.135598819484379,14.266483993738863,14.397369167993348,14.528254342247834,14.659139516502318,14.790024690756804,14.92090986501129,15.051795039265773,15.182680213520257,15.31356538777474,15.444450562029227,15.575335736283712,15.706220910538196,15.837106084792682,15.967991259047167,16.09887643330165,16.22976160755614,16.360646781810622,16.491531956065106,16.622417130319594,16.753302304574078,16.88418747882856,17.01507265308305,17.145957827337533,17.276843001592017,17.407728175846504,17.538613350100988,17.669498524355472,17.80038369860996,17.93126887286444,18.062154047118927,18.19303922137341,18.323924395627895,18.454809569882382,18.585694744136866,18.71657991839135,18.847465092645837,18.97835026690032,19.109235441154805,19.24012061540929,19.371005789663776,19.50189096391826,19.632776138172744,19.76366131242723,19.894546486681715,20.0254316609362,20.156316835190687,20.28720200944517,20.418087183699654,20.548972357954142,20.679857532208626,20.81074270646311,20.941627880717597,21.07251305497208,21.203398229226565,21.334283403481052,21.465168577735536,21.59605375199002,21.726938926244507,21.85782410049899,21.988709274753475,22.119594449007963,22.250479623262446,22.38136479751693,22.512249971771418,22.6431351460259,22.774020320280385,22.904905494534873,23.035790668789357,23.16667584304384,23.297561017298328,23.428446191552812,23.559331365807296,23.690216540061783,23.821101714316267,23.95198688857075,24.08287206282524,24.213757237079722,24.34464241133421,24.475527585588694,24.606412759843177,24.737297934097658,24.868183108352145,24.99906828260663,25.129953456861113,25.2608386311156,25.391723805370084,25.522608979624568,25.653494153879056,25.78437932813354,25.915264502388023,26.04614967664251,26.177034850896995,26.30792002515148,26.438805199405966,26.56969037366045,26.700575547914934,26.83146072216942,26.962345896423905,27.09323107067839,27.224116244932876,27.35500141918736,27.485886593441844,27.61677176769633,27.747656941950815,27.8785421162053,28.009427290459787,28.14031246471427,28.271197638968758,28.40208281322324,28.532967987477726,28.663853161732213,28.794738335986697,28.92562351024118,29.056508684495668,29.187393858750152,29.318279033004636,29.449164207259123,29.580049381513607,29.71093455576809,29.84181973002258,29.972704904277062,30.103590078531546,30.234475252786027,30.365360427040514,30.496245601294998,30.62713077554948,30.75801594980397,30.888901124058453,31.019786298312937,31.150671472567424,31.281556646821908,31.412441821076392,31.54332699533088,31.674212169585363,31.80509734383985,31.935982518094335,32.06686769234882,32.1977528666033,32.32863804085779,32.45952321511228,32.59040838936676,32.721293563621245,32.85217873787573,32.98306391213021,33.1139490863847,33.24483426063919,33.37571943489367,33.506604609148155,33.63748978340264,33.76837495765712,33.89926013191161,34.0301453061661,34.16103048042058,34.291915654675066,34.42280082892955,34.55368600318403,34.68457117743852,34.81545635169301,34.94634152594749,35.077226700201976,35.20811187445646,35.338997048710944,35.46988222296543,35.60076739721992,35.7316525714744,35.86253774572888,35.99342291998337,36.124308094237854,36.255193268492334,36.38607844274682,36.51696361700131,36.64784879125579,36.77873396551028,36.909619139764764,37.040504314019245,37.17138948827373,37.30227466252822,37.4331598367827,37.56404501103719,37.694930185291675,37.825815359546155,37.95670053380064,38.08758570805513,38.21847088230961,38.3493560565641,38.48024123081858,38.611126405073065,38.74201157932755,38.87289675358203,39.00378192783652,39.13466710209101,39.26555227634549,39.396437450599976,39.52732262485446,39.65820779910894,39.78909297336343,39.91997814761792,40.0508633218724,40.181748496126886,40.31263367038137,40.443518844635854,40.57440401889034,40.70528919314483,40.83617436739931,40.967059541653796,41.097944715908284,41.228829890162764,41.35971506441725,41.49060023867174,41.62148541292622,41.75237058718071,41.883255761435194,42.014140935689674,42.14502610994416,42.27591128419865,42.40679645845313,42.53768163270762,42.668566806962104,42.799451981216585,42.93033715547107,43.06122232972556,43.19210750398004,43.32299267823453,43.453877852489015,43.584763026743495,43.71564820099798,43.84653337525247,43.97741854950695,44.10830372376144,44.239188898015925,44.370074072270405,44.50095924652489,44.63184442077938,44.76272959503386,44.89361476928835,45.024499943542835,45.155385117797316,45.2862702920518,45.41715546630629,45.54804064056077,45.67892581481526,45.809810989069746,45.940696163324226,46.07158133757871,46.2024665118332,46.33335168608768,46.46423686034217,46.595122034596656,46.72600720885114,46.856892383105624,46.98777755736011,47.11866273161459,47.24954790586908,47.38043308012357,47.51131825437805,47.642203428632534,47.77308860288702,47.9039737771415,48.03485895139599,48.16574412565048,48.29662929990496,48.427514474159445,48.55839964841393,48.68928482266842,48.8201699969229,48.95105517117739,49.081940345431875,49.212825519686355,49.34371069394084,49.474595868195316,49.6054810424498,49.73636621670429,49.86725139095877,49.99813656521326,50.129021739467746,50.259906913722226,50.39079208797671,50.5216772622312,50.65256243648568,50.78344761074017,50.914332784994656,51.045217959249136,51.176103133503624,51.30698830775811,51.43787348201259,51.56875865626708,51.699643830521566,51.83052900477605,51.961414179030534,52.09229935328502,52.2231845275395,52.35406970179399,52.48495487604848,52.61584005030296,52.746725224557444,52.87761039881193,53.00849557306641,53.1393807473209,53.27026592157539,53.40115109582987,53.532036270084355,53.66292144433884,53.79380661859332,53.92469179284781,54.0555769671023,54.18646214135678,54.317347315611265,54.44823248986575,54.57911766412023,54.71000283837472,54.84088801262921,54.97177318688369,55.102658361138175,55.23354353539266,55.36442870964714,55.49531388390163,55.62619905815612,55.7570842324106,55.887969406665086,56.01885458091957,56.14973975517406,56.28062492942854,56.41151010368303,56.542395277937516,56.673280452191996,56.80416562644648,56.93505080070097,57.06593597495545,57.19682114920994,57.327706323464426,57.458591497718906,57.589476671973394,57.72036184622788,57.85124702048236,57.98213219473685,58.113017368991336,58.24390254324582,58.374787717500304,58.50567289175479,58.63655806600927,58.76744324026376,58.89832841451825,59.02921358877273,59.160098763027214,59.2909839372817,59.42186911153618,59.55275428579067,59.68363946004516,59.81452463429964,59.945409808554125,60.07629498280861,60.20718015706309,60.33806533131758,60.46895050557205,60.59983567982654,60.73072085408103,60.86160602833551,60.992491202589996,61.12337637684448,61.25426155109896,61.38514672535345,61.51603189960794,61.64691707386242,61.777802248116906,61.90868742237139,62.039572596625874,62.17045777088036,62.30134294513485,62.43222811938933,62.563113293643816,62.693998467898304,62.824883642152784,62.95576881640727,63.08665399066176,63.217539164916246,63.34842433917073,63.479309513425214,63.6101946876797,63.74107986193418,63.87196503618867,64.00285021044316,64.13373538469764,64.26462055895212,64.3955057332066,64.52639090746109,64.65727608171558,64.78816125597007,64.91904643022455,65.04993160447903,65.18081677873352,65.311701952988,65.44258712724249,65.57347230149698,65.70435747575146,65.83524265000594,65.96612782426043,66.09701299851491,66.2278981727694,66.35878334702389,66.48966852127838,66.62055369553285,66.75143886978734,66.88232404404182,67.01320921829631,67.1440943925508,67.27497956680529,67.40586474105976,67.53674991531425,67.66763508956873,67.79852026382322,67.92940543807771,68.0602906123322,68.19117578658667,68.32206096084116,68.45294613509564,68.58383130935013,68.71471648360462,68.8456016578591,68.97648683211358,69.10737200636807,69.23825718062255,69.36914235487704,69.50002752913153,69.63091270338602,69.7617978776405,69.89268305189498,70.02356822614946,70.15445340040395,70.28533857465844,70.41622374891293,70.54710892316741,70.67799409742189,70.80887927167637,70.93976444593086,71.07064962018535,71.20153479443984,71.33241996869432,71.4633051429488,71.59419031720329,71.72507549145776,71.85596066571225,71.98684583996673,72.11773101422122,72.24861618847571,72.3795013627302,72.51038653698467,72.64127171123916,72.77215688549364,72.90304205974813,73.03392723400262,73.1648124082571,73.29569758251158,73.42658275676607,73.55746793102055,73.68835310527504,73.81923827952953,73.95012345378402,74.08100862803849,74.21189380229298,74.34277897654746,74.47366415080195,74.60454932505644,74.73543449931093,74.8663196735654,74.99720484781989,75.12809002207437,75.25897519632886,75.38986037058335,75.52074554483784,75.65163071909231,75.7825158933468,75.91340106760128,76.04428624185577,76.17517141611026,76.30605659036475,76.43694176461922,76.56782693887371,76.6987121131282,76.82959728738268],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Ext Rule 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307],"y":[0,0.23433313762759608,0.46866627525519217,0.7029994128827882,0.9373325505103843,1.1716656881379803,1.4059988257655764,1.6403319633931726,1.8746651010207687,2.1089982386483648,2.3433313762759607,2.577664513903557,2.811997651531153,3.0463307891587488,3.280663926786345,3.5149970644139414,3.7493302020415373,3.983663339669133,4.2179964772967296,4.452329614924325,4.686662752551921,4.920995890179518,5.155329027807114,5.38966216543471,5.623995303062306,5.858328440689902,6.0926615783174976,6.326994715945094,6.56132785357269,6.795660991200286,7.029994128827883,7.264327266455479,7.498660404083075,7.732993541710671,7.967326679338266,8.201659816965863,8.435992954593459,8.670326092221055,8.90465922984865,9.138992367476247,9.373325505103843,9.60765864273144,9.841991780359036,10.076324917986632,10.310658055614228,10.544991193241824,10.77932433086942,11.013657468497017,11.247990606124612,11.482323743752207,11.716656881379803,11.9509900190074,12.185323156634995,12.419656294262591,12.653989431890189,12.888322569517785,13.12265570714538,13.356988844772976,13.591321982400572,13.825655120028168,14.059988257655766,14.294321395283362,14.528654532910958,14.762987670538553,14.99732080816615,15.231653945793745,15.465987083421343,15.700320221048939,15.934653358676533,16.16898649630413,16.403319633931726,16.63765277155932,16.871985909186918,17.106319046814512,17.34065218444211,17.574985322069704,17.8093184596973,18.0436515973249,18.277984734952494,18.51231787258009,18.746651010207685,18.980984147835283,19.21531728546288,19.449650423090475,19.683983560718072,19.918316698345667,20.152649835973264,20.38698297360086,20.621316111228456,20.855649248856054,21.089982386483648,21.324315524111245,21.55864866173884,21.792981799366437,22.027314936994035,22.26164807462163,22.495981212249223,22.730314349876817,22.964647487504415,23.19898062513201,23.433313762759607,23.667646900387204,23.9019800380148,24.136313175642396,24.37064631326999,24.604979450897588,24.839312588525182,25.07364572615278,25.307978863780377,25.54231200140797,25.77664513903557,26.010978276663163,26.24531141429076,26.47964455191836,26.713977689545953,26.94831082717355,27.182643964801144,27.416977102428742,27.651310240056336,27.885643377683934,28.11997651531153,28.354309652939126,28.588642790566723,28.822975928194317,29.057309065821915,29.291642203449513,29.525975341077107,29.760308478704705,29.9946416163323,30.228974753959896,30.46330789158749,30.697641029215088,30.931974166842686,31.16630730447028,31.400640442097878,31.634973579725468,31.869306717353066,32.10363985498066,32.33797299260826,32.57230613023585,32.80663926786345,33.04097240549105,33.27530554311864,33.509638680746235,33.743971818373836,33.97830495600143,34.212638093629025,34.446971231256626,34.68130436888422,34.915637506511814,35.14997064413941,35.38430378176701,35.6186369193946,35.8529700570222,36.0873031946498,36.32163633227739,36.55596946990499,36.79030260753259,37.02463574516018,37.25896888278778,37.49330202041537,37.72763515804297,37.961968295670566,38.19630143329816,38.43063457092576,38.664967708553355,38.89930084618095,39.133633983808544,39.367967121436145,39.60230025906374,39.83663339669133,40.070966534318934,40.30529967194653,40.53963280957412,40.77396594720172,41.00829908482932,41.24263222245691,41.476965360084506,41.71129849771211,41.9456316353397,42.179964772967296,42.4142979105949,42.64863104822249,42.882964185850085,43.11729732347768,43.35163046110528,43.585963598732874,43.82029673636047,44.05462987398807,44.288963011615664,44.52329614924326,44.75762928687085,44.991962424498446,45.22629556212604,45.460628699753634,45.69496183738123,45.92929497500883,46.163628112636424,46.39796125026402,46.63229438789162,46.86662752551921,47.10096066314681,47.33529380077441,47.569626938402,47.8039600760296,48.03829321365719,48.27262635128479,48.506959488912386,48.74129262653998,48.97562576416758,49.209958901795176,49.44429203942277,49.678625177050364,49.912958314677965,50.14729145230556,50.38162458993315,50.615957727560755,50.85029086518835,51.08462400281594,51.31895714044354,51.55329027807114,51.78762341569873,52.021956553326326,52.25628969095393,52.49062282858152,52.724955966209116,52.95928910383672,53.19362224146431,53.427955379091905,53.6622885167195,53.8966216543471,54.130954791974695,54.36528792960229,54.59962106722989,54.833954204857484,55.06828734248508,55.30262048011267,55.536953617740274,55.77128675536787,56.00561989299546,56.23995303062306,56.47428616825066,56.70861930587825,56.942952443505845,57.17728558113345,57.41161871876104,57.645951856388635,57.880284994016236,58.11461813164383,58.348951269271424,58.583284406899025,58.81761754452662,59.051950682154214,59.28628381978181,59.52061695740941,59.754950095037,59.9892832326646,60.2236163702922,60.45794950791979,60.69228264554739,60.92661578317498,61.16094892080258,61.395282058430176,61.62961519605777,61.86394833368537,62.098281471312966,62.33261460894056,62.566947746568154,62.801280884195755,63.03561402182334,63.269947159450936,63.50428029707854,63.73861343470613,63.972946572333726,64.20727970996133,64.44161284758891,64.67594598521652,64.91027912284412,65.1446122604717,65.3789453980993,65.6132785357269,65.84761167335449,66.0819448109821,66.3162779486097,66.55061108623728,66.78494422386488,67.01927736149247,67.25361049912007,67.48794363674767,67.72227677437526,67.95660991200286,68.19094304963046,68.42527618725805,68.65960932488565,68.89394246251325,69.12827560014084,69.36260873776844,69.59694187539604,69.83127501302363,70.06560815065123,70.29994128827882,70.53427442590642,70.76860756353402,71.0029407011616,71.2372738387892,71.47160697641681,71.7059401140444,71.940273251672],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Acad Rule 3","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702],"y":[0,0.06560108181267389,0.13120216362534778,0.1968032454380217,0.26240432725069557,0.32800540906336945,0.3936064908760434,0.4592075726887172,0.5248086545013911,0.590409736314065,0.6560108181267389,0.7216118999394129,0.7872129817520868,0.8528140635647605,0.9184151453774344,0.9840162271901084,1.0496173090027823,1.115218390815456,1.18081947262813,1.246420554440804,1.3120216362534778,1.3776227180661518,1.4432237998788258,1.5088248816914995,1.5744259635041735,1.6400270453168473,1.705628127129521,1.771229208942195,1.8368302907548688,1.9024313725675426,1.9680324543802168,2.0336335361928906,2.0992346180055645,2.1648356998182385,2.230436781630912,2.296037863443586,2.36163894525626,2.427240027068934,2.492841108881608,2.5584421906942816,2.6240432725069556,2.6896443543196296,2.7552454361323035,2.820846517944977,2.8864475997576515,2.9520486815703246,3.017649763382999,3.083250845195673,3.148851927008347,3.2144530088210206,3.2800540906336946,3.3456551724463686,3.411256254259042,3.476857336071716,3.54245841788439,3.6080594996970636,3.6736605815097376,3.739261663322412,3.804862745135085,3.8704638269477596,3.9360649087604336,4.001665990573108,4.067267072385781,4.132868154198455,4.198469236011129,4.264070317823803,4.329671399636477,4.395272481449151,4.460873563261824,4.526474645074498,4.592075726887172,4.657676808699846,4.72327789051252,4.788878972325194,4.854480054137868,4.920081135950541,4.985682217763216,5.05128329957589,5.116884381388563,5.182485463201237,5.248086545013911,5.313687626826585,5.379288708639259,5.444889790451933,5.510490872264607,5.57609195407728,5.641693035889954,5.707294117702629,5.772895199515303,5.838496281327977,5.904097363140649,5.969698444953324,6.035299526765998,6.100900608578672,6.166501690391346,6.23210277220402,6.297703854016694,6.363304935829367,6.428906017642041,6.494507099454715,6.560108181267389,6.625709263080063,6.691310344892737,6.75691142670541,6.822512508518084,6.888113590330758,6.953714672143432,7.019315753956106,7.08491683576878,7.150517917581455,7.216118999394127,7.281720081206801,7.347321163019475,7.41292224483215,7.478523326644824,7.544124408457498,7.60972549027017,7.675326572082845,7.740927653895519,7.806528735708193,7.872129817520867,7.937730899333541,8.003331981146216,8.068933062958887,8.134534144771562,8.200135226584235,8.26573630839691,8.331337390209585,8.396938472022258,8.462539553834933,8.528140635647606,8.59374171746028,8.659342799272954,8.724943881085627,8.790544962898302,8.856146044710975,8.921747126523648,8.987348208336323,9.052949290148996,9.118550371961671,9.184151453774344,9.24975253558702,9.315353617399692,9.380954699212365,9.44655578102504,9.512156862837713,9.577757944650388,9.643359026463061,9.708960108275736,9.774561190088411,9.840162271901082,9.905763353713757,9.971364435526432,10.036965517339105,10.10256659915178,10.168167680964453,10.233768762777126,10.299369844589801,10.364970926402474,10.43057200821515,10.496173090027822,10.561774171840497,10.62737525365317,10.692976335465843,10.758577417278518,10.824178499091191,10.889779580903866,10.95538066271654,11.020981744529214,11.086582826341887,11.15218390815456,11.217784989967235,11.283386071779908,11.348987153592583,11.414588235405258,11.480189317217931,11.545790399030606,11.611391480843277,11.676992562655954,11.742593644468627,11.808194726281299,11.873795808093975,11.939396889906648,12.004997971719323,12.070599053531996,12.136200135344671,12.201801217157344,12.267402298970017,12.333003380782692,12.398604462595365,12.46420554440804,12.529806626220713,12.595407708033388,12.661008789846061,12.726609871658734,12.79221095347141,12.857812035284082,12.923413117096757,12.98901419890943,13.054615280722103,13.120216362534778,13.185817444347451,13.251418526160126,13.3170196079728,13.382620689785474,13.448221771598147,13.51382285341082,13.579423935223495,13.645025017036168,13.710626098848843,13.776227180661516,13.841828262474191,13.907429344286864,13.973030426099537,14.038631507912212,14.104232589724885,14.16983367153756,14.235434753350233,14.30103583516291,14.366636916975581,14.432237998788255,14.49783908060093,14.563440162413603,14.62904124422628,14.69464232603895,14.760243407851627,14.8258444896643,14.891445571476972,14.957046653289648,15.02264773510232,15.088248816914996,15.15384989872767,15.21945098054034,15.285052062353017,15.35065314416569,15.416254225978365,15.481855307791038,15.547456389603713,15.613057471416386,15.67865855322906,15.744259635041734,15.809860716854407,15.875461798667082,15.941062880479755,16.006663962292432,16.0722650441051,16.137866125917775,16.20346720773045,16.269068289543124,16.3346693713558,16.40027045316847,16.465871534981147,16.53147261679382,16.597073698606494,16.66267478041917,16.72827586223184,16.793876944044516,16.85947802585719,16.925079107669866,16.99068018948254,17.056281271295212,17.121882353107885,17.18748343492056,17.253084516733235,17.31868559854591,17.38428668035858,17.449887762171254,17.515488843983928,17.581089925796604,17.646691007609277,17.71229208942195,17.777893171234624,17.843494253047297,17.909095334859973,17.974696416672646,18.04029749848532,18.105898580297993,18.17149966211067,18.237100743923342,18.302701825736015,18.36830290754869,18.43390398936136,18.49950507117404,18.56510615298671,18.630707234799385,18.696308316612058,18.76190939842473,18.827510480237407,18.89311156205008,18.958712643862754,19.024313725675427,19.089914807488103,19.155515889300776,19.22111697111345,19.286718052926123,19.352319134738796,19.417920216551472,19.483521298364145,19.549122380176822,19.61472346198949,19.680324543802165,19.74592562561484,19.811526707427515,19.87712778924019,19.942728871052864,20.008329952865534,20.07393103467821,20.139532116490884,20.20513319830356,20.270734280116233,20.336335361928906,20.40193644374158,20.467537525554253,20.53313860736693,20.598739689179602,20.664340770992276,20.72994185280495,20.795542934617625,20.8611440164303,20.92674509824297,20.992346180055645,21.057947261868318,21.123548343680994,21.189149425493667,21.25475050730634,21.320351589119014,21.385952670931687,21.451553752744363,21.517154834557036,21.58275591636971,21.648356998182383,21.71395807999506,21.779559161807732,21.845160243620406,21.91076132543308,21.97636240724575,22.04196348905843,22.1075645708711,22.173165652683775,22.238766734496448,22.30436781630912,22.369968898121797,22.43556997993447,22.501171061747147,22.566772143559817,22.63237322537249,22.697974307185167,22.76357538899784,22.829176470810516,22.894777552623186,22.960378634435862,23.025979716248536,23.091580798061212,23.15718187987388,23.222782961686555,23.28838404349923,23.353985125311908,23.419586207124578,23.485187288937254,23.550788370749927,23.616389452562597,23.681990534375274,23.74759161618795,23.813192698000623,23.878793779813297,23.94439486162597,24.009995943438646,24.075597025251316,24.141198107063992,24.206799188876666,24.272400270689342,24.338001352502012,24.40360243431469,24.46920351612736,24.534804597940035,24.600405679752708,24.666006761565384,24.731607843378058,24.79720892519073,24.862810007003404,24.92841108881608,24.99401217062875,25.059613252441427,25.1252143342541,25.190815416066776,25.256416497879446,25.322017579692123,25.3876186615048,25.45321974331747,25.518820825130142,25.58442190694282,25.650022988755488,25.715624070568165,25.781225152380838,25.846826234193514,25.912427316006184,25.97802839781886,26.043629479631537,26.109230561444207,26.17483164325688,26.240432725069557,26.306033806882233,26.371634888694903,26.43723597050758,26.502837052320253,26.568438134132922,26.6340392159456,26.699640297758275,26.76524137957095,26.830842461383618,26.896443543196295,26.96204462500897,27.02764570682164,27.093246788634318,27.15884787044699,27.224448952259667,27.290050034072337,27.355651115885014,27.421252197697687,27.486853279510356,27.552454361323033,27.61805544313571,27.683656524948383,27.749257606761056,27.81485868857373,27.880459770386405,27.946060852199075,28.01166193401175,28.077263015824425,28.1428640976371,28.20846517944977,28.274066261262448,28.33966734307512,28.405268424887794,28.470869506700467,28.536470588513144,28.60207167032582,28.66767275213849,28.733273833951163,28.79887491576384,28.86447599757651,28.930077079389186,28.99567816120186,29.061279243014535,29.126880324827205,29.19248140663988,29.25808248845256,29.323683570265228,29.3892846520779,29.454885733890578,29.520486815703254,29.586087897515924,29.6516889793286,29.717290061141274,29.782891142953943,29.84849222476662,29.914093306579296,29.979694388391966,30.04529547020464,30.110896552017316,30.176497633829992,30.242098715642662,30.30769979745534,30.37330087926801,30.43890196108068,30.504503042893358,30.570104124706035,30.635705206518708,30.70130628833138,30.766907370144054,30.83250845195673,30.8981095337694,30.963710615582077,31.02931169739475,31.094912779207426,31.160513861020096,31.226114942832773,31.291716024645446,31.35731710645812,31.422918188270792,31.48851927008347,31.55412035189614,31.619721433708815,31.685322515521488,31.750923597334165,31.816524679146834,31.88212576095951,31.947726842772184,32.013327924584864,32.07892900639753,32.1445300882102,32.21013117002288,32.27573225183555,32.34133333364823,32.4069344154609,32.472535497273576,32.53813657908625,32.60373766089892,32.6693387427116,32.73493982452427,32.80054090633694,32.86614198814962,32.931743069962295,32.99734415177497,33.06294523358764,33.128546315400314,33.19414739721299,33.25974847902566,33.32534956083834,33.39095064265101,33.45655172446368,33.52215280627636,33.58775388808903,33.653354969901706,33.71895605171438,33.78455713352705,33.85015821533973,33.9157592971524,33.98136037896508,34.04696146077775,34.112562542590425,34.1781636244031,34.24376470621577,34.309365788028444,34.37496686984112,34.44056795165379,34.50616903346647,34.571770115279136,34.63737119709182,34.70297227890449,34.76857336071716,34.834174442529836,34.89977552434251,34.96537660615518,35.030977687967855,35.09657876978053,35.16217985159321,35.227780933405874,35.293382015218555,35.35898309703123,35.4245841788439,35.490185260656574,35.55578634246925,35.62138742428193,35.68698850609459,35.752589587907266,35.81819066971995,35.88379175153262,35.94939283334529,36.014993915157966,36.08059499697064,36.14619607878331,36.211797160595985,36.277398242408665,36.34299932422134,36.408600406034004,36.474201487846685,36.53980256965936,36.60540365147203,36.671004733284704,36.73660581509738,36.80220689691006,36.86780797872272,36.9334090605354,36.99901014234808,37.06461122416074,37.13021230597342,37.195813387786096,37.26141446959877,37.32701555141144,37.392616633224115,37.458217715036795,37.52381879684946,37.58941987866214,37.655020960474815,37.72062204228749,37.78622312410016,37.851824205912834,37.91742528772551,37.98302636953818,38.04862745135085,38.11422853316353,38.17982961497621,38.24543069678888,38.31103177860155,38.376632860414226,38.4422339422269,38.50783502403957,38.573436105852245,38.639037187664925,38.70463826947759,38.77023935129027,38.835840433102945,38.90144151491562,38.96704259672829,39.032643678540964,39.098244760353644,39.16384584216631,39.22944692397898,39.295048005791664,39.36064908760433,39.42625016941701,39.49185125122968,39.557452333042356,39.62305341485503,39.6886544966677,39.75425557848038,39.81985666029305,39.88545774210573,39.9510588239184,40.01665990573107,40.08226098754375,40.14786206935642,40.213463151169094,40.27906423298177,40.34466531479444,40.41026639660712,40.47586747841979,40.54146856023247,40.60706964204514,40.67267072385781,40.738271805670486,40.80387288748316,40.86947396929583,40.935075051108505,41.00067613292118,41.06627721473386,41.13187829654653,41.197479378359205,41.26308046017188,41.32868154198455,41.394282623797224,41.4598837056099,41.52548478742257,41.59108586923525,41.65668695104792,41.7222880328606,41.78788911467327,41.85349019648594,41.919091278298616,41.98469236011129,42.05029344192397,42.115894523736635,42.18149560554931,42.24709668736199,42.312697769174655,42.378298850987335,42.44389993280001,42.50950101461268,42.575102096425354,42.64070317823803,42.70630426005071,42.77190534186337,42.83750642367605,42.90310750548873,42.9687085873014,43.03430966911407,43.099910750926746,43.16551183273942,43.23111291455209,43.296713996364765,43.362315078177446,43.42791615999012,43.49351724180279,43.559118323615465,43.62471940542814,43.69032048724081,43.755921569053484,43.82152265086616,43.88712373267883,43.9527248144915,44.018325896304184,44.08392697811686,44.14952805992953,44.2151291417422,44.280730223554876,44.34633130536755,44.41193238718022,44.477533468992895,44.543134550805576,44.60873563261824,44.67433671443092,44.739937796243595,44.80553887805627,44.87113995986894,44.936741041681614,45.002342123494294,45.06794320530696,45.13354428711963,45.199145368932314,45.26474645074498,45.33034753255766,45.39594861437033,45.461549696183006,45.52715077799568,45.59275185980835,45.65835294162103,45.7239540234337,45.78955510524637,45.85515618705905,45.920757268871725,45.9863583506844,46.05195943249707],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Rule 27","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542],"y":[0,0.07696436259459728,0.15392872518919457,0.2308930877837918,0.30785745037838913,0.3848218129729864,0.4617861755675836,0.538750538162181,0.6157149007567783,0.6926792633513755,0.7696436259459728,0.8466079885405701,0.9235723511351672,1.0005367137297647,1.077501076324362,1.1544654389189593,1.2314298015135565,1.3083941641081536,1.385358526702751,1.462322889297348,1.5392872518919456,1.6162516144865429,1.6932159770811401,1.7701803396757374,1.8471447022703344,1.924109064864932,2.0010734274595294,2.0780377900541263,2.155002152648724,2.231966515243321,2.3089308778379185,2.3858952404325158,2.462859603027113,2.5398239656217103,2.616788328216307,2.693752690810905,2.770717053405502,2.8476814160000994,2.924645778594696,3.001610141189294,3.078574503783891,3.1555388663784885,3.2325032289730857,3.3094675915676826,3.3864319541622803,3.4633963167568775,3.540360679351475,3.6173250419460725,3.694289404540669,3.7712537671352666,3.848218129729864,3.925182492324461,4.002146854919059,4.079111217513655,4.1560755801082525,4.233039942702851,4.310004305297448,4.386968667892045,4.463933030486642,4.540897393081239,4.617861755675837,4.694826118270434,4.7717904808650315,4.848754843459628,4.925719206054226,5.002683568648823,5.079647931243421,5.156612293838018,5.233576656432614,5.310541019027212,5.38750538162181,5.464469744216407,5.541434106811004,5.6183984694056015,5.695362832000199,5.772327194594796,5.849291557189392,5.92625591978399,6.003220282378588,6.080184644973185,6.157149007567782,6.23411337016238,6.311077732756977,6.388042095351574,6.4650064579461715,6.54197082054077,6.618935183135365,6.695899545729963,6.7728639083245605,6.849828270919158,6.926792633513755,7.003756996108352,7.08072135870295,7.157685721297547,7.234650083892145,7.311614446486742,7.388578809081338,7.465543171675936,7.542507534270533,7.6194718968651305,7.696436259459728,7.773400622054325,7.850364984648922,7.92732934724352,8.004293709838118,8.081258072432714,8.15822243502731,8.235186797621909,8.312151160216505,8.389115522811103,8.466079885405701,8.543044248000298,8.620008610594896,8.696972973189492,8.77393733578409,8.850901698378687,8.927866060973283,9.004830423567881,9.081794786162478,9.158759148757076,9.235723511351674,9.31268787394627,9.389652236540869,9.466616599135465,9.543580961730063,9.62054532432466,9.697509686919256,9.774474049513854,9.851438412108452,9.928402774703049,10.005367137297647,10.082331499892243,10.159295862486841,10.236260225081438,10.313224587676036,10.390188950270634,10.467153312865229,10.544117675459828,10.621082038054425,10.698046400649023,10.77501076324362,10.851975125838216,10.928939488432814,11.00590385102741,11.082868213622008,11.159832576216605,11.236796938811203,11.3137613014058,11.390725664000398,11.467690026594994,11.544654389189592,11.621618751784188,11.698583114378785,11.775547476973385,11.85251183956798,11.92947620216258,12.006440564757176,12.083404927351774,12.16036928994637,12.237333652540968,12.314298015135565,12.391262377730161,12.46822674032476,12.545191102919356,12.622155465513954,12.69911982810855,12.776084190703148,12.853048553297745,12.930012915892343,13.00697727848694,13.08394164108154,13.160906003676134,13.23787036627073,13.31483472886533,13.391799091459927,13.468763454054525,13.545727816649121,13.62269217924372,13.699656541838316,13.776620904432914,13.85358526702751,13.930549629622107,14.007513992216705,14.084478354811301,14.1614427174059,14.238407080000496,14.315371442595094,14.39233580518969,14.46930016778429,14.546264530378885,14.623228892973485,14.700193255568081,14.777157618162676,14.854121980757276,14.931086343351872,15.00805070594647,15.085015068541066,15.161979431135665,15.238943793730261,15.31590815632486,15.392872518919456,15.469836881514052,15.54680124410865,15.623765606703246,15.700729969297845,15.777694331892441,15.85465869448704,15.931623057081636,16.008587419676235,16.08555178227083,16.162516144865428,16.239480507460026,16.31644487005462,16.39340923264922,16.470373595243817,16.547337957838415,16.62430232043301,16.70126668302761,16.778231045622206,16.855195408216805,16.932159770811403,17.009124133405997,17.086088496000595,17.163052858595194,17.24001722118979,17.316981583784386,17.393945946378985,17.470910308973583,17.54787467156818,17.624839034162775,17.701803396757374,17.77876775935197,17.855732121946566,17.932696484541164,18.009660847135763,18.08662520973036,18.163589572324955,18.240553934919557,18.31751829751415,18.39448266010875,18.471447022703348,18.548411385297943,18.62537574789254,18.70234011048714,18.779304473081737,18.85626883567633,18.93323319827093,19.010197560865528,19.087161923460126,19.16412628605472,19.24109064864932,19.318055011243917,19.39501937383851,19.471983736433113,19.548948099027708,19.625912461622306,19.702876824216904,19.779841186811502,19.856805549406097,19.933769912000695,20.010734274595293,20.087698637189888,20.164662999784486,20.241627362379084,20.318591724973682,20.395556087568277,20.472520450162875,20.549484812757473,20.62644917535207,20.703413537946666,20.780377900541268,20.857342263135862,20.934306625730457,21.011270988325055,21.088235350919657,21.16519971351425,21.24216407610885,21.319128438703444,21.396092801298046,21.47305716389264,21.55002152648724,21.626985889081833,21.70395025167643,21.78091461427103,21.857878976865628,21.934843339460222,22.01180770205482,22.088772064649422,22.165736427244017,22.24270078983861,22.31966515243321,22.39662951502781,22.473593877622406,22.550558240217004,22.6275226028116,22.704486965406197,22.781451328000795,22.858415690595393,22.935380053189988,23.012344415784586,23.089308778379184,23.166273140973782,23.243237503568377,23.320201866162975,23.39716622875757,23.47413059135217,23.55109495394677,23.628059316541364,23.70502367913596,23.78198804173056,23.85895240432516,23.935916766919753,24.01288112951435,24.089845492108946,24.166809854703548,24.243774217298142,24.32073857989274,24.397702942487335,24.474667305081937,24.55163166767653,24.62859603027113,24.705560392865724,24.782524755460322,24.859489118054924,24.93645348064952,25.013417843244113,25.09038220583871,25.167346568433313,25.244310931027908,25.321275293622506,25.3982396562171,25.475204018811702,25.552168381406297,25.629132744000895,25.70609710659549,25.783061469190088,25.860025831784686,25.936990194379284,26.01395455697388,26.090918919568477,26.16788328216308,26.244847644757673,26.321812007352268,26.398776369946866,26.47574073254146,26.552705095136062,26.62966945773066,26.706633820325255,26.783598182919853,26.86056254551445,26.93752690810905,27.014491270703644,27.091455633298242,27.168419995892837,27.24538435848744,27.322348721082033,27.39931308367663,27.476277446271226,27.553241808865828,27.630206171460422,27.70717053405502,27.784134896649615,27.861099259244213,27.938063621838815,28.01502798443341,28.091992347028008,28.168956709622602,28.245921072217204,28.3228854348118,28.399849797406397,28.47681416000099,28.553778522595593,28.630742885190188,28.707707247784786,28.78467161037938,28.86163597297398,28.93860033556858,29.015564698163175,29.09252906075777,29.169493423352368,29.24645778594697,29.323422148541564,29.400386511136162,29.477350873730757,29.55431523632535,29.631279598919953,29.70824396151455,29.785208324109146,29.862172686703744,29.939137049298342,30.01610141189294,30.093065774487535,30.170030137082133,30.246994499676727,30.32395886227133,30.400923224865924,30.477887587460522,30.554851950055117,30.63181631264972,30.708780675244316,30.78574503783891,30.86270940043351,30.939673763028104,31.016638125622706,31.0936024882173,31.1705668508119,31.247531213406493,31.324495576001095,31.40145993859569,31.478424301190287,31.555388663784882,31.632353026379484,31.70931738897408,31.786281751568676,31.86324611416327,31.94021047675787,32.01717483935247,32.094139201947065,32.17110356454166,32.24806792713626,32.325032289730856,32.40199665232546,32.47896101492005,32.55592537751465,32.63288974010924,32.709854102703844,32.78681846529844,32.86378282789304,32.940747190487635,33.017711553082236,33.09467591567683,33.171640278271425,33.24860464086602,33.32556900346062,33.40253336605522,33.47949772864982,33.55646209124441,33.63342645383901,33.71039081643361,33.787355179028204,33.864319541622805,33.9412839042174,34.018248266811995,34.095212629406596,34.17217699200119,34.249141354595785,34.32610571719039,34.40307007978498,34.48003444237958,34.55699880497418,34.63396316756877,34.710927530163374,34.78789189275797,34.86485625535257,34.941820617947165,35.01878498054176,35.09574934313636,35.172713705730956,35.24967806832555,35.32664243092015,35.40360679351475,35.48057115610935,35.55753551870394,35.63449988129854,35.71146424389313,35.788428606487734,35.86539296908233,35.94235733167693,36.019321694271525,36.09628605686613,36.17325041946072,36.250214782055316,36.32717914464991,36.40414350724451,36.481107869839114,36.55807223243371,36.6350365950283,36.7120009576229,36.7889653202175,36.865929682812094,36.942894045406696,37.01985840800129,37.096822770595885,37.17378713319049,37.25075149578508,37.327715858379676,37.40468022097428,37.48164458356888,37.558608946163474,37.63557330875807,37.71253767135266,37.789502033947265,37.86646639654186,37.94343075913646,38.020395121731056,38.09735948432565,38.17432384692025,38.25128820951485,38.32825257210944,38.40521693470404,38.48218129729864,38.55914565989324,38.636110022487834,38.71307438508243,38.79003874767702,38.867003110271625,38.94396747286623,39.02093183546082,39.097896198055416,39.17486056065002,39.25182492324461,39.32878928583921,39.40575364843381,39.4827180110284,39.559682373623005,39.6366467362176,39.713611098812194,39.79057546140679,39.86753982400139,39.944504186595985,40.02146854919059,40.09843291178518,40.175397274379776,40.25236163697438,40.32932599956897,40.40629036216357,40.48325472475817,40.56021908735277,40.637183449947365,40.71414781254196,40.791112175136554,40.868076537731156,40.94504090032575,41.02200526292035,41.09896962551495,41.17593398810954,41.25289835070414,41.32986271329874,41.40682707589333,41.483791438487934,41.560755801082536,41.63772016367713,41.714684526271725],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Ext Rule 3","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107],"y":[0,0.2967759183589808,0.5935518367179616,0.8903277550769424,1.1871036734359233,1.4838795917949041,1.7806555101538848,2.0774314285128654,2.3742073468718465,2.670983265230827,2.9677591835898083,3.264535101948789,3.5613110203077696,3.8580869386667507,4.154862857025731,4.451638775384712,4.748414693743693,5.045190612102673,5.341966530461654,5.6387424488206355,5.935518367179617,6.232294285538597,6.529070203897578,6.825846122256559,7.122622040615539,7.41939795897452,7.716173877333501,8.012949795692482,8.309725714051462,8.606501632410444,8.903277550769424,9.200053469128404,9.496829387487386,9.793605305846366,10.090381224205347,10.387157142564329,10.683933060923309,10.980708979282289,11.277484897641271,11.574260816000251,11.871036734359233,12.167812652718213,12.464588571077194,12.761364489436176,13.058140407795156,13.354916326154136,13.651692244513118,13.948468162872098,14.245244081231078,14.54201999959006,14.83879591794904,15.135571836308022,15.432347754667003,15.729123673025983,16.025899591384963,16.322675509743945,16.619451428102924,16.916227346461906,17.213003264820887,17.509779183179866,17.806555101538848,18.10333101989783,18.40010693825681,18.69688285661579,18.993658774974772,19.29043469333375,19.587210611692733,19.883986530051715,20.180762448410693,20.477538366769675,20.774314285128657,21.071090203487636,21.367866121846617,21.6646420402056,21.961417958564578,22.25819387692356,22.554969795282542,22.851745713641524,23.148521632000502,23.445297550359484,23.742073468718466,24.038849387077445,24.335625305436427,24.63240122379541,24.929177142154387,25.22595306051337,25.52272897887235,25.81950489723133,26.11628081559031,26.413056733949293,26.709832652308272,27.006608570667254,27.303384489026236,27.600160407385214,27.896936325744196,28.193712244103178,28.490488162462157,28.78726408082114,29.08403999918012,29.380815917539103,29.67759183589808,29.974367754257063,30.271143672616045,30.567919590975023,30.864695509334005,31.161471427692987,31.458247346051966,31.755023264410948],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Rule 10: Volume Spike</td>
                <td>ORIGINAL</td>
                <td class="positive">76.83%</td>
                <td>65.4%</td>
                <td>587</td>
                <td>1.14</td>
                <td>0.00</td>
                <td>24.06%</td>
                <td>80.4</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Ext Rule 5: ATR Volatility Expansion</td>
                <td>UNKNOWN</td>
                <td class="positive">71.94%</td>
                <td>66.1%</td>
                <td>307</td>
                <td>1.28</td>
                <td>0.00</td>
                <td>9.31%</td>
                <td>78.6</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Acad Rule 3: Volatility Breakout</td>
                <td>ACADEMIC</td>
                <td class="positive">46.05%</td>
                <td>64.0%</td>
                <td>702</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>27.48%</td>
                <td>67.6</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Rule 27: Structure Break Up</td>
                <td>ORIGINAL</td>
                <td class="positive">41.71%</td>
                <td>64.4%</td>
                <td>542</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>23.69%</td>
                <td>66.0</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                <td>UNKNOWN</td>
                <td class="positive">31.76%</td>
                <td>68.2%</td>
                <td>107</td>
                <td>1.36</td>
                <td>0.00</td>
                <td>26.88%</td>
                <td>63.2</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">31.27%</td>
                <td>66.8%</td>
                <td>196</td>
                <td>1.18</td>
                <td>0.00</td>
                <td>10.25%</td>
                <td>62.6</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">32.76%</td>
                <td>64.7%</td>
                <td>1046</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>41.48%</td>
                <td>62.5</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Acad Rule 2: Mean Reversion Factor</td>
                <td>ACADEMIC</td>
                <td class="positive">29.36%</td>
                <td>64.9%</td>
                <td>775</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>37.35%</td>
                <td>61.2</td>
            </tr>
            
            <tr>
                <td>9</td>
                <td>Volume Rule 4: Volume Breakout Confirmation</td>
                <td>UNKNOWN</td>
                <td class="positive">13.53%</td>
                <td>63.6%</td>
                <td>165</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>20.69%</td>
                <td>54.5</td>
            </tr>
            
            <tr>
                <td>10</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">7.73%</td>
                <td>65.8%</td>
                <td>219</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>35.74%</td>
                <td>52.8</td>
            </tr>
            
            <tr>
                <td>11</td>
                <td>Rule 2: Golden Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">38.67%</td>
                <td>83.8%</td>
                <td>37</td>
                <td>3.09</td>
                <td>0.00</td>
                <td>4.93%</td>
                <td>51.7</td>
            </tr>
            
            <tr>
                <td>12</td>
                <td>Rule 28: Volume Breakout</td>
                <td>ORIGINAL</td>
                <td class="positive">5.39%</td>
                <td>63.1%</td>
                <td>122</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>15.75%</td>
                <td>51.1</td>
            </tr>
            
            <tr>
                <td>13</td>
                <td>Volatility Rule 2: ATR Expansion Signal</td>
                <td>UNKNOWN</td>
                <td class="positive">4.41%</td>
                <td>62.7%</td>
                <td>276</td>
                <td>1.02</td>
                <td>0.00</td>
                <td>24.34%</td>
                <td>50.6</td>
            </tr>
            
            <tr>
                <td>14</td>
                <td>Volume Rule 3: Dark Pool Activity</td>
                <td>UNKNOWN</td>
                <td class="positive">12.56%</td>
                <td>67.9%</td>
                <td>28</td>
                <td>1.60</td>
                <td>0.00</td>
                <td>5.21%</td>
                <td>33.8</td>
            </tr>
            
            <tr>
                <td>15</td>
                <td>Advanced Rule 7: DMI ADX Filter</td>
                <td>UNKNOWN</td>
                <td class="positive">1.27%</td>
                <td>67.9%</td>
                <td>28</td>
                <td>1.04</td>
                <td>0.00</td>
                <td>7.39%</td>
                <td>29.3</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 812,700<br>
            • Backtest Range: 300 to 813,000<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
