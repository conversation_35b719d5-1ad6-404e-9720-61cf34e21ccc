
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 7 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 17:08:44</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">7</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">43.8%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">8.9%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">15.0%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">69.5%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">1,703</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["11.5%","15.0%","11.0%","9.5%","6.9%","1.7%","6.9%"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","AI Rule 10: Composite Sentiment Reversal","Rule 6: Stochastic Oversold Cross","Professional Rule 10: CCI Reversal Enhanced","Volume Rule 5: Smart Money Volume","Price Action Rule 3: Engulfing Pattern"],"y":[11.455179343703465,14.984089956852287,10.991557579119297,9.53668811839774,6.9329170231458415,1.6994464415248514,6.923028389344676],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","AI Rule 10: Composite Sentiment Reversal","Rule 6: Stochastic Oversold Cross","Professional Rule 10: CCI Reversal Enhanced","Volume Rule 5: Smart Money Volume","Price Action Rule 3: Engulfing Pattern"],"y":[75,385,497,64,25,29,9],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","AI Rule 10: Composite Sentiment Reversal","Rule 6: Stochastic Oversold Cross","Professional Rule 10: CCI Reversal Enhanced","Volume Rule 5: Smart Money Volume","Price Action Rule 3: Engulfing Pattern"],"y":[34,227,298,33,11,15,1],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[11.455179343703465,14.984089956852287,10.991557579119297,9.53668811839774,6.9329170231458415,1.6994464415248514,6.923028389344676],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["Prof Rule 7","Rule 7","AI Rule 10","Rule 6","Professional Rule 10","Volume Rule 5","Price Action Rule 3"],"textposition":"top center","x":[12.12804621210469,40.61754810529939,32.19507808303527,8.775156666492666,4.738248244746644,6.609257080156575,1.3086639511127782],"y":[11.455179343703465,14.984089956852287,10.991557579119297,9.53668811839774,6.9329170231458415,1.6994464415248514,6.923028389344676],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["11.5%","12.3%","11.0%","5.2%"],"textposition":"auto","x":["PROFESSIONAL","ORIGINAL","AI_GENERATED","UNKNOWN"],"y":[11.455179343703465,12.260389037625014,10.991557579119297,5.185130618005123],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["109","612","795","97","36","44","10"],"textposition":"auto","x":["Prof Rule 7: Mean Reversion Volatility Filter","Rule 7: Bollinger Band Bounce","AI Rule 10: Composite Sentiment Reversal","Rule 6: Stochastic Oversold Cross","Professional Rule 10: CCI Reversal Enhanced","Volume Rule 5: Smart Money Volume","Price Action Rule 3: Engulfing Pattern"],"y":[109,612,795,97,36,44,10],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109],"y":[0,0.10509338847434373,0.21018677694868745,0.31528016542303117,0.4203735538973749,0.5254669423717186,0.6305603308460623,0.735653719320406,0.8407471077947498,0.9458404962690935,1.0509338847434373,1.156027273217781,1.2611206616921247,1.3662140501664684,1.471307438640812,1.5764008271151557,1.6814942155894996,1.7865876040638433,1.891680992538187,1.9967743810125307,2.1018677694868746,2.206961157961218,2.312054546435562,2.4171479349099054,2.5222413233842493,2.6273347118585932,2.7324281003329367,2.8375214888072806,2.942614877281624,3.047708265755968,3.1528016542303114,3.2578950427046554,3.3629884311789993,3.4680818196533427,3.5731752081276866,3.67826859660203,3.783361985076374,3.888455373550718,3.9935487620250614,4.098642150499405,4.203735538973749,4.308828927448093,4.413922315922436,4.5190157043967805,4.624109092871124,4.729202481345467,4.834295869819811,4.939389258294155,5.044482646768499,5.149576035242842,5.2546694237171865,5.35976281219153,5.464856200665873,5.569949589140217,5.675042977614561,5.780136366088904,5.885229754563248,5.990323143037592,6.095416531511936,6.2005099199862785,6.305603308460623,6.410696696934966,6.515790085409311,6.620883473883654,6.7259768623579985,6.831070250832341,6.9361636393066854,7.041257027781029,7.146350416255373,7.251443804729716,7.35653719320406,7.461630581678404,7.566723970152748,7.6718173586270915,7.776910747101436,7.882004135575778,7.987097524050123,8.092190912524465,8.19728430099881,8.302377689473154,8.407471077947498,8.512564466421841,8.617657854896185,8.722751243370528,8.827844631844872,8.932938020319217,9.038031408793561,9.143124797267904,9.248218185742248,9.35331157421659,9.458404962690935,9.563498351165277,9.668591739639622,9.773685128113966,9.87877851658831,9.983871905062653,10.088965293536997,10.19405868201134,10.299152070485684,10.404245458960029,10.509338847434373,10.614432235908716,10.71952562438306,10.824619012857402,10.929712401331747,11.03480578980609,11.139899178280434,11.244992566754778,11.350085955229122,11.455179343703465],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612],"y":[0,0.024483807119039684,0.04896761423807937,0.07345142135711906,0.09793522847615874,0.12241903559519843,0.14690284271423812,0.17138664983327778,0.19587045695231747,0.22035426407135716,0.24483807119039686,0.2693218783094365,0.29380568542847624,0.31828949254751593,0.34277329966655556,0.36725710678559526,0.39174091390463495,0.41622472102367464,0.44070852814271433,0.465192335261754,0.4896761423807937,0.5141599494998335,0.538643756618873,0.5631275637379127,0.5876113708569525,0.6120951779759921,0.6365789850950319,0.6610627922140715,0.6855465993331111,0.7100304064521509,0.7345142135711905,0.7589980206902303,0.7834818278092699,0.8079656349283096,0.8324494420473493,0.8569332491663889,0.8814170562854287,0.9059008634044683,0.930384670523508,0.9548684776425477,0.9793522847615874,1.003836091880627,1.028319898999667,1.0528037061187066,1.077287513237746,1.1017713203567858,1.1262551274758255,1.1507389345948653,1.175222741713905,1.1997065488329444,1.2241903559519842,1.2486741630710239,1.2731579701900637,1.2976417773091034,1.322125584428143,1.3466093915471826,1.3710931986662223,1.3955770057852621,1.4200608129043018,1.4445446200233414,1.469028427142381,1.4935122342614209,1.5179960413804605,1.5424798484995002,1.5669636556185398,1.5914474627375794,1.6159312698566193,1.640415076975659,1.6648988840946986,1.6893826912137382,1.7138664983327778,1.7383503054518177,1.7628341125708573,1.7873179196898972,1.8118017268089366,1.8362855339279762,1.860769341047016,1.8852531481660557,1.9097369552850954,1.9342207624041352,1.9587045695231748,1.9831883766422145,2.007672183761254,2.0321559908802937,2.056639797999334,2.0811236051183735,2.105607412237413,2.1300912193564523,2.154575026475492,2.179058833594532,2.2035426407135716,2.2280264478326113,2.252510254951651,2.2769940620706905,2.3014778691897306,2.3259616763087703,2.35044548342781,2.3749292905468495,2.3994130976658887,2.423896904784929,2.4483807119039684,2.472864519023008,2.4973483261420477,2.521832133261088,2.5463159403801274,2.570799747499167,2.5952835546182067,2.6197673617372463,2.644251168856286,2.6687349759753256,2.6932187830943652,2.717702590213405,2.7421863973324445,2.7666702044514846,2.7911540115705242,2.815637818689564,2.8401216258086035,2.864605432927643,2.8890892400466828,2.9135730471657224,2.938056854284762,2.9625406614038017,2.9870244685228418,3.0115082756418814,3.035992082760921,3.0604758898799607,3.0849596969990003,3.1094435041180404,3.1339273112370796,3.158411118356119,3.182894925475159,3.2073787325941985,3.2318625397132386,3.256346346832278,3.280830153951318,3.3053139610703575,3.329797768189397,3.354281575308437,3.3787653824274764,3.403249189546516,3.4277329966655556,3.4522168037845953,3.4767006109036354,3.501184418022675,3.5256682251417146,3.5501520322607543,3.5746358393797943,3.599119646498834,3.623603453617873,3.648087260736913,3.6725710678559524,3.6970548749749925,3.721538682094032,3.746022489213072,3.7705062963321114,3.794990103451151,3.8194739105701907,3.8439577176892303,3.8684415248082704,3.89292533192731,3.9174091390463497,3.9418929461653893,3.966376753284429,3.9908605604034686,4.015344367522508,4.039828174641547,4.0643119817605875,4.088795788879627,4.113279595998668,4.137763403117707,4.162247210236747,4.186731017355786,4.211214824474826,4.235698631593865,4.260182438712905,4.284666245831945,4.309150052950984,4.333633860070025,4.358117667189064,4.382601474308104,4.407085281427143,4.431569088546183,4.456052895665223,4.480536702784263,4.505020509903302,4.529504317022341,4.553988124141381,4.578471931260421,4.602955738379461,4.6274395454985005,4.6519233526175405,4.67640715973658,4.70089096685562,4.725374773974659,4.749858581093699,4.774342388212738,4.7988261953317775,4.823310002450818,4.847793809569858,4.872277616688898,4.896761423807937,4.921245230926977,4.945729038046016,4.970212845165056,4.994696652284095,5.019180459403135,5.043664266522176,5.068148073641215,5.092631880760255,5.117115687879294,5.141599494998334,5.166083302117373,5.190567109236413,5.215050916355453,5.239534723474493,5.264018530593532,5.288502337712572,5.312986144831612,5.337469951950651,5.361953759069691,5.3864375661887305,5.410921373307771,5.43540518042681,5.45988898754585,5.484372794664889,5.508856601783929,5.533340408902969,5.557824216022008,5.5823080231410485,5.606791830260088,5.631275637379128,5.655759444498167,5.680243251617207,5.704727058736246,5.729210865855286,5.753694672974326,5.7781784800933655,5.802662287212406,5.827146094331445,5.851629901450485,5.876113708569524,5.900597515688564,5.925081322807603,5.949565129926643,5.9740489370456835,5.998532744164723,6.023016551283763,6.047500358402802,6.071984165521842,6.096467972640881,6.120951779759921,6.1454355868789605,6.169919393998001,6.19440320111704,6.218887008236081,6.24337081535512,6.267854622474159,6.292338429593199,6.316822236712238,6.3413060438312785,6.365789850950318,6.390273658069358,6.414757465188397,6.439241272307438,6.463725079426477,6.488208886545516,6.512692693664556,6.537176500783596,6.561660307902636,6.586144115021675,6.610627922140715,6.635111729259754,6.659595536378794,6.684079343497834,6.708563150616874,6.7330469577359136,6.757530764854953,6.782014571973993,6.806498379093032,6.830982186212072,6.855465993331111,6.879949800450151,6.904433607569191,6.9289174146882315,6.953401221807271,6.977885028926311,7.00236883604535,7.026852643164389,7.051336450283429,7.0758202574024684,7.1003040645215085,7.124787871640548,7.149271678759589,7.173755485878628,7.198239292997668,7.222723100116707,7.247206907235746,7.271690714354786,7.296174521473826,7.320658328592866,7.345142135711905,7.369625942830945,7.394109749949985,7.418593557069025,7.443077364188064,7.467561171307104,7.492044978426144,7.516528785545183,7.541012592664223,7.565496399783262,7.589980206902302,7.614464014021341,7.638947821140381,7.663431628259421,7.687915435378461,7.7123992424975,7.736883049616541,7.761366856735581,7.78585066385462,7.81033447097366,7.834818278092699,7.8593020852117395,7.883785892330779,7.908269699449819,7.932753506568858,7.957237313687898,7.981721120806937,8.006204927925976,8.030688735045016,8.055172542164057,8.079656349283095,8.104140156402135,8.128623963521175,8.153107770640215,8.177591577759253,8.202075384878295,8.226559191997335,8.251042999116374,8.275526806235414,8.300010613354454,8.324494420473494,8.348978227592532,8.373462034711572,8.397945841830612,8.422429648949652,8.44691345606869,8.47139726318773,8.49588107030677,8.52036487742581,8.54484868454485,8.56933249166389,8.59381629878293,8.618300105901968,8.642783913021008,8.66726772014005,8.69175152725909,8.716235334378128,8.740719141497168,8.765202948616208,8.789686755735246,8.814170562854287,8.838654369973327,8.863138177092367,8.887621984211405,8.912105791330445,8.936589598449485,8.961073405568525,8.985557212687564,9.010041019806604,9.034524826925644,9.059008634044682,9.083492441163722,9.107976248282762,9.132460055401804,9.156943862520842,9.181427669639882,9.205911476758923,9.23039528387796,9.254879090997001,9.279362898116041,9.303846705235081,9.32833051235412,9.35281431947316,9.3772981265922,9.40178193371124,9.426265740830278,9.450749547949318,9.475233355068358,9.499717162187398,9.524200969306436,9.548684776425477,9.573168583544517,9.597652390663555,9.622136197782597,9.646620004901637,9.671103812020677,9.695587619139715,9.720071426258755,9.744555233377795,9.769039040496834,9.793522847615874,9.818006654734914,9.842490461853954,9.866974268972992,9.891458076092032,9.915941883211072,9.940425690330112,9.96490949744915,9.98939330456819,10.013877111687231,10.03836091880627,10.06284472592531,10.087328533044351,10.111812340163391,10.13629614728243,10.16077995440147,10.18526376152051,10.20974756863955,10.234231375758588,10.258715182877628,10.283198989996668,10.307682797115707,10.332166604234747,10.356650411353787,10.381134218472827,10.405618025591865,10.430101832710905,10.454585639829945,10.479069446948985,10.503553254068024,10.528037061187064,10.552520868306106,10.577004675425144,10.601488482544184,10.625972289663224,10.650456096782264,10.674939903901302,10.699423711020343,10.723907518139383,10.748391325258421,10.772875132377461,10.797358939496501,10.821842746615541,10.84632655373458,10.87081036085362,10.89529416797266,10.9197779750917,10.944261782210738,10.968745589329778,10.993229396448818,11.017713203567858,11.042197010686898,11.066680817805938,11.091164624924978,11.115648432044017,11.140132239163057,11.164616046282097,11.189099853401137,11.213583660520175,11.238067467639215,11.262551274758255,11.287035081877294,11.311518888996334,11.336002696115374,11.360486503234414,11.384970310353452,11.409454117472492,11.433937924591532,11.458421731710573,11.482905538829613,11.507389345948653,11.531873153067693,11.556356960186731,11.580840767305771,11.605324574424811,11.629808381543851,11.65429218866289,11.67877599578193,11.70325980290097,11.72774361002001,11.752227417139048,11.776711224258088,11.801195031377128,11.825678838496167,11.850162645615207,11.874646452734247,11.899130259853287,11.923614066972325,11.948097874091367,11.972581681210407,11.997065488329445,12.021549295448485,12.046033102567526,12.070516909686566,12.095000716805604,12.119484523924644,12.143968331043684,12.168452138162724,12.192935945281762,12.217419752400803,12.241903559519843,12.266387366638881,12.290871173757921,12.315354980876961,12.339838787996001,12.36432259511504,12.38880640223408,12.413290209353121,12.437774016472162,12.4622578235912,12.48674163071024,12.51122543782928,12.535709244948318,12.560193052067358,12.584676859186398,12.609160666305439,12.633644473424477,12.658128280543517,12.682612087662557,12.707095894781597,12.731579701900635,12.756063509019675,12.780547316138716,12.805031123257754,12.829514930376794,12.853998737495834,12.878482544614876,12.902966351733914,12.927450158852954,12.951933965971994,12.976417773091033,13.000901580210073,13.025385387329113,13.049869194448153,13.074353001567191,13.098836808686231,13.123320615805271,13.147804422924311,13.17228823004335,13.19677203716239,13.22125584428143,13.24573965140047,13.270223458519508,13.294707265638548,13.319191072757588,13.343674879876627,13.368158686995669,13.392642494114709,13.417126301233749,13.441610108352787,13.466093915471827,13.490577722590867,13.515061529709905,13.539545336828946,13.564029143947986,13.588512951067026,13.612996758186064,13.637480565305104,13.661964372424144,13.686448179543184,13.710931986662223,13.735415793781263,13.759899600900303,13.784383408019341,13.808867215138381,13.833351022257423,13.857834829376463,13.882318636495501,13.906802443614541,13.931286250733582,13.955770057852622,13.98025386497166,14.0047376720907,14.02922147920974,14.053705286328778,14.078189093447818,14.102672900566859,14.127156707685899,14.151640514804937,14.176124321923977,14.200608129043017,14.225091936162057,14.249575743281095,14.274059550400136,14.298543357519177,14.323027164638216,14.347510971757256,14.371994778876296,14.396478585995336,14.420962393114374,14.445446200233414,14.469930007352454,14.494413814471493,14.518897621590533,14.543381428709573,14.567865235828613,14.592349042947651,14.616832850066691,14.641316657185731,14.665800464304771,14.69028427142381,14.71476807854285,14.73925188566189,14.76373569278093,14.78821949989997,14.81270330701901,14.83718711413805,14.861670921257089,14.886154728376129,14.910638535495169,14.935122342614209,14.959606149733247,14.984089956852287],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795],"y":[0,0.013825858590087165,0.02765171718017433,0.041477575770261496,0.05530343436034866,0.06912929295043584,0.08295515154052299,0.09678101013061016,0.11060686872069732,0.12443272731078449,0.13825858590087167,0.15208444449095881,0.16591030308104598,0.17973616167113315,0.19356202026122032,0.20738787885130747,0.22121373744139464,0.2350395960314818,0.24886545462156898,0.2626913132116562,0.27651717180174334,0.2903430303918305,0.30416888898191763,0.3179947475720048,0.33182060616209197,0.34564646475217914,0.3594723233422663,0.3732981819323535,0.38712404052244065,0.4009498991125278,0.41477575770261493,0.4286016162927021,0.4424274748827893,0.45625333347287644,0.4700791920629636,0.4839050506530508,0.49773090924313795,0.5115567678332251,0.5253826264233123,0.5392084850133995,0.5530343436034867,0.5668602021935738,0.580686060783661,0.5945119193737481,0.6083377779638353,0.6221636365539225,0.6359894951440096,0.6498153537340968,0.6636412123241839,0.6774670709142712,0.6912929295043583,0.7051187880944455,0.7189446466845326,0.7327705052746197,0.746596363864707,0.7604222224547941,0.7742480810448813,0.7880739396349684,0.8018997982250556,0.8157256568151428,0.8295515154052299,0.8433773739953171,0.8572032325854042,0.8710290911754914,0.8848549497655785,0.8986808083556658,0.9125066669457529,0.92633252553584,0.9401583841259272,0.9539842427160143,0.9678101013061016,0.9816359598961887,0.9954618184862759,1.009287677076363,1.0231135356664502,1.0369393942565375,1.0507652528466247,1.064591111436712,1.078416970026799,1.0922428286168862,1.1060686872069734,1.1198945457970604,1.1337204043871476,1.1475462629772348,1.161372121567322,1.175197980157409,1.1890238387474963,1.2028496973375835,1.2166755559276705,1.2305014145177577,1.244327273107845,1.2581531316979322,1.2719789902880192,1.2858048488781064,1.2996307074681936,1.3134565660582807,1.3272824246483679,1.341108283238455,1.3549341418285423,1.3687600004186293,1.3825858590087166,1.3964117175988038,1.410237576188891,1.424063434778978,1.4378892933690652,1.4517151519591525,1.4655410105492395,1.4793668691393267,1.493192727729414,1.5070185863195011,1.5208444449095881,1.5346703034996754,1.5484961620897626,1.5623220206798496,1.5761478792699368,1.589973737860024,1.6037995964501113,1.6176254550401983,1.6314513136302855,1.6452771722203727,1.6591030308104597,1.672928889400547,1.6867547479906342,1.7005806065807214,1.7144064651708084,1.7282323237608956,1.7420581823509829,1.7558840409410699,1.769709899531157,1.7835357581212443,1.7973616167113315,1.8111874753014185,1.8250133338915058,1.838839192481593,1.85266505107168,1.8664909096617672,1.8803167682518545,1.8941426268419417,1.9079684854320287,1.921794344022116,1.9356202026122031,1.9494460612022901,1.9632719197923774,1.9770977783824646,1.9909236369725518,2.004749495562639,2.018575354152726,2.0324012127428133,2.0462270713329005,2.0600529299229877,2.073878788513075,2.087704647103162,2.1015305056932494,2.1153563642833366,2.129182222873424,2.1430080814635106,2.156833940053598,2.170659798643685,2.1844856572337723,2.1983115158238595,2.2121373744139468,2.225963233004034,2.2397890915941208,2.253614950184208,2.267440808774295,2.2812666673643824,2.2950925259544697,2.308918384544557,2.322744243134644,2.336570101724731,2.350395960314818,2.3642218189049053,2.3780476774949926,2.39187353608508,2.405699394675167,2.4195252532652542,2.433351111855341,2.4471769704454283,2.4610028290355155,2.4748286876256027,2.48865454621569,2.502480404805777,2.5163062633958644,2.530132121985951,2.5439579805760384,2.5577838391661256,2.571609697756213,2.5854355563463,2.5992614149363873,2.6130872735264745,2.6269131321165613,2.6407389907066485,2.6545648492967358,2.668390707886823,2.68221656647691,2.6960424250669974,2.7098682836570847,2.7236941422471714,2.7375200008372587,2.751345859427346,2.765171718017433,2.7789975766075203,2.7928234351976076,2.806649293787695,2.820475152377782,2.834301010967869,2.848126869557956,2.8619527281480432,2.8757785867381305,2.8896044453282177,2.903430303918305,2.917256162508392,2.931082021098479,2.944907879688566,2.9587337382786534,2.9725595968687406,2.986385455458828,3.000211314048915,3.0140371726390023,3.027863031229089,3.0416888898191763,3.0555147484092635,3.0693406069993507,3.083166465589438,3.096992324179525,3.1108181827696124,3.124644041359699,3.1384698999497864,3.1522957585398736,3.166121617129961,3.179947475720048,3.1937733343101353,3.2075991929002226,3.2214250514903093,3.2352509100803966,3.249076768670484,3.262902627260571,3.2767284858506582,3.2905543444407455,3.3043802030308327,3.3182060616209195,3.3320319202110067,3.345857778801094,3.359683637391181,3.3735094959812684,3.3873353545713556,3.401161213161443,3.4149870717515296,3.428812930341617,3.442638788931704,3.4564646475217913,3.4702905061118785,3.4841163647019657,3.497942223292053,3.5117680818821397,3.525593940472227,3.539419799062314,3.5532456576524014,3.5670715162424886,3.580897374832576,3.594723233422663,3.60854909201275,3.622374950602837,3.6362008091929243,3.6500266677830115,3.6638525263730988,3.677678384963186,3.6915042435532732,3.70533010214336,3.7191559607334472,3.7329818193235345,3.7468076779136217,3.760633536503709,3.774459395093796,3.7882852536838834,3.80211111227397,3.8159369708640574,3.8297628294541446,3.843588688044232,3.857414546634319,3.8712404052244063,3.8850662638144935,3.8988921224045803,3.9127179809946675,3.9265438395847547,3.940369698174842,3.954195556764929,3.9680214153550164,3.9818472739451036,3.9956731325351904,4.009498991125278,4.023324849715365,4.037150708305452,4.050976566895539,4.0648024254856265,4.078628284075713,4.092454142665801,4.106280001255888,4.120105859845975,4.133931718436063,4.14775757702615,4.161583435616238,4.175409294206324,4.189235152796411,4.203061011386499,4.216886869976586,4.230712728566673,4.24453858715676,4.258364445746848,4.2721903043369345,4.286016162927021,4.299842021517109,4.313667880107196,4.327493738697283,4.34131959728737,4.355145455877458,4.368971314467545,4.382797173057631,4.396623031647719,4.410448890237806,4.4242747488278935,4.43810060741798,4.451926466008068,4.465752324598155,4.4795781831882415,4.493404041778329,4.507229900368416,4.521055758958504,4.53488161754859,4.548707476138678,4.562533334728765,4.576359193318852,4.590185051908939,4.604010910499026,4.617836769089114,4.631662627679201,4.645488486269288,4.659314344859375,4.673140203449462,4.6869660620395495,4.700791920629636,4.714617779219724,4.728443637809811,4.742269496399898,4.756095354989985,4.769921213580072,4.78374707217016,4.797572930760246,4.811398789350334,4.825224647940421,4.8390505065305085,4.852876365120595,4.866702223710682,4.88052808230077,4.8943539408908565,4.908179799480944,4.922005658071031,4.935831516661119,4.949657375251205,4.963483233841292,4.97730909243138,4.991134951021467,5.004960809611554,5.018786668201641,5.032612526791729,5.0464383853818156,5.060264243971902,5.07409010256199,5.087915961152077,5.1017418197421645,5.115567678332251,5.129393536922339,5.143219395512426,5.1570452541025125,5.1708711126926,5.184696971282687,5.198522829872775,5.212348688462861,5.226174547052949,5.240000405643036,5.253826264233123,5.26765212282321,5.281477981413297,5.295303840003385,5.3091296985934715,5.322955557183559,5.336781415773646,5.350607274363733,5.36443313295382,5.378258991543907,5.392084850133995,5.405910708724082,5.419736567314169,5.433562425904256,5.447388284494343,5.4612141430844305,5.475040001674517,5.488865860264605,5.502691718854692,5.516517577444779,5.530343436034866,5.544169294624954,5.557995153215041,5.5718210118051275,5.585646870395215,5.599472728985302,5.61329858757539,5.627124446165476,5.640950304755564,5.654776163345651,5.668602021935738,5.682427880525825,5.696253739115912,5.710079597706,5.7239054562960865,5.737731314886174,5.751557173476261,5.765383032066348,5.779208890656435,5.793034749246522,5.80686060783661,5.820686466426697,5.834512325016784,5.848338183606871,5.862164042196958,5.8759899007870455,5.889815759377132,5.90364161796722,5.917467476557307,5.931293335147394,5.945119193737481,5.958945052327568,5.972770910917656,5.986596769507742,6.00042262809783,6.014248486687917,6.028074345278005,6.041900203868091,6.055726062458178,6.069551921048266,6.083377779638353,6.09720363822844,6.111029496818527,6.124855355408615,6.1386812139987015,6.152507072588788,6.166332931178876,6.180158789768963,6.19398464835905,6.207810506949137,6.221636365539225,6.235462224129312,6.249288082719398,6.263113941309486,6.276939799899573,6.2907656584896605,6.304591517079747,6.318417375669835,6.332243234259922,6.3460690928500085,6.359894951440096,6.373720810030183,6.387546668620271,6.401372527210357,6.415198385800445,6.429024244390532,6.442850102980619,6.456675961570706,6.470501820160793,6.484327678750881,6.498153537340968,6.511979395931055,6.525805254521142,6.539631113111229,6.5534569717013165,6.567282830291403,6.581108688881491,6.594934547471578,6.608760406061665,6.622586264651752,6.636412123241839,6.650237981831927,6.664063840422013,6.677889699012101,6.691715557602188,6.7055414161922755,6.719367274782362,6.733193133372449,6.747018991962537,6.7608448505526235,6.774670709142711,6.788496567732798,6.802322426322886,6.816148284912972,6.829974143503059,6.843800002093147,6.857625860683234,6.871451719273321,6.885277577863408,6.899103436453496,6.912929295043583,6.926755153633669,6.940581012223757,6.954406870813844,6.9682327294039315,6.982058587994018,6.995884446584106,7.009710305174193,7.0235361637642795,7.037362022354367,7.051187880944454,7.065013739534542,7.078839598124628,7.092665456714716,7.106491315304803,7.12031717389489,7.134143032484977,7.147968891075064,7.161794749665152,7.1756206082552385,7.189446466845326,7.203272325435413,7.2170981840255,7.230924042615587,7.244749901205674,7.258575759795762,7.272401618385849,7.286227476975936,7.300053335566023,7.31387919415611,7.3277050527461975,7.341530911336284,7.355356769926372,7.369182628516459,7.3830084871065464,7.396834345696633,7.41066020428672,7.424486062876808,7.4383119214668945,7.452137780056982,7.465963638647069,7.479789497237157,7.493615355827243,7.50744121441733,7.521267073007418,7.535092931597505,7.548918790187592,7.562744648777679,7.576570507367767,7.5903963659578535,7.60422222454794,7.618048083138028,7.631873941728115,7.645699800318202,7.659525658908289,7.673351517498377,7.687177376088464,7.70100323467855,7.714829093268638,7.728654951858725,7.7424808104488125,7.756306669038899,7.770132527628987,7.783958386219074,7.7977842448091605,7.811610103399248,7.825435961989335,7.839261820579423,7.8530876791695094,7.866913537759597,7.880739396349684,7.894565254939771,7.908391113529858,7.922216972119945,7.936042830710033,7.94986868930012,7.963694547890207,7.977520406480294,7.991346265070381,8.005172123660468,8.018997982250555,8.032823840840642,8.04664969943073,8.060475558020817,8.074301416610904,8.088127275200991,8.101953133791078,8.115778992381166,8.129604850971253,8.14343070956134,8.157256568151427,8.171082426741515,8.184908285331602,8.198734143921689,8.212560002511776,8.226385861101862,8.24021171969195,8.25403757828204,8.267863436872126,8.281689295462213,8.2955151540523,8.309341012642387,8.323166871232475,8.336992729822562,8.350818588412649,8.364644447002735,8.378470305592822,8.39229616418291,8.406122022772998,8.419947881363084,8.433773739953171,8.44759959854326,8.461425457133346,8.475251315723433,8.48907717431352,8.502903032903607,8.516728891493695,8.530554750083782,8.544380608673869,8.558206467263956,8.572032325854043,8.585858184444131,8.599684043034218,8.613509901624305,8.627335760214391,8.64116161880448,8.654987477394567,8.668813335984654,8.68263919457474,8.696465053164827,8.710290911754916,8.724116770345002,8.73794262893509,8.751768487525176,8.765594346115263,8.779420204705351,8.793246063295438,8.807071921885525,8.820897780475612,8.8347236390657,8.848549497655787,8.862375356245874,8.87620121483596,8.890027073426047,8.903852932016136,8.917678790606223,8.93150464919631,8.945330507786396,8.959156366376483,8.972982224966572,8.986808083556658,9.000633942146745,9.014459800736832,9.02828565932692,9.042111517917007,9.055937376507094,9.06976323509718,9.083589093687268,9.097414952277356,9.111240810867443,9.12506666945753,9.138892528047617,9.152718386637703,9.166544245227792,9.180370103817879,9.194195962407965,9.208021820998052,9.22184767958814,9.235673538178228,9.249499396768314,9.263325255358401,9.277151113948488,9.290976972538576,9.304802831128663,9.31862868971875,9.332454548308837,9.346280406898924,9.360106265489012,9.373932124079099,9.387757982669186,9.401583841259272,9.415409699849361,9.429235558439448,9.443061417029535,9.456887275619621,9.470713134209708,9.484538992799797,9.498364851389884,9.51219070997997,9.526016568570057,9.539842427160144,9.553668285750232,9.56749414434032,9.581320002930406,9.595145861520493,9.608971720110581,9.622797578700668,9.636623437290755,9.650449295880842,9.664275154470928,9.678101013061017,9.691926871651104,9.70575273024119,9.719578588831277,9.733404447421364,9.747230306011453,9.76105616460154,9.774882023191626,9.788707881781713,9.802533740371802,9.816359598961888,9.830185457551975,9.844011316142062,9.857837174732149,9.871663033322237,9.885488891912324,9.89931475050241,9.913140609092498,9.926966467682584,9.940792326272673,9.95461818486276,9.968444043452847,9.982269902042933,9.996095760633022,10.009921619223109,10.023747477813195,10.037573336403282,10.051399194993369,10.065225053583458,10.079050912173544,10.092876770763631,10.106702629353718,10.120528487943805,10.134354346533893,10.14818020512398,10.162006063714067,10.175831922304154,10.189657780894242,10.203483639484329,10.217309498074416,10.231135356664502,10.24496121525459,10.258787073844678,10.272612932434765,10.286438791024851,10.300264649614938,10.314090508205025,10.327916366795113,10.3417422253852,10.355568083975287,10.369393942565374,10.383219801155462,10.39704565974555,10.410871518335636,10.424697376925723,10.43852323551581,10.452349094105898,10.466174952695985,10.480000811286072,10.493826669876158,10.507652528466245,10.521478387056334,10.53530424564642,10.549130104236507,10.562955962826594,10.576781821416683,10.59060768000677,10.604433538596856,10.618259397186943,10.63208525577703,10.645911114367118,10.659736972957205,10.673562831547292,10.687388690137379,10.701214548727465,10.715040407317554,10.72886626590764,10.742692124497728,10.756517983087814,10.770343841677903,10.78416970026799,10.797995558858076,10.811821417448163,10.82564727603825,10.839473134628339,10.853298993218425,10.867124851808512,10.880950710398599,10.894776568988686,10.908602427578774,10.922428286168861,10.936254144758948,10.950080003349035,10.963905861939123,10.97773172052921,10.991557579119297],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97],"y":[0,0.09831637235461586,0.19663274470923173,0.29494911706384763,0.39326548941846345,0.49158186177307933,0.5898982341276953,0.688214606482311,0.7865309788369269,0.8848473511915429,0.9831637235461587,1.0814800959007747,1.1797964682553905,1.2781128406100062,1.376429212964622,1.4747455853192382,1.5730619576738538,1.6713783300284697,1.7696947023830858,1.8680110747377014,1.9663274470923173,2.064643819446933,2.1629601918015493,2.261276564156165,2.359592936510781,2.4579093088653967,2.5562256812200124,2.6545420535746285,2.752858425929244,2.8511747982838602,2.9494911706384763,3.0478075429930915,3.1461239153477076,3.2444402877023237,3.3427566600569394,3.4410730324115555,3.5393894047661716,3.637705777120787,3.736022149475403,3.834338521830019,3.9326548941846347,4.03097126653925,4.129287638893866,4.2276040112484825,4.325920383603099,4.424236755957715,4.52255312831233,4.620869500666946,4.719185873021562,4.817502245376177,4.915818617730793,5.0141349900854095,5.112451362440025,5.210767734794641,5.309084107149257,5.407400479503873,5.505716851858488,5.604033224213105,5.7023495965677204,5.800665968922336,5.898982341276953,5.997298713631568,6.095615085986183,6.1939314583408,6.292247830695415,6.390564203050031,6.4888805754046475,6.587196947759264,6.685513320113879,6.783829692468495,6.882146064823111,6.980462437177726,7.078778809532343,7.177095181886958,7.275411554241574,7.373727926596191,7.472044298950806,7.570360671305422,7.668677043660038,7.766993416014654,7.865309788369269,7.963626160723885,8.0619425330785,8.160258905433118,8.258575277787733,8.35689165014235,8.455208022496965,8.55352439485158,8.651840767206197,8.750157139560812,8.84847351191543,8.946789884270045,9.04510625662466,9.143422628979277,9.241739001333892,9.340055373688507,9.438371746043124,9.53668811839774],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Professional Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],"y":[0,0.1925810284207178,0.3851620568414356,0.5777430852621535,0.7703241136828712,0.9629051421035891,1.155486170524307,1.3480671989450248,1.5406482273657425,1.7332292557864604,1.9258102842071783,2.1183913126278964,2.310972341048614,2.5035533694693317,2.6961343978900496,2.8887154263107675,3.081296454731485,3.273877483152203,3.4664585115729207,3.6590395399936386,3.8516205684143565,4.044201596835075,4.236782625255793,4.42936365367651,4.621944682097228,4.8145257105179455,5.007106738938663,5.199687767359381,5.392268795780099,5.584849824200817,5.777430852621535,5.970011881042253,6.16259290946297,6.355173937883688,6.547754966304406,6.740335994725124,6.9329170231458415],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">11.46%</td>
                <td>68.8%</td>
                <td>109</td>
                <td>1.21</td>
                <td>0.00</td>
                <td>12.13%</td>
                <td>55.2</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">14.98%</td>
                <td>62.9%</td>
                <td>612</td>
                <td>1.05</td>
                <td>0.00</td>
                <td>40.62%</td>
                <td>54.9</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">10.99%</td>
                <td>62.5%</td>
                <td>795</td>
                <td>1.03</td>
                <td>0.00</td>
                <td>32.20%</td>
                <td>53.2</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">9.54%</td>
                <td>67.0%</td>
                <td>97</td>
                <td>1.21</td>
                <td>0.00</td>
                <td>8.78%</td>
                <td>53.0</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">6.93%</td>
                <td>69.4%</td>
                <td>36</td>
                <td>1.46</td>
                <td>0.00</td>
                <td>4.74%</td>
                <td>34.4</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">1.70%</td>
                <td>65.9%</td>
                <td>44</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>6.61%</td>
                <td>33.7</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">6.92%</td>
                <td>90.0%</td>
                <td>10</td>
                <td>6.15</td>
                <td>0.00</td>
                <td>1.31%</td>
                <td>32.8</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
