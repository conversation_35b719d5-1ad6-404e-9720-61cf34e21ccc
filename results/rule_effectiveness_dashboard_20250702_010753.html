
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 01:07:53 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">-176.41%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">15,151</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">61.1%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.41</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>30.85%</strong></td>
                    <td>63.2%</td>
                    <td>1856</td>
                    <td>1.04</td>
                    <td class="negative">30.67%</td>
                    <td class="positive"><strong>0.8639</strong></td>
                    <td class="negative">+0.86% / -1.40%</td>
                    <td>2h34m<br><small>(1.0m - 49h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>25.26%</strong></td>
                    <td>63.8%</td>
                    <td>961</td>
                    <td>1.06</td>
                    <td class="negative">15.05%</td>
                    <td class="positive"><strong>0.8116</strong></td>
                    <td class="negative">+0.87% / -1.42%</td>
                    <td>1h56m<br><small>(1.0m - 32h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>25.17%</strong></td>
                    <td>63.0%</td>
                    <td>2096</td>
                    <td>1.03</td>
                    <td class="negative">33.34%</td>
                    <td class="positive"><strong>0.8529</strong></td>
                    <td class="negative">+0.88% / -1.45%</td>
                    <td>1h54m<br><small>(1.0m - 49h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>14.73%</strong></td>
                    <td>62.9%</td>
                    <td>1565</td>
                    <td>1.02</td>
                    <td class="negative">28.45%</td>
                    <td class="positive"><strong>0.8008</strong></td>
                    <td class="negative">+0.89% / -1.48%</td>
                    <td>1h51m<br><small>(1.0m - 49h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>5.61%</strong></td>
                    <td>65.6%</td>
                    <td>96</td>
                    <td>1.14</td>
                    <td class="negative">7.57%</td>
                    <td class="positive"><strong>0.5670</strong></td>
                    <td class="negative">+0.95% / -1.45%</td>
                    <td>1h26m<br><small>(1.0m - 12h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>3.96%</strong></td>
                    <td>60.7%</td>
                    <td>275</td>
                    <td>1.03</td>
                    <td class="negative">11.49%</td>
                    <td class="positive"><strong>0.6313</strong></td>
                    <td class="negative">+0.90% / -1.41%</td>
                    <td>2h14m<br><small>(1.0m - 21h24m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>3.52%</strong></td>
                    <td>62.5%</td>
                    <td>56</td>
                    <td>1.14</td>
                    <td class="neutral">4.53%</td>
                    <td class="positive"><strong>0.5096</strong></td>
                    <td class="negative">+0.90% / -1.37%</td>
                    <td>3h8m<br><small>(2.0m - 31h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>3.51%</strong></td>
                    <td>63.7%</td>
                    <td>411</td>
                    <td>1.02</td>
                    <td class="negative">16.63%</td>
                    <td class="positive"><strong>0.6579</strong></td>
                    <td class="negative">+0.87% / -1.46%</td>
                    <td>2h2m<br><small>(1.0m - 49h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>3.41%</strong></td>
                    <td>63.4%</td>
                    <td>142</td>
                    <td>1.06</td>
                    <td class="negative">9.80%</td>
                    <td class="positive"><strong>0.5707</strong></td>
                    <td class="negative">+0.85% / -1.41%</td>
                    <td>2h7m<br><small>(1.0m - 17h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>1.85%</strong></td>
                    <td>61.0%</td>
                    <td>136</td>
                    <td>1.03</td>
                    <td class="negative">12.73%</td>
                    <td class="positive"><strong>0.5588</strong></td>
                    <td class="negative">+0.91% / -1.37%</td>
                    <td>1h52m<br><small>(1.0m - 14h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>1.53%</strong></td>
                    <td>100.0%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.81% / 0.00%</td>
                    <td>1h37m<br><small>(22.0m - 2h52m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>1.38%</strong></td>
                    <td>61.9%</td>
                    <td>291</td>
                    <td>1.01</td>
                    <td class="negative">17.54%</td>
                    <td class="positive"><strong>0.6137</strong></td>
                    <td class="negative">+0.89% / -1.46%</td>
                    <td>1h31m<br><small>(1.0m - 13h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="positive"><strong>0.94%</strong></td>
                    <td>66.7%</td>
                    <td>6</td>
                    <td>1.50</td>
                    <td class="positive">1.27%</td>
                    <td class="positive"><strong>0.3689</strong></td>
                    <td class="negative">+1.14% / -1.39%</td>
                    <td>2.7m<br><small>(1.0m - 5.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>0.15%</strong></td>
                    <td>57.7%</td>
                    <td>26</td>
                    <td>1.01</td>
                    <td class="neutral">4.43%</td>
                    <td class="positive"><strong>0.3911</strong></td>
                    <td class="negative">+0.95% / -1.41%</td>
                    <td>3h37m<br><small>(9.0m - 17h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="negative"><strong>-0.36%</strong></td>
                    <td>56.8%</td>
                    <td>37</td>
                    <td>0.98</td>
                    <td class="negative">7.41%</td>
                    <td class="positive"><strong>0.4138</strong></td>
                    <td class="negative">+1.01% / -1.45%</td>
                    <td>32.1m<br><small>(2.0m - 4h38m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="negative"><strong>-0.90%</strong></td>
                    <td>61.3%</td>
                    <td>168</td>
                    <td>0.99</td>
                    <td class="negative">14.48%</td>
                    <td class="positive"><strong>0.5642</strong></td>
                    <td class="negative">+0.94% / -1.39%</td>
                    <td>2h31m<br><small>(1.0m - 20h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="negative"><strong>-4.25%</strong></td>
                    <td>62.2%</td>
                    <td>1411</td>
                    <td>0.99</td>
                    <td class="negative">25.54%</td>
                    <td class="positive"><strong>0.7424</strong></td>
                    <td class="negative">+0.89% / -1.46%</td>
                    <td>2h3m<br><small>(1.0m - 49h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="negative"><strong>-4.27%</strong></td>
                    <td>60.7%</td>
                    <td>435</td>
                    <td>0.98</td>
                    <td class="negative">13.33%</td>
                    <td class="positive"><strong>0.6440</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>2h14m<br><small>(1.0m - 43h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-5.69%</strong></td>
                    <td>59.1%</td>
                    <td>66</td>
                    <td>0.82</td>
                    <td class="negative">13.02%</td>
                    <td class="positive"><strong>0.4164</strong></td>
                    <td class="negative">+0.88% / -1.36%</td>
                    <td>2h29m<br><small>(2.0m - 13h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="negative"><strong>-10.23%</strong></td>
                    <td>59.3%</td>
                    <td>270</td>
                    <td>0.92</td>
                    <td class="negative">15.52%</td>
                    <td class="positive"><strong>0.5611</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h23m<br><small>(1.0m - 31h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="negative"><strong>-17.78%</strong></td>
                    <td>56.7%</td>
                    <td>282</td>
                    <td>0.87</td>
                    <td class="negative">25.82%</td>
                    <td class="positive"><strong>0.5211</strong></td>
                    <td class="negative">+0.89% / -1.35%</td>
                    <td>3h5m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="negative"><strong>-20.39%</strong></td>
                    <td>57.8%</td>
                    <td>465</td>
                    <td>0.91</td>
                    <td class="negative">35.11%</td>
                    <td class="positive"><strong>0.5592</strong></td>
                    <td class="negative">+0.90% / -1.38%</td>
                    <td>2h37m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="negative"><strong>-26.32%</strong></td>
                    <td>56.3%</td>
                    <td>295</td>
                    <td>0.83</td>
                    <td class="negative">32.50%</td>
                    <td class="positive"><strong>0.4815</strong></td>
                    <td class="negative">+0.91% / -1.38%</td>
                    <td>2h7m<br><small>(1.0m - 24h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="negative"><strong>-29.95%</strong></td>
                    <td>56.6%</td>
                    <td>408</td>
                    <td>0.85</td>
                    <td class="negative">35.04%</td>
                    <td class="positive"><strong>0.5097</strong></td>
                    <td class="negative">+0.94% / -1.39%</td>
                    <td>1h54m<br><small>(1.0m - 24h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="negative"><strong>-52.80%</strong></td>
                    <td>57.8%</td>
                    <td>1115</td>
                    <td>0.90</td>
                    <td class="negative">59.50%</td>
                    <td class="positive"><strong>0.5295</strong></td>
                    <td class="negative">+0.93% / -1.38%</td>
                    <td>2h0m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="negative"><strong>-53.08%</strong></td>
                    <td>59.0%</td>
                    <td>1216</td>
                    <td>0.91</td>
                    <td class="negative">67.08%</td>
                    <td class="positive"><strong>0.5238</strong></td>
                    <td class="negative">+0.89% / -1.41%</td>
                    <td>2h24m<br><small>(1.0m - 45h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-72.26%</strong></td>
                    <td>57.6%</td>
                    <td>1064</td>
                    <td>0.86</td>
                    <td class="negative">74.76%</td>
                    <td class="positive"><strong>0.4385</strong></td>
                    <td class="negative">+0.90% / -1.41%</td>
                    <td>2h8m<br><small>(1.0m - 34h15m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.53%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.81% / 0.00%</td>
                    <td>1h37m<br><small>(22.0m - 2h52m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">0.94%</td>
                    <td>6</td>
                    <td>1.50</td>
                    <td class="positive">1.27%</td>
                    <td class="positive"><strong>0.3689</strong></td>
                    <td class="negative">+1.14% / -1.39%</td>
                    <td>2.7m<br><small>(1.0m - 5.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>65.6%</strong></td>
                    <td class="positive">5.61%</td>
                    <td>96</td>
                    <td>1.14</td>
                    <td class="negative">7.57%</td>
                    <td class="positive"><strong>0.5670</strong></td>
                    <td class="negative">+0.95% / -1.45%</td>
                    <td>1h26m<br><small>(1.0m - 12h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>63.8%</strong></td>
                    <td class="positive">25.26%</td>
                    <td>961</td>
                    <td>1.06</td>
                    <td class="negative">15.05%</td>
                    <td class="positive"><strong>0.8116</strong></td>
                    <td class="negative">+0.87% / -1.42%</td>
                    <td>1h56m<br><small>(1.0m - 32h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>63.7%</strong></td>
                    <td class="positive">3.51%</td>
                    <td>411</td>
                    <td>1.02</td>
                    <td class="negative">16.63%</td>
                    <td class="positive"><strong>0.6579</strong></td>
                    <td class="negative">+0.87% / -1.46%</td>
                    <td>2h2m<br><small>(1.0m - 49h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>63.4%</strong></td>
                    <td class="positive">3.41%</td>
                    <td>142</td>
                    <td>1.06</td>
                    <td class="negative">9.80%</td>
                    <td class="positive"><strong>0.5707</strong></td>
                    <td class="negative">+0.85% / -1.41%</td>
                    <td>2h7m<br><small>(1.0m - 17h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>63.2%</strong></td>
                    <td class="positive">30.85%</td>
                    <td>1856</td>
                    <td>1.04</td>
                    <td class="negative">30.67%</td>
                    <td class="positive"><strong>0.8639</strong></td>
                    <td class="negative">+0.86% / -1.40%</td>
                    <td>2h34m<br><small>(1.0m - 49h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>63.0%</strong></td>
                    <td class="positive">25.17%</td>
                    <td>2096</td>
                    <td>1.03</td>
                    <td class="negative">33.34%</td>
                    <td class="positive"><strong>0.8529</strong></td>
                    <td class="negative">+0.88% / -1.45%</td>
                    <td>1h54m<br><small>(1.0m - 49h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>62.9%</strong></td>
                    <td class="positive">14.73%</td>
                    <td>1565</td>
                    <td>1.02</td>
                    <td class="negative">28.45%</td>
                    <td class="positive"><strong>0.8008</strong></td>
                    <td class="negative">+0.89% / -1.48%</td>
                    <td>1h51m<br><small>(1.0m - 49h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>62.5%</strong></td>
                    <td class="positive">3.52%</td>
                    <td>56</td>
                    <td>1.14</td>
                    <td class="neutral">4.53%</td>
                    <td class="positive"><strong>0.5096</strong></td>
                    <td class="negative">+0.90% / -1.37%</td>
                    <td>3h8m<br><small>(2.0m - 31h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>62.2%</strong></td>
                    <td class="negative">-4.25%</td>
                    <td>1411</td>
                    <td>0.99</td>
                    <td class="negative">25.54%</td>
                    <td class="positive"><strong>0.7424</strong></td>
                    <td class="negative">+0.89% / -1.46%</td>
                    <td>2h3m<br><small>(1.0m - 49h17m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>61.9%</strong></td>
                    <td class="positive">1.38%</td>
                    <td>291</td>
                    <td>1.01</td>
                    <td class="negative">17.54%</td>
                    <td class="positive"><strong>0.6137</strong></td>
                    <td class="negative">+0.89% / -1.46%</td>
                    <td>1h31m<br><small>(1.0m - 13h18m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>61.3%</strong></td>
                    <td class="negative">-0.90%</td>
                    <td>168</td>
                    <td>0.99</td>
                    <td class="negative">14.48%</td>
                    <td class="positive"><strong>0.5642</strong></td>
                    <td class="negative">+0.94% / -1.39%</td>
                    <td>2h31m<br><small>(1.0m - 20h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>61.0%</strong></td>
                    <td class="positive">1.85%</td>
                    <td>136</td>
                    <td>1.03</td>
                    <td class="negative">12.73%</td>
                    <td class="positive"><strong>0.5588</strong></td>
                    <td class="negative">+0.91% / -1.37%</td>
                    <td>1h52m<br><small>(1.0m - 14h43m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>60.7%</strong></td>
                    <td class="positive">3.96%</td>
                    <td>275</td>
                    <td>1.03</td>
                    <td class="negative">11.49%</td>
                    <td class="positive"><strong>0.6313</strong></td>
                    <td class="negative">+0.90% / -1.41%</td>
                    <td>2h14m<br><small>(1.0m - 21h24m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>60.7%</strong></td>
                    <td class="negative">-4.27%</td>
                    <td>435</td>
                    <td>0.98</td>
                    <td class="negative">13.33%</td>
                    <td class="positive"><strong>0.6440</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>2h14m<br><small>(1.0m - 43h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>59.3%</strong></td>
                    <td class="negative">-10.23%</td>
                    <td>270</td>
                    <td>0.92</td>
                    <td class="negative">15.52%</td>
                    <td class="positive"><strong>0.5611</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h23m<br><small>(1.0m - 31h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>59.1%</strong></td>
                    <td class="negative">-5.69%</td>
                    <td>66</td>
                    <td>0.82</td>
                    <td class="negative">13.02%</td>
                    <td class="positive"><strong>0.4164</strong></td>
                    <td class="negative">+0.88% / -1.36%</td>
                    <td>2h29m<br><small>(2.0m - 13h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>59.0%</strong></td>
                    <td class="negative">-53.08%</td>
                    <td>1216</td>
                    <td>0.91</td>
                    <td class="negative">67.08%</td>
                    <td class="positive"><strong>0.5238</strong></td>
                    <td class="negative">+0.89% / -1.41%</td>
                    <td>2h24m<br><small>(1.0m - 45h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>57.8%</strong></td>
                    <td class="negative">-20.39%</td>
                    <td>465</td>
                    <td>0.91</td>
                    <td class="negative">35.11%</td>
                    <td class="positive"><strong>0.5592</strong></td>
                    <td class="negative">+0.90% / -1.38%</td>
                    <td>2h37m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>57.8%</strong></td>
                    <td class="negative">-52.80%</td>
                    <td>1115</td>
                    <td>0.90</td>
                    <td class="negative">59.50%</td>
                    <td class="positive"><strong>0.5295</strong></td>
                    <td class="negative">+0.93% / -1.38%</td>
                    <td>2h0m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>57.7%</strong></td>
                    <td class="positive">0.15%</td>
                    <td>26</td>
                    <td>1.01</td>
                    <td class="neutral">4.43%</td>
                    <td class="positive"><strong>0.3911</strong></td>
                    <td class="negative">+0.95% / -1.41%</td>
                    <td>3h37m<br><small>(9.0m - 17h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>57.6%</strong></td>
                    <td class="negative">-72.26%</td>
                    <td>1064</td>
                    <td>0.86</td>
                    <td class="negative">74.76%</td>
                    <td class="positive"><strong>0.4385</strong></td>
                    <td class="negative">+0.90% / -1.41%</td>
                    <td>2h8m<br><small>(1.0m - 34h15m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>56.8%</strong></td>
                    <td class="negative">-0.36%</td>
                    <td>37</td>
                    <td>0.98</td>
                    <td class="negative">7.41%</td>
                    <td class="positive"><strong>0.4138</strong></td>
                    <td class="negative">+1.01% / -1.45%</td>
                    <td>32.1m<br><small>(2.0m - 4h38m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>56.7%</strong></td>
                    <td class="negative">-17.78%</td>
                    <td>282</td>
                    <td>0.87</td>
                    <td class="negative">25.82%</td>
                    <td class="positive"><strong>0.5211</strong></td>
                    <td class="negative">+0.89% / -1.35%</td>
                    <td>3h5m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>56.6%</strong></td>
                    <td class="negative">-29.95%</td>
                    <td>408</td>
                    <td>0.85</td>
                    <td class="negative">35.04%</td>
                    <td class="positive"><strong>0.5097</strong></td>
                    <td class="negative">+0.94% / -1.39%</td>
                    <td>1h54m<br><small>(1.0m - 24h31m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>56.3%</strong></td>
                    <td class="negative">-26.32%</td>
                    <td>295</td>
                    <td>0.83</td>
                    <td class="negative">32.50%</td>
                    <td class="positive"><strong>0.4815</strong></td>
                    <td class="negative">+0.91% / -1.38%</td>
                    <td>2h7m<br><small>(1.0m - 24h31m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'AI Rule 3: Smart Money Flow Di...', 'AI Rule 10: Composite Sentimen...', 'Acad Rule 2: Mean Reversion Fa...', 'Momentum Rule 2: Momentum Dive...', 'Prof Rule 7: Mean Reversion Vo...', 'Volume Rule 5: Smart Money Vol...', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 8: Momentum Divergence...', 'Professional Rule 10: CCI Reve...', 'Ext Rule 3: Bollinger Squeeze ...', 'Rule 7: Bollinger Band Bounce', 'Professional Rule 7: Chaikin M...', 'Price Action Rule 3: Engulfing...', 'Ext Rule 5: ATR Volatility Exp...'],
                y: [63.20043103448276, 63.78772112382934, 63.02480916030534, 62.939297124600635, 65.625, 63.74695863746959, 63.38028169014085, 60.72727272727273, 61.855670103092784, 61.029411764705884, 61.30952380952381, 62.15450035435861, 60.689655172413794, 59.25925925925925, 56.73758865248227],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'AI Rule 3: Smart Money Flow Di...', 'AI Rule 10: Composite Sentimen...', 'Acad Rule 2: Mean Reversion Fa...', 'Momentum Rule 2: Momentum Dive...', 'Prof Rule 7: Mean Reversion Vo...', 'Volume Rule 5: Smart Money Vol...', 'Rule 6: Stochastic Oversold Cr...', 'AI Rule 8: Momentum Divergence...', 'Professional Rule 10: CCI Reve...', 'Ext Rule 3: Bollinger Squeeze ...', 'Rule 7: Bollinger Band Bounce', 'Professional Rule 7: Chaikin M...', 'Price Action Rule 3: Engulfing...', 'Ext Rule 5: ATR Volatility Exp...'],
                y: [np.float64(30.846042746639025), np.float64(25.258553057117574), np.float64(25.168713045142805), np.float64(14.730764860838361), np.float64(5.608089129861982), np.float64(3.5102309514049193), np.float64(3.405325111619597), np.float64(3.955924929409222), np.float64(1.3830792720278842), np.float64(1.8515308406553084), np.float64(-0.9035910268422521), np.float64(-4.246889608168014), np.float64(-4.268684578859458), np.float64(-10.23330980873703), np.float64(-17.781401626991673)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
