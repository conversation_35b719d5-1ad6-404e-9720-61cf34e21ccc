
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 11:49:16 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">53.03%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">9,474</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.2%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1.39</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>60.09%</strong></td>
                    <td>69.3%</td>
                    <td>300</td>
                    <td>1.31</td>
                    <td class="negative">13.59%</td>
                    <td class="positive"><strong>0.8283</strong></td>
                    <td class="negative">+0.87% / -1.51%</td>
                    <td>2h12m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>39.40%</strong></td>
                    <td>62.4%</td>
                    <td>2617</td>
                    <td>1.02</td>
                    <td class="negative">46.62%</td>
                    <td class="positive"><strong>0.8904</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h30m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>19.38%</strong></td>
                    <td>64.3%</td>
                    <td>300</td>
                    <td>1.09</td>
                    <td class="negative">21.62%</td>
                    <td class="positive"><strong>0.6759</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>1h37m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>10.15%</strong></td>
                    <td>66.7%</td>
                    <td>54</td>
                    <td>1.29</td>
                    <td class="negative">12.04%</td>
                    <td class="positive"><strong>0.5501</strong></td>
                    <td class="negative">+0.94% / -1.33%</td>
                    <td>1h58m<br><small>(1.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>9.51%</strong></td>
                    <td>68.2%</td>
                    <td>22</td>
                    <td>1.76</td>
                    <td class="neutral">4.24%</td>
                    <td class="positive"><strong>0.5285</strong></td>
                    <td class="negative">+0.86% / -1.33%</td>
                    <td>3h5m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>9.41%</strong></td>
                    <td>63.0%</td>
                    <td>73</td>
                    <td>1.18</td>
                    <td class="negative">8.00%</td>
                    <td class="positive"><strong>0.5512</strong></td>
                    <td class="negative">+0.89% / -1.37%</td>
                    <td>3h14m<br><small>(7.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>8.56%</strong></td>
                    <td>63.3%</td>
                    <td>49</td>
                    <td>1.25</td>
                    <td class="negative">7.19%</td>
                    <td class="positive"><strong>0.5287</strong></td>
                    <td class="negative">+0.96% / -1.39%</td>
                    <td>1h40m<br><small>(2.0m - 15h6m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>7.79%</strong></td>
                    <td>65.3%</td>
                    <td>95</td>
                    <td>1.10</td>
                    <td class="negative">8.63%</td>
                    <td class="positive"><strong>0.5481</strong></td>
                    <td class="negative">+0.88% / -1.53%</td>
                    <td>2h39m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>6.97%</strong></td>
                    <td>78.6%</td>
                    <td>14</td>
                    <td>2.17</td>
                    <td class="neutral">2.63%</td>
                    <td class="positive"><strong>0.5410</strong></td>
                    <td class="negative">+0.90% / -1.38%</td>
                    <td>2h8m<br><small>(3.0m - 11h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>5.97%</strong></td>
                    <td>66.7%</td>
                    <td>12</td>
                    <td>1.75</td>
                    <td class="neutral">2.91%</td>
                    <td class="positive"><strong>0.4639</strong></td>
                    <td class="negative">+1.18% / -1.59%</td>
                    <td>2.8m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>5.65%</strong></td>
                    <td>61.9%</td>
                    <td>1993</td>
                    <td>1.00</td>
                    <td class="negative">51.19%</td>
                    <td class="positive"><strong>0.7665</strong></td>
                    <td class="negative">+0.88% / -1.43%</td>
                    <td>2h23m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>2.90%</strong></td>
                    <td>100.0%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.81% / 0.00%</td>
                    <td>25.0m<br><small>(13.0m - 37.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>2.61%</strong></td>
                    <td>63.6%</td>
                    <td>165</td>
                    <td>1.02</td>
                    <td class="negative">19.01%</td>
                    <td class="positive"><strong>0.5663</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h0m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>0.98%</strong></td>
                    <td>64.7%</td>
                    <td>34</td>
                    <td>1.04</td>
                    <td class="negative">6.30%</td>
                    <td class="positive"><strong>0.4117</strong></td>
                    <td class="negative">+0.82% / -1.53%</td>
                    <td>2h22m<br><small>(6.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-8.41%</strong></td>
                    <td>52.2%</td>
                    <td>23</td>
                    <td>0.65</td>
                    <td class="negative">15.78%</td>
                    <td class="positive"><strong>0.2463</strong></td>
                    <td class="negative">+0.94% / -1.34%</td>
                    <td>1h44m<br><small>(13.0m - 7h10m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="negative"><strong>-39.81%</strong></td>
                    <td>61.5%</td>
                    <td>1187</td>
                    <td>0.96</td>
                    <td class="negative">69.94%</td>
                    <td class="positive"><strong>0.5628</strong></td>
                    <td class="negative">+0.87% / -1.42%</td>
                    <td>2h31m<br><small>(1.0m - 32h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-88.11%</strong></td>
                    <td>60.9%</td>
                    <td>2534</td>
                    <td>0.96</td>
                    <td class="negative">99.93%</td>
                    <td class="positive"><strong>0.4731</strong></td>
                    <td class="negative">+0.86% / -1.39%</td>
                    <td>2h58m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">2.90%</td>
                    <td>2</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.81% / 0.00%</td>
                    <td>25.0m<br><small>(13.0m - 37.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>78.6%</strong></td>
                    <td class="positive">6.97%</td>
                    <td>14</td>
                    <td>2.17</td>
                    <td class="neutral">2.63%</td>
                    <td class="positive"><strong>0.5410</strong></td>
                    <td class="negative">+0.90% / -1.38%</td>
                    <td>2h8m<br><small>(3.0m - 11h26m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>69.3%</strong></td>
                    <td class="positive">60.09%</td>
                    <td>300</td>
                    <td>1.31</td>
                    <td class="negative">13.59%</td>
                    <td class="positive"><strong>0.8283</strong></td>
                    <td class="negative">+0.87% / -1.51%</td>
                    <td>2h12m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>68.2%</strong></td>
                    <td class="positive">9.51%</td>
                    <td>22</td>
                    <td>1.76</td>
                    <td class="neutral">4.24%</td>
                    <td class="positive"><strong>0.5285</strong></td>
                    <td class="negative">+0.86% / -1.33%</td>
                    <td>3h5m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">10.15%</td>
                    <td>54</td>
                    <td>1.29</td>
                    <td class="negative">12.04%</td>
                    <td class="positive"><strong>0.5501</strong></td>
                    <td class="negative">+0.94% / -1.33%</td>
                    <td>1h58m<br><small>(1.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="positive">5.97%</td>
                    <td>12</td>
                    <td>1.75</td>
                    <td class="neutral">2.91%</td>
                    <td class="positive"><strong>0.4639</strong></td>
                    <td class="negative">+1.18% / -1.59%</td>
                    <td>2.8m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>65.3%</strong></td>
                    <td class="positive">7.79%</td>
                    <td>95</td>
                    <td>1.10</td>
                    <td class="negative">8.63%</td>
                    <td class="positive"><strong>0.5481</strong></td>
                    <td class="negative">+0.88% / -1.53%</td>
                    <td>2h39m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>64.7%</strong></td>
                    <td class="positive">0.98%</td>
                    <td>34</td>
                    <td>1.04</td>
                    <td class="negative">6.30%</td>
                    <td class="positive"><strong>0.4117</strong></td>
                    <td class="negative">+0.82% / -1.53%</td>
                    <td>2h22m<br><small>(6.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>64.3%</strong></td>
                    <td class="positive">19.38%</td>
                    <td>300</td>
                    <td>1.09</td>
                    <td class="negative">21.62%</td>
                    <td class="positive"><strong>0.6759</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>1h37m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>63.6%</strong></td>
                    <td class="positive">2.61%</td>
                    <td>165</td>
                    <td>1.02</td>
                    <td class="negative">19.01%</td>
                    <td class="positive"><strong>0.5663</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h0m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>63.3%</strong></td>
                    <td class="positive">8.56%</td>
                    <td>49</td>
                    <td>1.25</td>
                    <td class="negative">7.19%</td>
                    <td class="positive"><strong>0.5287</strong></td>
                    <td class="negative">+0.96% / -1.39%</td>
                    <td>1h40m<br><small>(2.0m - 15h6m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>63.0%</strong></td>
                    <td class="positive">9.41%</td>
                    <td>73</td>
                    <td>1.18</td>
                    <td class="negative">8.00%</td>
                    <td class="positive"><strong>0.5512</strong></td>
                    <td class="negative">+0.89% / -1.37%</td>
                    <td>3h14m<br><small>(7.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>62.4%</strong></td>
                    <td class="positive">39.40%</td>
                    <td>2617</td>
                    <td>1.02</td>
                    <td class="negative">46.62%</td>
                    <td class="positive"><strong>0.8904</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h30m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>61.9%</strong></td>
                    <td class="positive">5.65%</td>
                    <td>1993</td>
                    <td>1.00</td>
                    <td class="negative">51.19%</td>
                    <td class="positive"><strong>0.7665</strong></td>
                    <td class="negative">+0.88% / -1.43%</td>
                    <td>2h23m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>61.5%</strong></td>
                    <td class="negative">-39.81%</td>
                    <td>1187</td>
                    <td>0.96</td>
                    <td class="negative">69.94%</td>
                    <td class="positive"><strong>0.5628</strong></td>
                    <td class="negative">+0.87% / -1.42%</td>
                    <td>2h31m<br><small>(1.0m - 32h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>60.9%</strong></td>
                    <td class="negative">-88.11%</td>
                    <td>2534</td>
                    <td>0.96</td>
                    <td class="negative">99.93%</td>
                    <td class="positive"><strong>0.4731</strong></td>
                    <td class="negative">+0.86% / -1.39%</td>
                    <td>2h58m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>52.2%</strong></td>
                    <td class="negative">-8.41%</td>
                    <td>23</td>
                    <td>0.65</td>
                    <td class="negative">15.78%</td>
                    <td class="positive"><strong>0.2463</strong></td>
                    <td class="negative">+0.94% / -1.34%</td>
                    <td>1h44m<br><small>(13.0m - 7h10m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'AI Rule 8: Momentum Divergence...', 'Volume Rule 5: Smart Money Vol...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'Professional Rule 10: CCI Reve...', 'Professional Rule 7: Chaikin M...', 'Momentum Rule 2: Momentum Dive...', 'AI Rule 3: Smart Money Flow Di...', 'Rule 27: Structure Break Up', 'Price Action Rule 3: Engulfing...', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [69.33333333333334, 62.43790599923577, 64.33333333333333, 65.26315789473685, 61.8665328650276, 63.63636363636363, 63.013698630136986, 66.66666666666666, 63.26530612244898, 61.499578770008426, 100.0, 68.18181818181817, 78.57142857142857, 64.70588235294117, 66.66666666666666],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Prof Rule 7: Mean Reversion Vo...', 'AI Rule 10: Composite Sentimen...', 'AI Rule 8: Momentum Divergence...', 'Volume Rule 5: Smart Money Vol...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'Professional Rule 10: CCI Reve...', 'Professional Rule 7: Chaikin M...', 'Momentum Rule 2: Momentum Dive...', 'AI Rule 3: Smart Money Flow Di...', 'Rule 27: Structure Break Up', 'Price Action Rule 3: Engulfing...', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [np.float64(60.09430607714322), np.float64(39.395991181265714), np.float64(19.380855051872786), np.float64(7.785947384364496), np.float64(5.650472047896415), np.float64(2.613479769338257), np.float64(9.414737133916978), np.float64(10.149140198466643), np.float64(8.562168398300535), np.float64(-39.814497059684506), np.float64(2.899266787177767), np.float64(9.508866552922758), np.float64(6.966641510762464), np.float64(0.9770966542385576), np.float64(5.968591548923417)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
