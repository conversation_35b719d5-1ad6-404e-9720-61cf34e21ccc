
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 12:04:28 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">354.12%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">5,327</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">64.3%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">10.43</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>107.04%</strong></td>
                    <td>64.0%</td>
                    <td>1438</td>
                    <td>1.08</td>
                    <td class="negative">30.52%</td>
                    <td class="positive"><strong>1.0390</strong></td>
                    <td class="negative">+0.83% / -1.36%</td>
                    <td>5h15m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>81.71%</strong></td>
                    <td>64.7%</td>
                    <td>1115</td>
                    <td>1.07</td>
                    <td class="negative">28.82%</td>
                    <td class="positive"><strong>0.9483</strong></td>
                    <td class="negative">+0.83% / -1.41%</td>
                    <td>4h23m<br><small>(1.0m - 73h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>70.79%</strong></td>
                    <td>63.9%</td>
                    <td>1479</td>
                    <td>1.05</td>
                    <td class="negative">35.20%</td>
                    <td class="positive"><strong>0.9352</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>4h43m<br><small>(1.0m - 85h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>66.09%</strong></td>
                    <td>65.3%</td>
                    <td>733</td>
                    <td>1.09</td>
                    <td class="negative">24.38%</td>
                    <td class="positive"><strong>0.8816</strong></td>
                    <td class="negative">+0.84% / -1.39%</td>
                    <td>4h42m<br><small>(1.0m - 85h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>29.48%</strong></td>
                    <td>65.7%</td>
                    <td>178</td>
                    <td>1.17</td>
                    <td class="negative">21.42%</td>
                    <td class="positive"><strong>0.6608</strong></td>
                    <td class="negative">+0.82% / -1.45%</td>
                    <td>2h55m<br><small>(1.0m - 27h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>10.82%</strong></td>
                    <td>65.1%</td>
                    <td>109</td>
                    <td>1.10</td>
                    <td class="negative">19.17%</td>
                    <td class="positive"><strong>0.5551</strong></td>
                    <td class="negative">+0.83% / -1.44%</td>
                    <td>4h12m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>8.71%</strong></td>
                    <td>72.2%</td>
                    <td>18</td>
                    <td>1.59</td>
                    <td class="negative">9.72%</td>
                    <td class="positive"><strong>0.4784</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>2h49m<br><small>(5.0m - 14h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>6.43%</strong></td>
                    <td>69.2%</td>
                    <td>13</td>
                    <td>1.66</td>
                    <td class="neutral">4.45%</td>
                    <td class="positive"><strong>0.4511</strong></td>
                    <td class="negative">+0.80% / -1.35%</td>
                    <td>3h46m<br><small>(9.0m - 17h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>6.23%</strong></td>
                    <td>100.0%</td>
                    <td>4</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.87% / 0.00%</td>
                    <td>1.5m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>2.32%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.26% / 0.00%</td>
                    <td>8.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>1.20%</strong></td>
                    <td>71.4%</td>
                    <td>7</td>
                    <td>1.19</td>
                    <td class="neutral">3.83%</td>
                    <td class="positive"><strong>0.3214</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>14h12m<br><small>(28.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>0.86%</strong></td>
                    <td>75.0%</td>
                    <td>4</td>
                    <td>1.27</td>
                    <td class="neutral">3.07%</td>
                    <td class="positive"><strong>0.3330</strong></td>
                    <td class="negative">+1.40% / -1.40%</td>
                    <td>30h23m<br><small>(15.0m - 83h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-0.75%</strong></td>
                    <td>61.3%</td>
                    <td>31</td>
                    <td>0.98</td>
                    <td class="negative">11.50%</td>
                    <td class="positive"><strong>0.3894</strong></td>
                    <td class="negative">+0.82% / -1.35%</td>
                    <td>5h36m<br><small>(6.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="negative"><strong>-1.54%</strong></td>
                    <td>66.7%</td>
                    <td>18</td>
                    <td>0.92</td>
                    <td class="negative">12.54%</td>
                    <td class="positive"><strong>0.3256</strong></td>
                    <td class="negative">+0.80% / -1.38%</td>
                    <td>5h36m<br><small>(4.0m - 47h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="negative"><strong>-2.77%</strong></td>
                    <td>0.0%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0631</strong></td>
                    <td class="negative">+0.00% / -1.35%</td>
                    <td>2h48m</td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-5.06%</strong></td>
                    <td>60.7%</td>
                    <td>28</td>
                    <td>0.84</td>
                    <td class="negative">12.09%</td>
                    <td class="positive"><strong>0.3320</strong></td>
                    <td class="negative">+0.81% / -1.39%</td>
                    <td>8h9m<br><small>(25.0m - 38h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-5.13%</strong></td>
                    <td>40.0%</td>
                    <td>5</td>
                    <td>0.33</td>
                    <td class="negative">6.17%</td>
                    <td class="negative"><strong>-0.0427</strong></td>
                    <td class="negative">+0.89% / -1.29%</td>
                    <td>5h12m<br><small>(41.0m - 20h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-10.87%</strong></td>
                    <td>56.4%</td>
                    <td>55</td>
                    <td>0.82</td>
                    <td class="negative">18.61%</td>
                    <td class="positive"><strong>0.3670</strong></td>
                    <td class="negative">+0.82% / -1.34%</td>
                    <td>8h55m<br><small>(12.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-11.43%</strong></td>
                    <td>61.1%</td>
                    <td>90</td>
                    <td>0.88</td>
                    <td class="negative">23.22%</td>
                    <td class="positive"><strong>0.4299</strong></td>
                    <td class="negative">+0.82% / -1.36%</td>
                    <td>4h46m<br><small>(1.0m - 57h25m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">6.23%</td>
                    <td>4</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.87% / 0.00%</td>
                    <td>1.5m<br><small>(1.0m - 3.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">2.32%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.26% / 0.00%</td>
                    <td>8.0m</td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>75.0%</strong></td>
                    <td class="positive">0.86%</td>
                    <td>4</td>
                    <td>1.27</td>
                    <td class="neutral">3.07%</td>
                    <td class="positive"><strong>0.3330</strong></td>
                    <td class="negative">+1.40% / -1.40%</td>
                    <td>30h23m<br><small>(15.0m - 83h41m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>72.2%</strong></td>
                    <td class="positive">8.71%</td>
                    <td>18</td>
                    <td>1.59</td>
                    <td class="negative">9.72%</td>
                    <td class="positive"><strong>0.4784</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>2h49m<br><small>(5.0m - 14h20m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>71.4%</strong></td>
                    <td class="positive">1.20%</td>
                    <td>7</td>
                    <td>1.19</td>
                    <td class="neutral">3.83%</td>
                    <td class="positive"><strong>0.3214</strong></td>
                    <td class="negative">+0.86% / -1.34%</td>
                    <td>14h12m<br><small>(28.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>69.2%</strong></td>
                    <td class="positive">6.43%</td>
                    <td>13</td>
                    <td>1.66</td>
                    <td class="neutral">4.45%</td>
                    <td class="positive"><strong>0.4511</strong></td>
                    <td class="negative">+0.80% / -1.35%</td>
                    <td>3h46m<br><small>(9.0m - 17h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>66.7%</strong></td>
                    <td class="negative">-1.54%</td>
                    <td>18</td>
                    <td>0.92</td>
                    <td class="negative">12.54%</td>
                    <td class="positive"><strong>0.3256</strong></td>
                    <td class="negative">+0.80% / -1.38%</td>
                    <td>5h36m<br><small>(4.0m - 47h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>65.7%</strong></td>
                    <td class="positive">29.48%</td>
                    <td>178</td>
                    <td>1.17</td>
                    <td class="negative">21.42%</td>
                    <td class="positive"><strong>0.6608</strong></td>
                    <td class="negative">+0.82% / -1.45%</td>
                    <td>2h55m<br><small>(1.0m - 27h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>AI Rule 3: Smart Money Flow Divergence</td>
                    <td class="positive"><strong>65.3%</strong></td>
                    <td class="positive">66.09%</td>
                    <td>733</td>
                    <td>1.09</td>
                    <td class="negative">24.38%</td>
                    <td class="positive"><strong>0.8816</strong></td>
                    <td class="negative">+0.84% / -1.39%</td>
                    <td>4h42m<br><small>(1.0m - 85h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>65.1%</strong></td>
                    <td class="positive">10.82%</td>
                    <td>109</td>
                    <td>1.10</td>
                    <td class="negative">19.17%</td>
                    <td class="positive"><strong>0.5551</strong></td>
                    <td class="negative">+0.83% / -1.44%</td>
                    <td>4h12m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>64.7%</strong></td>
                    <td class="positive">81.71%</td>
                    <td>1115</td>
                    <td>1.07</td>
                    <td class="negative">28.82%</td>
                    <td class="positive"><strong>0.9483</strong></td>
                    <td class="negative">+0.83% / -1.41%</td>
                    <td>4h23m<br><small>(1.0m - 73h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>64.0%</strong></td>
                    <td class="positive">107.04%</td>
                    <td>1438</td>
                    <td>1.08</td>
                    <td class="negative">30.52%</td>
                    <td class="positive"><strong>1.0390</strong></td>
                    <td class="negative">+0.83% / -1.36%</td>
                    <td>5h15m<br><small>(1.0m - 104h55m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>63.9%</strong></td>
                    <td class="positive">70.79%</td>
                    <td>1479</td>
                    <td>1.05</td>
                    <td class="negative">35.20%</td>
                    <td class="positive"><strong>0.9352</strong></td>
                    <td class="negative">+0.83% / -1.39%</td>
                    <td>4h43m<br><small>(1.0m - 85h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>61.3%</strong></td>
                    <td class="negative">-0.75%</td>
                    <td>31</td>
                    <td>0.98</td>
                    <td class="negative">11.50%</td>
                    <td class="positive"><strong>0.3894</strong></td>
                    <td class="negative">+0.82% / -1.35%</td>
                    <td>5h36m<br><small>(6.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>61.1%</strong></td>
                    <td class="negative">-11.43%</td>
                    <td>90</td>
                    <td>0.88</td>
                    <td class="negative">23.22%</td>
                    <td class="positive"><strong>0.4299</strong></td>
                    <td class="negative">+0.82% / -1.36%</td>
                    <td>4h46m<br><small>(1.0m - 57h25m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>60.7%</strong></td>
                    <td class="negative">-5.06%</td>
                    <td>28</td>
                    <td>0.84</td>
                    <td class="negative">12.09%</td>
                    <td class="positive"><strong>0.3320</strong></td>
                    <td class="negative">+0.81% / -1.39%</td>
                    <td>8h9m<br><small>(25.0m - 38h40m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>56.4%</strong></td>
                    <td class="negative">-10.87%</td>
                    <td>55</td>
                    <td>0.82</td>
                    <td class="negative">18.61%</td>
                    <td class="positive"><strong>0.3670</strong></td>
                    <td class="negative">+0.82% / -1.34%</td>
                    <td>8h55m<br><small>(12.0m - 68h37m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="neutral"><strong>40.0%</strong></td>
                    <td class="negative">-5.13%</td>
                    <td>5</td>
                    <td>0.33</td>
                    <td class="negative">6.17%</td>
                    <td class="negative"><strong>-0.0427</strong></td>
                    <td class="negative">+0.89% / -1.29%</td>
                    <td>5h12m<br><small>(41.0m - 20h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">-2.77%</td>
                    <td>1</td>
                    <td>0.00</td>
                    <td class="positive">0.00%</td>
                    <td class="negative"><strong>-1.0631</strong></td>
                    <td class="negative">+0.00% / -1.35%</td>
                    <td>2h48m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 10: Composite Sentimen...', 'AI Rule 3: Smart Money Flow Di...', 'Prof Rule 7: Mean Reversion Vo...', 'AI Rule 8: Momentum Divergence...', 'Rule 6: Stochastic Oversold Cr...', 'Acad Rule 2: Mean Reversion Fa...', 'Rule 27: Structure Break Up', 'Momentum Rule 2: Momentum Dive...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'Price Action Rule 3: Engulfing...', 'Professional Rule 7: Chaikin M...', 'Rule 10: Volume Spike'],
                y: [63.97774687065368, 64.66367713004485, 63.894523326572006, 65.34788540245566, 65.73033707865169, 65.13761467889908, 61.111111111111114, 100.0, 100.0, 72.22222222222221, 56.36363636363636, 61.29032258064516, 69.23076923076923, 66.66666666666666, 60.71428571428571],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['Ext Rule 6: Fibonacci Support ...', 'Rule 7: Bollinger Band Bounce', 'AI Rule 10: Composite Sentimen...', 'AI Rule 3: Smart Money Flow Di...', 'Prof Rule 7: Mean Reversion Vo...', 'AI Rule 8: Momentum Divergence...', 'Rule 6: Stochastic Oversold Cr...', 'Acad Rule 2: Mean Reversion Fa...', 'Rule 27: Structure Break Up', 'Momentum Rule 2: Momentum Dive...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'Price Action Rule 3: Engulfing...', 'Professional Rule 7: Chaikin M...', 'Rule 10: Volume Spike'],
                y: [np.float64(107.03756520636215), np.float64(81.70807231057631), np.float64(70.7917752531647), np.float64(66.09436171947542), np.float64(29.47602597536611), np.float64(10.815533084873866), np.float64(-11.427702589537935), np.float64(6.22992393178925), np.float64(2.323798839496856), np.float64(8.710049443701166), np.float64(-10.873816813623852), np.float64(-0.7459263014301541), np.float64(6.4266967183185475), np.float64(-1.5392502545514288), np.float64(-5.062955738456105)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
