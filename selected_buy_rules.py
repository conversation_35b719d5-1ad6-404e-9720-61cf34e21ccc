"""
Selected Buy Rules - Extracted from lastrules.md
This file contains the 48 buy rules specified in lastrules.md
"""

import numpy as np


class SelectedBuyRules:
    """Selected buy rules from lastrules.md"""
    
    def __init__(self, df):
        self.df = df
        self.category = 'SELECTED'
    
    def _safe_get(self, column, idx, default=np.nan):
        """Safely get value from dataframe"""
        if idx < 0 or idx >= len(self.df) or column not in self.df.columns:
            return default
        return self.df[column].iloc[idx]
    
    # AI Rule 1: Multi-Timeframe Momentum
    def ai_rule_1_multi_timeframe_momentum(self, idx):
        """
        Multi-Timeframe Momentum Convergence
        Logic: When short, medium, and long-term momentum align bullishly
        """
        if idx < 50:
            return False
        
        # Short-term momentum (5-period ROC)
        roc_5 = self._safe_get('ROC_5', idx)
        short_momentum = roc_5 > 0.5
        
        # Medium-term momentum (14-period RSI)
        rsi = self._safe_get('RSI', idx)
        medium_momentum = 40 < rsi < 70
        
        # Long-term momentum (50-period EMA slope)
        ema_50 = self._safe_get('EMA_50', idx)
        ema_50_prev = self._safe_get('EMA_50', idx-5)
        long_momentum = ema_50 > ema_50_prev
        
        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_confirmation = volume_ratio > 1.2
        
        # Price above key moving averages
        close = self._safe_get('close', idx)
        ema_21 = self._safe_get('EMA_21', idx)
        price_structure = close > ema_21
        
        return short_momentum and medium_momentum and long_momentum and volume_confirmation and price_structure

    # Price Action Rule 3: Engulfing Pattern
    def price_action_rule_3_engulfing(self, idx):
        """
        Bullish Engulfing Pattern
        Logic: Current candle completely engulfs previous bearish candle
        """
        if idx < 2:
            return False
        
        # Current candle data
        open_curr = self._safe_get('open', idx)
        close_curr = self._safe_get('close', idx)
        high_curr = self._safe_get('high', idx)
        low_curr = self._safe_get('low', idx)
        
        # Previous candle data
        open_prev = self._safe_get('open', idx-1)
        close_prev = self._safe_get('close', idx-1)
        high_prev = self._safe_get('high', idx-1)
        low_prev = self._safe_get('low', idx-1)
        
        # Previous candle was bearish
        prev_bearish = close_prev < open_prev
        
        # Current candle is bullish
        curr_bullish = close_curr > open_curr
        
        # Current candle engulfs previous candle
        engulfs = (open_curr < close_prev and close_curr > open_prev)
        
        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 1.1
        
        # Not in extreme overbought territory
        rsi = self._safe_get('RSI', idx)
        not_overbought = rsi < 75
        
        return prev_bearish and curr_bullish and engulfs and volume_ok and not_overbought

    # Prof Rule 7: Mean Reversion Volatility Filter
    def prof_rule_7_mean_reversion_volatility_filter(self, idx):
        """
        Mean Reversion with Volatility Filter
        Based on academic research on volatility clustering
        """
        if idx < 30:
            return False
        
        # Price below lower Bollinger Band (oversold)
        close = self._safe_get('close', idx)
        bb_lower = self._safe_get('BB_lower', idx)
        bb_middle = self._safe_get('BB_middle', idx)
        
        oversold = close < bb_lower
        
        # Volatility expansion (but not extreme)
        bb_width = self._safe_get('BB_width', idx)
        avg_bb_width = np.mean([self._safe_get('BB_width', i) 
                               for i in range(max(0, idx-20), idx)])
        
        volatility_expansion = 1.2 < bb_width / avg_bb_width < 2.0
        
        # RSI divergence (price lower, RSI higher)
        rsi = self._safe_get('RSI', idx)
        rsi_prev = self._safe_get('RSI', idx-5)
        close_prev = self._safe_get('close', idx-5)
        
        rsi_divergence = close < close_prev and rsi > rsi_prev
        
        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 1.1
        
        return oversold and volatility_expansion and (rsi_divergence or volume_ok)

    # Rule 6: Stochastic Oversold Cross
    def rule_6_stochastic_oversold_cross(self, idx):
        """
        Stochastic Oversold Cross
        Logic: Stochastic crosses above 20 from oversold territory
        """
        if idx < 5:
            return False
        
        stoch_k = self._safe_get('STOCH_K', idx)
        stoch_k_prev = self._safe_get('STOCH_K', idx-1)
        stoch_d = self._safe_get('STOCH_D', idx)
        
        # Was oversold (below 20)
        was_oversold = stoch_k_prev < 20
        
        # Now crossing above 20
        crossing_up = stoch_k > 20 and stoch_k_prev <= 20
        
        # %K above %D (bullish momentum)
        k_above_d = stoch_k > stoch_d
        
        # Price confirmation
        close = self._safe_get('close', idx)
        close_prev = self._safe_get('close', idx-1)
        price_up = close > close_prev
        
        # Volume support
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 0.8
        
        return was_oversold and crossing_up and k_above_d and price_up and volume_ok

    # Volume Rule 5: Smart Money Volume
    def volume_rule_5_smart_money_volume(self, idx):
        """
        Smart Money Volume Pattern
        Logic: Accumulation pattern with institutional volume characteristics
        """
        if idx < 10:
            return False
        
        # Volume accumulation pattern (increasing volume on up days)
        volume_ratio = self._safe_get('volume_ratio', idx)
        close = self._safe_get('close', idx)
        close_prev = self._safe_get('close', idx-1)
        
        up_day = close > close_prev
        high_volume = volume_ratio > 1.3
        
        # Smart money characteristics: volume leads price
        volume_ma = np.mean([self._safe_get('volume_ratio', i) 
                            for i in range(max(0, idx-5), idx)])
        volume_trending = volume_ratio > volume_ma * 1.2
        
        # Price near support (not chasing)
        bb_position = self._safe_get('BB_position', idx)
        near_support = bb_position < 0.4
        
        # Momentum building
        rsi = self._safe_get('RSI', idx)
        momentum_building = 35 < rsi < 65
        
        # No recent volume spike (avoiding FOMO)
        recent_spike = any(self._safe_get('volume_ratio', i) > 2.0 
                          for i in range(max(0, idx-3), idx))
        
        return up_day and high_volume and volume_trending and near_support and momentum_building and not recent_spike

    # Volume Rule 4: Volume Breakout Confirmation
    def volume_rule_4_breakout_confirmation(self, idx):
        """
        Volume Breakout Confirmation
        Logic: Price breakout confirmed by significant volume increase
        """
        if idx < 20:
            return False
        
        # Price breakout above resistance
        close = self._safe_get('close', idx)
        high_20 = max([self._safe_get('high', i) for i in range(max(0, idx-20), idx)])
        
        breakout = close > high_20 * 1.001  # 0.1% above 20-day high
        
        # Volume confirmation (2x average)
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_confirmation = volume_ratio > 2.0
        
        # Sustained breakout (not just a spike)
        close_prev = self._safe_get('close', idx-1)
        sustained = close > close_prev * 0.999
        
        # Not overbought
        rsi = self._safe_get('RSI', idx)
        not_overbought = rsi < 80
        
        # Momentum support
        macd = self._safe_get('MACD', idx)
        macd_signal = self._safe_get('MACD_signal', idx)
        momentum_ok = macd > macd_signal
        
        return breakout and volume_confirmation and sustained and not_overbought and momentum_ok

    # New Rule 4: Ultimate Oscillator Breakout
    def new_rule_4_ultimate_oscillator_breakout(self, idx):
        """
        Ultimate Oscillator Breakout
        Ultimate Oscillator above 30 and rising
        """
        if idx < 28:
            return False

        try:
            # Calculate True Range components
            high = self._safe_get('high', idx)
            low = self._safe_get('low', idx)
            close = self._safe_get('close', idx)
            close_prev = self._safe_get('close', idx-1)

            tr = max(high - low, abs(high - close_prev), abs(low - close_prev))

            # Calculate Buying Pressure
            bp = close - min(low, close_prev)

            # Calculate Ultimate Oscillator for current period
            bp_sum_7 = sum([self._safe_get('close', i) - min(self._safe_get('low', i), self._safe_get('close', i-1))
                           for i in range(max(0, idx-6), idx+1)])
            tr_sum_7 = sum([max(self._safe_get('high', i) - self._safe_get('low', i),
                               abs(self._safe_get('high', i) - self._safe_get('close', i-1)),
                               abs(self._safe_get('low', i) - self._safe_get('close', i-1)))
                           for i in range(max(0, idx-6), idx+1)])

            bp_sum_14 = sum([self._safe_get('close', i) - min(self._safe_get('low', i), self._safe_get('close', i-1))
                            for i in range(max(0, idx-13), idx+1)])
            tr_sum_14 = sum([max(self._safe_get('high', i) - self._safe_get('low', i),
                                abs(self._safe_get('high', i) - self._safe_get('close', i-1)),
                                abs(self._safe_get('low', i) - self._safe_get('close', i-1)))
                            for i in range(max(0, idx-13), idx+1)])

            bp_sum_28 = sum([self._safe_get('close', i) - min(self._safe_get('low', i), self._safe_get('close', i-1))
                            for i in range(max(0, idx-27), idx+1)])
            tr_sum_28 = sum([max(self._safe_get('high', i) - self._safe_get('low', i),
                                abs(self._safe_get('high', i) - self._safe_get('close', i-1)),
                                abs(self._safe_get('low', i) - self._safe_get('close', i-1)))
                            for i in range(max(0, idx-27), idx+1)])

            if tr_sum_7 == 0 or tr_sum_14 == 0 or tr_sum_28 == 0:
                return False

            avg7 = bp_sum_7 / tr_sum_7
            avg14 = bp_sum_14 / tr_sum_14
            avg28 = bp_sum_28 / tr_sum_28

            current_ult_osc = 100 * (4 * avg7 + 2 * avg14 + avg28) / 7

            # Calculate previous Ultimate Oscillator
            prev_bp_sum_7 = sum([self._safe_get('close', i) - min(self._safe_get('low', i), self._safe_get('close', i-1))
                                for i in range(max(0, idx-7), idx)])
            prev_tr_sum_7 = sum([max(self._safe_get('high', i) - self._safe_get('low', i),
                                    abs(self._safe_get('high', i) - self._safe_get('close', i-1)),
                                    abs(self._safe_get('low', i) - self._safe_get('close', i-1)))
                                for i in range(max(0, idx-7), idx)])

            if prev_tr_sum_7 == 0:
                return False

            prev_avg7 = prev_bp_sum_7 / prev_tr_sum_7
            prev_ult_osc = 100 * (4 * prev_avg7 + 2 * avg14 + avg28) / 7

            # Signal conditions
            above_30 = current_ult_osc > 30
            rising = current_ult_osc > prev_ult_osc

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.1

            return above_30 and rising and volume_confirmation

        except Exception:
            return False

    # Ext Rule 3: Bollinger Squeeze Breakout
    def ext_rule_3_bollinger_squeeze_breakout(self, idx):
        """
        Bollinger Band Squeeze Breakout
        Low volatility followed by breakout above upper band
        """
        if idx < 20:
            return False

        try:
            # Get Bollinger Band data
            bb_upper = self._safe_get('BB_upper', idx)
            bb_lower = self._safe_get('BB_lower', idx)
            bb_middle = self._safe_get('BB_middle', idx)
            current_close = self._safe_get('close', idx)

            if any(np.isnan([bb_upper, bb_lower, bb_middle, current_close])):
                return False

            # Calculate Bollinger Band width for squeeze detection
            current_width = (bb_upper - bb_lower) / bb_middle

            # Get historical widths for comparison
            widths = []
            for i in range(max(0, idx-20), idx):
                upper = self._safe_get('BB_upper', i)
                lower = self._safe_get('BB_lower', i)
                middle = self._safe_get('BB_middle', i)
                if not any(np.isnan([upper, lower, middle])) and middle != 0:
                    widths.append((upper - lower) / middle)

            if len(widths) < 10:
                return False

            avg_width = np.mean(widths)

            # Squeeze: current width in bottom 20% of recent widths
            squeeze_condition = current_width < np.percentile(widths, 20)

            # Breakout: price closes above upper band
            breakout_condition = current_close > bb_upper

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.2

            return squeeze_condition and breakout_condition and volume_confirmation

        except Exception:
            return False

    # Rule 2: Golden Cross
    def rule_2_golden_cross(self, idx):
        """Golden Cross: SMA50 crosses above SMA200"""
        if idx < 200:
            return False

        sma_50 = self._safe_get('SMA_50', idx)
        sma_50_prev = self._safe_get('SMA_50', idx-1)
        sma_200 = self._safe_get('SMA_200', idx)
        sma_200_prev = self._safe_get('SMA_200', idx-1)

        # Current: SMA50 > SMA200
        current_above = sma_50 > sma_200

        # Previous: SMA50 <= SMA200
        previous_below = sma_50_prev <= sma_200_prev

        # Crossover occurred
        crossover = current_above and previous_below

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 1.0

        return crossover and volume_ok

    # Rule 7: Bollinger Band Bounce
    def rule_7_bollinger_band_bounce(self, idx):
        """Bollinger Band lower band bounce"""
        if idx < 20:
            return False

        close = self._safe_get('close', idx)
        bb_lower = self._safe_get('BB_lower', idx)
        bb_position = self._safe_get('BB_position', idx)

        # Price at or below lower band
        at_lower_band = close <= bb_lower

        # Low position in Bollinger Band range
        low_position = bb_position < 0.1

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 0.8

        return at_lower_band and low_position and volume_ok

    # AI Rule 8: Momentum Divergence Reversal
    def ai_rule_8_momentum_divergence_reversal(self, idx):
        """
        Momentum Divergence Reversal
        Logic: Price makes new lows but momentum indicators don't confirm
        """
        if idx < 20:
            return False

        # Price analysis - looking for lower lows
        close = self._safe_get('close', idx)
        close_prev = self._safe_get('close', idx-10)

        price_lower_low = close < close_prev

        # RSI divergence - RSI higher despite lower price
        rsi = self._safe_get('RSI', idx)
        rsi_prev = self._safe_get('RSI', idx-10)

        rsi_higher = rsi > rsi_prev

        # MACD divergence
        macd = self._safe_get('MACD', idx)
        macd_prev = self._safe_get('MACD', idx-10)

        macd_higher = macd > macd_prev

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_support = volume_ratio > 1.1

        # Oversold condition
        oversold = rsi < 40

        return price_lower_low and (rsi_higher or macd_higher) and volume_support and oversold

    # Rule 27: Structure Break Up
    def rule_27_structure_break_up(self, idx):
        """Market structure break upward"""
        if idx < 10:
            return False

        # Look for structure break pattern
        close = self._safe_get('close', idx)
        high_5 = max([self._safe_get('high', i) for i in range(max(0, idx-5), idx)])

        # Current close breaks above recent highs
        structure_break = close > high_5 * 1.001

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_confirmation = volume_ratio > 1.2

        # Momentum support
        rsi = self._safe_get('RSI', idx)
        momentum_ok = rsi > 50

        return structure_break and volume_confirmation and momentum_ok

    # New Buy 5: CMF Positive
    def new_buy_5_cmf_positive(self, idx):
        """CMF Positive: Chaikin Money Flow is positive"""
        if idx < 20:
            return False

        # Calculate CMF if not available
        cmf = self._safe_get('CMF', idx)
        if np.isnan(cmf):
            # Calculate CMF manually
            try:
                high = self._safe_get('high', idx)
                low = self._safe_get('low', idx)
                close = self._safe_get('close', idx)
                volume = self._safe_get('volume', idx)

                if high == low:
                    return False

                mfv = ((close - low) - (high - close)) / (high - low)

                # Get recent data for rolling calculation
                mfv_sum = 0
                volume_sum = 0
                for i in range(max(0, idx-19), idx+1):
                    h = self._safe_get('high', i)
                    l = self._safe_get('low', i)
                    c = self._safe_get('close', i)
                    v = self._safe_get('volume', i)

                    if h != l:
                        mfv_i = ((c - l) - (h - c)) / (h - l)
                        mfv_sum += mfv_i * v
                        volume_sum += v

                if volume_sum > 0:
                    cmf = mfv_sum / volume_sum
                else:
                    return False
            except:
                return False

        # CMF positive indicates buying pressure
        cmf_positive = cmf > 0

        # Additional confirmation
        close = self._safe_get('close', idx)
        close_prev = self._safe_get('close', idx-1)
        price_up = close > close_prev

        return cmf_positive and price_up

    # Rule 9: EMA Alignment
    def rule_9_ema_alignment(self, idx):
        """EMA Alignment: EMA12 > EMA26 > EMA50"""
        if idx < 50:
            return False

        ema_12 = self._safe_get('EMA_12', idx)
        ema_26 = self._safe_get('EMA_26', idx)
        ema_50 = self._safe_get('EMA_50', idx)

        # EMA alignment
        alignment = ema_12 > ema_26 > ema_50

        # Price above EMAs
        close = self._safe_get('close', idx)
        price_above = close > ema_12

        # Volume support
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 0.8

        return alignment and price_above and volume_ok

    def get_all_rules(self):
        """Get all selected buy rules"""
        return [
            ('AI Rule 1: Multi-Timeframe Momentum', self.ai_rule_1_multi_timeframe_momentum),
            ('Price Action Rule 3: Engulfing Pattern', self.price_action_rule_3_engulfing),
            ('Prof Rule 7: Mean Reversion Volatility Filter', self.prof_rule_7_mean_reversion_volatility_filter),
            ('Rule 6: Stochastic Oversold Cross', self.rule_6_stochastic_oversold_cross),
            ('Volume Rule 5: Smart Money Volume', self.volume_rule_5_smart_money_volume),
            ('Volume Rule 4: Volume Breakout Confirmation', self.volume_rule_4_breakout_confirmation),
            ('New Rule 4: Ultimate Oscillator Breakout', self.new_rule_4_ultimate_oscillator_breakout),
            ('Ext Rule 3: Bollinger Squeeze Breakout', self.ext_rule_3_bollinger_squeeze_breakout),
            ('Rule 2: Golden Cross', self.rule_2_golden_cross),
            ('Rule 7: Bollinger Band Bounce', self.rule_7_bollinger_band_bounce),
            ('AI Rule 8: Momentum Divergence Reversal', self.ai_rule_8_momentum_divergence_reversal),
            ('Rule 27: Structure Break Up', self.rule_27_structure_break_up),
            ('New Buy 5: CMF Positive', self.new_buy_5_cmf_positive),
            ('Rule 9: EMA Alignment', self.rule_9_ema_alignment),
            ('Prof Rule 10: Hull MA Trend', self.prof_rule_10_hull_ma_trend),
            ('Professional Rule 7: Chaikin Money Flow Reversal', self.professional_rule_7_chaikin_money_flow_reversal),
            ('Rule 22: Higher High Pattern', self.rule_22_higher_high_pattern),
            ('Professional Rule 1: Ichimoku Cloud Breakout', self.professional_rule_1_ichimoku_cloud_breakout),
            ('Professional Rule 10: CCI Reversal Enhanced', self.professional_rule_10_cci_reversal_enhanced),
            ('Rule 3: RSI Oversold', self.rule_3_rsi_oversold),
            ('Advanced Rule 5: Donchian Channel Breakout', self.advanced_rule_5_donchian_channel_breakout),
            ('Advanced Rule 7: DMI ADX Filter', self.advanced_rule_7_dmi_adx_filter),
            ('Ext Rule 6: Fibonacci Support Confluence', self.ext_rule_6_fibonacci_support_confluence),
            ('Rule 24: MFI Oversold', self.rule_24_mfi_oversold),
            ('Professional Rule 5: Bollinger Band Squeeze', self.professional_rule_5_bollinger_band_squeeze),
            ('Rule 12: Hammer Pattern', self.rule_12_hammer_pattern),
            ('AI Rule 10: Composite Sentiment Reversal', self.ai_rule_10_composite_sentiment_reversal),
            ('Ext Rule 5: ATR Volatility Expansion', self.ext_rule_5_atr_volatility_expansion),
            ('Ext Rule 1: RSI Bullish Divergence', self.ext_rule_1_rsi_bullish_divergence),
            ('Research Rule 5: Volatility Breakout', self.research_rule_5_volatility_breakout),
            ('SMC Rule 5: Institutional Candle Pattern', self.smc_rule_5_institutional_candle_pattern),
            ('Momentum Rule 5: Momentum Breakout', self.momentum_rule_5_momentum_breakout),
            ('Volume Rule 3: Dark Pool Activity', self.volume_rule_3_dark_pool_activity),
            ('Rule 11: RSI Divergence', self.rule_11_rsi_divergence),
            ('Rule 21: Gap Up', self.rule_21_gap_up),
            ('AI Rule 3: Smart Money Flow Divergence', self.ai_rule_3_smart_money_flow_divergence),
            ('Momentum Rule 2: Momentum Divergence Recovery', self.momentum_rule_2_momentum_divergence_recovery),
            ('Volatility Rule 2: ATR Expansion Signal', self.volatility_rule_2_atr_expansion_signal),
            ('Reversal Rule 4: Bullish Divergence Confluence', self.reversal_rule_4_bullish_divergence_confluence),
            ('Rule 10: Volume Spike', self.rule_10_volume_spike),
            ('AI Rule 6: Market Structure Shift', self.ai_rule_6_market_structure_shift),
            ('Acad Rule 3: Volatility Breakout', self.acad_rule_3_volatility_breakout),
            ('Acad Rule 2: Mean Reversion Factor', self.acad_rule_2_mean_reversion_factor),
            ('Acad Rule 1: Momentum Factor', self.acad_rule_1_momentum_factor),
            ('Rule 1: MA Alignment with RSI Oversold', self.rule_1_ma_alignment_with_rsi_oversold),
            ('Rule 28: Volume Breakout', self.rule_28_volume_breakout),
            ('SMC Rule 1: Order Block Retest', self.smc_rule_1_order_block_retest),
            ('SMC Rule 2: Fair Value Gap Fill', self.smc_rule_2_fair_value_gap_fill),
        ]

    # Advanced Rule 7: DMI ADX Filter
    def advanced_rule_7_dmi_adx_filter(self, idx):
        """
        Directional Movement (DMI/ADX) Filter
        Buy when +DI crosses above -DI and ADX indicates strong trend
        """
        if idx < 14:
            return False

        try:
            # Get DMI/ADX values
            current_plus_di = self._safe_get('PLUS_DI', idx)
            current_minus_di = self._safe_get('MINUS_DI', idx)
            current_adx = self._safe_get('ADX', idx)

            prev_plus_di = self._safe_get('PLUS_DI', idx-1)
            prev_minus_di = self._safe_get('MINUS_DI', idx-1)

            if any(np.isnan([current_plus_di, current_minus_di, current_adx, prev_plus_di, prev_minus_di])):
                return False

            # DMI crossover: +DI crosses above -DI
            di_crossover = current_plus_di > current_minus_di and prev_plus_di <= prev_minus_di

            # ADX trend strength filter
            strong_trend = current_adx > 25

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_ok = volume_ratio > 1.0

            return di_crossover and strong_trend and volume_ok

        except Exception:
            return False

    # Ext Rule 6: Fibonacci Support Confluence
    def ext_rule_6_fibonacci_support_confluence(self, idx):
        """
        Fibonacci Support Confluence
        Price bounces from key Fibonacci retracement levels
        """
        if idx < 50:
            return False

        try:
            current_close = self._safe_get('close', idx)
            current_low = self._safe_get('low', idx)

            # Find recent swing high and low for Fibonacci calculation
            lookback = 30
            highs = [self._safe_get('high', i) for i in range(idx-lookback, idx)]
            lows = [self._safe_get('low', i) for i in range(idx-lookback, idx)]

            if any(np.isnan(highs)) or any(np.isnan(lows)):
                return False

            swing_high = max(highs)
            swing_low = min(lows)

            # Calculate Fibonacci retracement levels
            fib_range = swing_high - swing_low
            fib_236 = swing_high - (fib_range * 0.236)
            fib_382 = swing_high - (fib_range * 0.382)
            fib_618 = swing_high - (fib_range * 0.618)

            # Check if price is near key Fibonacci levels (within 0.5%)
            tolerance = current_close * 0.005

            near_fib_236 = abs(current_close - fib_236) < tolerance
            near_fib_382 = abs(current_close - fib_382) < tolerance
            near_fib_618 = abs(current_close - fib_618) < tolerance

            near_fib_level = near_fib_236 or near_fib_382 or near_fib_618

            # Bounce confirmation: low touched level, close above it
            bounce_confirmation = current_low <= min(fib_236, fib_382, fib_618) and current_close > current_low

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.1

            return near_fib_level and bounce_confirmation and volume_confirmation

        except Exception:
            return False

    # Rule 24: MFI Oversold
    def rule_24_mfi_oversold(self, idx):
        """Money Flow Index oversold"""
        if idx < 14:
            return False

        mfi = self._safe_get('MFI', idx)
        mfi_prev = self._safe_get('MFI', idx-1)

        if np.isnan(mfi) or np.isnan(mfi_prev):
            return False

        # MFI crossing above 20 from oversold
        oversold_recovery = mfi_prev < 20 and mfi > 20

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 0.8

        return oversold_recovery and volume_ok

    # Professional Rule 5: Bollinger Band Squeeze
    def professional_rule_5_bollinger_band_squeeze(self, idx):
        """
        Bollinger Band Squeeze Strategy
        Low volatility followed by expansion with directional bias
        """
        if idx < 20:
            return False

        try:
            # Get Bollinger Band data
            bb_upper = self._safe_get('BB_upper', idx)
            bb_lower = self._safe_get('BB_lower', idx)
            bb_middle = self._safe_get('BB_middle', idx)

            if any(np.isnan([bb_upper, bb_lower, bb_middle])):
                return False

            # Calculate band width
            current_width = (bb_upper - bb_lower) / bb_middle

            # Get previous width
            prev_upper = self._safe_get('BB_upper', idx-1)
            prev_lower = self._safe_get('BB_lower', idx-1)
            prev_middle = self._safe_get('BB_middle', idx-1)

            if any(np.isnan([prev_upper, prev_lower, prev_middle])):
                return False

            prev_width = (prev_upper - prev_lower) / prev_middle

            # Squeeze detection: low volatility
            avg_width = np.mean([(self._safe_get('BB_upper', i) - self._safe_get('BB_lower', i)) / self._safe_get('BB_middle', i)
                                for i in range(max(0, idx-20), idx) if not np.isnan(self._safe_get('BB_middle', i))])

            squeeze = current_width < avg_width * 0.8

            # Expansion: width increasing
            expanding = current_width > prev_width

            # Directional bias: price above middle band
            current_close = self._safe_get('close', idx)
            bullish_bias = current_close > bb_middle

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.1

            return squeeze and expanding and bullish_bias and volume_confirmation

        except Exception:
            return False

    # Rule 12: Hammer Pattern
    def rule_12_hammer_pattern(self, idx):
        """Hammer candlestick pattern"""
        if idx < 2:
            return False

        # Current candle data
        open_price = self._safe_get('open', idx)
        close_price = self._safe_get('close', idx)
        high_price = self._safe_get('high', idx)
        low_price = self._safe_get('low', idx)

        if any(np.isnan([open_price, close_price, high_price, low_price])):
            return False

        # Hammer pattern characteristics
        body = abs(close_price - open_price)
        total_range = high_price - low_price

        if total_range == 0:
            return False

        # Small body (less than 1/3 of total range)
        small_body = body < total_range / 3

        # Long lower shadow (at least 2x body size)
        lower_shadow = min(open_price, close_price) - low_price
        long_lower_shadow = lower_shadow >= body * 2

        # Short upper shadow (less than body size)
        upper_shadow = high_price - max(open_price, close_price)
        short_upper_shadow = upper_shadow < body

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 1.0

        return small_body and long_lower_shadow and short_upper_shadow and volume_ok

    # AI Rule 10: Composite Sentiment Reversal
    def ai_rule_10_composite_sentiment_reversal(self, idx):
        """
        Composite Sentiment Reversal
        Logic: Multiple oversold indicators align for reversal signal
        """
        if idx < 30:
            return False

        # RSI oversold
        rsi = self._safe_get('RSI', idx)
        rsi_oversold = rsi < 35

        # Stochastic oversold
        stoch_k = self._safe_get('STOCH_K', idx)
        stoch_oversold = stoch_k < 25

        # Williams %R oversold
        williams_r = self._safe_get('WILLIAMS_R', idx)
        williams_oversold = williams_r < -75

        # Price near support (Bollinger lower band)
        close = self._safe_get('close', idx)
        bb_lower = self._safe_get('BB_lower', idx)
        near_support = close <= bb_lower * 1.02

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_support = volume_ratio > 1.1

        # At least 2 oversold conditions + support + volume
        oversold_count = sum([rsi_oversold, stoch_oversold, williams_oversold])

        return oversold_count >= 2 and near_support and volume_support

    # Ext Rule 5: ATR Volatility Expansion
    def ext_rule_5_atr_volatility_expansion(self, idx):
        """
        ATR Volatility Expansion
        Buy when volatility expands after compression
        """
        if idx < 20:
            return False

        try:
            # Current ATR
            current_atr = self._safe_get('ATR', idx)

            # Average ATR over last 20 periods
            atr_values = [self._safe_get('ATR', i) for i in range(max(0, idx-19), idx+1)]
            atr_values = [x for x in atr_values if not np.isnan(x)]

            if len(atr_values) < 10:
                return False

            avg_atr = np.mean(atr_values)

            # Volatility expansion: current ATR significantly above average
            volatility_expansion = current_atr > avg_atr * 1.3

            # Price breakout confirmation
            close = self._safe_get('close', idx)
            high_10 = max([self._safe_get('high', i) for i in range(max(0, idx-9), idx+1)])

            price_breakout = close > high_10 * 0.999

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.2

            return volatility_expansion and price_breakout and volume_confirmation

        except Exception:
            return False

    # Ext Rule 1: RSI Bullish Divergence
    def ext_rule_1_rsi_bullish_divergence(self, idx):
        """
        RSI Bullish Divergence
        Price makes lower lows while RSI makes higher lows
        """
        if idx < 20:
            return False

        try:
            # Get recent price and RSI data
            lookback = 10
            prices = [self._safe_get('close', i) for i in range(idx-lookback, idx+1)]
            rsi_values = [self._safe_get('RSI', i) for i in range(idx-lookback, idx+1)]

            if any(np.isnan(prices)) or any(np.isnan(rsi_values)):
                return False

            # Find recent low
            recent_low_idx = np.argmin(prices[-5:])  # Last 5 periods
            recent_low_price = prices[-5:][recent_low_idx]
            recent_low_rsi = rsi_values[-5:][recent_low_idx]

            # Find earlier low
            earlier_prices = prices[:-5]
            if len(earlier_prices) < 5:
                return False

            earlier_low_idx = np.argmin(earlier_prices)
            earlier_low_price = earlier_prices[earlier_low_idx]
            earlier_low_rsi = rsi_values[earlier_low_idx]

            # Bullish divergence: price lower low, RSI higher low
            price_lower_low = recent_low_price < earlier_low_price
            rsi_higher_low = recent_low_rsi > earlier_low_rsi

            # Current RSI recovery
            current_rsi = self._safe_get('RSI', idx)
            rsi_recovery = current_rsi > recent_low_rsi

            return price_lower_low and rsi_higher_low and rsi_recovery

        except Exception:
            return False

    # Research Rule 5: Volatility Breakout
    def research_rule_5_volatility_breakout(self, idx):
        """
        Volatility Breakout Strategy
        Based on academic research on volatility clustering
        """
        if idx < 20:
            return False

        try:
            # Calculate volatility (standard deviation of returns)
            returns = []
            for i in range(max(0, idx-19), idx+1):
                close = self._safe_get('close', i)
                close_prev = self._safe_get('close', i-1)
                if not np.isnan(close) and not np.isnan(close_prev) and close_prev != 0:
                    returns.append((close - close_prev) / close_prev)

            if len(returns) < 10:
                return False

            current_volatility = np.std(returns)

            # Historical volatility average
            historical_returns = []
            for i in range(max(0, idx-39), idx-19):
                close = self._safe_get('close', i)
                close_prev = self._safe_get('close', i-1)
                if not np.isnan(close) and not np.isnan(close_prev) and close_prev != 0:
                    historical_returns.append((close - close_prev) / close_prev)

            if len(historical_returns) < 10:
                return False

            historical_volatility = np.std(historical_returns)

            # Volatility breakout: current volatility significantly higher
            volatility_breakout = current_volatility > historical_volatility * 1.5

            # Price momentum confirmation
            close = self._safe_get('close', idx)
            close_prev = self._safe_get('close', idx-1)
            momentum_up = close > close_prev

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.2

            return volatility_breakout and momentum_up and volume_confirmation

        except Exception:
            return False

    # SMC Rule 5: Institutional Candle Pattern
    def smc_rule_5_institutional_candle_pattern(self, idx):
        """Institutional candle pattern"""
        if idx < 5:
            return False

        try:
            # Large institutional candle characteristics
            current_open = self._safe_get('open', idx)
            current_close = self._safe_get('close', idx)
            current_high = self._safe_get('high', idx)
            current_low = self._safe_get('low', idx)

            if any(np.isnan([current_open, current_close, current_high, current_low])):
                return False

            # Body size
            body_size = abs(current_close - current_open) / current_open * 100

            # Range analysis
            total_range = (current_high - current_low) / current_low * 100
            body_to_range_ratio = body_size / total_range if total_range > 0 else 0

            # Institutional characteristics
            large_body = body_size > 0.8
            strong_body_ratio = body_to_range_ratio > 0.7
            bullish_direction = current_close > current_open

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            high_volume = volume_ratio > 2.0

            # Follow-through potential
            recent_high = max([self._safe_get('high', i) for i in range(max(0, idx-10), idx)])
            above_resistance = current_close > recent_high

            return large_body and strong_body_ratio and bullish_direction and high_volume and above_resistance

        except Exception:
            return False

    # Momentum Rule 5: Momentum Breakout
    def momentum_rule_5_momentum_breakout(self, idx):
        """Momentum-based breakout"""
        if idx < 20:
            return False

        try:
            # Price momentum
            close = self._safe_get('close', idx)
            close_10 = self._safe_get('close', idx-10)

            if np.isnan(close) or np.isnan(close_10) or close_10 == 0:
                return False

            momentum = (close - close_10) / close_10 * 100
            strong_momentum = momentum > 3.0

            # Volume momentum
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_momentum = volume_ratio > 1.5

            # RSI momentum (not overbought)
            rsi = self._safe_get('RSI', idx)
            rsi_ok = 50 < rsi < 75

            # Price breakout
            high_20 = max([self._safe_get('high', i) for i in range(max(0, idx-20), idx)])
            breakout = close > high_20 * 1.001

            return strong_momentum and volume_momentum and rsi_ok and breakout

        except Exception:
            return False

    # Volume Rule 3: Dark Pool Activity
    def volume_rule_3_dark_pool_activity(self, idx):
        """Detect potential dark pool activity"""
        if idx < 30:
            return False

        try:
            # Large volume with minimal price impact
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_spike = volume_ratio > 2.0

            # Low volatility during volume spike
            current_range = self._safe_get('high', idx) - self._safe_get('low', idx)
            avg_range = np.mean([self._safe_get('high', i) - self._safe_get('low', i)
                               for i in range(max(0, idx-10), idx)])

            low_volatility = current_range < avg_range * 0.7

            # Subsequent breakout potential
            close = self._safe_get('close', idx)
            prev_high = max([self._safe_get('high', i) for i in range(max(0, idx-5), idx)])

            breakout_potential = close > prev_high * 0.999

            return volume_spike and low_volatility and breakout_potential

        except Exception:
            return False

    # Rule 11: RSI Divergence
    def rule_11_rsi_divergence(self, idx):
        """RSI divergence pattern"""
        if idx < 20:
            return False

        try:
            # Get recent price and RSI data
            prices = [self._safe_get('close', i) for i in range(idx-10, idx+1)]
            rsi_values = [self._safe_get('RSI', i) for i in range(idx-10, idx+1)]

            if any(np.isnan(prices)) or any(np.isnan(rsi_values)):
                return False

            # Find recent low and earlier low
            recent_low_price = min(prices[-5:])
            recent_low_rsi = min(rsi_values[-5:])

            earlier_low_price = min(prices[:-5])
            earlier_low_rsi = min(rsi_values[:-5])

            # Bullish divergence: price lower low, RSI higher low
            price_lower_low = recent_low_price < earlier_low_price
            rsi_higher_low = recent_low_rsi > earlier_low_rsi

            # Current recovery
            current_rsi = self._safe_get('RSI', idx)
            rsi_recovery = current_rsi > recent_low_rsi

            return price_lower_low and rsi_higher_low and rsi_recovery

        except Exception:
            return False

    # Rule 21: Gap Up
    def rule_21_gap_up(self, idx):
        """Gap up signal"""
        if idx < 1:
            return False

        # Gap up detection
        current_low = self._safe_get('low', idx)
        prev_high = self._safe_get('high', idx-1)

        if np.isnan(current_low) or np.isnan(prev_high):
            return False

        # Gap up: current low > previous high
        gap_up = current_low > prev_high

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 1.2

        # Price strength
        close = self._safe_get('close', idx)
        open_price = self._safe_get('open', idx)
        strong_close = close > open_price

        return gap_up and volume_ok and strong_close

    # AI Rule 3: Smart Money Flow Divergence
    def ai_rule_3_smart_money_flow_divergence(self, idx):
        """
        Smart Money Flow Divergence
        Logic: Institutional money flow diverges from retail sentiment
        """
        if idx < 20:
            return False

        # Volume analysis for smart money detection
        volume_ratio = self._safe_get('volume_ratio', idx)
        large_volume = volume_ratio > 1.5

        # Price action vs volume divergence
        close = self._safe_get('close', idx)
        close_prev = self._safe_get('close', idx-5)

        price_declining = close < close_prev
        volume_increasing = volume_ratio > 1.2

        # Smart money accumulation pattern
        smart_money_pattern = price_declining and volume_increasing

        # Momentum reversal signal
        rsi = self._safe_get('RSI', idx)
        oversold_recovery = 25 < rsi < 45

        return smart_money_pattern and oversold_recovery and large_volume

    # Momentum Rule 2: Momentum Divergence Recovery
    def momentum_rule_2_momentum_divergence_recovery(self, idx):
        """Momentum divergence recovery pattern"""
        if idx < 15:
            return False

        try:
            # Price and momentum comparison
            close = self._safe_get('close', idx)
            close_10 = self._safe_get('close', idx-10)

            rsi = self._safe_get('RSI', idx)
            rsi_10 = self._safe_get('RSI', idx-10)

            if any(np.isnan([close, close_10, rsi, rsi_10])):
                return False

            # Divergence: price lower, momentum higher
            price_lower = close < close_10
            momentum_higher = rsi > rsi_10

            # Recovery signal
            macd = self._safe_get('MACD', idx)
            macd_signal = self._safe_get('MACD_signal', idx)
            macd_bullish = macd > macd_signal

            # Volume support
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_ok = volume_ratio > 1.0

            return price_lower and momentum_higher and macd_bullish and volume_ok

        except Exception:
            return False

    # Volatility Rule 2: ATR Expansion Signal
    def volatility_rule_2_atr_expansion_signal(self, idx):
        """ATR expansion signal"""
        if idx < 20:
            return False

        try:
            # Current ATR vs historical average
            current_atr = self._safe_get('ATR', idx)
            avg_atr = np.mean([self._safe_get('ATR', i) for i in range(max(0, idx-20), idx)])

            if np.isnan(current_atr) or np.isnan(avg_atr) or avg_atr == 0:
                return False

            # ATR expansion
            atr_expansion = current_atr > avg_atr * 1.3

            # Price breakout confirmation
            close = self._safe_get('close', idx)
            high_10 = max([self._safe_get('high', i) for i in range(max(0, idx-10), idx)])

            breakout = close > high_10

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.2

            return atr_expansion and breakout and volume_confirmation

        except Exception:
            return False

    # Reversal Rule 4: Bullish Divergence Confluence
    def reversal_rule_4_bullish_divergence_confluence(self, idx):
        """Bullish divergence confluence across multiple indicators"""
        if idx < 20:
            return False

        try:
            # Price analysis
            close = self._safe_get('close', idx)
            close_10 = self._safe_get('close', idx-10)

            # Multiple indicator divergence
            rsi = self._safe_get('RSI', idx)
            rsi_10 = self._safe_get('RSI', idx-10)

            macd = self._safe_get('MACD', idx)
            macd_10 = self._safe_get('MACD', idx-10)

            if any(np.isnan([close, close_10, rsi, rsi_10, macd, macd_10])):
                return False

            # Price lower, indicators higher
            price_lower = close < close_10
            rsi_higher = rsi > rsi_10
            macd_higher = macd > macd_10

            # Confluence: at least 2 indicators showing divergence
            divergence_count = sum([rsi_higher, macd_higher])

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_ok = volume_ratio > 1.1

            return price_lower and divergence_count >= 2 and volume_ok

        except Exception:
            return False

    # Rule 10: Volume Spike
    def rule_10_volume_spike(self, idx):
        """Volume spike signal"""
        if idx < 20:
            return False

        # Volume spike detection
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_spike = volume_ratio > 2.0

        # Price confirmation
        close = self._safe_get('close', idx)
        open_price = self._safe_get('open', idx)
        bullish_candle = close > open_price

        # Not overbought
        rsi = self._safe_get('RSI', idx)
        not_overbought = rsi < 75

        return volume_spike and bullish_candle and not_overbought

    # AI Rule 6: Market Structure Shift
    def ai_rule_6_market_structure_shift(self, idx):
        """
        Market Structure Shift Detection
        Logic: Identifies shifts from bearish to bullish market structure
        """
        if idx < 30:
            return False

        # Structure analysis
        close = self._safe_get('close', idx)

        # Recent lows and highs
        recent_lows = [self._safe_get('low', i) for i in range(max(0, idx-10), idx+1)]
        recent_highs = [self._safe_get('high', i) for i in range(max(0, idx-10), idx+1)]

        if any(np.isnan(recent_lows)) or any(np.isnan(recent_highs)):
            return False

        # Higher low formation
        current_low = min(recent_lows[-3:])
        previous_low = min(recent_lows[:-3])
        higher_low = current_low > previous_low

        # Break of recent resistance
        recent_resistance = max(recent_highs[:-1])
        resistance_break = close > recent_resistance

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_confirmation = volume_ratio > 1.3

        return higher_low and resistance_break and volume_confirmation

    # Acad Rule 3: Volatility Breakout
    def acad_rule_3_volatility_breakout(self, idx):
        """
        Volatility Breakout Strategy
        Based on Donchian channel breakouts and volatility research
        """
        if idx < 20:
            return False

        # Donchian channel breakout
        high_20 = max([self._safe_get('high', i) for i in range(max(0, idx-20), idx)])
        close = self._safe_get('close', idx)

        breakout = close > high_20

        # Low volatility before breakout
        atr = self._safe_get('ATR', idx)
        avg_atr = np.mean([self._safe_get('ATR', i) for i in range(max(0, idx-20), idx)])

        if np.isnan(atr) or np.isnan(avg_atr) or avg_atr == 0:
            return False

        low_volatility_before = atr < avg_atr * 0.8

        # Volume expansion on breakout
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_expansion = volume_ratio > 1.5

        return breakout and low_volatility_before and volume_expansion

    # Acad Rule 2: Mean Reversion Factor
    def acad_rule_2_mean_reversion_factor(self, idx):
        """
        Short-term Mean Reversion
        Based on Lehmann (1990) and Jegadeesh (1990) research
        """
        if idx < 5:
            return False

        # Short-term reversal: buy after recent decline
        close = self._safe_get('close', idx)
        close_5d_ago = self._safe_get('close', idx - 5)

        if np.isnan(close) or np.isnan(close_5d_ago) or close_5d_ago == 0:
            return False

        short_term_return = (close / close_5d_ago - 1) * 100

        # Buy after significant decline
        significant_decline = short_term_return < -3.0

        # But not in a major downtrend
        sma_50 = self._safe_get('SMA_50', idx)
        above_long_term_trend = close > sma_50 * 0.95 if not np.isnan(sma_50) else True

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 1.1

        return significant_decline and above_long_term_trend and volume_ok

    # Acad Rule 1: Momentum Factor
    def acad_rule_1_momentum_factor(self, idx):
        """
        Momentum Factor Strategy
        Based on Jegadeesh & Titman (1993) research
        """
        if idx < 30:
            return False

        # Medium-term momentum (3-12 months, using 30 days as proxy)
        close = self._safe_get('close', idx)
        close_30d_ago = self._safe_get('close', idx - 30)

        if np.isnan(close) or np.isnan(close_30d_ago) or close_30d_ago == 0:
            return False

        momentum_return = (close / close_30d_ago - 1) * 100

        # Strong positive momentum
        strong_momentum = momentum_return > 5.0

        # Trend continuation signal
        sma_20 = self._safe_get('SMA_20', idx)
        trend_continuation = close > sma_20 if not np.isnan(sma_20) else True

        # Volume support
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_support = volume_ratio > 1.0

        return strong_momentum and trend_continuation and volume_support

    # Rule 1: MA Alignment with RSI Oversold
    def rule_1_ma_alignment_with_rsi_oversold(self, idx):
        """MA7 > MA25 > MA50 and RSI14 < 30 for high-confidence entries"""
        if idx < 50:
            return False

        # Get moving averages
        ma7 = self._safe_get('MA_7', idx)
        ma25 = self._safe_get('MA_25', idx)
        ma50 = self._safe_get('MA_50', idx)
        rsi = self._safe_get('RSI', idx)

        if any(np.isnan([ma7, ma25, ma50, rsi])):
            return False

        # Check MA alignment: MA7 > MA25 > MA50
        ma_alignment = ma7 > ma25 > ma50

        # Check RSI oversold condition
        rsi_oversold = rsi < 30

        return ma_alignment and rsi_oversold

    # Rule 28: Volume Breakout
    def rule_28_volume_breakout(self, idx):
        """Volume breakout signal"""
        if idx < 20:
            return False

        # Volume breakout
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_breakout = volume_ratio > 2.5

        # Price breakout confirmation
        close = self._safe_get('close', idx)
        high_20 = max([self._safe_get('high', i) for i in range(max(0, idx-20), idx)])

        price_breakout = close > high_20 * 1.001

        # Momentum confirmation
        rsi = self._safe_get('RSI', idx)
        momentum_ok = rsi > 50

        return volume_breakout and price_breakout and momentum_ok

    # SMC Rule 1: Order Block Retest
    def smc_rule_1_order_block_retest(self, idx):
        """Order block retest pattern"""
        if idx < 10:
            return False

        try:
            # Identify potential order block (large volume candle)
            volumes = [self._safe_get('volume', i) for i in range(max(0, idx-10), idx)]
            avg_volume = np.mean(volumes)

            # Find recent high volume candle
            order_block_idx = None
            for i in range(max(0, idx-10), idx):
                vol = self._safe_get('volume', i)
                if vol > avg_volume * 2.0:
                    order_block_idx = i
                    break

            if order_block_idx is None:
                return False

            # Order block price level
            order_block_low = self._safe_get('low', order_block_idx)
            order_block_high = self._safe_get('high', order_block_idx)

            # Current price testing order block
            current_low = self._safe_get('low', idx)
            current_close = self._safe_get('close', idx)

            # Retest condition
            retest = (current_low <= order_block_high and
                     current_close >= order_block_low)

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_ok = volume_ratio > 1.1

            return retest and volume_ok

        except Exception:
            return False

    # SMC Rule 2: Fair Value Gap Fill
    def smc_rule_2_fair_value_gap_fill(self, idx):
        """Fair value gap fill pattern"""
        if idx < 3:
            return False

        try:
            # Identify fair value gap (gap between candles)
            high_2 = self._safe_get('high', idx-2)
            low_1 = self._safe_get('low', idx-1)

            # Gap exists if high of 2 candles ago < low of previous candle
            gap_exists = high_2 < low_1

            if not gap_exists:
                return False

            # Current candle filling the gap
            current_low = self._safe_get('low', idx)
            current_high = self._safe_get('high', idx)

            # Gap fill condition
            gap_fill = (current_low <= high_2 and current_high >= low_1)

            # Bullish continuation after gap fill
            current_close = self._safe_get('close', idx)
            current_open = self._safe_get('open', idx)
            bullish_continuation = current_close > current_open

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_ok = volume_ratio > 1.0

            return gap_fill and bullish_continuation and volume_ok

        except Exception:
            return False

    # Prof Rule 10: Hull MA Trend
    def prof_rule_10_hull_ma_trend(self, idx):
        """
        Hull Moving Average Trend
        Based on reduced-lag moving average methodology
        """
        if idx < 25:
            return False

        # Hull MA trending up
        hma = self._safe_get('HMA_20', idx)
        hma_prev = self._safe_get('HMA_20', idx-3)

        if np.isnan(hma) or np.isnan(hma_prev):
            return False

        hma_trending_up = hma > hma_prev

        # Price above Hull MA
        close = self._safe_get('close', idx)
        above_hma = close > hma

        # Recent pullback to Hull MA
        low = self._safe_get('low', idx)
        near_hma = abs(low - hma) / hma < 0.01 if hma > 0 else False

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 0.8

        return hma_trending_up and above_hma and near_hma and volume_ok

    # Professional Rule 7: Chaikin Money Flow Reversal
    def professional_rule_7_chaikin_money_flow_reversal(self, idx):
        """
        Chaikin Money Flow Reversal Strategy
        CMF crossing above zero indicates buying pressure
        """
        if idx < 20:
            return False

        try:
            period = 20

            # Calculate Money Flow Volume for each period
            mf_volumes = []
            volumes = []

            for i in range(idx-period+1, idx+1):
                high = self._safe_get('high', i)
                low = self._safe_get('low', i)
                close = self._safe_get('close', i)
                volume = self._safe_get('volume', i)

                if any(np.isnan([high, low, close, volume])):
                    return False

                # Money Flow Multiplier
                if high != low:
                    mf_multiplier = ((close - low) - (high - close)) / (high - low)
                else:
                    mf_multiplier = 0

                # Money Flow Volume
                mf_volume = mf_multiplier * volume

                mf_volumes.append(mf_volume)
                volumes.append(volume)

            # Chaikin Money Flow
            total_mf_volume = sum(mf_volumes)
            total_volume = sum(volumes)

            if total_volume == 0:
                return False

            current_cmf = total_mf_volume / total_volume

            # Calculate previous CMF
            prev_mf_volumes = []
            prev_volumes = []

            for i in range(idx-period, idx):
                high = self._safe_get('high', i)
                low = self._safe_get('low', i)
                close = self._safe_get('close', i)
                volume = self._safe_get('volume', i)

                if any(np.isnan([high, low, close, volume])):
                    continue

                if high != low:
                    mf_multiplier = ((close - low) - (high - close)) / (high - low)
                else:
                    mf_multiplier = 0

                mf_volume = mf_multiplier * volume
                prev_mf_volumes.append(mf_volume)
                prev_volumes.append(volume)

            if sum(prev_volumes) == 0:
                return False

            prev_cmf = sum(prev_mf_volumes) / sum(prev_volumes)

            # CMF reversal: was negative, now positive
            cmf_reversal = prev_cmf <= 0 and current_cmf > 0

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.1

            return cmf_reversal and volume_confirmation

        except Exception:
            return False

    # Rule 22: Higher High Pattern
    def rule_22_higher_high_pattern(self, idx):
        """Higher high pattern"""
        if idx < 5:
            return False

        # Check for series of higher highs
        highs = [self._safe_get('high', idx-i) for i in range(5)]

        if any(np.isnan(highs)):
            return False

        higher_highs = all(highs[i] > highs[i+1] for i in range(4))

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 1.0

        # Price momentum
        close = self._safe_get('close', idx)
        close_prev = self._safe_get('close', idx-1)
        momentum_up = close > close_prev

        return higher_highs and volume_ok and momentum_up

    # Professional Rule 1: Ichimoku Cloud Breakout
    def professional_rule_1_ichimoku_cloud_breakout(self, idx):
        """
        Ichimoku Cloud Breakout Strategy
        """
        if idx < 52:
            return False

        try:
            # Calculate Ichimoku components (simplified)
            period1 = 9   # Tenkan-sen
            period2 = 26  # Kijun-sen
            period3 = 52  # Senkou Span B

            # Get recent highs and lows
            highs = [self._safe_get('high', i) for i in range(idx-period3, idx+1)]
            lows = [self._safe_get('low', i) for i in range(idx-period3, idx+1)]

            if any(np.isnan(highs)) or any(np.isnan(lows)):
                return False

            # Tenkan-sen (Conversion Line)
            tenkan_high = max(highs[-period1:])
            tenkan_low = min(lows[-period1:])
            tenkan_sen = (tenkan_high + tenkan_low) / 2

            # Kijun-sen (Base Line)
            kijun_high = max(highs[-period2:])
            kijun_low = min(lows[-period2:])
            kijun_sen = (kijun_high + kijun_low) / 2

            # Senkou Span A (Leading Span A)
            senkou_span_a = (tenkan_sen + kijun_sen) / 2

            # Senkou Span B (Leading Span B)
            senkou_span_b = (max(highs) + min(lows)) / 2

            # Current price
            current_close = self._safe_get('close', idx)

            # Cloud breakout: price above both spans
            above_cloud = current_close > max(senkou_span_a, senkou_span_b)

            # Tenkan above Kijun (bullish signal)
            tenkan_above_kijun = tenkan_sen > kijun_sen

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.1

            return above_cloud and tenkan_above_kijun and volume_confirmation

        except Exception:
            return False

    # Professional Rule 10: CCI Reversal Enhanced
    def professional_rule_10_cci_reversal_enhanced(self, idx):
        """
        CCI Reversal Enhanced Strategy
        CCI crossing above -100 from oversold territory
        """
        if idx < 20:
            return False

        try:
            period = 20

            # Calculate CCI manually
            highs = [self._safe_get('high', i) for i in range(idx-period+1, idx+1)]
            lows = [self._safe_get('low', i) for i in range(idx-period+1, idx+1)]
            closes = [self._safe_get('close', i) for i in range(idx-period+1, idx+1)]

            if any(np.isnan(highs)) or any(np.isnan(lows)) or any(np.isnan(closes)):
                return False

            # Typical Price
            typical_prices = [(h + l + c) / 3 for h, l, c in zip(highs, lows, closes)]

            # Simple Moving Average of Typical Price
            sma_tp = np.mean(typical_prices)

            # Mean Absolute Deviation
            mad = np.mean([abs(tp - sma_tp) for tp in typical_prices])

            if mad == 0:
                return False

            # Current CCI
            current_cci = (typical_prices[-1] - sma_tp) / (0.015 * mad)

            # Previous CCI
            prev_highs = [self._safe_get('high', i) for i in range(idx-period, idx)]
            prev_lows = [self._safe_get('low', i) for i in range(idx-period, idx)]
            prev_closes = [self._safe_get('close', i) for i in range(idx-period, idx)]

            if any(np.isnan(prev_highs)) or any(np.isnan(prev_lows)) or any(np.isnan(prev_closes)):
                return False

            prev_typical_prices = [(h + l + c) / 3 for h, l, c in zip(prev_highs, prev_lows, prev_closes)]
            prev_sma_tp = np.mean(prev_typical_prices)
            prev_mad = np.mean([abs(tp - prev_sma_tp) for tp in prev_typical_prices])

            if prev_mad == 0:
                return False

            prev_cci = (prev_typical_prices[-1] - prev_sma_tp) / (0.015 * prev_mad)

            # CCI reversal: was below -100, now above -100
            cci_reversal = prev_cci <= -100 and current_cci > -100

            # Volume confirmation
            volume_ratio = self._safe_get('volume_ratio', idx)
            volume_confirmation = volume_ratio > 1.1

            # Price momentum
            close = self._safe_get('close', idx)
            close_prev = self._safe_get('close', idx-1)
            momentum_up = close > close_prev

            return cci_reversal and volume_confirmation and momentum_up

        except Exception:
            return False

    # Rule 3: RSI Oversold
    def rule_3_rsi_oversold(self, idx):
        """RSI oversold reversal"""
        if idx < 14:
            return False

        rsi = self._safe_get('RSI', idx)
        rsi_prev = self._safe_get('RSI', idx-1)

        # RSI was oversold and now recovering
        was_oversold = rsi_prev < 30
        recovering = rsi > 30

        # Volume support
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_ok = volume_ratio > 0.8

        return was_oversold and recovering and volume_ok

    # Advanced Rule 5: Donchian Channel Breakout
    def advanced_rule_5_donchian_channel_breakout(self, idx):
        """
        Donchian Channel Breakout
        Price breaks above highest high of last N periods
        """
        if idx < 20:
            return False

        period = 20

        # Get recent highs
        highs = [self._safe_get('high', i) for i in range(idx-period, idx)]

        if any(np.isnan(highs)):
            return False

        # Donchian upper channel
        donchian_upper = max(highs)

        # Current price
        current_close = self._safe_get('close', idx)
        current_high = self._safe_get('high', idx)

        # Breakout condition
        breakout = current_high > donchian_upper

        # Close above breakout level
        close_above = current_close > donchian_upper * 0.999

        # Volume confirmation
        volume_ratio = self._safe_get('volume_ratio', idx)
        volume_confirmation = volume_ratio > 1.5

        # Momentum confirmation
        rsi = self._safe_get('RSI', idx)
        momentum_ok = rsi > 50

        return breakout and close_above and volume_confirmation and momentum_ok
