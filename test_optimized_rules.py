"""
Test Script for Optimized Rules
Quick verification that the optimized rules are working correctly
"""

import pandas as pd
import numpy as np
from selected_buy_rules_optimized import SelectedBuyRulesOptimized
from indicators import TechnicalIndicators


def generate_test_data(num_rows=1000):
    """Generate simple test data for verification"""
    np.random.seed(42)
    
    # Generate price series
    base_price = 100
    returns = np.random.normal(0.0001, 0.02, num_rows)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    prices = np.array(prices)
    
    # Generate OHLC
    high_noise = np.random.uniform(0.001, 0.01, num_rows)
    low_noise = np.random.uniform(-0.01, -0.001, num_rows)
    
    df = pd.DataFrame({
        'close': prices,
        'open': prices * (1 + np.random.uniform(-0.005, 0.005, num_rows)),
        'high': prices * (1 + high_noise),
        'low': prices * (1 + low_noise),
        'volume': np.random.lognormal(10, 1, num_rows),
    })
    
    return df


def test_optimized_rules():
    """Test the optimized rules implementation"""
    print("🧪 Testing Optimized Rules Implementation")
    print("=" * 50)
    
    # Generate test data
    print("📊 Generating test data...")
    df = generate_test_data(1000)
    
    # Calculate indicators
    print("📈 Calculating technical indicators...")
    indicators = TechnicalIndicators(df)
    df = indicators.calculate_all_indicators()
    
    # Initialize optimized rules
    print("🚀 Initializing optimized rules...")
    try:
        optimized_rules = SelectedBuyRulesOptimized(df)
        print(f"✅ Optimized rules loaded successfully!")
        
        # Get all rules
        all_rules = optimized_rules.get_all_rules()
        print(f"📋 Found {len(all_rules)} optimized rules")
        
        # Test a few rules
        print("\n🔍 Testing rule execution...")
        test_idx = 500  # Use middle of dataset
        
        successful_tests = 0
        failed_tests = 0
        
        for i, (rule_name, rule_func) in enumerate(all_rules[:10]):  # Test first 10 rules
            try:
                result = rule_func(test_idx)
                print(f"  ✅ {rule_name}: {result}")
                successful_tests += 1
            except Exception as e:
                print(f"  ❌ {rule_name}: ERROR - {e}")
                failed_tests += 1
        
        print(f"\n📊 Test Results:")
        print(f"  ✅ Successful: {successful_tests}")
        print(f"  ❌ Failed: {failed_tests}")
        
        if failed_tests == 0:
            print("\n🎉 All tested rules executed successfully!")
            return True
        else:
            print(f"\n⚠️ {failed_tests} rules failed - check implementation")
            return False
            
    except Exception as e:
        print(f"❌ Failed to load optimized rules: {e}")
        return False


def test_performance_comparison():
    """Quick performance test"""
    print("\n⚡ Performance Test")
    print("=" * 30)
    
    # Generate larger dataset
    df = generate_test_data(5000)
    indicators = TechnicalIndicators(df)
    df = indicators.calculate_all_indicators()
    
    # Test optimized rules
    optimized_rules = SelectedBuyRulesOptimized(df)
    all_rules = optimized_rules.get_all_rules()
    
    import time
    
    # Test execution time
    test_idx = 2500
    start_time = time.perf_counter()
    
    for rule_name, rule_func in all_rules[:5]:  # Test first 5 rules
        try:
            result = rule_func(test_idx)
        except:
            pass
    
    elapsed = time.perf_counter() - start_time
    
    print(f"⏱️ Executed 5 rules in {elapsed*1000:.2f}ms")
    print(f"📊 Average per rule: {elapsed*1000/5:.2f}ms")
    print(f"🚀 Estimated throughput: {1/(elapsed/5):.0f} rules/second")
    
    return elapsed


def main():
    """Run all tests"""
    print("🚀 OPTIMIZED RULES TEST SUITE")
    print("=" * 50)
    
    # Test basic functionality
    success = test_optimized_rules()
    
    if success:
        # Test performance
        test_performance_comparison()
        
        print("\n✅ ALL TESTS PASSED!")
        print("🎯 Optimized rules are ready for backtesting!")
        print("\nTo run backtesting with optimized rules:")
        print("  python main.py optimized      # Quick test")
        print("  python main.py optimizedfull  # Full dataset")
    else:
        print("\n❌ TESTS FAILED!")
        print("Please check the optimized rules implementation")


if __name__ == "__main__":
    main()
