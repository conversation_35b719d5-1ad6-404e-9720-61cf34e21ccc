"""
Test Script for Multiple Positions and Multiprocessing Fixes
Verify that the KeyError and multiprocessing issues are resolved
"""

import pandas as pd
import numpy as np
from config import FullAnalysisConfig
from backtesting_engine import BacktestingEngine
from indicators import TechnicalIndicators


def generate_test_data(num_rows=1000):
    """Generate test data for verification"""
    np.random.seed(42)
    
    # Generate price series
    base_price = 50000
    returns = np.random.normal(0.0002, 0.015, num_rows)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    prices = np.array(prices)
    
    # Generate OHLC
    high_noise = np.random.uniform(0.002, 0.008, num_rows)
    low_noise = np.random.uniform(-0.008, -0.002, num_rows)
    
    df = pd.DataFrame({
        'close': prices,
        'open': prices * (1 + np.random.uniform(-0.003, 0.003, num_rows)),
        'high': prices * (1 + high_noise),
        'low': prices * (1 + low_noise),
        'volume': np.random.lognormal(10, 1, num_rows),
    })
    
    return df


def test_rule_performance_initialization():
    """Test that rule performance dictionary has all required keys"""
    print("🧪 Testing Rule Performance Initialization")
    print("=" * 50)
    
    # Generate test data
    df = generate_test_data(500)
    indicators = TechnicalIndicators(df)
    df = indicators.calculate_all_indicators()
    
    # Initialize engine
    config = FullAnalysisConfig()
    engine = BacktestingEngine(df, config)
    
    # Check if optimized rules are loaded
    if hasattr(engine, 'optimized_buy_rules'):
        print("✅ Optimized rules loaded successfully")
        
        # Test rule performance initialization
        rule_performance = {rule_name: {
            'signals': 0, 'entries': 0, 'exits': 0, 'total_pnl': 0, 'total_pnl_pct': 0,
            'wins': 0, 'losses': 0, 'total_win_pnl': 0, 'total_loss_pnl': 0, 'trades': []
        } for rule_name, _ in engine.all_buy_rules}
        
        # Check if all required keys are present
        required_keys = ['signals', 'entries', 'exits', 'total_pnl', 'total_pnl_pct', 
                        'wins', 'losses', 'total_win_pnl', 'total_loss_pnl', 'trades']
        
        sample_rule = list(rule_performance.keys())[0]
        sample_performance = rule_performance[sample_rule]
        
        missing_keys = [key for key in required_keys if key not in sample_performance]
        
        if not missing_keys:
            print("✅ All required performance keys present")
            print(f"   Keys: {list(sample_performance.keys())}")
            return True
        else:
            print(f"❌ Missing keys: {missing_keys}")
            return False
    else:
        print("❌ Optimized rules not loaded")
        return False


def test_multiprocessing_configuration():
    """Test multiprocessing configuration"""
    print("\n🚀 Testing Multiprocessing Configuration")
    print("=" * 50)
    
    config = FullAnalysisConfig()
    
    # Check configuration values
    tests = [
        ("USE_UNIFIED_EVALUATION", True),
        ("USE_INDEPENDENT_EVALUATION", False),
        ("MAX_CONCURRENT_TRADES", 5),
        ("USE_MULTIPROCESSING", True),
    ]
    
    all_passed = True
    
    for attr_name, expected_value in tests:
        actual_value = getattr(config, attr_name, None)
        if actual_value == expected_value:
            print(f"✅ {attr_name}: {actual_value}")
        else:
            print(f"❌ {attr_name}: Expected {expected_value}, got {actual_value}")
            all_passed = False
    
    return all_passed


def test_small_backtest():
    """Test a small backtest to verify fixes"""
    print("\n📊 Testing Small Backtest")
    print("=" * 40)
    
    try:
        # Generate test data
        df = generate_test_data(500)
        indicators = TechnicalIndicators(df)
        df = indicators.calculate_all_indicators()
        
        # Initialize engine
        config = FullAnalysisConfig()
        # Disable multiprocessing for this test to avoid complexity
        config.USE_MULTIPROCESSING = False
        
        engine = BacktestingEngine(df, config)
        
        print(f"✅ Engine initialized with {len(engine.all_buy_rules)} rules")
        print(f"✅ Max concurrent trades: {engine.max_concurrent_trades}")
        
        # Run small backtest
        start_idx = 100
        end_idx = 300
        
        print(f"📈 Running backtest from {start_idx} to {end_idx}...")
        
        result = engine._run_unified_backtest(
            start_idx=start_idx,
            end_idx=end_idx,
            stop_loss_pct=1.3,
            take_profit_pct=0.75
        )
        
        print(f"✅ Backtest completed successfully!")
        print(f"   - Total trades: {result['total_trades']}")
        print(f"   - Final capital: ${result['final_capital']:,.0f}")
        print(f"   - Total return: {result['total_return_pct']:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multiprocessing_rule_evaluation():
    """Test multiprocessing rule evaluation"""
    print("\n⚡ Testing Multiprocessing Rule Evaluation")
    print("=" * 50)
    
    try:
        # Generate test data
        df = generate_test_data(200)
        indicators = TechnicalIndicators(df)
        df = indicators.calculate_all_indicators()
        
        # Initialize engine with multiprocessing enabled
        config = FullAnalysisConfig()
        config.USE_MULTIPROCESSING = True
        
        engine = BacktestingEngine(df, config)
        
        # Test the multiprocessing rule evaluation method
        idx = 150
        rule_performance = {rule_name: {
            'signals': 0, 'entries': 0, 'exits': 0, 'total_pnl': 0, 'total_pnl_pct': 0,
            'wins': 0, 'losses': 0, 'total_win_pnl': 0, 'total_loss_pnl': 0, 'trades': []
        } for rule_name, _ in engine.all_buy_rules}
        
        print(f"🔍 Testing rule evaluation at index {idx}...")
        
        # Test multiprocessing evaluation
        active_rules = engine._evaluate_all_buy_rules_for_multiple_positions(idx, rule_performance)
        
        print(f"✅ Multiprocessing evaluation completed!")
        print(f"   - Active rules found: {len(active_rules)}")
        if active_rules:
            print(f"   - Sample rules: {active_rules[:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Multiprocessing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("🚀 FIXES VERIFICATION TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_rule_performance_initialization,
        test_multiprocessing_configuration,
        test_small_backtest,
        test_multiprocessing_rule_evaluation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ KeyError fix verified")
        print("✅ Multiprocessing configuration verified")
        print("✅ Multiple positions functionality working")
        print("\nReady to run: python main.py full")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the error messages above")


if __name__ == "__main__":
    main()
