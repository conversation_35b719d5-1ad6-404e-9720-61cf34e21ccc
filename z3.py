import pandas as pd

def convert_binance_csv_format(input_csv_path, output_csv_path):
    # CSV'yi oku
    df = pd.read_csv(input_csv_path)

    # <PERSON><PERSON>tun adlarını doğru isimlerle değiştir
    df.columns = [
        "open_time", "open", "high", "low", "close", "volume",
        "close_time", "quote_asset_volume", "number_of_trades",
        "taker_buy_base_asset_volume", "taker_buy_quote_asset_volume", "ignored"
    ]

    # Zaman damgaları mikrosaniye (us) cinsinden, datetime'a çevir
    df["open_time"] = pd.to_datetime(df["open_time"], unit="us")
    df["close_time"] = pd.to_datetime(df["close_time"], unit="us")

    # CSV olarak kaydet
    df.to_csv(output_csv_path, index=False)
    print(f"✅ Format dönüştürüldü ve kaydedildi: {output_csv_path}")

# 📦 Kullanım
convert_binance_csv_format("raw_BTCUSDT_1m.csv", "formatted_BTCUSDT_1m.csv")
