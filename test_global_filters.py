#!/usr/bin/env python3
"""
Test Global Market Filters
Verify that EMA200 and RSI75 filters are working correctly
"""

import pandas as pd
import numpy as np
from config import Config
from backtesting_engine import BacktestingEngine

def test_global_filters():
    """Test that global market filters are working"""
    print("🧪 TESTING GLOBAL MARKET FILTERS")
    print("=" * 60)
    
    # Test 1: Filters enabled (default)
    print("\n1️⃣ Testing with GLOBAL FILTERS ENABLED")
    config1 = Config()
    config1.ENABLE_GLOBAL_MARKET_FILTERS = True
    config1.GLOBAL_EMA200_FILTER = True
    config1.GLOBAL_RSI_OVERBOUGHT_FILTER = True
    config1.GLOBAL_RSI_OVERBOUGHT_THRESHOLD = 75
    
    print(f"   ENABLE_GLOBAL_MARKET_FILTERS = {config1.ENABLE_GLOBAL_MARKET_FILTERS}")
    print(f"   GLOBAL_EMA200_FILTER = {config1.GLOBAL_EMA200_FILTER}")
    print(f"   GLOBAL_RSI_OVERBOUGHT_FILTER = {config1.GLOBAL_RSI_OVERBOUGHT_FILTER}")
    print(f"   GLOBAL_RSI_OVERBOUGHT_THRESHOLD = {config1.GLOBAL_RSI_OVERBOUGHT_THRESHOLD}")
    
    engine1 = BacktestingEngine(config1)
    
    # Test 2: Filters disabled
    print("\n2️⃣ Testing with GLOBAL FILTERS DISABLED")
    config2 = Config()
    config2.ENABLE_GLOBAL_MARKET_FILTERS = False
    
    print(f"   ENABLE_GLOBAL_MARKET_FILTERS = {config2.ENABLE_GLOBAL_MARKET_FILTERS}")
    
    engine2 = BacktestingEngine(config2)
    
    # Test the filter function directly on some sample data
    print("\n🔍 TESTING FILTER LOGIC ON SAMPLE DATA")
    print("-" * 40)
    
    # Find some test indices where we can check the filters
    test_indices = [1000, 2000, 3000, 4000, 5000]
    
    for idx in test_indices:
        if idx < len(engine1.df):
            # Get current values
            current_price = engine1.df['close'].iloc[idx]
            ema200 = engine1.df.get('EMA_200')
            rsi = engine1.df.get('RSI')
            
            if ema200 is not None and rsi is not None:
                ema200_value = ema200.iloc[idx]
                rsi_value = rsi.iloc[idx]
                
                # Test filter results
                filter_result_enabled = engine1._check_global_market_filters(idx)
                filter_result_disabled = engine2._check_global_market_filters(idx)
                
                print(f"\n📊 Index {idx}:")
                print(f"   Price: ${current_price:.2f}")
                print(f"   EMA200: ${ema200_value:.2f}")
                print(f"   RSI: {rsi_value:.2f}")
                print(f"   Price > EMA200: {current_price > ema200_value}")
                print(f"   RSI < 75: {rsi_value < 75}")
                print(f"   Filter (Enabled): {'✅ PASS' if filter_result_enabled else '❌ BLOCKED'}")
                print(f"   Filter (Disabled): {'✅ PASS' if filter_result_disabled else '❌ BLOCKED'}")
                
                # Check individual filter conditions
                if not pd.isna(ema200_value) and current_price < ema200_value:
                    print(f"   🚫 EMA200 Filter: Price below EMA200 - BUY BLOCKED")
                if not pd.isna(rsi_value) and rsi_value > 75:
                    print(f"   🚫 RSI Filter: RSI overbought ({rsi_value:.1f} > 75) - BUY BLOCKED")
    
    print("\n✅ Global filter testing completed!")
    print("\nThe filters should:")
    print("   • Block buys when price is below EMA200")
    print("   • Block buys when RSI is above 75")
    print("   • Allow buys when both conditions are met")

def test_filter_impact():
    """Test the impact of filters on actual trading"""
    print("\n\n🎯 TESTING FILTER IMPACT ON TRADING")
    print("=" * 60)
    
    # Run a small backtest with filters enabled
    print("\n📈 Running backtest WITH global filters...")
    config_with_filters = Config()
    config_with_filters.ENABLE_GLOBAL_MARKET_FILTERS = True
    config_with_filters.CURRENT_DATASET_SIZE = 5000  # Small test
    
    engine_with = BacktestingEngine(config_with_filters)
    results_with = engine_with._run_unified_backtest(0, 5000, 1.3, 0.75)
    
    # Run a small backtest with filters disabled
    print("\n📈 Running backtest WITHOUT global filters...")
    config_without_filters = Config()
    config_without_filters.ENABLE_GLOBAL_MARKET_FILTERS = False
    config_without_filters.CURRENT_DATASET_SIZE = 5000  # Small test
    
    engine_without = BacktestingEngine(config_without_filters)
    results_without = engine_without._run_unified_backtest(0, 5000, 1.3, 0.75)
    
    # Compare results
    print("\n📊 COMPARISON RESULTS:")
    print("-" * 40)
    print(f"WITH Filters:")
    print(f"   Total Trades: {results_with.get('total_trades', 0)}")
    print(f"   Total Return: {results_with.get('total_return_pct', 0):.2f}%")
    print(f"   Final Capital: ${results_with.get('final_capital', 0):,.2f}")
    
    print(f"\nWITHOUT Filters:")
    print(f"   Total Trades: {results_without.get('total_trades', 0)}")
    print(f"   Total Return: {results_without.get('total_return_pct', 0):.2f}%")
    print(f"   Final Capital: ${results_without.get('final_capital', 0):,.2f}")
    
    # Calculate difference
    trade_diff = results_without.get('total_trades', 0) - results_with.get('total_trades', 0)
    return_diff = results_without.get('total_return_pct', 0) - results_with.get('total_return_pct', 0)
    
    print(f"\n🔍 FILTER IMPACT:")
    print(f"   Trades Blocked: {trade_diff}")
    print(f"   Return Difference: {return_diff:.2f}%")
    
    if trade_diff > 0:
        print(f"   ✅ Filters are working - {trade_diff} trades were blocked")
    else:
        print(f"   ⚠️ No trades were blocked - filters may not be active in this period")

if __name__ == "__main__":
    test_global_filters()
    test_filter_impact()
